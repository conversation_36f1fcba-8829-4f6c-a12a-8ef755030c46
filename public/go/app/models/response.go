// <AUTHOR> 2025/2/24 15:54:00
package models

type ErrorRes struct {
	Result bool   `json:"result"`
	Msg    string `json:"msg"`
}

type CardWhiteListedRes struct {
	Result      bool     `json:"result"`
	Row         int      `json:"row"`
	AddCount    int      `json:"addCount"`
	UpdateCount int      `json:"updateCount"`
	RepeatCount int      `json:"repeatCount"`
	ErrorCount  int      `json:"errorCount"`
	ErrorCards  []string `json:"errorCards"`
}
