// <AUTHOR> 2025/2/24 10:00:00
package models

type Merchants struct {
	CreatedAt           string      `json:"created_at"`
	UpdatedAt           string      `json:"updated_at"`
	DeletedAt           string `json:"deleted_at"`
	MerchantId          string      `json:"merchant_id"`
	MerchantName        string      `json:"merchant_name"`
	Phone               string      `json:"phone"`
	Email               string      `json:"email"`
	JoinFee             float64     `json:"join_fee"`
	AnnualFee           float64     `json:"annual_fee"`
	Transfer            string      `json:"transfer"`
	TransferAccountType int         `json:"transfer_account_type"`
	TransferCurrency    string      `json:"transfer_currency"`
	TransferType        int         `json:"transfer_type"`
	DeductionType       int         `json:"deduction_type"`
	Status              int         `json:"status"`
	ApiToken            string      `json:"api_token"`
	OrderUprate         float64     `json:"order_uprate"`
	ParitiesUprate      float64     `json:"parities_uprate"`
	IsCredit            int         `json:"is_credit"`
	IsVirtual           int         `json:"is_virtual"`
	SpecialAuthChannels string      `json:"special_auth_channels"`
	ExternalMerchantId  interface{} `json:"external_merchant_id"`
	ExternalTerminalId  interface{} `json:"external_terminal_id"`
}


