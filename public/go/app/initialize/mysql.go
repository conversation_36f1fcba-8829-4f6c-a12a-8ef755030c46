// <AUTHOR> 2025/2/20 11:03:00
package initialize

import (
	"database/sql"
	"fmt"
	"os"
	"payment/app"
	"time"

	_ "github.com/go-sql-driver/mysql"
)

func init() {
	// 构建 DSN (Data Source Name)
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?parseTime=true",
		os.<PERSON>env("DB_USERNAME"),
		os.<PERSON>("DB_PASSWORD"),
		os.<PERSON>env("DB_HOST"),
		os.<PERSON>en<PERSON>("DB_PORT"),
		os.Getenv("DB_DATABASE"),
	)

	// 初始化连接池
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		panic(err)
	}

	// 连接池配置
	db.SetMaxOpenConns(3)                  // 最大连接数
	db.SetMaxIdleConns(2)                  // 最大空闲连接
	db.SetConnMaxLifetime(5 * time.Minute) // 连接最大生存时间

	// 验证连接
	if err = db.<PERSON>(); err != nil {
		panic(err)
	}

	app.DB = db
}
