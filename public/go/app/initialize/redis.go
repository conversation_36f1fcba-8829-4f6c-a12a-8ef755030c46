// <AUTHOR> 2025/2/20 11:25:00
package initialize

// func init() {
// 	rdb := redis.NewClient(&redis.Options{
// 		Addr:     os.Getenv("REDIS_HOST") + ":" + os.Getenv("REDIS_PORT"),
// 		Password: os.Getenv("REDIS_PASSWORD"),
// 		DB:       utils.GetRedisDB(),
// 	})
//
// 	if err := rdb.Ping(context.Background()).Err(); err != nil {
// 		log.Fatalf("Redis连接失败: %v", err)
// 	}
//
// 	app.Cache = rdb
// }
