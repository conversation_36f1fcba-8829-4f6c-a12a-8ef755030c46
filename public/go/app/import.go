// <AUTHOR> 2025/2/19 17:08:00
package app

import (
	"context"
	"database/sql"
	"encoding/csv"
	"fmt"
	"os"
	"payment/app/models"
	"payment/app/utils"
	"regexp"
	"strings"
	"time"
)

// CardWhiteListed 卡号白名单导入
func CardWhiteListed(src string) {
	// 打开CSV文件
	file, err := os.Open(src)
	if err != nil {
		fmt.Println(utils.JsonPrintln(models.ErrorRes{
			Result: false,
			Msg:    "打开文件失败：" + err.Error(),
		}))
		return
	}
	defer file.Close()

	reader := csv.NewReader(file)
	// 预分配内存
	handleCards := make([]models.CardRecord, 0, 10000) // 预分配容量
	cardList := make(map[string]bool)
	row, addCount, updateCount, repeatCount := 0, 0, 0, 0
	var errorCards []string

	// 预编译正则表达式
	re := regexp.MustCompile(`^[0-9]{6}[X]{6}[0-9]{4}$`)
	me := regexp.MustCompile(`^\d+(&\d+)*$`)

	merchantList, err := getMerchantList()
	if err != nil {
		fmt.Println(utils.JsonPrintln(models.ErrorRes{
			Result: false,
			Msg:    "获取商户列表失败：" + err.Error(),
		}))
		return
	}

	// 转换为map提高查询效率
	merchantMap := make(map[string]bool)
	for _, m := range merchantList {
		merchantMap[m.MerchantId] = true
	}

	for {
		record, err := reader.Read()
		if err != nil {
			break
		}

		row++

		// 验证卡掩码格式
		if !re.MatchString(record[0]) {
			errorCards = append(errorCards, fmt.Sprintf("第%d行：卡号掩码格式错误：%s", row, record[0]))
			continue
		}

		// 假设cardMasks是[][]string类型，merchantList是[]string类型
		if len(record) > 2 && record[2] != "" {
			// 正则验证格式（数字和&组成）
			matched := me.MatchString(record[2])
			if !matched {
				errorCards = append(errorCards, fmt.Sprintf("第%d行：商户ID格式错误：%s", row, record[2]))
				continue
			}

			// 过滤有效商户ID
			var validMerchants []string
			for _, m := range strings.Split(record[2], "&") {
				if merchantMap[m] {
					validMerchants = append(validMerchants, m)
				}
			}

			// 用&重新连接有效商户ID
			if len(validMerchants) > 0 {
				record[2] = strings.Join(validMerchants, "&")
			} else {
				record[2] = ""
			}
		}

		// 加密处理
		encrypted, err := utils.DES3Encrypt(record[0])
		if err != nil {
			fmt.Println(utils.JsonPrintln(models.ErrorRes{
				Result: false,
				Msg:    "加密失败：" + err.Error(),
			}))
			return
		}

		if ok, _ := cardList[encrypted]; !ok {
			handleCards = append(handleCards, models.CardRecord{
				CardNumber:  encrypted,
				CardMask:    utils.GetMarkCard(record[0]),
				CcType:      record[1],
				MerchantIds: record[2],
				CreatedAt:   time.Now().Format("2006-01-02 15:04:05"),
				UpdatedAt:   time.Now().Format("2006-01-02 15:04:05"),
			})
		}

		// 批量插入
		if len(handleCards) >= 10000 {
			aCount, uCount, rCount, err := bulkInsert(context.TODO(), DB, handleCards)
			if err != nil {
				fmt.Println(utils.JsonPrintln(models.ErrorRes{
					Result: false,
					Msg:    "插入失败：" + err.Error(),
				}))
				return
			}

			addCount += aCount
			updateCount += uCount
			repeatCount += rCount
			handleCards = handleCards[:0] // 清空切片
		}
	}

	// 插入剩余记录
	if len(handleCards) > 0 {
		aCount, uCount, rCount, err := bulkInsert(context.TODO(), DB, handleCards)
		if err != nil {
			fmt.Println(utils.JsonPrintln(models.ErrorRes{
				Result: false,
				Msg:    "最后插入失败：" + err.Error(),
			}))
			return
		} else {
			addCount += aCount
			updateCount += uCount
			repeatCount += rCount
		}
	}

	fmt.Println(utils.JsonPrintln(models.CardWhiteListedRes{
		Result:      true,
		Row:         row,
		AddCount:    addCount,
		UpdateCount: updateCount,
		RepeatCount: repeatCount,
		ErrorCount:  len(errorCards),
		ErrorCards:  errorCards,
	}))
}

func bulkInsert(ctx context.Context, db *sql.DB, records []models.CardRecord) (insertCount, updateCount, repeat int, err error) {
	// 批量查询已存在的卡号
	existingCards, err := getExistingCards(ctx, records)
	if err != nil {
		return
	}

	// 分离插入和更新数据
	var insertList, updateList []models.CardRecord
	for _, r := range records {
		if existing, ok := existingCards[r.CardNumber]; ok {
			// 合并商户ID（去重）
			merged := mergeMerchantIDs(existing.merchantIDs, r.MerchantIds)
			if merged == "" || existing.merchantIDs == merged {
				repeat++
				continue
			}

			updateList = append(updateList, models.CardRecord{
				CardNumber:  r.CardNumber,
				MerchantIds: merged,
				UpdatedAt:   time.Now().Format("2006-01-02 15:04:05"),
			})
		} else {
			insertList = append(insertList, r)
		}
	}

	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		err = fmt.Errorf("开启事务失败: %w", err)
		return
	}
	defer tx.Rollback()

	// 执行批量插入
	if len(insertList) > 0 {
		insertCount, err = doBatchInsert(ctx, tx, insertList)
		if err != nil {
			return
		}
	}

	// 执行批量更新
	if len(updateList) > 0 {
		updateCount, err = doBatchUpdate(ctx, tx, updateList)
		if err != nil {
			return
		}
	}

	if err = tx.Commit(); err != nil {
		err = fmt.Errorf("提交事务失败: %w", err)
		return
	}
	return
}

// 批量获取已存在卡号的商户ID
func getExistingCards(ctx context.Context, records []models.CardRecord) (map[string]struct{ merchantIDs string }, error) {
	placeholders := make([]string, len(records))
	args := make([]interface{}, len(records))
	for i, r := range records {
		placeholders[i] = "?"
		args[i] = r.CardNumber
	}

	query := fmt.Sprintf(`
        SELECT card_number, merchant_ids
        FROM card_white_listed
        WHERE card_number IN (%s)`,
		strings.Join(placeholders, ","))

	rows, err := DB.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("查询已存在卡号失败: %w", err)
	}
	defer rows.Close()

	result := make(map[string]struct{ merchantIDs string })
	for rows.Next() {
		var cardNumber, merchantIDs string
		if err = rows.Scan(&cardNumber, &merchantIDs); err != nil {
			return nil, fmt.Errorf("解析存在数据失败: %w", err)
		}
		result[cardNumber] = struct{ merchantIDs string }{merchantIDs: merchantIDs}
	}
	return result, nil
}

// 商户ID合并去重逻辑
func mergeMerchantIDs(existing, new string) string {
	merged := make(map[string]struct{})

	// 合并现有数据
	for _, m := range strings.Split(existing, "&") {
		if m != "" {
			merged[m] = struct{}{}
		}
	}

	// 合并新数据
	for _, m := range strings.Split(new, "&") {
		if m != "" {
			merged[m] = struct{}{}
		}
	}

	// 重新拼接
	if len(merged) == 0 {
		return ""
	}
	result := make([]string, 0, len(merged))
	for m := range merged {
		result = append(result, m)
	}
	return strings.Join(result, "&")
}

// 批量插入新数据
func doBatchInsert(ctx context.Context, tx *sql.Tx, records []models.CardRecord) (int, error) {
	const insertSQL = `INSERT IGNORE INTO card_white_listed
        (card_number, card_mask, cc_type, merchant_ids, created_at, updated_at)
        VALUES %s`

	placeholders := make([]string, len(records))
	args := make([]interface{}, 0, len(records)*6)
	for i, r := range records {
		placeholders[i] = "(?,?,?,?,?,?)"
		args = append(args, r.CardNumber, r.CardMask, r.CcType, r.MerchantIds, r.CreatedAt, r.UpdatedAt)
	}

	stmt := fmt.Sprintf(insertSQL, strings.Join(placeholders, ","))
	result, err := tx.ExecContext(ctx, stmt, args...)
	if err != nil {
		return 0, fmt.Errorf("批量插入失败: %w", err)
	}
	return getRowsAffected(result)
}

// 批量更新已有数据
func doBatchUpdate(ctx context.Context, tx *sql.Tx, records []models.CardRecord) (int, error) {
	const updateSQL = `UPDATE card_white_listed SET
        merchant_ids = CASE %s END,
        updated_at = CASE %s END
        WHERE card_number IN (%s)`

	// 参数索引计数器
	var merchantCases, timeCases []string
	var midArgs []interface{}
	var dateArgs []interface{}
	var cardArgs []interface{}
	whereParams := make([]string, 0, len(records))

	// 构建CASE语句参数（每个记录需要4个参数）
	for _, r := range records {
		// merchant_ids CASE子句参数
		merchantCases = append(merchantCases, "WHEN card_number = ? THEN ?")
		// updated_at CASE子句参数
		timeCases = append(timeCases, "WHEN card_number = ? THEN ?")
		// 收集WHERE条件参数
		whereParams = append(whereParams, "?")

		// 添加参数：卡号、merchant_ids、卡号、updated_at
		midArgs = append(midArgs, r.CardNumber, r.MerchantIds)
		dateArgs = append(dateArgs, r.CardNumber, r.UpdatedAt)
		cardArgs = append(cardArgs, r.CardNumber)
	}

	// 计算总长度预分配内存
	totalLen := len(midArgs) + len(dateArgs) + len(cardArgs)
	args := make([]interface{}, 0, totalLen)

	// 按顺序追加（保持参数顺序）
	args = append(args, midArgs...)
	args = append(args, dateArgs...)
	args = append(args, cardArgs...)

	// 构建完整SQL
	stmt := fmt.Sprintf(updateSQL,
		strings.Join(merchantCases, " "),
		strings.Join(timeCases, " "),
		strings.Join(whereParams, ","))

	result, err := tx.ExecContext(ctx, stmt, args...)
	if err != nil {
		return 0, fmt.Errorf("批量更新失败: %w\nSQL: %s\n参数: %v", err, stmt, args)
	}
	return getRowsAffected(result)
}

func getRowsAffected(result sql.Result) (int, error) {
	rows, err := result.RowsAffected()
	if err != nil {
		return 0, fmt.Errorf("获取影响行数失败: %w", err)
	}
	return int(rows), nil
}

func getMerchantList() ([]models.Merchants, error) {
	// 构建基础SQL
	querySQL := `
        SELECT merchant_id, merchant_name, status, created_at
        FROM merchants
    `

	// 执行查询
	rows, err := DB.Query(querySQL)
	if err != nil {
		return nil, fmt.Errorf("查询失败: %w", err)
	}
	defer rows.Close()

	// 解析结果集
	var merchants []models.Merchants
	for rows.Next() {
		var merchant models.Merchants
		if err = rows.Scan(
			&merchant.MerchantId,
			&merchant.MerchantName,
			&merchant.Status,
			&merchant.CreatedAt,
		); err != nil {
			return nil, fmt.Errorf("数据解析失败: %w", err)
		}
		merchants = append(merchants, merchant)
	}

	return merchants, nil
}
