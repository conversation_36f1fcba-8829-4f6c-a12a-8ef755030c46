// <AUTHOR> 2025/2/20 10:43:00
package utils

import (
	"bytes"
	"crypto/cipher"
	"crypto/des"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"strings"
	"time"
)

func GetMarkCard(value string) string {
	value = strings.TrimSpace(value)
	if len(value) < 12 { // 至少需要 12 位才能保证替换逻辑
		return value // 或返回错误
	}
	// 1. 保留前 6 位 (0-5)
	// 2. 从第6位开始替换 (索引6)
	// 3. 替换长度为总长度-10 → 实际保留最后4位
	return value[:6] + "XXXXXX" + value[len(value)-4:]
}

// DES3Encrypt 3DES 加密 (24字节密钥)
func DES3Encrypt(plaintext string) (string, error) {
	key := []byte(os.Getenv("DES3_KEY"))
	// 输入验证
	if len(key) != 24 { // 3DES 需要24字节密钥
		return "", errors.New("invalid key size, need 24 bytes")
	}

	// 转换明文为字节
	origData := []byte(plaintext)

	// 创建加密块
	block, err := des.NewTripleDESCipher(key)
	if err != nil {
		return "", err
	}

	// PKCS7 填充
	origData = pkcs7Pad(origData, block.BlockSize())

	// CBC 模式需要 IV (建议使用随机IV)
	iv := []byte(os.Getenv("DES3_IV"))

	// 加密
	ciphertext := make([]byte, len(origData))
	mode := cipher.NewCBCEncrypter(block, iv)
	mode.CryptBlocks(ciphertext, origData)

	// 返回十六进制字符串
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// pkcs7Pad PKCS7填充
func pkcs7Pad(data []byte, blockSize int) []byte {
	padding := blockSize - len(data)%blockSize
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(data, padText...)
}

// PrintMemReport 生成内存报告（含时间统计）
func PrintMemReport(start, end runtime.MemStats, startTime time.Time) string {
	return fmt.Sprintf(`
=== 内存变化报告 ===
运行时长: %.2fs

[堆内存]
启动时: %s
结束时: %s
净增长: %s

[系统内存]
总分配: %s
释放量: %s

[GC统计]
GC次数: %d 次
GC耗时: %dms
`,
		time.Since(startTime).Seconds(),
		FormatBytes(start.HeapAlloc),
		FormatBytes(end.HeapAlloc),
		FormatBytes(end.HeapAlloc-start.HeapAlloc),
		FormatBytes(end.Sys),
		FormatBytes(end.TotalAlloc-end.Frees),
		end.NumGC,
		end.PauseTotalNs/1e6,
	)
}

// FormatBytes 字节单位转换（智能选择合适单位）
func FormatBytes(b uint64) string {
	const unit = 1024
	if b < unit {
		return fmt.Sprintf("%d B", b)
	}
	div, exp := uint64(unit), 0
	for n := b / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.2f %cB", float64(b)/float64(div), "KMGTPE"[exp])
}

// FindEnvPath 自动寻找最近的.env文件（最多向上找5层）
func FindEnvPath() string {
	currentDir, _ := os.Getwd()
	for i := 0; i < 5; i++ {
		checkPath := filepath.Join(currentDir, ".env")
		if _, err := os.Stat(checkPath); err == nil {
			return checkPath
		}
		// 到达根目录时停止
		parentDir := filepath.Dir(currentDir)
		if parentDir == currentDir {
			break
		}
		currentDir = parentDir
	}
	return ""
}

func GetRedisDB() int {
	val := os.Getenv("REDIS_DB")
	if val == "" {
		return 0 // 默认使用 DB 0
	}

	// 支持十进制和十六进制格式
	dbNum, err := strconv.ParseInt(val, 10, 64)
	if err != nil {
		// 尝试十六进制解析（如 0x1a）
		dbNum, err = strconv.ParseInt(val, 16, 64)
		if err != nil {
			log.Printf("非法 REDIS_DB 值: %s，使用默认值 0", val)
			return 0
		}
	}

	return int(dbNum)
}

func JsonPrintln(v any) string {
	marshal, err := json.Marshal(v)
	if err != nil {
		return `{"result":false,"msg":"json解析失败"}`
	}

	return string(marshal)
}
