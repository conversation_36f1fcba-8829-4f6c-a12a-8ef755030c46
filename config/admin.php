<?php

$serviceType = env('SERVICE_TYPE', 'default');

$adminRoute = [
    'route'      => [
        'prefix' => env('ADMIN_ROUTE_PREFIX', 'admin'),
        'namespace' => 'App\\Admin\\Controllers',
        'middleware' => ['checkPermission'],
        'enable_session_middleware' => true,
    ],
    'auth'       => [
        'enable' => true,
        'controller' => App\Admin\Controllers\AuthController::class,
        'guard' => 'admin',
        'guards' => [
            'admin' => [
                'driver'   => 'session',
                'provider' => 'admin',
            ],
        ],
        'providers' => [
            'admin' => [
                'driver' => 'eloquent',
                'model'  => Dcat\Admin\Models\Administrator::class,
            ],
        ],
    ],
];

if ($serviceType == 'default' || $serviceType == 'm') {
    $adminRoute = [
        'route' => [
            'domain' => env('ADMIN_ROUTE_DOMAIN'),
            'prefix' => env('ADMIN_ROUTE_PREFIX', 'admin'),
            'namespace' => 'App\\Admin\\Controllers',
            'middleware' => ['web', 'admin', '2fa', 'changeLocale'],
            'enable_session_middleware' => true,
        ],
        'auth'  => [
            'enable' => true,
            'controller' => App\Admin\Controllers\AuthController::class,
            'guard' => 'admin',
            'guards' => [
                'admin' => [
                    'driver'   => 'session',
                    'provider' => 'admin',
                ],
            ],
            'providers' => [
                'admin' => [
                    'driver' => 'eloquent',
                    'model'  => Dcat\Admin\Models\Administrator::class,
                ],
            ],
            'remember'  => true,
            'except'    => [
                'auth/login',
                'auth/logout',
            ],
            'enable_session_middleware' => true,
        ],
        'grid' => [
            'grid_action_class' => Dcat\Admin\Grid\Displayers\Actions::class,
        ],
    ];
}

$appName = env('APP_NAME', 'Laravel');
switch ($appName) {
    case 'Embracy':
        $logo     = '<img src="/media/logos/embracy-logo.png" style="max-width: 200px !important;max-height: 200px !important;">';
        $logoMini = '<img src="/media/logos/embracy-logo.png">';
        break;

    case 'PunctualPay':
        $logo     = '<img src="/media/logos/punctualPaylogo.png" style="max-width: 150px !important;max-height: 100px !important;">';
        $logoMini = '<img src="/media/logos/punctualPaylogo.png">';
        break;

    case 'Hpaymerchants':
        $logo     = '<img src="/media/logos/hpaymerchants-logo.png" style="max-width: 150px !important;max-height: 100px !important;">';
        $logoMini = '<img src="/media/logos/hpaymerchants-logo.png">';
        break;

    default:
        $logo     = '<img src="/media/logos/logo.png" width="35"> eachy Pay';
        $logoMini = '<img src="/media/logos/logo.png" width="35"> eachy Pay';
        break;
}

$adminConfig = [

    /*
    |--------------------------------------------------------------------------
    | dcat-admin name
    |--------------------------------------------------------------------------
    |
    | This value is the name of dcat-admin, This setting is displayed on the
    | login page.
    |
    */
    'name' => 'Dcat Admin',

    /*
    |--------------------------------------------------------------------------
    | dcat-admin logo
    |--------------------------------------------------------------------------
    |
    | The logo of all admin pages. You can also set it as an image by using a
    | `img` tag, eg '<img src="http://logo-url" alt="Admin logo">'.
    |
    */
    'logo' => $logo,
    /*
    |--------------------------------------------------------------------------
    | dcat-admin mini logo
    |--------------------------------------------------------------------------
    |
    | The logo of all admin pages when the sidebar menu is collapsed. You can
    | also set it as an image by using a `img` tag, eg
    | '<img src="http://logo-url" alt="Admin logo">'.
    |
    */
    'logo-mini' => $logoMini,

    /*
     |--------------------------------------------------------------------------
     | User default avatar
     |--------------------------------------------------------------------------
     |
     | Set a default avatar for newly created users.
     |
     */
    'default_avatar' => '@admin/images/default-avatar.jpg',


    /*
    |--------------------------------------------------------------------------
    | dcat-admin install directory
    |--------------------------------------------------------------------------
    |
    | The installation directory of the controller and routing configuration
    | files of the administration page. The default is `app/Admin`, which must
    | be set before running `artisan admin::install` to take effect.
    |
    */
    'directory' => app_path('Admin'),

    /*
    |--------------------------------------------------------------------------
    | dcat-admin html title
    |--------------------------------------------------------------------------
    |
    | Html title for all pages.
    |
    */
    'title' => 'Admin',

    /*
    |--------------------------------------------------------------------------
    | Assets hostname
    |--------------------------------------------------------------------------
    |
   */
    'assets_server' => env('ADMIN_ASSETS_SERVER'),

    /*
    |--------------------------------------------------------------------------
    | Access via `https`
    |--------------------------------------------------------------------------
    |
    | If your page is going to be accessed via https, set it to `true`.
    |
    */
    'https' => env('ADMIN_HTTPS', false),

    /*
    |--------------------------------------------------------------------------
    | dcat-admin helpers setting.
    |--------------------------------------------------------------------------
    */
    'helpers' => [
        'enable' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | dcat-admin permission setting
    |--------------------------------------------------------------------------
    |
    | Permission settings for all admin pages.
    |
    */
    'permission' => [
        // Whether enable permission.
        'enable' => true,

        // All method to path like: auth/users/*/edit
        // or specific method to path like: get:auth/users.
        'except' => [
            '/',
            'auth/login',
            'auth/logout',
            'auth/setting',
            '2fa/password/verificationPage',
            '2fa/google2fa/verificationPage',
            '2fa/validateToken',
            '2fa/changePassword'
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | dcat-admin menu setting
    |--------------------------------------------------------------------------
    |
    */
    'menu' => [
        'cache' => [
            // enable cache or not
            'enable' => false,
            'store'  => 'file',
        ],

        // Whether enable menu bind to a permission.
        'bind_permission' => true,

    ],

    /*
    |--------------------------------------------------------------------------
    | dcat-admin upload setting
    |--------------------------------------------------------------------------
    |
    | File system configuration for form upload files and images, including
    | disk and upload path.
    |
    */
    'upload' => [

        // Disk in `config/filesystem.php`.
        'disk' => 'updateTemp',

        // Image and file upload path under the disk above.
        'directory' => [
            'image' => 'images',
            'file'  => 'files',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | dcat-admin database settings
    |--------------------------------------------------------------------------
    |
    | Here are database settings for dcat-admin builtin model & tables.
    |
    */
    'database' => [

        // Database connection for following tables.
        'connection' => '',

        // User tables and model.
        'users_table' => 'admin_users',
        'users_model' => Dcat\Admin\Models\Administrator::class,

        // Role table and model.
        'roles_table' => 'admin_roles',
        'roles_model' => Dcat\Admin\Models\Role::class,

        // Permission table and model.
        'permissions_table' => 'admin_permissions',
        'permissions_model' => Dcat\Admin\Models\Permission::class,

        // Menu table and model.
        'menu_table' => 'admin_menu',
        'menu_model' => Dcat\Admin\Models\Menu::class,

        // Pivot table for table above.
        'role_users_table'       => 'admin_role_users',
        'role_permissions_table' => 'admin_role_permissions',
        'role_menu_table'        => 'admin_role_menu',
        'permission_menu_table'  => 'admin_permission_menu',
        'settings_table'         => 'admin_settings',
        'extensions_table'       => 'admin_extensions',
        'extension_histories_table' => 'admin_extension_histories',
    ],

    /*
    |--------------------------------------------------------------------------
    | Application layout
    |--------------------------------------------------------------------------
    |
    | This value is the layout of admin pages.
    */
    'layout' => [
        // default, blue, blue-light, green
        'color' => 'default',

        'body_class' => '',

        'sidebar_collapsed' => false,

        // light, primary, dark
        'sidebar_style' => 'light',

        'dark_mode_switch' => false,

        // bg-primary, bg-info, bg-warning, bg-success, bg-danger, bg-dark
        'navbar_color' => '',
    ],

    /*
    |--------------------------------------------------------------------------
    | The exception handler class
    |--------------------------------------------------------------------------
    |
    */
    'exception_handler' => Dcat\Admin\Exception\Handler::class,

    /*
    |--------------------------------------------------------------------------
    | Enable default breadcrumb
    |--------------------------------------------------------------------------
    |
    | Whether enable default breadcrumb for every page content.
    */
    'enable_default_breadcrumb' => true,

    /*
    |--------------------------------------------------------------------------
    | Extension
    |--------------------------------------------------------------------------
    */
    'extension' => [
        // When you use command `php artisan admin:ext-make` to generate extensions,
        // the extension files will be generated in this directory.
        'dir' => base_path('dcat-admin-extensions'),
    ],
    'multi_app' => [
        'merchant' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | aws_alb
    |--------------------------------------------------------------------------
    |
    */
    'aws_alb' => env('AWS_ALB',false),
];

return array_merge($adminConfig, $adminRoute);
