<?php
$serviceType = env('SERVICE_TYPE', 'default');

$merchantRoute = [
    'route'      => [
        'prefix' => env('ADMIN_ROUTE_PREFIX', 'admin'),
        'namespace' => 'App\\Admin\\Controllers',
        'middleware' => ['checkPermission'],
        'enable_session_middleware' => true,
    ],
    'auth' => [
        'enable' => true,
        'controller' => App\Merchant\Controllers\AuthController::class,
        'guard' => 'web',
        'guards' => [
            'web' => [
                'driver'   => 'session',
                'provider' => 'merchant',
            ],
        ],
        'providers' => [
            'merchant' => [
                'driver' => 'custom',
                'model'  => App\Models\User::class,
            ],
        ],

    ],
];

if ($serviceType == 'default' || $serviceType == 'm') {
    $merchantRoute = [
        'route' => [
            'prefix' => 'merchant',
            'namespace' => 'App\\Merchant\\Controllers',
            'middleware' => ['web', 'admin', 'merchantStatus', '2faMerchant','change-locale'],
            'enable_session_middleware' => true,
        ],
        'auth' => [
            'enable' => true,

            'controller' => App\Merchant\Controllers\AuthController::class,

            'guard' => 'web',

            'guards' => [
                'web' => [
                    'driver'   => 'session',
                    'provider' => 'merchant',
                ],
            ],

            'providers' => [
                'merchant' => [
                    'driver' => 'custom',
                    'model'  => App\Models\User::class,
                ],
            ],

            // Add "remember me" to login form
            'remember' => true,

            // All method to path like: auth/users/*/edit
            // or specific method to path like: get:auth/users.
            'except' => [
                'auth/login',
                'auth/logout',
            ],

            'enable_session_middleware' => true,
        ],

        'grid' => [

            /*
            |--------------------------------------------------------------------------
            | The global Grid action display class.
            |--------------------------------------------------------------------------
            */
            'grid_action_class' => Dcat\Admin\Grid\Displayers\Actions::class,
        ],
    ];
}

$appName = env('APP_NAME', 'Laravel');
switch ($appName) {
    case 'Embracy':
        $logo     = '<img src="/media/logos/embracy-logo-vector.png" style="max-width: 200px !important;max-height: 200px !important;">';
        $logoMini = '<img src="/media/logos/embracy-logo-vector.png">';
        break;

    case 'PunctualPay':
        $logo     = '<img src="/media/logos/punctualPaylogo.png" style="max-width: 150px !important;max-height: 100px !important;">';
        $logoMini = '<img src="/media/logos/punctualPaylogo.png">';
        break;

    case 'Hpaymerchants':
        $logo     = '<img src="/media/logos/hpaymerchants-logo-vector.png" style="max-width: 150px !important;max-height: 100px !important;">';
        $logoMini = '<img src="/media/logos/hpaymerchants-logo-vector.png">';
        break;

    default:
        $logo     = '<img src="/media/logos/logo.png" width="35"> eachy Pay';
        $logoMini = '<img src="/media/logos/logo.png" width="35"> eachy Pay';
        break;
}

$merchantConfig = [

    /*
    |--------------------------------------------------------------------------
    | dcat-admin name
    |--------------------------------------------------------------------------
    |
    | This value is the name of dcat-admin, This setting is displayed on the
    | login page.
    |
    */
    'name' => env('APP_NAME', '商户平台'),

    /*
    |--------------------------------------------------------------------------
    | dcat-admin logo
    |--------------------------------------------------------------------------
    |
    | The logo of all admin pages. You can also set it as an image by using a
    | `img` tag, eg '<img src="http://logo-url" alt="Admin logo">'.
    |
    */
    'logo' => $logo,

    /*
    |--------------------------------------------------------------------------
    | dcat-admin mini logo
    |--------------------------------------------------------------------------
    |
    | The logo of all admin pages when the sidebar menu is collapsed. You can
    | also set it as an image by using a `img` tag, eg
    | '<img src="http://logo-url" alt="Admin logo">'.
    |
    */
    'logo-mini' => $logoMini,

    /*
	 |--------------------------------------------------------------------------
	 | User default avatar
	 |--------------------------------------------------------------------------
	 |
	 | Set a default avatar for newly created users.
	 |
	 */
	'default_avatar' => '@admin/images/default-avatar.jpg',

    /*
    |--------------------------------------------------------------------------
    | dcat-admin install directory
    |--------------------------------------------------------------------------
    |
    | The installation directory of the controller and routing configuration
    | files of the administration page. The default is `app/Admin`, which must
    | be set before running `artisan admin::install` to take effect.
    |
    */
    'directory' => app_path('Merchant'),

    /*
    |--------------------------------------------------------------------------
    | dcat-admin html title
    |--------------------------------------------------------------------------
    |
    | Html title for all pages.
    |
    */
    'title' => 'Admin',

    /*
    |--------------------------------------------------------------------------
    | Assets hostname
    |--------------------------------------------------------------------------
    |
   */
    'assets_server' => env('ADMIN_ASSETS_SERVER'),

    /*
    |--------------------------------------------------------------------------
    | Access via `https`
    |--------------------------------------------------------------------------
    |
    | If your page is going to be accessed via https, set it to `true`.
    |
    */
    'https' => env('ADMIN_HTTPS', false),

    /*
    |--------------------------------------------------------------------------
    | dcat-admin helpers setting.
    |--------------------------------------------------------------------------
    */
    'helpers' => [
        'enable' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | dcat-admin permission setting
    |--------------------------------------------------------------------------
    |
    | Permission settings for all admin pages.
    |
    */
    'permission' => [
        // Whether enable permission.
        'enable' => true,

        // All method to path like: auth/users/*/edit
        // or specific method to path like: get:auth/users.
        'except' => [
            '/',
            'auth/login',
            'auth/logout',
            'auth/setting',
            '2fa/password/verificationPage',
            '2fa/google2fa/verificationPage',
            '2fa/validateToken',
            '2fa/changePassword'
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | dcat-admin menu setting
    |--------------------------------------------------------------------------
    |
    */
    'menu' => [
        'cache' => [
            // enable cache or not
            'enable' => false,
            'store'  => 'file',
        ],

        // Whether enable menu bind to a permission.
        'bind_permission' => true,

    ],

    /*
    |--------------------------------------------------------------------------
    | dcat-admin upload setting
    |--------------------------------------------------------------------------
    |
    | File system configuration for form upload files and images, including
    | disk and upload path.
    |
    */
    'upload' => [

        // Disk in `config/filesystem.php`.
        'disk' => 'merchant',

        // Image and file upload path under the disk above.
        'directory' => [
            'image' => 'images',
            'file'  => 'files',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | dcat-admin database settings
    |--------------------------------------------------------------------------
    |
    | Here are database settings for dcat-admin builtin model & tables.
    |
    */
    'database' => [

        // Database connection for following tables.
        'connection' => '',

        // User tables and model.
        'users_table' => 'users',
        'users_model' => App\Models\User::class,

        // Role table and model.
        'roles_table' => 'merchant_roles',
        'roles_model' => App\Models\MerchantRole::class,

        // Permission table and model.
        'permissions_table' => 'merchant_permissions',
        'permissions_model' => App\Models\MerchantPermission::class,

        // Menu table and model.
        'menu_table' => 'merchant_menus',
        'menu_model' => App\Models\MerchantMenu::class,

        // Pivot table for table above.
        'operation_log_table'    => 'admin_operation_log',
        'role_users_table'       => 'merchant_role_users',
        'role_permissions_table' => 'merchant_role_permissions',
        'role_menu_table'        => 'merchant_role_menu',
        'permission_menu_table'  => 'merchant_permission_menu',
    ],

    /*
    |--------------------------------------------------------------------------
    | User operation log setting
    |--------------------------------------------------------------------------
    |
    | By setting this option to open or close operation log in dcat-admin.
    |
    */
    'operation_log' => [

        'enable' => true,

        // Only logging allowed methods in the list
        'allowed_methods' => ['GET', 'HEAD', 'POST', 'PUT', 'DELETE', 'CONNECT', 'OPTIONS', 'TRACE', 'PATCH'],

        'secret_fields' => [
            'password',
            'password_confirmation',
        ],

        // Routes that will not log to database.
        // All method to path like: auth/logs/*/edit
        // or specific method to path like: get:auth/logs.
        'except' => [
            'auth/logs*',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Admin map field provider
    |--------------------------------------------------------------------------
    |
    | Supported: "tencent", "google", "yandex".
    |
    */
    'map_provider' => 'google',

    /*
    |--------------------------------------------------------------------------
    | Application layout
    |--------------------------------------------------------------------------
    |
    | This value is the layout of admin pages.
    */
    'layout' => [
        // indigo, blue, blue-light, blue-dark, green
        'color' => 'default',

        'body_class' => '',

        'sidebar_collapsed' => false,

        // light, primary, dark
        'sidebar_style' => env('SIDEBAR_STYLE','light'),

        'dark_mode_switch' => false,

        // bg-primary, bg-info, bg-warning, bg-success, bg-danger, bg-dark
        'navbar_color' => '',
    ],

    /*
    |--------------------------------------------------------------------------
    | The exception handler class
    |--------------------------------------------------------------------------
    |
    */
    'exception_handler' => Dcat\Admin\Exception\Handler::class,

    /*
    |--------------------------------------------------------------------------
    | Enable default breadcrumb
    |--------------------------------------------------------------------------
    |
    | Whether enable default breadcrumb for every page content.
    */
    'enable_default_breadcrumb' => env('APP_NAME', 'Laravel') == 'Embracy' ? false : true,

    /*
    |--------------------------------------------------------------------------
    | Settings for extensions.
    |--------------------------------------------------------------------------
    |
    | You can find all available extensions here
    | https://github.com/dcat-admin-extensions.
    |
    */
    'extensions' => [

    ]
];

return array_merge($merchantConfig, $merchantRoute);
