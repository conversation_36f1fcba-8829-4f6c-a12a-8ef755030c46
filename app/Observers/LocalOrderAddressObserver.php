<?php

namespace App\Observers;

use App\Models\LocalOrderAddress;
use App\Models\DirectoryCountry;
use App\Classes\Supports\Traits\HasHttpRequest;

class LocalOrderAddressObserver
{
    use HasHttpRequest;
    
    /**
     * Handle the local order address "created" event.
     *
     * @param  App\Models\LocalOrderAddress  $address
     * @return void
     */
    public function creating(LocalOrderAddress $address)
    {
        $address->bill_name = $address->bill_first_name . ' ' . $address->bill_last_name;
        $address->ship_name = $address->ship_first_name . ' ' . $address->ship_last_name;

        $address->bill_country = DirectoryCountry::getCountryByIsoa2($address['bill_country_isoa2']);
        $address->ship_country = DirectoryCountry::getCountryByIsoa2($address['ship_country_isoa2']);

        if ($ipData = json_decode($this->get('http://***********/DbipService.php?ip=' . $address['ip']), true)) {
            $address->ip_country       = DirectoryCountry::getCountryByIsoa2($ipData['country']['iso_code']);
            $address->ip_country_isoa2 = $ipData['country']['iso_code'] ?? '';
            $address->ip_isp           = $ipData['traits']['isp'] ?? '';
            $address->ip_city          = $ipData['city']['names']['en'] ?? '';
            $address->ip_postal_code   = isset($ipData['postal']['code']) ? $ipData['postal']['code'] : '';
        }
    }
}
