<?php

namespace App\Observers;

use App\Models\CardSource;
use Faker\Factory;
use Illuminate\Support\Facades\Validator;
use App\Classes\Supports\Traits\HasHttpRequest;
use App\Models\OrderAddress;

class CardSourceObserver
{
    use HasHttpRequest;

    //美国邮箱后缀
    public $_emailSuffix = [
        'netzero.net', 'twcny.rr.com', 'comcast.net', 'warwick.net', 'comcast.net', 'cs.com', 'verizon.net'
    ];

    //美国IP段
    public $_ipData = [
        ['*******', '*************'], ['*******', '*************'], ['********', '**************'], ['2*******', '**************'], ['********', '**************'], ['********', '**************'], ['********', '**************'], ['100.0.0.0', '**************'], ['21*******', '*************'], ['1********', '**************'],
    ];


	public function creating(CardSource $cardSource)
	{
        $factory     = Factory::create();
        $emailResult = false;
        $ipResult    = false;

        $email = strtolower($factory->firstName . $factory->lastName) . '@' . $this->_emailSuffix[array_rand($this->_emailSuffix)];

        $validator = Validator::make(['email' => $email], [
            'email' => 'email:rfc,dns'
        ]);
        if (!$validator->fails()) {
            //email验证通过
            $emailResult = true;
        }

        $tempIp = $this->_ipData[array_rand($this->_ipData)];

        $startIp = explode('.', $tempIp[0]);
        $endIp   = explode('.', $tempIp[1]);

        $ip = '';
        foreach ($startIp as $k => $v) {
            $ip .= rand($v, $endIp[$k]) . '.';
        }

        $ip = trim($ip, '.');

        if ($ipDate = json_decode($this->get('http://***********/DbipService.php?ip=' . $ip), true)) {
            if ($ipDate['country']['iso_code'] == 'US') {
                //ip 验证通过
                $ipResult = true;
            }
        }

        if (!$emailResult || !$ipResult) {

            $orderAddress = OrderAddress::where('ip_country_isoa2', 'US')->inRandomOrder()->first();

            if (!$emailResult) {
                $email = strtolower($factory->firstName . $factory->lastName) . '@' . explode('@', $orderAddress->bill_email)[1];
            }

            if (!$ipResult) {
                $tempIp    = explode('.', $orderAddress->ip);
                $tempIp[3] = rand(0, 256);
                $ip        = implode('.', $tempIp);
            }
        }

		$phone   = $factory->phoneNumber; // (************* x87954

		// 去掉 x以后
		if ($position = strpos($phone, 'x')) {
			$phone = substr($phone, 0, $position - 1);
		}

		$cardSource->cardInfo()->create([
			'card_number' => $cardSource->card_number,
			'ip'          => $ip,
			'first_name'  => $factory->firstName,
			'last_name'   => $factory->lastName,
			'email'       => $email,
			'phone'       => $phone,
			'address'     => $factory->streetAddress,
			'city'        => $factory->city,
			'state'       => $factory->state,
			'country'     => 'US',
			'post_code'   => $factory->postcode,
		]);
	}
}
