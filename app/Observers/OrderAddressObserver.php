<?php

namespace App\Observers;

use App\Models\DirectoryCountry;
use App\Models\OrderAddress;
use App\Classes\Supports\Traits\HasHttpRequest;

// creating, created, updating, updated, saving,
// saved,  deleting, deleted, restoring, restored
class OrderAddressObserver
{
    use HasHttpRequest;

    public function creating(OrderAddress $address)
    {
        // billState/shipState为空时，默认填充NA
        $address->bill_state = empty($address->bill_state) ? 'NA' : $address->bill_state;
        $address->ship_state = empty($address->ship_state) ? 'NA' : $address->ship_state;

        $address->bill_name = $address->bill_first_name . ' ' . $address->bill_last_name;
        $address->ship_name = $address->ship_first_name . ' ' . $address->ship_last_name;

        $address->bill_country = DirectoryCountry::getCountryByIsoa2($address['bill_country_isoa2']);
        $address->ship_country = DirectoryCountry::getCountryByIsoa2($address['ship_country_isoa2']);

        if ($ipData = json_decode($this->get('http://***********/DbipService.php?ip=' . $address['ip']), true)) {
            $address->ip_country       = DirectoryCountry::getCountryByIsoa2($ipData['country']['iso_code']);
            $address->ip_country_isoa2 = $ipData['country']['iso_code'] ?? '';
            $address->ip_isp           = $ipData['traits']['isp'] ?? '';
            $address->ip_city          = $ipData['city']['names']['en'] ?? '';
            $address->ip_postal_code   = isset($ipData['postal']['code']) ? $ipData['postal']['code'] : '';
        }
    }
}
