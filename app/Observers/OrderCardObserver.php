<?php

namespace App\Observers;

use App\Models\OrderCard;
use DES3;

// creating, created, updating, updated, saving,
// saved,  deleting, deleted, restoring, restored
class OrderCardObserver
{
    public function creating(OrderCard $orderCard)
    {
        $orderCard->cc_type          = isset($orderCard->cc_type) && !empty($orderCard->cc_type) ? $orderCard->cc_type : get_cc_type($orderCard->card_number);
        $orderCard->card_number      = DES3::encrypt($orderCard->card_number);
        $orderCard->expiration_month = DES3::encrypt($orderCard->expiration_month);
        $orderCard->expiration_year  = DES3::encrypt($orderCard->expiration_year);
        $orderCard->cvv              = DES3::encrypt($orderCard->cvv);
    }
}
