<?php

namespace App\Exports\Bills\Coshine;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\DefaultValueBinder;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;

class DailyBillExport extends DefaultValueBinder implements FromArray, WithHeadings, WithCustomValueBinder
{

    /**
     * @var array
     */
    private $rows;

    public function __construct(array $rows)
    {
        $this->rows = $rows;
    }

    public function array(): array
    {
        return $this->rows;
    }

    public function headings(): array
    {
        return [
            'Merchant Transaction ID',
            'Transaction ID',
            'Transaction Type',
            'Card BIN/Last 4 Digits',
            'Transaction Amount',
            'Transaction Currency',
            'Reconciliation Amount',
            'Reconciliation Currency',
            'Fee Amount',
            'Fee Currency',
            'Other Fee Amount',
            'Other Fee Currency',
            'Transaction Time',
            'Settlement Date',
            'Settlement Cycle',
            'Merchant ID',
            'Terminal ID',
            'Transaction Status',
            'Approval Code',
            'Arn',
            'Network Transaction ID',
            'Indicator',
            'Advice Code',
            'Trace Number',
            'Entry Mode',
            'Condition Code',
            'Chargeback Reason Code',
            'Chargeback Reference ID',
            'Product Identifier',
        ];
    }

    public function bindValue(Cell $cell, $value)
    {
        if (is_numeric($value)) {
            $cell->setValueExplicit($value, DataType::TYPE_STRING);
            return true;
        }

        return parent::bindValue($cell, $value);
    }
}