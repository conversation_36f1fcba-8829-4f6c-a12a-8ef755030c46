<?php

namespace App\Exports\MerchantReportForm;

use App\Classes\Pay\Contracts\Support;
use App\Models\DirectoryDictionary;
use App\Models\MerchantBusiness;
use App\Models\MerchantRiskFrozenOperation;
use App\Models\OrderSettlement;
use App\Models\SettleAdjustment;
use App\Models\SettleDetail;
use App\Models\SettleDetailMerchant;
use App\Models\TransferTicket;
use App\Services\SettlementService;
use Illuminate\Support\Facades\DB;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

trait SettleTotal
{
    protected $params;
    protected $lang;
    protected $currencyList = [];
	protected $title = [
		'en' 	=> 'MID Statement',
		'zh_CN' => 'MID对账单',
	];
    protected $fieldData = [
        'en'    => [
            'title'                 => 'Merchant Settlement Report',
            'count'                 => 'Count',
            'amount'                => 'Amount',
            'current_withdrawal'    => 'Current Withdrawal',
            'historical_processing' => 'Historical/Processing Withdrawal',
            'refund_chargeback'     => 'Refund/Chargeback Pending Settlement',
            'frozen_amount'         => 'Amount Frozen',
            'amount_type_list'      => [
                'transaction_amount'         => 'Total Transaction',
                'line_null_01'               => '',
                'transaction_fee'            => 'Transaction Fee',
                'transaction_3d_fee'         => '3D Transaction Fee',
                'transaction_rate_fee'       => 'Merchant Discount Rate Fee',
                'transaction_deposit'        => 'Rolling Reserve',
                'transaction_risk_fee'       => 'Risk Control Fee',
                'line_null_02'               => '',
                'refund_amount'              => 'Total Refund',
                'refund_fee'                 => 'Refund Fee',
                'line_null_03'               => '',
                'chargeback_amount'          => 'Total Chargeback',
                'chargeback_fee'             => 'Chargeback Fee',
                'chargeback_reversal_amount' => 'Total Chargeback Resolved',
                'pre_chargeback_fee'         => 'Chargeback Alert Fee',
                'chargeback_penalty'         => 'Chargeback Penalty',
                'chargeback_penalty_special' => 'Chargeback Penalty *',
                'line_null_04'               => '',
                'settle_adjust_amount'       => 'Total Settlement Adjustment'
            ],
            'amount_settle_total_list' => [
                'settle_amount'      => 'Current Settlement',
                'in_deposit_amount'  => 'Current Reserve',
                'out_deposit_amount' => 'Release of Current Reserve'
            ]
        ],
        'zh_CN' => [
            'title'                 => '商户结算报表:',
            'count'                 => '笔数(Count)',
            'amount'                => '金额(Amount)',
            'current_withdrawal'    => '当期提现金额(Current Withdrawal)',
            'historical_processing' => '已提现、处理中提现金额(Historical/Processing Withdrawal)',
            'refund_chargeback'     => '未结算退款拒付(Refund/Chargeback Pending Settlement)',
            'frozen_amount'         => '冻结金额(Amount Frozen)',
            'amount_type_list'      => [
                'transaction_amount'         => '交易金额总计(Total Transaction）',
                'line_null_01'               => '',
                'transaction_fee'            => '交易处理费(Transaction Fee)',
                'transaction_3d_fee'         => '3D处理费(3D Transaction Fee)',
                'transaction_rate_fee'       => '比例手续费(Merchant Discount Rate Fee)',
                'transaction_deposit'        => '交易保证金(Rolling Reserve)',
                'transaction_risk_fee'       => '风控处理费(Risk Control Fee)',
                'line_null_02'               => '',
                'refund_amount'              => '退款金额总计(Total Refund)',
                'refund_fee'                 => '退款处理费(Refund Fee)',
                'line_null_03'               => '',
                'chargeback_amount'          => '拒付金额总计(Total Chargeback)',
                'chargeback_fee'             => '拒付处理费(Chargeback Fee)',
                'chargeback_reversal_amount' => '申诉成功金额总计(Total Chargeback Resolved)',
                'pre_chargeback_fee'         => '预拒付处理费(Chargeback Alert Fee)',
                'chargeback_penalty'         => '拒付罚金(Chargeback Penalty)',
                'chargeback_penalty_special' => '拒付罚金*(Chargeback Penalty *)',
                'line_null_04'               => '',
                'settle_adjust_amount'       => '结算调整金额总计(Total Settlement Adjustment)'
            ],
            'amount_settle_total_list' => [
                'settle_amount'      => '当期到账金额(Current Settlement)',
                'in_deposit_amount'  => '当期保证金入账金额(Current Reserve)',
                'out_deposit_amount' => '当期保证金返还金额(Release of Current Reserve)'
            ]
        ]
    ];

    public function __construct(array $params = [], string $lang = 'zh_CN')
    {
        $this->params = $params;
        $this->lang   = $lang;
    }

    public function title(): string
    {
        return $this->title[$this->lang];
    }

    public function bindValue(Cell $cell, $value)
    {
        $cell->setValueExplicit($value, DataType::TYPE_STRING);

        return true;
    }

    public function collection()
    {
        // 语言和语言包
        $lang      = $this->lang;
        $fieldData = $this->fieldData[$lang];

        $settleDetails    = []; //BID结算详情
        $settleAdjustment = []; //BID调整详情
        $settleMerchant   = []; //BID结转MID明细
        $tempAmountType   = ['50' => 'settle_amount', '51' => 'in_deposit_amount', '52' => 'out_deposit_amount'];
        $businessIds      = [];
        $staSettleAt      = '2200-01-01';
        $endSettleAt      = '1970-01-01';
        foreach ($this->params['query_criteria'] as $value) {
            $businessIds[] = $value['business_id'];
            $staSettleAt   = SettlementService::mixDate($value['str_at'], $staSettleAt);
            $endSettleAt   = SettlementService::maxDate($value['end_at'], $endSettleAt);

            //获取MID币种列表
            $business           = MerchantBusiness::query()->where('internal_status', MerchantBusiness::INTERNAL_STATUS_ENABLE)
                ->where('business_id', $value['business_id'])
                ->get()->toArray();
            $tempCurrency       = !empty($business) ? array_unique(explode(',', implode(',', array_column($business, 'settle_currencies')))) : [];
            $this->currencyList = array_keys(array_flip(array_merge($tempCurrency, $this->currencyList)));
            $tempCurrencyList   = $this->currencyList;

            //BID结算明细汇总信息
            $tempSettleDetails = SettleDetail::query()->where('business_id', $value['business_id'])
                ->whereBetween('settle_at', [$value['str_at'], $value['end_at']])
                ->where('settle_at', '<=', date('Y-m-d'))
                ->whereNotIn('amount_type', [SettleDetail::AMOUNT_TYPE_50, SettleDetail::AMOUNT_TYPE_51, SettleDetail::AMOUNT_TYPE_52])
                ->groupBy('settle_currency', 'amount_type')
                ->get([
                    'settle_currency',
                    'amount_type',
                    DB::raw('COUNT(id) as total'),
                    DB::raw('SUM(settle_amount) as settle_amount'),
                ])->toArray();

            foreach ($tempSettleDetails as $detail) {
                $field = SettleDetail::$settlementAmountTypeMap[$detail['amount_type']] ?? '';

                if (empty($field)) continue;

                if (!isset($settleDetails[$field][$detail['settle_currency']])) {
                    $settleDetails[$field][$detail['settle_currency']]['total']         = '0';
                    $settleDetails[$field][$detail['settle_currency']]['settle_amount'] = '0.00';
                }

                $settleDetails[$field][$detail['settle_currency']]['total']         += $detail['total'];
                $settleDetails[$field][$detail['settle_currency']]['settle_amount'] += $detail['settle_amount'];
            }

            //释放内存
            $tempSettleDetails = null;

            //BID结转MID明细
            $tempSettleDetails = SettleDetail::query()->where('business_id', $value['business_id'])
                ->whereBetween('settle_at', [$value['str_at'], $value['end_at']])
                ->where('settle_at', '<=', date('Y-m-d'))
                ->whereIn('amount_type', [SettleDetail::AMOUNT_TYPE_50, SettleDetail::AMOUNT_TYPE_51, SettleDetail::AMOUNT_TYPE_52])
                ->groupBy('settle_currency', 'amount_type')
                ->get([
                    'settle_currency',
                    'amount_type',
                    DB::raw('COUNT(id) as total'),
                    DB::raw('SUM(settle_amount) as settle_amount'),
                ])->toArray();

            foreach ($tempSettleDetails as $detail) {
                $field = $tempAmountType[$detail['amount_type']] ?? '';

                if (empty($field)) continue;

                if (!isset($settleMerchant[$field][$detail['settle_currency']])) {
                    $settleMerchant[$field][$detail['settle_currency']]['total']         = '0';
                    $settleMerchant[$field][$detail['settle_currency']]['settle_amount'] = '0.00';
                }

                $settleMerchant[$field][$detail['settle_currency']]['total']         += $detail['total'];
                $settleMerchant[$field][$detail['settle_currency']]['settle_amount'] += $detail['settle_amount'];
            }

            //释放内存
            $tempSettleDetails = null;

            $tempSettleAdjustment = SettleAdjustment::query()->where('business_id', $value['business_id'])
                ->whereBetween('settle_at', [$value['str_at'], $value['end_at']])
                ->where('type', SettleAdjustment::TYPE_BID)
                ->groupBy('d_type_id', 'settle_currency')
                ->get([
                    'd_type_id',
                    'settle_currency',
                    DB::raw('COUNT(id) as total'),
                    DB::raw('SUM(settle_amount) as settle_amount')
                ])->toArray();

            foreach ($tempSettleAdjustment as $adjustment) {
                $settleAdjustment[$adjustment['d_type_id']][$adjustment['settle_currency']]['settle_amount'] = $adjustment['settle_amount'];
                $settleAdjustment[$adjustment['d_type_id']][$adjustment['settle_currency']]['total']         = $adjustment['total'];
            }

            //释放内存
            $tempSettleAdjustment = null;
        }

        // 返回数组
        $data      = [];
        $data[]    = [];
        $maxLength = count($this->currencyList) * 2 + 3; // 空3格

        // 标题
        $data[] = [
            '', '', '',
            $fieldData['title'],
        ];

        // 空两行
        $data[] = [];
        $data[] = [];

        // 币种行
        $cnt = 0;
        array_unshift($tempCurrencyList, '', '', '');

        foreach ($tempCurrencyList as $key => $value) {
            if (empty($value)) {
                $cnt++;
                continue;
            }

            $tempCurrencyList[$cnt] = $value;
            $cnt++;
            $tempCurrencyList[$cnt] = '';
            $cnt++;
        }

        $data[] = $tempCurrencyList;

        // 金额/笔数 标题栏
        $amountTitle = ['', '', ''];

        foreach ($this->currencyList as $key => $value) {
            $amountTitle[] = $fieldData['count'];
            $amountTitle[] = $fieldData['amount'];
        }

        $data[] = $amountTitle;
        // 金额详情列表(BID项)
        $amountTypeList = $fieldData['amount_type_list'];

        foreach ($amountTypeList as $field => $text) {
            if (empty($text)) {
                $data[] = [];  // 空行
                continue;
            }

            $amountList = $settleDetails[$field] ?? [];
            $amountData = ['', '', $text];

            foreach ($this->currencyList as $currency) {
                $amountData[] = !empty($amountList[$currency]['total']) ? $amountList[$currency]['total'] : '0';
                $amountData[] = !empty($amountList[$currency]['settle_amount']) ? $amountList[$currency]['settle_amount'] : '0.00';
            }

            $data[] = $amountData;
        }

        // 结算调整明细汇总
        $adjustmentTypeList = DirectoryDictionary::select('id', 'name', 'remarks')->where('type', '结算调整类型')->orderBy('sort')->get()->keyBy('id')->toArray();
        foreach ($adjustmentTypeList as $id => $text) {
            if ($lang == 'zh_CN') {
                $tempName = $text['name'] . '(' . $text['remarks'] . ')';
            } else {
                $tempName = $text['remarks'];
            }

            $adjustmentAmount = $settleAdjustment[$id] ?? [];
            $adjustmentData   = ['', '', $tempName];

            foreach ($this->currencyList as $currency) {
                $adjustmentData[] = !empty($adjustmentAmount[$currency]['total']) ? $adjustmentAmount[$currency]['total'] : '0';
                $adjustmentData[] = !empty($adjustmentAmount[$currency]['settle_amount']) ? $adjustmentAmount[$currency]['settle_amount'] : '0.00';
            }

            $data[] = $adjustmentData;
        }

        // 空两行
        $data[] = [];
        $data[] = [];

        // MID结算明细汇总信
        $settleDetails     = [];
        $tempSettleDetails = SettleDetailMerchant::query()->where('merchant_id', $this->params['merchant_id'])
            ->where('settle_at', '<=', date('Y-m-d'))
            ->whereBetween('settle_at', [$staSettleAt, $endSettleAt])
            ->where('amount_type', '<>', SettleDetailMerchant::AMOUNT_TYPE_0)
            ->groupBy('settle_currency', 'amount_type')
            ->get([
                'settle_currency',
                'amount_type',
                DB::raw('COUNT(id) as total'),
                DB::raw('SUM(settle_amount) as settle_amount'),
            ])->toArray();

        foreach ($tempSettleDetails as $detail) {
            if (!isset($settleDetails[$detail['amount_type']][$detail['settle_currency']])) {
                $settleDetails[$detail['amount_type']][$detail['settle_currency']]['total']         = '0';
                $settleDetails[$detail['amount_type']][$detail['settle_currency']]['settle_amount'] = '0.00';
            }

            $settleDetails[$detail['amount_type']][$detail['settle_currency']]['total']         += $detail['total'];
            $settleDetails[$detail['amount_type']][$detail['settle_currency']]['settle_amount'] += $detail['settle_amount'];
        }

        // 释放内存
        $tempSettleDetails = null;

        //把BID结转MID明细汇总添加到数组
        if (!empty($settleMerchant['settle_amount'])) {
            foreach ($settleMerchant['settle_amount'] as $currency => $value) {
                if (!isset($settleDetails[SettleDetailMerchant::AMOUNT_TYPE_0][$currency])) {
                    $settleDetails[SettleDetailMerchant::AMOUNT_TYPE_0][$currency]['total']         = '0';
                    $settleDetails[SettleDetailMerchant::AMOUNT_TYPE_0][$currency]['settle_amount'] = '0.00';
                }

                $settleDetails[SettleDetailMerchant::AMOUNT_TYPE_0][$currency]['total']         += $value['total'];
                $settleDetails[SettleDetailMerchant::AMOUNT_TYPE_0][$currency]['settle_amount'] += $value['settle_amount'];
            }
        }

        // 金额详情列表(MID项)
        $amountTypeList = SettleDetailMerchant::$amountTypeMap;

        foreach ($amountTypeList as $type => $text) {
            if ($lang == 'zh_CN') {
                $tempName = $text . '(' . SettleDetailMerchant::$amountTypeNoteMap[$type] . ')';
            } else {
                $tempName = SettleDetailMerchant::$amountTypeNoteMap[$type];
            }

            $amountList = $settleDetails[$type] ?? [];
            $amountData = ['', '', $tempName];

            foreach ($this->currencyList as $currency) {
                $amountData[] = !empty($amountList[$currency]['total']) ? $amountList[$currency]['total'] : '0';
                $amountData[] = !empty($amountList[$currency]['settle_amount']) ? $amountList[$currency]['settle_amount'] : '0.00';
            }

            $data[] = $amountData;
        }

        // 空一行
        $data[] = [];

        // MID结算汇总项
        $amountSettleTotalList = $fieldData['amount_settle_total_list'];
        foreach ($amountSettleTotalList as $field => $text) {
            $amountData = ['', '', $text];

            foreach ($this->currencyList as $currency) {
                $amount = '0.00';
                if (isset($settleMerchant[$field][$currency]['settle_amount'])) {
                    $amount = Support::amount_format($settleMerchant[$field][$currency]['settle_amount']);
                }

                $amountData[] = '-';
                $amountData[] = $amount;
            }

            $data[] = $amountData;
        }

        // 空两行
        $data[] = [];
        $data[] = [];

        // MID 提现项
        // 总计提现
        $transferStatusList = [
            TransferTicket::TRANSFER_STATUS_SUCCESS,
            TransferTicket::TRANSFER_STATUS_CHECK,
            TransferTicket::TRANSFER_STATUS_HANDLE,
            TransferTicket::TRANSFER_STATUS_PROCESS
        ];
        $transfer           = TransferTicket::query()->whereIn('status', $transferStatusList);
        $transfer->where('applicant_id', $this->params['merchant_id']);

        $transferList = $transfer->groupBy('currency')
            ->get(['currency', DB::raw('SUM(deduction_amount) as amount')])
            ->pluck('amount', 'currency')
            ->toArray();

        // 当期提现
        $transferCurrent = $transfer->where('status', TransferTicket::TRANSFER_STATUS_SUCCESS)
            ->whereBetween('updated_at', [$staSettleAt . ' 00:00:00', $endSettleAt . ' 23:59:59']);

        $transferCurrentList = $transferCurrent->groupBy('currency')
            ->get(['currency', DB::raw('SUM(deduction_amount) as amount')])
            ->pluck('amount', 'currency')
            ->toArray();

        // 获取未结算退款拒付
        $typeList      = [
            OrderSettlement::TYPE_REFUND,
            OrderSettlement::TYPE_CHARGEBACK,
            OrderSettlement::TYPE_CHARGEBACK_REVERSAL
        ];
        $noSettleQuery = OrderSettlement::query()
            ->whereIn('type', $typeList)
            ->where('status', OrderSettlement::STATUS_APPROVED)
            ->where('is_settle', '0');


        $noSettleQuery->whereIn('business_id', $businessIds);

        $noSettleList = $noSettleQuery->groupBy(['settle_currency'])
            ->get([
                'settle_currency',
                DB::raw('SUM(IF(type=5, settle_amount, +settle_amount)) settle_amount')
            ])
            ->pluck('settle_amount', 'settle_currency')
            ->toArray();

        // 冻结金额
        $frozenCurrencyList = MerchantRiskFrozenOperation::query()->select(
            'currency',
            DB::raw('SUM(IF(type=1, amount, - amount)) amount')
        )->where('merchant_id', $this->params['merchant_id'])
        ->groupBy('currency')
        ->get()
        ->pluck('amount', 'currency')
        ->toArray();

        $transferCurrent = ['', '', $fieldData['current_withdrawal']];
        $transferTotal   = ['', '', $fieldData['historical_processing']];
        $noSettle        = ['', '', $fieldData['refund_chargeback']];
        $frozenAmount    = ['', '', $fieldData['frozen_amount']];

        foreach ($this->currencyList as $currency) {
            $transferCurrent[] = '-';
            $transferCurrent[] = $transferCurrentList[$currency] ?? '0.00';
            $transferTotal[]   = '-';
            $transferTotal[]   = $transferList[$currency] ?? '0.00';
            $noSettle[]        = '-';
            $noSettle[]        = $noSettleList[$currency] ?? '0.00';
            $frozenAmount[]    = '-';
            $frozenAmount[]    = $frozenCurrencyList[$currency] ?? '0.00';
        }

        $data[] = $transferCurrent;
        $data[] = $transferTotal;
        $data[] = $noSettle;
        $data[] = $frozenAmount;

        // 空行填充
        foreach ($data as $key => $value) {
            if (empty($value)) {
                $tempData = [];

                for ($i = 0; $i < $maxLength; $i++) {
                    $tempData[$i] = '';
                }

                $data[$key] = array_merge($tempData, $value);
            }
        }

        return collect($data);
    }

    public function styles(Worksheet $sheet)
    {
        // 合并单元格
        $currencyCount = count($this->currencyList);
        for ($i = 0; $i < $currencyCount; $i++) {
            $start    = chr(68 + $i * 2);
            $end      = chr(67 + ($i + 1) * 2);

            $sheet->mergeCells($start . '5:' . $end . '5');
            $sheet->getColumnDimension($start)->setWidth(20);
            $sheet->getColumnDimension($end)->setWidth(20);
        }

        // 最大行列
        $minStart = 'D';
        $maxEnd   = $sheet->getHighestColumn();
        $maxRow   = $sheet->getHighestRow();

        $sheet->getColumnDimension('C')->setWidth(56);
        $sheet->mergeCells($minStart . '2:' . $maxEnd . '2');
        $sheet->getDefaultRowDimension()->setRowHeight(22); // 设置行高
        $sheet->getStyle('A1:' . $maxEnd . $maxRow)->getAlignment()->setVertical('center'); // 垂直居中
        $sheet->getStyle('D1:' . $maxEnd . $maxRow)->applyFromArray(['alignment' => ['horizontal' => 'center']]); // 设置水平居中
        $sheet->getStyle('A2:' . $maxEnd . '2')->applyFromArray(['font' => ['bold' => true, 'color' => ['rgb' => '0072ff']]])->getAlignment()->setWrapText(true); // 字体设置
        $sheet->getRowDimension(2)->setRowHeight(40); // 设置行高
    }
}
