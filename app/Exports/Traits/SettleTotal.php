<?php

namespace App\Exports\Traits;

use App\Models\DirectoryDictionary;
use App\Models\MerchantBusiness;
use App\Models\OrderSettlement;
use App\Models\SettleAdjustment;
use App\Models\SettleDetail;
use App\Models\SettleDetailMerchant;
use App\Models\SettleMerchant;
use App\Models\TransferTicket;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

trait SettleTotal
{
    protected $inputs;
    protected $filter;
    protected $lang;
    protected $currencyList = [];
    protected $fieldData = [
        'en'    => [
            'settlement_date'       => 'Settlement Date:',
            'count'                 => 'Count',
            'amount'                => 'Amount',
            'current_withdrawal'    => 'Current Withdrawal',
            'historical_processing' => 'Historical/Processing Withdrawal',
            'refund_chargeback'     => 'Refund/Chargeback Pending Settlement',
            'amount_type_list'      => [
                'transaction_amount'         => 'Total Transaction',
                'line_null_01'               => '',
                'transaction_fee'            => 'Transaction Fee',
                'transaction_3d_fee'         => '3D Transaction Fee',
                'transaction_rate_fee'       => 'Merchant Discount Rate Fee',
                'transaction_deposit'        => 'Rolling Reserve',
                'transaction_risk_fee'       => 'Risk Control Fee',
                'line_null_02'               => '',
                'refund_amount'              => 'Total Refund',
                'refund_fee'                 => 'Refund Fee',
                'line_null_03'               => '',
                'chargeback_amount'          => 'Total Chargeback',
                'chargeback_fee'             => 'Chargeback Fee',
                'chargeback_reversal_amount' => 'Total Chargeback Resolved',
                'pre_chargeback_fee'         => 'Chargeback Alert Fee',
                'chargeback_penalty'         => 'Chargeback Penalty',
                'chargeback_penalty_special' => 'Chargeback Penalty *',
                'line_null_04'               => '',
                'settle_adjust_amount'       => 'Total Settlement Adjustment'
            ],
            'amount_settle_total_list' => [
                'settle_amount'      => 'Current Settlement',
                'in_deposit_amount'  => 'Current Reserve',
                'out_deposit_amount' => 'Release of Current Reserve'
            ]
        ],
        'zh_CN' => [
            'settlement_date'       => '结算日期:',
            'count'                 => '笔数(Count)',
            'amount'                => '金额(Amount)',
            'current_withdrawal'    => '当期提现金额(Current Withdrawal)',
            'historical_processing' => '已提现、处理中提现金额(Historical/Processing Withdrawal)',
            'refund_chargeback'     => '未结算退款拒付(Refund/Chargeback Pending Settlement)',
            'amount_type_list'      => [
                'transaction_amount'         => '交易金额总计(Total Transaction）',
                'line_null_01'               => '',
                'transaction_fee'            => '交易处理费(Transaction Fee)',
                'transaction_3d_fee'         => '3D处理费(3D Transaction Fee)',
                'transaction_rate_fee'       => '比例手续费(Merchant Discount Rate Fee)',
                'transaction_deposit'        => '交易保证金(Rolling Reserve)',
                'transaction_risk_fee'       => '风控处理费(Risk Control Fee)',
                'line_null_02'               => '',
                'refund_amount'              => '退款金额总计(Total Refund)',
                'refund_fee'                 => '退款处理费(Refund Fee)',
                'line_null_03'               => '',
                'chargeback_amount'          => '拒付金额总计(Total Chargeback)',
                'chargeback_fee'             => '拒付处理费(Chargeback Fee)',
                'chargeback_reversal_amount' => '申诉成功金额总计(Total Chargeback Resolved)',
                'pre_chargeback_fee'         => '预拒付处理费(Chargeback Alert Fee)',
                'chargeback_penalty'         => '拒付罚金(Chargeback Penalty)',
                'chargeback_penalty_special' => '拒付罚金*(Chargeback Penalty *)',
                'line_null_04'               => '',
                'settle_adjust_amount'       => '结算调整金额总计(Total Settlement Adjustment)'
            ],
            'amount_settle_total_list' => [
                'settle_amount'      => '当期到账金额(Current Settlement)',
                'in_deposit_amount'  => '当期保证金入账金额(Current Reserve)',
                'out_deposit_amount' => '当期保证金返还金额(Release of Current Reserve)'
            ]
        ]
    ];

    public function __construct(array $inputs = [], array $filter = [], string $lang = 'zh_CN')
    {
        $this->inputs = $inputs;
        $this->filter = $filter;
        $this->lang   = $lang;
    }

    public function title(): string
    {
        return 'MID对账单';
    }

    public function chunkSize(): int
    {
        return 500;
    }

    public function bindValue(Cell $cell, $value)
    {
        $cell->setValueExplicit($value, DataType::TYPE_STRING);

        return true;
    }

    public function collection()
    {
        // 语言和语言包
        $lang      = $this->lang;
        $fieldData = $this->fieldData[$lang];
        // mid过滤
        $where = $this->merchantFilter($this->inputs);

        // 获取MID币种列表
        $businessQuery = MerchantBusiness::query()->where('internal_status', MerchantBusiness::INTERNAL_STATUS_ENABLE);

        foreach ($where as $field => $value) {
            $businessQuery->where($field, $value);
        }

        $business           = $businessQuery->get()->toArray();
        $this->currencyList = !empty($business) ? array_unique(explode(',', implode(',', array_column($business, 'settle_currencies')))) : [];
        $tempCurrencyList   = $this->currencyList;

        // 返回数组
        $data      = [];
        $data[]    = [];
        $maxLength = count($this->currencyList) * 2 + 3; // 空3格

        // 标题
        $data[] = [
            '', '', '',
            $fieldData['settlement_date'] . $this->inputs['settle_at.start'] . '~' . $this->inputs['settle_at.end']
        ];

        // 空两行
        $data[] = [];
        $data[] = [];

        // 币种行
        $cnt = 0;
        array_unshift($tempCurrencyList, '', '', '');

        foreach ($tempCurrencyList as $key => $value) {
            if (empty($value)) {
                $cnt++;
                continue;
            }

            $tempCurrencyList[$cnt] = $value;
            $cnt++;
            $tempCurrencyList[$cnt] = '';
            $cnt++;
        }

        $data[] = $tempCurrencyList;

        // 金额/笔数 标题栏
        $amountTitle = ['', '', ''];

        foreach ($this->currencyList as $key => $value) {
            $amountTitle[] = $fieldData['count'];
            $amountTitle[] = $fieldData['amount'];
        }

        $data[] = $amountTitle;

        // BID结算明细汇总信息
        $settleDetails     = [];
        $tempSettleDetails = $this->addConditions(SettleDetail::query(), $this->filter)
            ->where('settle_at', '<=', date('Y-m-d'))
            ->whereNotIn('amount_type', [SettleDetail::AMOUNT_TYPE_50, SettleDetail::AMOUNT_TYPE_51, SettleDetail::AMOUNT_TYPE_52])
            ->groupBy('settle_currency', 'amount_type')
            ->get([
                'settle_currency',
                'amount_type',
                DB::raw('COUNT(id) as total'),
                DB::raw('SUM(settle_amount) as settle_amount'),
            ])->toArray();

        foreach ($tempSettleDetails as $detail) {
            $field = SettleDetail::$settlementAmountTypeMap[$detail['amount_type']] ?? '';

            if (empty($field)) continue;

            if (!isset($settleDetails[$field][$detail['settle_currency']])) {
                $settleDetails[$field][$detail['settle_currency']]['total']         = '0';
                $settleDetails[$field][$detail['settle_currency']]['settle_amount'] = '0.00';
            }

            $settleDetails[$field][$detail['settle_currency']]['total']         += $detail['total'];
            $settleDetails[$field][$detail['settle_currency']]['settle_amount'] += $detail['settle_amount'];
        }

        // 释放内存
        $tempSettleDetails = null;

        // 金额详情列表(BID项)
        $amountTypeList = $fieldData['amount_type_list'];

        foreach ($amountTypeList as $field => $text) {
            if (empty($text)) {
                $data[] = [];  // 空行
                continue;
            }

            $amountList = $settleDetails[$field] ?? [];
            $amountData = ['', '', $text];

            foreach ($this->currencyList as $currency) {
                $amountData[] = !empty($amountList[$currency]['total']) ? $amountList[$currency]['total'] : '0';
                $amountData[] = !empty($amountList[$currency]['settle_amount']) ? $amountList[$currency]['settle_amount'] : '0.00';
            }

            $data[] = $amountData;
        }

        // 结算调整明细汇总
        $adjustmentTypeList   = DirectoryDictionary::select('id', 'name', 'remarks')->where('type', '结算调整类型')->orderBy('sort')->get()->keyBy('id')->toArray();
        $settleAdjustment     = [];
        $tempSettleAdjustment = $this->addConditions(SettleAdjustment::query(), $this->filter)
            ->where('type', SettleAdjustment::TYPE_BID)
            ->groupBy('d_type_id', 'settle_currency')
            ->get([
                'd_type_id',
                'settle_currency',
                DB::raw('COUNT(id) as total'),
                DB::raw('SUM(settle_amount) as settle_amount')
            ])->toArray();

        foreach ($tempSettleAdjustment as $adjustment) {
            $settleAdjustment[$adjustment['d_type_id']][$adjustment['settle_currency']]['settle_amount'] = $adjustment['settle_amount'];
            $settleAdjustment[$adjustment['d_type_id']][$adjustment['settle_currency']]['total']         = $adjustment['total'];
        }

        foreach ($adjustmentTypeList as $id => $text) {
            if ($lang == 'zh_CN') {
                $tempName = $text['name'] . '(' . $text['remarks'] . ')';
            } else {
                $tempName = $text['remarks'];
            }

            $adjustmentAmount = $settleAdjustment[$id] ?? [];
            $adjustmentData   = ['', '', $tempName];

            foreach ($this->currencyList as $currency) {
                $adjustmentData[] = !empty($adjustmentAmount[$currency]['total']) ? $adjustmentAmount[$currency]['total'] : '0';
                $adjustmentData[] = !empty($adjustmentAmount[$currency]['settle_amount']) ? $adjustmentAmount[$currency]['settle_amount'] : '0.00';
            }

            $data[] = $adjustmentData;
        }

        // 空两行
        $data[] = [];
        $data[] = [];

        // MID结算明细汇总信息
        $settleDetails       = [];
        $merchantSettleQuery = SettleDetailMerchant::query();

        foreach ($where as $field => $value) {
            if ($field != 'business_id') {
                $merchantSettleQuery->where($field, $value);
            }
        }

        $tempSettleDetails = $merchantSettleQuery->where('settle_at', '<=', date('Y-m-d'))
            ->whereBetween('settle_at', [$this->inputs['settle_at.start'], $this->inputs['settle_at.end']])
            ->groupBy('settle_currency', 'amount_type')
            ->get([
                'settle_currency',
                'amount_type',
                DB::raw('COUNT(id) as total'),
                DB::raw('SUM(settle_amount) as settle_amount'),
            ])->toArray();

        foreach ($tempSettleDetails as $detail) {
            if (!isset($settleDetails[$detail['amount_type']][$detail['settle_currency']])) {
                $settleDetails[$detail['amount_type']][$detail['settle_currency']]['total']         = '0';
                $settleDetails[$detail['amount_type']][$detail['settle_currency']]['settle_amount'] = '0.00';
            }

            $settleDetails[$detail['amount_type']][$detail['settle_currency']]['total']         += $detail['total'];
            $settleDetails[$detail['amount_type']][$detail['settle_currency']]['settle_amount'] += $detail['settle_amount'];
        }

        // 释放内存
        $tempSettleDetails = null;

        // 金额详情列表(MID项)
        $amountTypeList = SettleDetailMerchant::$amountTypeMap;

        foreach ($amountTypeList as $type => $text) {
            if ($lang == 'zh_CN') {
                $tempName = $text . '(' . SettleDetailMerchant::$amountTypeNoteMap[$type] . ')';
            } else {
                $tempName = SettleDetailMerchant::$amountTypeNoteMap[$type];
            }

            $amountList = $settleDetails[$type] ?? [];
            $amountData = ['', '', $tempName];

            foreach ($this->currencyList as $currency) {
                $amountData[] = !empty($amountList[$currency]['total']) ? $amountList[$currency]['total'] : '0';
                $amountData[] = !empty($amountList[$currency]['settle_amount']) ? $amountList[$currency]['settle_amount'] : '0.00';
            }

            $data[] = $amountData;
        }

        // 空一行
        $data[] = [];

        // MID 结算汇总
        $settleMerchant = [];
        $settleQuery    = SettleMerchant::query();

        foreach ($where as $field => $value) {
            if ($field != 'business_id') {
                $settleQuery->where($field, $value);
            }
        }

        $tempSettle = $settleQuery->where('settle_at', '<=', date('Y-m-d'))
            ->whereBetween('settle_at', [$this->inputs['settle_at.start'], $this->inputs['settle_at.end']])
            ->groupBy('settle_currency')
            ->get([
                'settle_currency',
                DB::raw('SUM(settle_amount) as settle_amount'),
                DB::raw('SUM(in_deposit_amount) as in_deposit_amount'),
                DB::raw('SUM(out_deposit_amount) as out_deposit_amount')
            ])->toArray();

        foreach ($tempSettle as $settle) {
            $settleMerchant[$settle['settle_currency']] = $settle;
        }

        // 释放内存
        $tempSettle = null;

        // MID结算汇总项
        $amountSettleTotalList = $fieldData['amount_settle_total_list'];

        foreach ($amountSettleTotalList as $field => $text) {
            $amountData = ['', '', $text];

            foreach ($this->currencyList as $currency) {
                $amountData[] = '-';
                $amountData[] = $settleMerchant[$currency][$field] ?? '0.00';
            }

            $data[] = $amountData;
        }

        // 空两行
        $data[] = [];
        $data[] = [];

        // MID 提现项
        // 总计提现
        $transferStatusList = [
            TransferTicket::TRANSFER_STATUS_SUCCESS,
            TransferTicket::TRANSFER_STATUS_CHECK,
            TransferTicket::TRANSFER_STATUS_HANDLE,
            TransferTicket::TRANSFER_STATUS_PROCESS
        ];
        $transfer           = TransferTicket::query()->whereIn('status', $transferStatusList);

        if (isset($where['merchant_id'])) {
            $transfer->where('applicant_id', $where['merchant_id']);
        }

        $transferList = $transfer->groupBy('currency')
            ->get(['currency', DB::raw('SUM(deduction_amount) as amount')])
            ->pluck('amount', 'currency')
            ->toArray();

        // 当期提现
        $transferCurrent = $transfer->where('status', TransferTicket::TRANSFER_STATUS_SUCCESS)
            ->whereBetween('updated_at', [$this->inputs['settle_at.start'] . ' 00:00:00', $this->inputs['settle_at.end'] . ' 23:59:59']);

        $transferCurrentList = $transferCurrent->groupBy('currency')
            ->get(['currency', DB::raw('SUM(deduction_amount) as amount')])
            ->pluck('amount', 'currency')
            ->toArray();

        // 获取未结算退款拒付
        $typeList      = [
            OrderSettlement::TYPE_REFUND,
            OrderSettlement::TYPE_CHARGEBACK,
            OrderSettlement::TYPE_CHARGEBACK_REVERSAL
        ];
        $noSettleQuery = OrderSettlement::query()
            ->whereIn('type', $typeList)
            ->where('status', OrderSettlement::STATUS_APPROVED)
            ->where('is_settle', '0');

        if (isset($where['merchant_id'])) {
            $noSettleQuery->where('merchant_id', $where['merchant_id']);
        }

        $noSettleList = $noSettleQuery->groupBy(['settle_currency'])
            ->get([
                'settle_currency',
                DB::raw('SUM(IF(type=5, settle_amount, +settle_amount)) settle_amount')
            ])
            ->pluck('settle_amount', 'settle_currency')
            ->toArray();

        $transferCurrent = ['', '', $fieldData['current_withdrawal']];
        $transferTotal   = ['', '', $fieldData['historical_processing']];
        $noSettle        = ['', '', $fieldData['refund_chargeback']];

        foreach ($this->currencyList as $currency) {
            $transferCurrent[] = '-';
            $transferCurrent[] = $transferCurrentList[$currency] ?? '0.00';
            $transferTotal[]   = '-';
            $transferTotal[]   = $transferList[$currency] ?? '0.00';
            $noSettle[]        = '-';
            $noSettle[]        = $noSettleList[$currency] ?? '0.00';
        }

        $data[] = $transferCurrent;
        $data[] = $transferTotal;
        $data[] = $noSettle;

        // 空行填充
        foreach ($data as $key => $value) {
            if (empty($value)) {
                $tempData = [];

                for ($i = 0; $i < $maxLength; $i++) {
                    $tempData[$i] = '';
                }

                $data[$key] = array_merge($tempData, $value);
            }
        }

        return collect($data);
    }

    /**
     * MID过滤
     *
     * @param array $input
     * @return array
     */
    private function merchantFilter(array $input): array
    {
        $filter = [];

        if (isset($input['merchant_id']) && strlen($input['merchant_id']) > 0) {
            $filter['merchant_id'] = $input['merchant_id'];
        }

        if (isset($input['business_id']) && strlen($input['business_id']) > 0) {
            $business = MerchantBusiness::find($input['business_id']);

            if (isset($filter['merchant_id'])) {
                if (empty($business) || $business->merchant_id != $filter['merchant_id']) {
                    $filter['merchant_id'] = 0;
                    $filter['business_id'] = 0;
                }
            } else {
                $filter['merchant_id'] = !empty($business) ? $business->merchant_id : '0';
                $filter['business_id'] = $input['business_id'];
            }
        }

        return $filter;
    }

    /**
     * Add conditions to grid model.
     *
     * @param Builder $query
     * @param array $conditions
     *
     * @return Builder
     */
    public function addConditions(Builder $query, array $conditions)
    {
        foreach ($conditions as $condition) {
            call_user_func_array([$query, key($condition)], current($condition));
        }

        return $query;
    }

    public function styles(Worksheet $sheet)
    {
        // 合并单元格
        $currencyCount = count($this->currencyList);
        for ($i = 0; $i < $currencyCount; $i++) {
            $start    = chr(68 + $i * 2);
            $end      = chr(67 + ($i + 1) * 2);

            $sheet->mergeCells($start . '5:' . $end . '5');
            $sheet->getColumnDimension($start)->setWidth(20);
            $sheet->getColumnDimension($end)->setWidth(20);
        }

        // 最大行列
        $minStart = 'D';
        $maxEnd   = $sheet->getHighestColumn();
        $maxRow   = $sheet->getHighestRow();

        $sheet->getColumnDimension('C')->setWidth(56);
        $sheet->mergeCells($minStart . '2:' . $maxEnd . '2');
        $sheet->getDefaultRowDimension()->setRowHeight(22); // 设置行高
        $sheet->getStyle('A1:' . $maxEnd . $maxRow)->getAlignment()->setVertical('center'); // 垂直居中
        $sheet->getStyle('D1:' . $maxEnd . $maxRow)->applyFromArray(['alignment' => ['horizontal' => 'center']]); // 设置水平居中
        $sheet->getStyle('A2:' . $maxEnd . '2')->applyFromArray(['font' => ['bold' => true, 'color' => ['rgb' => '0072ff']]])->getAlignment()->setWrapText(true); // 字体设置
        $sheet->getRowDimension(2)->setRowHeight(40); // 设置行高
    }
}
