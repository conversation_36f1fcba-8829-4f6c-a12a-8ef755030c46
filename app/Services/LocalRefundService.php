<?php
namespace App\Services;

use App\Classes\LocalPay\Pay;
use App\Http\Controllers\Traits\LocalPaymentController;
use App\Models\Channel;
use App\Models\LocalRefund;

class LocalRefundService
{
    use LocalPaymentController;

    /**
     * 退款查询
     * @param $refundId
     * @return mixed
     */
    public function refundQuery($refundId)
    {
        if (empty($refundId) || !is_array($refundId)) {
            return '参数不正确！';
        }
        
        $refund = LocalRefund::with(['order', 'paymentOrder', 'paymentRefund'])->whereIn('refund_id', $refundId)
            ->where([['status', LocalRefund::STATUS_PENDING], ['code', get_system_code('200')]])
            ->get();
        if (!count($refund)) {
            return '请选择正确的退款订单！';
        }

        $channelRefund   = $refund->groupBy('order.channel');
        $channels        = $refund->pluck('order.channel', 'order.channel')->toArray();
        $channelDataList = Channel::with(['channelSupplier', 'channelPid'])->whereIn('channel', $channels)->get();
        $successTotal = 0;
        $errorTotal   = 0;

        $channel = ['fomo', 'estuary', 'fdc', 'code'];
        foreach ($channelRefund as $key => $value) {
            $channelData         = $channelDataList->where('channel', $key)->first();
            $channelSupplierName = strtolower($channelData->channelSupplier->file_name);

            if (!in_array($channelSupplierName, $channel)) {
                continue;
            }

            // 获取账单标识配置信息
            $config      = $this->_getConfig($channelData->config, $channelSupplierName);
            $channelName = Pay::$channelSupplierName($config);

            if (empty($config)) {
                return '获取渠道信息错误：' . $key;
            }

            foreach ($value as $item) {

                try {
                    $result = $channelName->refundUpdate($item->toArray());
                } catch (\Exception $m) {
                    $errorTotal++;
                    continue;
                }

                if ($result->status == LocalRefund::STATUS_PENDING) {
                    $errorTotal++;
                    continue;
                }

                $this->updateRefund($item, $result);

                $successTotal++;
            }
        }

        return '更新退款订单：' . $successTotal . '条，未更新：' . $errorTotal . '条。';
    }
}
