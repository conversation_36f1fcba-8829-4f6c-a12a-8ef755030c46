<?php

namespace App\Services;

use App\Classes\Risk\Gateways\LN;
use App\Jobs\SendSlsLog;
use App\Models\LexisNexis;
use App\Models\LexisNexisScan;
use DateTime;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class LexisNexisService
{
    // 姓名类型
    const NAME_TYPE_NAME_AND_SURNAME = 0;
    const NAME_TYPE_FULL_NAME = 1;

    // 实体类型
    const ENTITY_TYPE_INDIVIDUAL = 1;
    const ENTITY_TYPE_ENTERPRISE = 2;

    const LN_SEARCH_PHONE_TYPE_ENTERPRISE = 0;
    const LN_SEARCH_PHONE_TYPE_CELL = 1;
    const LN_SEARCH_PHONE_TYPE_FAX = 2;
    const LN_SEARCH_PHONE_TYPE_HOME = 3;
    const LN_SEARCH_PHONE_TYPE_WORK = 4;
    const LN_SEARCH_PHONE_TYPE_UNKNOWN = 5;
    const LN_SEARCH_PHONE_TYPE_NONE = 6;

    const lnSearchPhoneTypeMap = [
        self::LN_SEARCH_PHONE_TYPE_ENTERPRISE => "Business",
        self::LN_SEARCH_PHONE_TYPE_CELL => "Cell",
        self::LN_SEARCH_PHONE_TYPE_FAX => "Fax",
        self::LN_SEARCH_PHONE_TYPE_HOME => "Home",
        self::LN_SEARCH_PHONE_TYPE_WORK => "Work",
        self::LN_SEARCH_PHONE_TYPE_UNKNOWN => "Unknown",
        self::LN_SEARCH_PHONE_TYPE_NONE => "None",
    ];

    const lexisNexisEntityMap = [
        self::ENTITY_TYPE_INDIVIDUAL => "Individual",
        self::ENTITY_TYPE_ENTERPRISE => "Business",
    ];

    /**
     * 根据条件决定跳转的页面以及是否要走LN的API查询接口
     * @param array $searchParams
     * @return array
     */
    public static function searchLexisNexisRecord(array $searchParams): array
    {
        try {
            $commonParams = self::buildCommonParams($searchParams);
            $haveMid = array_key_exists('merchant_id', $commonParams) && !empty($commonParams['merchant_id']);
            $lnBuilder = self::buildLNBuilderQueryParams($commonParams);
            // 数据库中存在数据就不需要调用LN查询接口，直接跳转
            if ($lnBuilder->exists()){
                if (!$haveMid) {
                    return [
                        'request_api' => false,
                        'jump' => false,
                        'commonParams'  => $commonParams,
                    ];
                }

                // 如果传了MID,则创建对应的扫描记录和查询出来的LN详情的关联记录
                $list = $lnBuilder->get();
                LexisNexisScanService::getLnScanRecord($commonParams, $list);
                return [
                    "entity_type" => $commonParams["entity_type"],
                    "merchant_id" => $commonParams["merchant_id"],
                    "infos" => [
                        [
                            "name" => $commonParams["name"],
                            "identification" => $commonParams["identification"],
                        ]
                    ]
                ];
            }


            if (!$haveMid) {
                return [
                    'request_api' => true,
                    'jump' => false,
                    'commonParams'  => $commonParams,
                ];
            }

            // 请求LN接口
            $entity = self::buildSearchLNApiRequestParams($commonParams);
            $LNService = new LN();
            $resp = $LNService->fraud($entity, Str::uuid()->toString());
            // 将LN接口返回的响应组装成LexisNexis模型
            $list = LexisNexisService::SearchLexisNexisApiRespHandel(
                $resp,
                $commonParams['name'],
                $commonParams['identification'],
                $commonParams['entity_type']
            );

            LexisNexisService::bulkInsertAndAssignIds($list, [
                'name' => $commonParams['name'],
                'identification' => $commonParams['identification'] ?? '',
                'entity_type' => $commonParams['entity_type']
            ]);

            // 创建对应的扫描记录与关联关系
            LexisNexisScanService::getLnScanRecord($commonParams, $list);
            return [
                "entity_type" => $commonParams["entity_type"],
                "merchant_id" => $commonParams["merchant_id"],
                "infos" => [
                    [
                        "name" => $commonParams["name"],
                        "identification" => $commonParams["identification"]
                    ]
                ]
            ];
        } catch (Exception $e) {
            logger()->channel('intercept')->info('LN数据搜索失败，失败原因：%s' . $e->getMessage());
            dispatch(new SendSlsLog([
                'message' => 'LN数据搜索失败，失败原因：%s' . $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
            ], [], 'info', 'intercept'));
            return [
                'error' => '搜索失败'
            ];
        }
    }

    /**
     * 将请求参数转化为查询数据库/api的参数
     * @param array $searchParams
     * @return array
     */
    public static function buildCommonParams(array $searchParams): array
    {
        $commonParams = [];
        if ($searchParams["current_tab"] == self::ENTITY_TYPE_ENTERPRISE) {
            $commonParams["name"] = $searchParams["company_name"];
            $commonParams["entity_type"] = LexisNexisScan::ENTITY_TYPE_ENTERPRISE;
            $commonParams["identification"] = $searchParams["EIN"];
            $commonParams["address1"] = $searchParams["company_address1"];
            $commonParams["address2"] = $searchParams["company_address2"];
            $commonParams["city"] = $searchParams["company_city"];
            $commonParams["state"] = $searchParams["company_state"];
            $commonParams["zip"] = $searchParams["company_zip"];
            $commonParams["country"] = $searchParams["company_country"];
            $commonParams["company"] = $searchParams["company_company"];
            $commonParams["phone"] = $searchParams["company_phone"];
            $commonParams["fax"] = $searchParams["company_fax"];
            $commonParams["unknown"] = $searchParams["company_unknown"];
            $commonParams["registration_number"] = $searchParams["registration_number"];
            $commonParams["issued_date"] = $searchParams["issued_date"];
            $commonParams["expiration_date"] = $searchParams["expiration_date"];
            $commonParams['merchant_id'] = $searchParams['company_merchant_id'];
        } else{
            $commonParams["entity_type"] = LexisNexisScan::ENTITY_TYPE_INDIVIDUAL;
            if ($searchParams["name_type"] == self::NAME_TYPE_NAME_AND_SURNAME) {
                $commonParams["name"] = $searchParams["surname"] . ($searchParams["middle_name"] ?? ''). $searchParams["first_name"];
                $commonParams["first_name"] = $searchParams["first_name"];
                $commonParams["middle_name"] = $searchParams["middle_name"];
                $commonParams["last_name"] = $searchParams["surname"];
            } else {
                $commonParams["name"] = $searchParams["full_name"];
            }
            $commonParams["seniority"] = $searchParams["seniority"];
            $commonParams["birthday"] = $searchParams["birthday"];
            $commonParams["SSN"] = $searchParams["SSN"];
            $commonParams["identification"] = $searchParams["nationality_id"];
            $commonParams["nationality"] = $searchParams["nationality"];
            $commonParams["address1"] = $searchParams["address1"];
            $commonParams["address2"] = $searchParams["address2"];
            $commonParams["city"] = $searchParams["city"];
            $commonParams["state"] = $searchParams["state"];
            $commonParams["zip"] = $searchParams["zip"];
            $commonParams["country"] = $searchParams["country"];
            $commonParams["company"] = $searchParams["company"];
            $commonParams["residence"] = $searchParams["residence"];
            $commonParams["work"] = $searchParams["work"];
            $commonParams["phone"] = $searchParams["phone"];
            $commonParams["fax"] = $searchParams["fax"];
            $commonParams["unknown"] = $searchParams["unknown"];
            $commonParams['merchant_id'] = $searchParams['merchant_id'];
        }

        $commonParams["name_type"] = $searchParams["name_type"];
        $commonParams["current_tab"] = $searchParams["current_tab"];
        return $commonParams;
    }

    /**
     * 构建查询数据库的查询器
     * @param array $commonParams
     * @return Builder
     */
    public static function buildLNBuilderQueryParams(array $commonParams): Builder
    {
        $lnBuilder = LexisNexis::query()
            ->where("entity_type", $commonParams["entity_type"])
            ->where("name", "like", "%".$commonParams["name"]."%")
            ->where("identification", $commonParams["identification"] ?? "");
        if ($commonParams["entity_type"] == LexisNexisScan::ENTITY_TYPE_INDIVIDUAL) {
            if (isset($commonParams["birthday"])) {
                $birthday = "\"Type\":\"DOB\",\"Value\":\"".$commonParams["birthday"]."\"";
                $lnBuilder->where("details", "like", "%".$birthday."%");
            }
            if (isset($commonParams["SSN"])) {
                $SSN = "\"Number\":\"".$commonParams["SSN"]."\",\"Type\":\"SSN\"";
                $lnBuilder->where("details", "like", "%".$SSN."%");
            }
        } else {
            if (isset($commonParams["EIN"])) {
                $EIN = "\"Number\":\"".$commonParams["EIN"]."\",\"Type\":\"EIN\"";
                $lnBuilder->where("details", "like", "%".$EIN."%");
            }
        }

        if (isset($commonParams["city"])) {
            $city = "\"City\":\"".$commonParams["city"]."\"";
            $lnBuilder->where("details", "like", "%".$city."%");
        }
        if (isset($commonParams["country"])) {
            $country = "\"Country\":\"".$commonParams["country"]."\"";
            $lnBuilder->where("details", "like", "%".$country."%");
        }
        if (isset($commonParams["state"])) {
            $state = "\"StateProvinceDistrict\":\"".$commonParams["state"]."\"";
            $lnBuilder->where("details", "like", "%".$state."%");
        }
        if (isset($commonParams["zip"])) {
            $zip = "\"PostalCode\":\"".$commonParams["zip"]."\"";
            $lnBuilder->where("details", "like", "%".$zip."%");
        }
        if (isset($commonParams["address1"])) {
            $address1 = "\"AddressLine1\":\"".$commonParams["address1"]."\"";
            $lnBuilder->where("details", "like", "%".$address1."%");
        }
        if (isset($commonParams["address2"])) {
            $address2 = "\"AddressLine2\":\"".$commonParams["address2"]."\"";
            $lnBuilder->where("details", "like", "%".$address2."%");
        }

        return $lnBuilder;
    }

    /**
     * 构建LN查询API的请求参数
     * @param array $commonParams
     * @return array
     */
    public static function buildSearchLNApiRequestParams(array $commonParams): array
    {
        $apiReqAdditionalInfo = array();
        if ($commonParams["entity_type"] == LexisNexisScan::ENTITY_TYPE_INDIVIDUAL) {
            if (isset($commonParams["birthday"])) {
                $birthday = DateTime::createFromFormat("Y-m-d", $commonParams["birthday"]);
                if ($birthday) {
                    $apiReqAdditionalInfo[] = [
                        "Type" => "DOB",
                        "Date" => [
                            "Day" => $birthday->format("d"),
                            "Month" => $birthday->format("m"),
                            "Year" => $birthday->format("Y"),
                            "Unparsed" => $birthday->format("Y-m-d")
                        ]
                    ];
                }
            }
        }

        // 构建地址查询参数
        $apiReqAddress = self::builderLNApiAddressParams($commonParams);

        $apiReqName = array();
        if($commonParams["entity_type"] == LexisNexisScan::ENTITY_TYPE_INDIVIDUAL) {
            if ($commonParams["name_type"] == self::NAME_TYPE_NAME_AND_SURNAME) {
                $apiReqName["Last"] = $commonParams["first_name"];
                $apiReqName["MiddleName"] = $commonParams["middle_name"] ?? '';
                $apiReqName["First"] = $commonParams["last_name"];
            } else{
                $apiReqName["Full"] = $commonParams["name"];
            }

            if (isset($commonParams["work"])) {
                $apiReqName["Title"] = $commonParams["work"];
            }
        } else{
            $apiReqName["Full"] = $commonParams["name"];
        }

        $apiReqPhone = array();
        if (isset($commonParams["phone"])) {
            $item = [];
            if ($commonParams["entity_type"] == LexisNexisScan::ENTITY_TYPE_INDIVIDUAL) {
                $item["Type"] = self::lnSearchPhoneTypeMap[self::LN_SEARCH_PHONE_TYPE_ENTERPRISE];
            } else {
                $item["Type"] = self::lnSearchPhoneTypeMap[self::LN_SEARCH_PHONE_TYPE_CELL];
            }
            $item["Number"] = $commonParams["phone"];

            $apiReqPhone[] = $item;
        }
        if (isset($commonParams["fax"])) {
            $apiReqPhone[] = [
                "Type" => self::lnSearchPhoneTypeMap[self::LN_SEARCH_PHONE_TYPE_FAX],
                "Number" => $commonParams["fax"]
            ];
        }

        $apiReqId = array();
        if (isset($commonParams["SSN"])) {
            $apiReqId[] = [
                "Type" => "SSN",
                "Number" => $commonParams["SSN"]
            ];
        }
        if (isset($commonParams["identification"])) {
            if ($commonParams["entity_type"] == LexisNexisScan::ENTITY_TYPE_INDIVIDUAL) {
                $apiReqId[] = [
                    "Type" => "National",
                    "Number" => $commonParams["identification"]
                ];
            } else{
                $apiReqId[] = [
                    "Type" => "EIN",
                    "Number" => $commonParams["identification"]
                ];
            }
        }

        $entity = [
            "EntityType" => self::lexisNexisEntityMap[$commonParams['current_tab']]
        ];
        if (!empty($apiReqAdditionalInfo)) {
            $entity["AdditionalInfo"] = $apiReqAdditionalInfo;
        }
        if (!empty($apiReqAddress)) {
            $entity["Addresses"] = $apiReqAddress;
        }
        if (!empty($apiReqName)) {
            $entity["Name"] = $apiReqName;
        }
        if (!empty($apiReqPhone)) {
            $entity["Phones"] = $apiReqPhone;
        }
        if (!empty($apiReqId)) {
            $entity["IDs"] = $apiReqId;
        }

        return $entity;
    }

    /**
     * 构建LN查询API的地址请求参数
     * @param array $commonParams
     * @return array
     */
    public static function builderLNApiAddressParams(array $commonParams): array
    {
        $address = array();
        $apiReqAddress = array();
        if (isset($commonParams["city"])) {
            $address["City"] = $commonParams["city"];
        }
        if (isset($commonParams["country"])) {
            $address["Country"] = $commonParams["country"];
            $address["District"] = $commonParams["country"];
        }
        if (isset($commonParams["state"])) {
            $address["StateProvinceDistrict"] = $commonParams["state"];
        }
        if (isset($commonParams["zip"])) {
            $address["PostalCode"] = $commonParams["zip"];
        }
        if (isset($commonParams["address1"])) {
            $address["Street1"] = $commonParams["address1"];
        }
        if (isset($commonParams["address2"])) {
            $address["Street2"] = $commonParams["address2"];
        }
        foreach ($address as $key => $value) {
            $apiReqAddress[] = ["Type" => "Current", $key => $value];
        }

        return $apiReqAddress;
    }
    public static function getInfoByAdditionalInfo(string $detail, string $key): string
    {
        $details = json_decode($detail, true);
        if (empty($details)) {
            return '';
        }

        if (isset($details['EntityDetails']['AdditionalInfo'])) {
            foreach ($details['EntityDetails']['AdditionalInfo'] as $item) {
                if ($item['Type'] == $key) {
                    return $item['Value'];
                }
            }
        }

        return '';
    }

    /**
     * 系统内部LN扫描方法
     * @param array $params
     * @return void
     * @throws Exception
     */
    public static function LexisNexisSearch(array $params)
    {
        $customMessages = [
            'merchant_id.required' => ':attribute 不能为空',
            'search_list.required' => ':attribute 必须是一个非空数组',
            'search_list.*.name.required' => '搜索项中缺少 :attribute',
        ];

        $customAttributes = [
            'merchant_id' => '商户ID',
            'entity_type' => '实体类型',
            'search_list' => '搜索列表',
            'name' => '姓名或公司名',
            'identification' => '身份证号码',
        ];

        $validator = Validator::make($params, [
            'merchant_id' => 'required|string',
            'entity_type' => 'required|in:0,1',
            'search_list' => 'required|array',
            'search_list.*.name' => 'required|string',
            'search_list.*.identification' => 'present|string',
        ], $customMessages, $customAttributes);

        if ($validator->fails()) {
            throw new Exception($validator->errors()->first());
        }

        // 循环处理每个 SearchList 中的元素
        foreach ($params['search_list'] as $item) {
            try {
                $list = self::LexisNexisApiSearch($item, $params['entity_type']);

                // 使用name、identification、entity_type 查询当前已存在的LN数据
                $lnBuilder = LexisNexis::query()->where('name', $item['name'])
                    ->where('identification', $item['identification'] ?? '')
                    ->where('entity_type', $params['entity_type']);

                // 如果数据存在，则利用$list的数据查询是否有需要更新
                // 如果$lnBuilder中存在$list中lexis_nexis_no一直，但check_sum不一致的数据
                // 则更新check_sum和details字段
                // 同时要记录一下$list中哪些是新增的
                if ($lnBuilder->exists()) {
                    $existingRecords = $lnBuilder->get()->keyBy('lexis_nexis_no');
                    $lexisNexisNos = $list->pluck('lexis_nexis_no')->filter();

                    // 1️⃣ 批量更新已存在的记录
                    if ($lexisNexisNos->isNotEmpty()) {
                        $lnBuilder
                            ->whereIn('lexis_nexis_no', $lexisNexisNos)
                            ->chunkById(200, function ($records) use ($list) {
                                foreach ($records as $record) {
                                    $item = $list->firstWhere('lexis_nexis_no', $record->lexis_nexis_no);
                                    if ($item && $record->check_sum !== $item->check_sum) {
                                        $record->check_sum = $item->check_sum;
                                        $record->details = $item->details;
                                        $record->save();
                                    }
                                }
                            });
                    }

                    // 2️⃣ 插入新的记录
                    $newList = $list->filter(function ($item) use ($existingRecords) {
                        return !$existingRecords->has($item['lexis_nexis_no']);
                    });

                    $list = $newList;
                }

                LexisNexisService::bulkInsertAndAssignIds($list, [
                    'name' => $item['name'],
                    'identification' => $item['identification'] ?? '',
                    'entity_type' => $params['entity_type']
                ]);

                LexisNexisScanService::getLnScanRecord([
                    'merchant_id' => $params['merchant_id'],
                    'name' => $item['name'],
                    'identification' => $item['identification'] ?? '',
                    'entity_type' => $params['entity_type'],
                ], $list);
            } catch (Exception $e) {
                logger()->channel('intercept')->warning('LN扫描失败，错误原因：' . $e->getMessage());
                dispatch(new SendSlsLog(
                    [
                        'message' => [
                            'LN扫描失败，错误原因：' . $e->getMessage(),
                        ]
                    ],
                    [],
                    'warning',
                    'intercept',
                ));
                continue;
            }
        }
    }

    /**
     * LN API 搜索
     * @param $data
     * @param $entityType
     * @return Collection
     */
    public static function LexisNexisApiSearch($data, $entityType): Collection
    {
        $gateway = new LN();
        $entity =[
            'EntityType' => LexisNexisScan::lexisNexisEntityTypeMap[$entityType],
            'Name' => [
                'Full' => $data['name'],
            ],
        ];

        if (isset($data['address']) && is_array($data['address'])) {
            $address = self::builderLNApiAddressParams($data['address']);
            $entity['Addresses'] = $address;
        }

        if (!empty($data['identification'])) {
            if ($entityType == LexisNexisScan::ENTITY_TYPE_INDIVIDUAL) {
                $entity['Ids'][] = [
                    'Type' => 'National',
                    'Number' => $data['identification'],
                ];
            } else{
                $entity['Ids'][] = [
                    'Type' => 'EIN',
                    'Number' => $data['identification'],
                ];
            }
        }

        $resp = $gateway->fraud($entity, Str::uuid()->toString());
        return LexisNexisService::SearchLexisNexisApiRespHandel(
            $resp,
            $data['name'],
            $data['identification'] ?? '',
            $entityType
        );
    }

    /**
     * LN API返回的数据处理
     * @param $resp
     * @param $name
     * @param $identification
     * @param $entityType
     * @return Collection
     */
    public static function searchLexisNexisApiRespHandel($resp, $name, $identification, $entityType): Collection
    {
        if (!isset($resp['Records'])) {
            return collect([]);
        }

        if (!isset($resp['Records'][0]['Watchlist']['Matches'])) {
            return collect([]);
        }

        $list = [];
        foreach ($resp['Records'][0]['Watchlist']['Matches'] as $item) {
            $lnModel = new LexisNexis();
            $lnModel->created_at = now();
            $lnModel->updated_at = now();
            $lnModel->lexis_nexis_no = $item['EntityDetails']['ListReferenceNumber'];
            $lnModel->name = $name;
            $lnModel->identification = $identification ?? '';
            $lnModel->entity_type = $entityType;
            $lnModel->fraction = $item['BestNameScore'];
            $lnModel->details = json_encode($item);
            $lnModel->check_sum = $item['CheckSum'];
            $list[] = $lnModel;
        }

        return collect($list);
    }

    /**
     * 批量插入后回写对应的记录ID
     * @param Collection $list
     * @param array $matchFields
     * @return void
     */
    public static function bulkInsertAndAssignIds(Collection $list, array $matchFields)
    {
        if ($list->isEmpty()) return;

        // Step 2: 批量插入主表数据
        LexisNexis::insert($list->toArray());

        // Step 3: 提取所有 lexis_nexis_no
        $lexisNexisNos = $list->pluck('lexis_nexis_no')->filter();
        if ($lexisNexisNos->isEmpty()) return;

        // Step 4: 构建查询条件，确保只查当前业务上下文下的数据
        $builder = LexisNexis::query();
        foreach ($matchFields as $field => $value) {
            $builder->where($field, $value);
        }

        $builder->whereIn('lexis_nexis_no', $lexisNexisNos);

        // Step 5: 查询真实 ID
        $insertedRecords = $builder->get(['id', 'lexis_nexis_no'])->keyBy('lexis_nexis_no');

        // Step 6: 回写 ID 到 $list 每个 item 上
        foreach ($list as $item) {
            if ($record = $insertedRecords->get($item->lexis_nexis_no)) {
                $item->setAttribute('id', $record->id);
            }
        }
    }

    /**
     * @throws Exception
     */
    public static function merchantKycLexisNexisSearch($company, $holdingCo, $ubo, $director, $merchantId)
    {
        try {
            if (is_null($merchantId)) {
                return;
            }

            // 公司类型
            $companyBaseInfo = [
                'merchant_id' => $merchantId,
                'entity_type' => LexisNexisScan::ENTITY_TYPE_ENTERPRISE,
            ];

            $companySearchList = [
                'search_list' => [
                    [
                        'name' => $company['company_name'],
                        'identification' => $company['reg_cert_no'],
                        'address' => [
                            'Street1' => $company['company_address'],
                        ],
                    ],
                ]
            ];

            if ($company['reg_cert_no'] != $company['business_reg_no']) {
                $companySearchList['search_list'][] = [
                        'name' => $company['company_name'],
                        'identification' => $company['business_reg_no'],
                        'address' => [
                            'Street1' => $company['company_address'],
                        ],
                ];
            }

            $companyCheckMap = [];
            if (!empty($holdingCo) && is_array($holdingCo)) {
                foreach ($holdingCo as $item) {
                    if (!empty($item['_remove_']) && $item['_remove_'] == '1') {
                        continue;
                    }

                    if (array_key_exists($item['company_name'].'-'.$item['reg_cert_no'], $companyCheckMap)) {
                        continue;
                    } else {
                        $companyCheckMap[$item['company_name'].'-'.$item['reg_cert_no']] = 1;
                    }

                    $companySearchList['search_list'][] = [
                        'name' => $item['company_name'],
                        'identification' => $item['reg_cert_no'],
                        'address' => [
                            'Street1' => $item['company_address'],
                        ],
                    ];
                }
            }

            // 公司信息和控股公司的LN扫描
            $companySearchLexisNexisSearch = array_merge($companyBaseInfo, $companySearchList);
            self::LexisNexisSearch($companySearchLexisNexisSearch);

            $individualBaseInfo = [
                'merchant_id' => $merchantId,
                'entity_type' => LexisNexisScan::ENTITY_TYPE_INDIVIDUAL,
            ];

            $individualCheckMap = [];
            $individualSearchList = [];
            if (!empty($ubo) && is_array($ubo)) {
                self::processIndividualList($ubo, $individualCheckMap, $individualSearchList);
            }

            if (!empty($director) && is_array($director)) {
                self::processIndividualList($director, $individualCheckMap, $individualSearchList);
            }

            // 法人/董事、最终受益人信息LN扫描
            $individualSearchLexisNexisSearch = array_merge($individualBaseInfo, $individualSearchList);
            self::LexisNexisSearch($individualSearchLexisNexisSearch);
        } catch (Exception $e) {
            logger()->channel('intercept')->warning('KYC信息进行LN扫描失败，失败原因：' . $e->getMessage());
            dispatch(new SendSlsLog([
                'message' => 'KYC信息进行LN扫描失败，失败原因：' . $e->getMessage(),
            ], [], 'warning', 'intercept'));
        }
    }

    /**
     * 处理个人列表（如 UBO、Director），过滤已移除和重复项，并生成搜索列表
     *
     * @param array $list
     * @param array &$checkMap
     * @param array $resultList
     */
    private static function processIndividualList(array $list, array &$checkMap, array &$resultList)
    {
        foreach ($list as $item) {
            if (!empty($item['_remove_']) && $item['_remove_'] == '1') {
                continue;
            }

            $key = $item['name'] . '-' . $item['cert_number'];
            if (array_key_exists($key, $checkMap)) {
                continue;
            }

            $checkMap[$key] = 1;
            $resultList['search_list'][] = [
                'name' => $item['name'],
                'identification' => $item['cert_number'],
            ];
        }
    }

}
