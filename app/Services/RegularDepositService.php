<?php

namespace App\Services;

use App\Jobs\SendSlsLog;
use App\Models\MerchantBusiness;
use App\Models\MerchantRegularDeposit;
use Illuminate\Support\Facades\DB;

class RegularDepositService
{
    /**
     * 获取可释放保证金数据
     *
     * @param array $where
     * @return array
     */
    public static function getReleaseDeposit (array $where)
    {
        $deposit = MerchantRegularDeposit::select(
            'actual_currency',
            DB::raw('SUM(IF(type=0, actual_amount, -actual_amount)) actual_amount')
        )->where($where)
        ->having('actual_amount', '>', '0')
        ->groupBy(['actual_currency'])
        ->get()
        ->toArray();

        return $deposit;
    }

    /**
     * 释放保证金
     *
     * @param array $pastDeposit
     * @param array $newDeposit
     * @param string $businessId
     * @param string $adminName
     * @return void
     */
    public static function releaseSecurityDeposit ($pastDeposit, $newDeposit, $businessId, $adminName)
    {
        // 找出更新前和更新后的差集
        $diff = [];
        foreach ($pastDeposit as $value) {
            if(!in_array($value, $newDeposit)){
                $diff[] = $value;
            }
        }

        if (empty($diff)) {
            return;
        }

        // 获取变动的固定保证金卡种
        $ccType = [];
        foreach ($diff as $value) {
            if ($value['deposit_type'] == MerchantBusiness::DEPOSIT_TYPE_FIXTION) {
                $ccType[] = $value['deposit_card'];
            }
        }

        // 获取可释放的固定保证金
        $deposit = MerchantRegularDeposit::select(
            'actual_currency',
            'merchant_id',
            'cc_type',
            DB::raw('SUM(IF(type=0, actual_amount, -actual_amount)) sum_actual_amount')
        )->where('business_id', $businessId)
        ->whereIn('cc_type', $ccType)
        ->having('sum_actual_amount', '>', '0')
        ->groupBy(['actual_currency', 'merchant_id', 'cc_type'])
        ->get()
        ->toArray();

        if (!empty($deposit)) {
            DB::beginTransaction();
            try {
                foreach ($deposit as $value) {
                    MerchantRegularDeposit::create([
                        'merchant_id'     => $value['merchant_id'],
                        'business_id'     => $businessId,
                        'type'            => MerchantRegularDeposit::TYPE_RELEASE,
                        'cc_type'         => $value['cc_type'],
                        'actual_currency' => $value['actual_currency'],
                        'actual_amount'   => $value['sum_actual_amount'],
                    ]);

                    // 记录日志
                    $message = "{$adminName}释放BID：{$businessId} {$value['cc_type']}卡 固定保证金：{$value['sum_actual_amount']} {$value['actual_currency']}";
                    logger()->channel('intercept')->info($message);
                    dispatch(new SendSlsLog(
                        ['message' => $message],
                    ));
                }
            } catch (\Throwable $th) {
                DB::rollBack();
                // 记录错误日志
                logger()->channel('intercept')->warning(
                    '释放固定保证金失败',
                    ['error' => $th->getMessage()]
                );
                dispatch(new SendSlsLog(
                    ['message' => '释放固定保证金失败'],
                    ['error' => $th->getMessage()],
                    'warning',
                ));
            }

            DB::commit();
        }
    }
}
