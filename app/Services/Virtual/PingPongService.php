<?php

namespace App\Services\Virtual;

use App\Classes\Card\Gateways\Pp\Support;
use App\Classes\Supports\Log;
use App\Classes\Supports\Logger;
use App\Jobs\CardBalanceQuery;
use App\Jobs\CardOperation;
use App\Jobs\SendSlsLog;
use App\Models\CardEntryTrans;
use App\Models\CardHolderChannelReports;
use App\Models\CardTicket;
use App\Models\CardTransaction;
use App\Models\CardVirtual;
use App\Models\CardVirtualBlack;
use App\Models\MerchantCard;
use App\Models\SettleDetailCard;
use App\Services\PinyinService;
use App\Services\ToolService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use App\Classes\Card\Gateways\Pp\PingPongCardSupplier;
use DES3;

class PingPongService extends VirtualAbstractService implements VirtualServiceInterface
{
    const CHANNEL_SERVICE_CODE = 'PingPong';

    public function __construct(PingPongCardSupplier $cardGateway)
    {
        parent::__construct($cardGateway);
    }

    protected function setLogger()
    {
        $log    = new Logger();
        $config = ['file' => storage_path('logs/pingpong.log')];
        $log->setConfig($config);
        Log::setInstance($log);
    }

    public function batchApplyCard(array $params = [], $num = 1): array
    {
        $res = ['success_number' => 0];
        //开卡结果
        $results = $this->gateway->batchApply($params, $num);
        $this->sendLog('CardService batchApplyCard.', [
            "params"    => array_diff_key($params, ['bin' => '']),
            "execCount" => $num,
            "responses" => $results,
        ], 'info');

        $cardVirtuals = [];
        foreach ($results as $result) {
            //正确的code
            if ($result['code'] == 6666 && !empty($result["data"])) {
                // 执行写入cardsql
                $cardVirtuals[] = [
                    "virtual_id"       => microsecond(),
                    "merchant_id"      => $params['merchant_id'] ?? '',
                    "merchant_name"    => $params['merchant_name'] ?? '',
                    "cards_id"         => $params['cards_id'] ?? '',
                    "card_bin_id"      => $params['bin']['id'],
                    "bin"              => $params['bin']['bin'],
                    "bin_name"         => $params['bin']['bin_name'],
                    "batch_id"         => $params['batch_id'] ?? '',
                    "trans_id"         => $result["data"],
                    "remark"           => $params['remark'] ?? '',
                    "card_type"        => $params['card_type'],
                    "day_amount_limit" => $params['day_amount_limit'] ?? 1.00,
                    "status"           => CardVirtual::PROGRESS,
                    "created_at"       => now(),
                ];
                $res['success_number']++;
            } else {
                $this->sendLog('CardService batchApplyCard fail.', ["params" => $params, "result" => $result], 'warning');
            }
        }

        if (count($cardVirtuals) > 0) {
            CardVirtual::insert($cardVirtuals);
        }

        return $res;
    }

    // 卡查询异步任务用
    public function batchGetCardInfo($transIds = []): void
    {
        if (!empty($transIds)) {
            // 请求渠道获取卡信息
            $results = $this->gateway->batchGetCardInfo($transIds);
            $this->sendLog('CardService checkCardInfo.', [
                "ppCardIds" => implode(',', $transIds),
                "result"    => $results
            ], 'info');

            foreach ($results as $key => $result) {
                if ($result["code"] == Support::RESPONSE_CODE_SUCCESS && !empty($result['data']['pp_card_id'])) {
                    $cardVirtual = CardVirtual::where('trans_id', $result['data']['pp_card_id'])->first();

                    if (!$cardVirtual) {
                        $this->sendLog('batchGetCardInfo TransId does not exist', $this->modifyValueOfKey($result, 'card_number'), 'warning');
                        continue;
                    }

                    // 激活状态的卡 跳过
                    if ($cardVirtual->status == CardVirtual::ACTIVATION) {
                        continue;
                    }

                    // 卡信息更新到虚拟卡表中
                    $cardVirtual->card_number       = $result['data']['card_number'];
                    $cardVirtual->cvv               = $result['data']['cvc'];
                    $cardVirtual->status            = CardVirtual::ACTIVATION;
                    $cardVirtual->allow_card_out    = $result['data']["allow_card_out"] == "true" ? 1 : 0;
                    $cardVirtual->expiration_year   = $result['data']['expiration_year'];
                    $cardVirtual->expiration_month  = $result['data']['expiration_month'];
                    $cardVirtual->available_balance = $result['data']['available_balance'] ?? 0;
                    $cardVirtual->date_api_balance  = now();
                    $cardVirtual->save();
                } else {
                    // 执行失败log
                    $this->sendLog("CardService checkCardInfo failed", [
                        'ppCardId' => $transIds[$key] ?? '',
                        'result'   => $this->modifyValueOfKey($result, 'card_number')
                    ], 'warning');
                }
            }
        }
    }

    //余额查询
    public function inquireBalance($virtual = []): array
    {
        $results = $this->gateway->inquireBalance($virtual);
        $this->sendLog('inquireBalance API Response', $results, 'info');

        if (isset($results['code']) && $results['code'] == '6666') {
            $response['code']            = 200;
            $response['data']['balance'] = $results['data']['available_balance'];
            CardVirtual::where('trans_id', $virtual['trans_id'])->update([
                'date_api_balance'  => now(),
                'available_balance' => $results['data']['available_balance'],
            ]);
        } else {
            $response['code'] = 0;
        }

        $response['message'] = $results['message'] ?? '';

        return $response;
    }

    //充值
    public function recharge($cardTicketData = []): array
    {
        $requestData = [
            'unique_order_id' => $cardTicketData['unique_id'],
            'pp_card_id'      => $cardTicketData['trans_id'],
            'amount'          => $cardTicketData['amount'],
        ];

        try {
            // 请求渠道进行充值
            $results = $this->gateway->recharge($requestData);
            $this->sendLog('recharge API Response', $results, 'info');
        } catch (\Throwable $th) {
            $this->sendLog('request timeout：' . $th->getMessage(), [], 'warning');
        }

        if (isset($results['code']) && $results['code'] == '6666') {
            $code = 200;

            // 更新充值表 渠道处理中
            CardTicket::where('unique_id', $cardTicketData['unique_id'])->update([
                'status'  => 3, // 渠道处理中
                'remarks' => $results['data'],
            ]);

            // 添加查卡操作任务
            $cardTicketData['status'] = 3;
            dispatch(new CardOperation($cardTicketData, 2 * 60));
        } else {
            // 充值失败后退还
            $this->cardTicketUpdateFailure($cardTicketData, $results ?? []);
            $code = 0;
        }

        return [
            'code'    => $code,
            'message' => $results['message'] ?? 'error',
            'data'    => $results['data'] ?? '',
        ];
    }

    protected function cardTicketUpdateFailure($cardTicketData, $results)
    {
        $cidInfo = MerchantCard::find($cardTicketData['cards_id']);

        // 充值失败返还
        if ($cardTicketData['type'] == 0) {
            DB::beginTransaction();
            // 更新CID余额
            DB::update("UPDATE merchant_cards SET `balance` = `balance` + '{$cardTicketData['amount']}' WHERE `cards_id` = '{$cardTicketData['cards_id']}'");

            // 结算数据对冲
            $settleDetailCardData = [
                'unique_id'     => CardTicket::getUniqueId(),
                'cards_id'      => $cardTicketData['cards_id'],
                'merchant_id'   => $cardTicketData['merchant_id'],
                'merchant_name' => $cardTicketData['merchant_name'],
                'virtual_id'    => $cardTicketData['virtual_id'],
                'card_number'   => $cardTicketData['card_number'],
                'amount'        => $cardTicketData['amount'],
                'type'          => SettleDetailCard::SETTLE_DETAIL_CARDS_TYPE_10,
                'settle_at'     => get_settle_date(),
                'balance'       => $cidInfo->balance,
            ];
            SettleDetailCard::create($settleDetailCardData);

            // 更新充值表
            $remarks = $results['message'] ?? 'error';
            CardTicket::where('unique_id', $cardTicketData['unique_id'])->update([
                'status'  => 0,
                'remarks' => $remarks,
            ]);

            DB::commit();
        }

        // 退值失败
        if ($cardTicketData['type'] == 1) {
            $remarks = $results['message'] ?? 'error';
            CardTicket::where('unique_id', $cardTicketData['unique_id'])->update([
                'status'  => 0,
                'remarks' => $remarks,
            ]);
        }
    }

    protected function cardTicketUpdateDetail($cardTicketData, $results): string
    {
        // 判断状态是否是渠道处理中
        if ($cardTicketData['status'] != 3) {
            if (in_array($cardTicketData['status'], [0, 1])) {
                return 'success';
            }

            $this->sendLog('Card Ticket Update Detail status error', ['params' => $this->modifyValueOfKey($cardTicketData, 'card_number'), 'result' => $results], 'info');
            return 'error';
        }

        // 缓存数据，防止重复更新
        $redisKey = 'Card_Tickets_Update_' . $cardTicketData['unique_id'];

        if (Cache::has($redisKey)) {
            $this->sendLog('Card Ticket Update Detail Processing', ['params' => $this->modifyValueOfKey($cardTicketData, 'card_number'), 'result' => $results], 'info');
            return 'success';
        }

        Cache::add($redisKey, $redisKey, 600);

        foreach ($results as $value) {
            if ($value['unique_order_id'] == $cardTicketData['unique_id']) {
                switch ($cardTicketData['type']) {
                    case '0': // 充值
                        switch ($value['status']) {
                            case 'SUCCESS':
                                $cardVirtual = CardVirtual::where('virtual_id', $cardTicketData['virtual_id'])->first()->toArray();

                                dispatch(new CardBalanceQuery($cardVirtual, 5)); // 添加异步查卡余额任务

                                // 更新充值表
                                CardTicket::where('unique_id', $cardTicketData['unique_id'])->update([
                                    'status' => 1, // 成功
                                ]);
                                break;
                            case 'FAIL':
                                // 状态为失败返还调整金额
                                $value['message'] = $value['status'];
                                $this->cardTicketUpdateFailure($cardTicketData, $value);
                                break;
                            case 'PROCESSING':
                                $this->sendLog('Card Ticket Update Detail Processing', ['params' => $this->modifyValueOfKey($cardTicketData, 'card_number'), 'result' => $results], 'info');
                                break;
                        }
                        break;
                    case '1': // 退值
                        switch ($value['status']) {
                            case 'SUCCESS':
                                DB::beginTransaction();
                                try {
                                    // 更新CID余额
                                    DB::update("UPDATE merchant_cards SET `balance` = `balance` + '{$cardTicketData['amount']}' WHERE `cards_id` = '{$cardTicketData['cards_id']}'");
                                    //增加结算对冲
                                    $settleDetailCardData = [
                                        'unique_id'     => $cardTicketData['unique_id'],
                                        'cards_id'      => $cardTicketData['cards_id'],
                                        'merchant_id'   => $cardTicketData['merchant_id'],
                                        'merchant_name' => $cardTicketData['merchant_name'],
                                        'virtual_id'    => $cardTicketData['virtual_id'],
                                        'card_number'   => $cardTicketData['card_number'],
                                        'amount'        => $cardTicketData['amount'],
                                        'type'          => SettleDetailCard::SETTLE_DETAIL_CARDS_TYPE_11,
                                        'settle_at'     => get_settle_date(),
                                        'balance'       => MerchantCard::where('cards_id', $cardTicketData['cards_id'])->value('balance'),
                                    ];
                                    SettleDetailCard::create($settleDetailCardData);
                                    // 更新cardTicket
                                    CardTicket::where('unique_id', $cardTicketData['unique_id'])->update([
                                        'status' => 1, // 成功
                                    ]);
                                    //更新卡余额
                                    $cardVirtual = CardVirtual::where('virtual_id', $cardTicketData['virtual_id'])->first()->toArray();
                                    dispatch(new CardBalanceQuery($cardVirtual, 5)); // 添加异步查卡余额任务
                                    DB::commit();
                                    if ($cardVirtual['status'] == CardVirtual::WAITDESTROY) { //判断是否销卡的退值
                                        $this->sendLog('Refund for card destruction', $this->modifyValueOfKey($cardTicketData, 'card_number'), 'info');

                                        $this->destroy($cardVirtual);
                                    }
                                } catch (\Exception $e) {
                                    DB::rollBack();
                                    $this->sendLog('Card Ticket Update Detail Processing Error', ['params' => $this->modifyValueOfKey($cardTicketData, 'card_number'), 'result' => $value], 'info');
                                }
                                break;
                            case 'FAIL':
                                $value['message'] = $value['status'];
                                // 如果有渠道待销状态的卡会导致永远销不了
                                if (CardVirtual::where('virtual_id', $cardTicketData['virtual_id'])->where('status', CardVirtual::WAITDESTROY)->exists()) { //判断是否销卡的退值
                                    $this->sendLog('Card exists in processing virtual_id：' . $cardTicketData['virtual_id'], $this->modifyValueOfKey($cardTicketData, 'card_number'), 'warning');
                                }
                                $this->cardTicketUpdateFailure($cardTicketData, $value);
                                break;
                            case 'PROCESSING':
                                $this->sendLog('Card Ticket Update Detail Processing', ['params' => $this->modifyValueOfKey($cardTicketData, 'card_number'), 'result' => $results], 'info');
                                break;
                        }
                        break;
                }
                break;
            }
        }
        return 'success';
    }

    //bin交易查询
    public function inquiryTrade(array $params = []): array
    {
        $params = [
            'row'        => 100,
            'startTime'  => $params['startTime'] ?? strtotime(date('Y-m-d', strtotime('-1 day'))),
            'endTime'    => $params['endTime'] ?? strtotime(date('Ymd')),
            'pp_card_id' => $params['trans_id'] ?? '',
        ];


        $statistics = ['total' => 0, 'success' => 0, 'error' => 0, 'repeat' => 0];
        for ($page = 1; $page < 10000; $page++) {
            $params['page'] = $page;
            $results        = $this->gateway->inquiryTrade($params);

            if (!$results || empty($results['data']) || !is_array($results['data']) || empty($results['data']['list']) || !is_array($results['data']['list'])) {
                if (!isset($results['code']) || $results['code'] != '6666') {
                    $this->sendLog('inquiryTrade API Response', ['Response' => $results, 'param' => $params], 'warning');
                } else {
                    $this->sendLog('inquiryTrade API Response', ['Response' => $results, 'param' => $params], 'info');
                }
                break;
            }

            $total               = $results['data']['total'] ?? 0;
            $cardTransactions    = [];
            $cardTickets         = [];
            $statistics['total'] += $total;

            if ($total == 0) {
                continue;
            }

            foreach ($results['data']['list'] as $v) {
                if (!isset($v['authorization_id'])) {
                    $statistics['error'] += 1;
                    continue;
                }

                if (CardTransaction::where('transaction_order_id', $v['authorization_id'])->exists()) {
                    $statistics['repeat'] += 1;
                    continue;
                }

                if (!isset($v['pp_card_id'])) {
                    $statistics['error'] += 1;
                    continue;
                }

                // 获取卡信息
                $cardVirtualInfo = CardVirtual::where('trans_id', $v['pp_card_id'])->first();
                if (!$cardVirtualInfo) {
                    $statistics['error'] += 1;
                    $this->sendLog('Transaction get CardVirtual failed', ['param' => $v['pp_card_id'], 'result' => $v], 'warning');
                    continue;
                }
                switch ($v['authorization_type']) {
                    case 'Auth':
                        $authorizationType = CardTransaction::TRANSACTION_TYPE_AUTH;
                        $cardTicketType    = CardTicket::TYPE_TRANSACTION;
                        break;
                    case 'Reversal':
                        $authorizationType = CardTransaction::TRANSACTION_TYPE_REVERSAL;
                        $cardTicketType    = CardTicket::TYPE_TRANSACTION;
                        break;
                    case 'Purchase Return':
                        $authorizationType = CardTransaction::TRANSACTION_TYPE_PURCHASE_RETURN;
                        $cardTicketType    = CardTicket::TYPE_REFUND;
                        break;
                    default:
                        $cardTicketType    = 404;
                        $authorizationType = 404;
                        $this->sendLog('Transaction get CardVirtual failed', ['param' => $v['pp_card_id'], 'result' => $v], 'warning');
                }

                $uniqueId = CardTicket::getUniqueId();

                // 组装数据
                $cardTransactions[] = [
                    'unique_id'               => $uniqueId,
                    'virtual_id'              => $cardVirtualInfo->virtual_id,
                    'merchant_id'             => $cardVirtualInfo->merchant_id,
                    'merchant_name'           => $cardVirtualInfo->merchant_name,
                    'cards_id'                => $cardVirtualInfo->cards_id,
                    'card_bin_id'             => $cardVirtualInfo->card_bin_id,
                    'bin'                     => $cardVirtualInfo->bin,
                    'bin_name'                => $cardVirtualInfo->bin_name,
                    'card_number'             => $cardVirtualInfo->card_number,
                    'amount'                  => $v['billing_amount'],
                    'currency'                => $v['billing_currency'],
                    'transaction_type'        => $authorizationType,
                    'transaction_status'      => $v['authorization_status'] == 'APPROVED' ? 1 : 0,
                    'transaction_order_id'    => $v['authorization_id'],
                    'transaction_description' => $v['merchant_name'] ?? '',
                    'transaction_mcc'         => $v['mcc'] ?? '',
                    'fail_reason'             => $v['fail_reason'] ?? '',
                    'date_comtplete'          => ToolService::convertToSystemTimeZone($v['authorization_date']),
                    'created_at'              => now(),
                    'auth_code'               => $v['approval_code'] ?? '',
                ];

                $cardTickets[] = [
                    'unique_id'       => $uniqueId,
                    'unique_order_id' => $uniqueId,
                    'merchant_id'     => $cardVirtualInfo->merchant_id,
                    'merchant_name'   => $cardVirtualInfo->merchant_name,
                    'cards_id'        => $cardVirtualInfo->cards_id,
                    'card_bin_id'     => $cardVirtualInfo->card_bin_id,
                    'bin'             => $cardVirtualInfo->bin,
                    'bin_name'        => $cardVirtualInfo->bin_name,
                    'virtual_id'      => $cardVirtualInfo->virtual_id,
                    'card_number'     => $cardVirtualInfo->card_number,
                    'amount'          => $v['billing_amount'],
                    'currency'        => $v['billing_currency'],
                    'type'            => $cardTicketType,
                    'originator'      => 0,
                    'status'          => $v['authorization_status'] == 'APPROVED' ? 1 : 0,
                    'remarks'         => $v['authorization_id'],
                    'created_at'      => now(),
                ];

                // 暂时不收取单笔处理费 transaction_fee
            }

            if (!empty($cardTransactions) && !empty($cardTickets)) {
                DB::beginTransaction();
                $rowTicketCount      = CardTicket::insert($cardTickets);
                $rowTransactionCount = CardTransaction::insert($cardTransactions);

                if ($rowTicketCount && $rowTransactionCount) {
                    $statistics['success'] += 1;
                    DB::commit();
                } else {
                    $statistics['error'] += 1;
                    $this->sendLog("Transaction add fail", [
                        "param"  => [$this->modifyValueOfKey($cardTransactions, 'card_number'), $this->modifyValueOfKey($cardTickets, 'card_number')],
                        "result" => [
                            'count'            => $rowTicketCount,
                            'TransactionCount' => $rowTransactionCount
                        ]
                    ], 'info');
                    DB::rollBack();
                }
            }
        }

        $this->sendLog(' Transaction inquiry statistics', $statistics, 'info');
        return [];
    }

    //结算查询
    public function inquirySettlement($params = []): array
    {
        $res    = [];
        $params = [
            'row'        => 100,
            'startTime'  => $params['startTime'] ?? date('Y-m-d', strtotime('-1 day')),
            'endTime'    => $params['endTime'] ?? date('Y-m-d'),
            'pp_card_id' => $params['trans_id'] ?? '',
        ];

        $statistics = ['total' => 0, 'success' => 0, 'error' => 0, 'repeat' => 0];
        for ($page = 1; $page < 10000; $page++) {
            $updateSettleIds  = [];
            $cardEntryTrans   = [];
            $cardTransactions = [];
            $cardTickets      = [];
            $params['page']   = $page;
            $results          = $this->gateway->inquirySettlement($params);
            if (!$results || empty($results['data']['total']) || !count($results['data']['list'])) {
                if (!isset($results['code']) || $results['code'] != '6666') {
                    $this->sendLog('inquirySettlement API Response', ['Response' => $results], 'warning');
                } else {
                    $this->sendLog('inquirySettlement API Response', ['Response' => $results], 'info');
                }
                break;
            }

            foreach ($results['data']['list'] as $v) {
                // 获取卡信息
                $cardVirtualInfo = CardVirtual::where('trans_id', $v['pp_card_id'])->first();
                if (!$cardVirtualInfo) {
                    $this->sendLog('Transaction get CardVirtual failed', ['param' => $v['pp_card_id'], 'result' => $v], 'warning');
                    continue;
                }

                $uniqueId           = CardTicket::getUniqueId();
                $transactionOrderId = $v['authorization_id'] ?? $v['transaction_id'];
                $isExists           = CardTransaction::where('transaction_order_id', $transactionOrderId)->exists();
                if ($v['type'] == 'Credit') {
                    $statistics['total'] += 1;
                    if ($isExists) {
                        $statistics['repeat'] += 1;
                        continue;
                    }

                    // 组装数据
                    $cardTransactions[] = [
                        'unique_id'                 => $uniqueId,
                        'virtual_id'                => $cardVirtualInfo->virtual_id,
                        'merchant_id'               => $cardVirtualInfo->merchant_id,
                        'merchant_name'             => $cardVirtualInfo->merchant_name,
                        'cards_id'                  => $cardVirtualInfo->cards_id,
                        'card_bin_id'               => $cardVirtualInfo->card_bin_id,
                        'bin'                       => $cardVirtualInfo->bin,
                        'bin_name'                  => $cardVirtualInfo->bin_name,
                        'card_number'               => $cardVirtualInfo->card_number,
                        'amount'                    => $v['merchant_amount'],
                        'currency'                  => $v['merchant_currency'],
                        'transaction_order_id'      => $transactionOrderId,
                        'transaction_type'          => CardTransaction::TRANSACTION_TYPE_PURCHASE_RETURN,
                        'transaction_status'        => 1,
                        'transaction_settle_status' => 1,
                        'transaction_description'   => $v['merchant_name'] ?? '',
                        'transaction_mcc'           => $v['mcc'] ?? '',
                        'fail_reason'               => $v['failReason'] ?? '',
                        'date_comtplete'            => ToolService::convertToSystemTimeZone($v['posting_date']),
                        'created_at'                => now(),
                        'updated_at'                => now(),
                        'auth_code'                 => $v['approval_code'] ?? '',
                    ];

                    $cardTickets[]         = [
                        'unique_id'       => $uniqueId,
                        'unique_order_id' => $uniqueId,
                        'merchant_id'     => $cardVirtualInfo->merchant_id,
                        'merchant_name'   => $cardVirtualInfo->merchant_name,
                        'cards_id'        => $cardVirtualInfo->cards_id,
                        'card_bin_id'     => $cardVirtualInfo->card_bin_id,
                        'bin'             => $cardVirtualInfo->bin,
                        'bin_name'        => $cardVirtualInfo->bin_name,
                        'virtual_id'      => $cardVirtualInfo->virtual_id,
                        'card_number'     => $cardVirtualInfo->card_number,
                        'amount'          => $v['merchant_amount'],
                        'currency'        => $v['merchant_currency'],
                        'type'            => CardTicket::TYPE_REFUND,
                        'originator'      => '0',
                        'status'          => '1',
                        'remarks'         => $v['transaction_id'],
                        'created_at'      => now(),
                        'updated_at'      => now(),
                    ];
                    $statistics['success'] += 1;
                    // 暂时不收取单笔处理费 transaction_fee
                } elseif ($v['type'] == 'Debit') {
                    // 如果原始交易订单号存在，则更新结算状态
                    if ($isExists) {
                        $updateSettleIds[] = $transactionOrderId;
                    }
                }


                $cardEntryTrans[] = [
                    'unique_id'      => $uniqueId,
                    'merchant_id'    => $cardVirtualInfo->merchant_id,
                    'merchant_name'  => $cardVirtualInfo->merchant_name,
                    'cards_id'       => $cardVirtualInfo->cards_id,
                    'card_bin_id'    => $cardVirtualInfo->card_bin_id,
                    'bin'            => $cardVirtualInfo->bin,
                    'bin_name'       => $cardVirtualInfo->bin_name,
                    'virtual_id'     => $cardVirtualInfo->virtual_id,
                    'card_number'    => $cardVirtualInfo->card_number,
                    'entry_order_id' => $v['transaction_id'],
                    'entry_amount'   => $v['billing_amount'],
                    'entry_currency' => $v['billing_currency'],
                    'entry_date'     => $v['posting_date'],
                    'trans_order_id' => $v['authorization_id'] ?? '',
                    'amount'         => $v['merchant_amount'],
                    'currency'       => $v['merchant_currency'],
                    'trans_date'     => $v['transaction_date'],
                    'trans_type'     => CardEntryTrans::TRANS_TYPE_CREDIT,
                    'trans_status'   => CardEntryTrans::TRANS_STATUS_SUCCESS,
                    'trans_addr'     => $v['merchant_country'] ?? '',
                    'trans_desc'     => $v['merchant_name'] ?? '',
                    'trans_mcc'      => $v['mcc'] ?? '',
                    'fail_reason'    => $v['failReason'] ?? '',
                    'auth_code'      => $v['approval_code'] ?? '',
                ];
            }

            if (!empty($cardTransactions) && !empty($cardTickets) && !empty($cardEntryTrans)) {
                DB::beginTransaction();
                $rowTicketCount      = CardTicket::insert($cardTickets);
                $rowTransactionCount = CardTransaction::insert($cardTransactions);
                $rowEntryTransCount  = CardEntryTrans::insert($cardEntryTrans);

                if ($rowTicketCount && $rowTransactionCount && $rowEntryTransCount) {
                    DB::commit();
                } else {
                    $this->sendLog("Settlement add fail", [
                        "param"  => [
                            $this->modifyValueOfKey($cardTransactions, 'card_number'),
                            $this->modifyValueOfKey($cardTickets, 'card_number'),
                            $this->modifyValueOfKey($cardEntryTrans, 'card_number')
                        ],
                        "result" => [
                            'count' => $rowTicketCount, 'TransactionCount' => $rowTransactionCount, 'entryCount' => $rowEntryTransCount
                        ]
                    ], 'info');
                    DB::rollBack();
                }
            }

            if (count($updateSettleIds) > 0) {
                CardTransaction::whereIn('transaction_order_id', $updateSettleIds)->update(['transaction_settle_status' => CardTransaction::TRANSACTION_SETTLE_STATUS_SETTLE]);
            }
        }

        $this->sendLog('Settlement inquiry statistics', $statistics, 'info');
        return $res;
    }

    //卡操作查询
    public function inquiryCardOperation($cardTicket = []): array
    {
        $params = [
            'unique_id' => $cardTicket['unique_id'],
            'row'       => 100,
            'page'      => 1,
        ];

        $results = $this->gateway->inquiryCardOperation($params);

        // 记录日志
        $this->sendLog('inquiryCardOperation API Response', $results, 'info');
        if (empty($results) || $results['code'] != '6666') {
            $this->sendLog('Inquiry Card Operation failed', ['params' => $params, 'result' => $results], 'warning');
            return $results;
        }

        if (empty($results['data']['list'])) {
            $this->cardTicketUpdateFailure($cardTicket, $results);
        } else {
            $this->cardTicketUpdateDetail($cardTicket, $results['data']['list']);
        }

        return $results;
    }

    //卡交易回调处理
    public function cardTradeNotify($params): string
    {
        $this->sendLog('cardTradeNotify', $params, 'info');
        if (!isset($params['authorization_id'])) {
            return 'error';
        }

        if (!isset($params['pp_card_id'])) {
            return 'error';
        }

        if ($params['authorization_type'] == 'Auth') {
            $authorizationType = CardTransaction::TRANSACTION_TYPE_AUTH;
            $cardTicketType    = CardTicket::TYPE_TRANSACTION;
        } elseif ($params['authorization_type'] == 'Reversal') {
            $authorizationType = CardTransaction::TRANSACTION_TYPE_REVERSAL;
            $cardTicketType    = CardTicket::TYPE_TRANSACTION;
        } elseif ($params['authorization_type'] == 'Purchase Return') {
            $authorizationType = CardTransaction::TRANSACTION_TYPE_PURCHASE_RETURN;
            $cardTicketType    = CardTicket::TYPE_REFUND;
        } else {
            $cardTicketType    = 404;
            $authorizationType = 404;
        }

        if (CardTransaction::where('transaction_order_id', $params['authorization_id'])->exists()) {
            return 'error';
        }

        // 获取卡信息
        $cardVirtualInfo = CardVirtual::where('trans_id', $params['pp_card_id'])->first();
        if (!$cardVirtualInfo) {
            $this->sendLog('Transaction get CardVirtual failed', ['param' => $params['pp_card_id'], 'result' => $params], 'warning');
            return 'error';
        }

        $uniqueId = CardTicket::getUniqueId();

        // 组装数据
        $cardTransaction = [
            'unique_id'               => $uniqueId,
            'virtual_id'              => $cardVirtualInfo->virtual_id,
            'merchant_id'             => $cardVirtualInfo->merchant_id,
            'merchant_name'           => $cardVirtualInfo->merchant_name,
            'cards_id'                => $cardVirtualInfo->cards_id,
            'card_bin_id'             => $cardVirtualInfo->card_bin_id,
            'bin'                     => $cardVirtualInfo->bin,
            'bin_name'                => $cardVirtualInfo->bin_name,
            'card_number'             => $cardVirtualInfo->card_number,
            'amount'                  => $params['billing_amount'],
            'currency'                => $params['billing_currency'],
            'transaction_type'        => $authorizationType,
            'transaction_status'      => $params['authorization_status'] == 'APPROVED' ? 1 : 0,
            'transaction_order_id'    => $params['authorization_id'],
            'transaction_description' => $params['merchant_name'] ?? '',
            'transaction_mcc'         => $params['mcc'] ?? '',
            'fail_reason'             => $params['fail_reason'] ?? '',
            'date_comtplete'          => ToolService::convertToSystemTimeZone($params['authorization_date']),
        ];

        $cardTicket = [
            'unique_id'       => $uniqueId,
            'unique_order_id' => $uniqueId,
            'merchant_id'     => $cardVirtualInfo->merchant_id,
            'merchant_name'   => $cardVirtualInfo->merchant_name,
            'cards_id'        => $cardVirtualInfo->cards_id,
            'card_bin_id'     => $cardVirtualInfo->card_bin_id,
            'bin'             => $cardVirtualInfo->bin,
            'bin_name'        => $cardVirtualInfo->bin_name,
            'virtual_id'      => $cardVirtualInfo->virtual_id,
            'card_number'     => $cardVirtualInfo->card_number,
            'amount'          => $params['billing_amount'],
            'currency'        => $params['billing_currency'],
            'type'            => $cardTicketType,
            'originator'      => 0,
            'status'          => $params['authorization_status'] == 'APPROVED' ? 1 : 0,
            'remarks'         => $params['authorization_id'],
        ];

        // 暂时不收取单笔处理费 transaction_fee
        DB::beginTransaction();
        $transaction = CardTransaction::create($cardTransaction);
        $ticket      = CardTicket::create($cardTicket);

        if ($transaction && $ticket) {
            DB::commit();
            return 'success';
        } else {
            $this->sendLog("Transaction add fail", [
                "param"  => [$this->modifyValueOfKey($cardTransaction, 'card_number'), $this->modifyValueOfKey($cardTicket, 'card_number')],
                "result" => [
                    'count' => $ticket, 'TransactionCount' => $transaction
                ]
            ], 'info');
            DB::rollBack();
            return 'error';
        }
    }

    //卡操作回调处理
    public function cardOperateNotify($cardTicket, $notify): string
    {
        $this->sendLog('cardOperateNotify', ['ticket' => $this->modifyValueOfKey($cardTicket, 'card_number'), 'notify' => $notify], 'info');
        return $this->cardTicketUpdateDetail($cardTicket, [$notify]);
    }

    //账户余额查询-常规
    public function accountBalance(array $params = []): array
    {
        $balance = 0;
        $this->sendLog('Start Account Balance Inquiry', $params, 'info');
        $results = $this->gateway->accountBalance($params);
        $this->sendLog('Response Start Account Balance Inquiry', $results, 'info');
        if ($results['code'] != Support::RESPONSE_CODE_SUCCESS) {
            return ['code' => 0];
        }

        foreach ($results['data'] as $budget) {
            if (isset($params['inquire_currency'], $budget['currency']) && $params['inquire_currency'] == $budget['currency']) {
                $balance += $budget['balance'];
            } else {
                if (isset($budget['currency']) && $budget['currency'] == 'USD') {
                    $balance += $budget['balance'];
                }
            }
        }

        return ['code' => 200, 'message' => 'success', 'data' => $results['data'], 'balance' => $balance];
    }

    //账户余额查询-共享
    public function inquireShareBalance(array $virtual = []): array
    {
        $budgetId = '';
        $balance  = 0;
        // 共享卡需要根据budget_id去匹配，所以先要查询卡信息
        $channelResults = $this->gateway->batchGetCardInfo([$virtual['trans_id']]);

        if (isset($channelResults[0]['code']) && $channelResults[0]['code'] == Support::RESPONSE_CODE_SUCCESS) {
            $budgetId = $channelResults[0]['data']['budget_id'] ?? '';
        }

        if (empty($budgetId)) {
            return ['code' => 0];
        }

        $this->sendLog('Start Account Share Balance Inquiry', $this->modifyValueOfKey($virtual, 'card_number'), 'info');
        $res = $this->accountBalance(['trans_id' => $virtual['trans_id']]);
        $this->sendLog('Response Start Account Share Balance Inquiry', $res, 'info');

        if ($res['code'] != 200) {
            return ['code' => 0];
        } else {
            foreach ($res['data'] as $account) {
                if ($budgetId == $account['budget_id']) {
                    $balance = $account['balance'];
                    break;
                }
            }
        }

        return ['code' => 200, 'message' => 'success', 'balance' => $balance];
    }

    //退值
    public function refund($cardTicketData = []): array
    {
        $params = [
            'unique_order_id' => $cardTicketData['unique_id'],
            'pp_card_id'      => $cardTicketData['trans_id'],
            'amount'          => $cardTicketData['amount'],
        ];
        $this->sendLog('Start card refund', $params, 'info');
        $results = $this->gateway->refund($params);
        if (isset($results['code']) && $results['code'] == '6666') {
            $code = 200;

            CardTicket::where('unique_id', $cardTicketData['unique_id'])->update([
                'status'  => 3, // 渠道处理中
                'remarks' => $results['data'],
            ]);

            // 添加查卡操作任务
            $cardTicketData['status'] = 3;
            dispatch(new CardOperation($cardTicketData, 2 * 60));
        } else {
            $code = 0;
            $this->cardTicketUpdateFailure($cardTicketData, $results);
        }

        return [
            'code'    => $code,
            'message' => $results['message'] ?? 'error',
            'data'    => $results['data'] ?? '',
        ];
    }

    //销卡
    public function destroy($virtualInfo = []): array
    {
        $params = [
            'pp_card_id' => $virtualInfo['trans_id'],
        ];
        $this->sendLog('Start card destroy', $params, 'info');
        if ($virtualInfo['card_type']) {
            //共享卡直接销卡
            $res = $this->gateway->cancellation($params);
            return $this->destroyUpdate($virtualInfo, $res);
        }

        //判断余额
        $cardBalanceResults = $this->inquireBalance($virtualInfo);
        if ($cardBalanceResults['code'] != '200') {
            $this->sendLog('Card Destroy Inquire Balance Error', $cardBalanceResults, 'warning');
            return ['code' => 0, 'message' => $cardBalanceResults['message']];
        }

        $balance  = $this->checkBinQuota($virtualInfo, $cardBalanceResults['data']['balance']);
        $uniqueId = CardTicket::getUniqueId();
        if ($balance > 0) {
            //需要退值.
            DB::beginTransaction();
            $cardTicketData = [
                'unique_id'       => $uniqueId,
                'unique_order_id' => $uniqueId,
                'merchant_id'     => $virtualInfo['merchant_id'],
                'merchant_name'   => $virtualInfo['merchant_name'],
                'cards_id'        => $virtualInfo['cards_id'],
                'card_bin_id'     => $virtualInfo['card_bin_id'],
                'bin'             => $virtualInfo['bin'],
                'bin_name'        => $virtualInfo['bin_name'],
                'virtual_id'      => $virtualInfo['virtual_id'],
                'card_number'     => $virtualInfo['card_number'],
                'amount'          => $balance,
                'currency'        => 'USD',
                'type'            => CardTicket::TYPE_CHARGE_OUT,
                'originator'      => CardTicket::ORIGINATOR_SYSTEM,
                'status'          => CardTicket::STATUS_PENDING, // 待处理
                'remarks'         => '',
            ];
            $cardTicket     = CardTicket::create($cardTicketData);

            $cardVirtualUpdate = CardVirtual::where('virtual_id', $virtualInfo['virtual_id'])->update([
                'date_complete_cancel' => now(),
                'status'               => CardVirtual::WAITDESTROY,
            ]);

            if (!$cardTicket || !$cardVirtualUpdate) {
                DB::rollBack();
                $this->sendLog('Card Destruction Refund Error', $this->modifyValueOfKey($cardTicketData, 'card_number'), 'warning');
                return ['code' => 0, 'message' => 'Card Destruction Refund Error'];
            }

            DB::commit();

            $cardTicketData['trans_id'] = $virtualInfo['trans_id'];
            return $this->refund($cardTicketData);
        }

        //不需要退值,直接销卡
        $res = $this->gateway->cancellation($params);
        return $this->destroyUpdate($virtualInfo, $res);
    }

    //冻结
    public function block($virtualInfo = [], $blockType = CardVirtual::MERCHANT_BLOCK): bool
    {
        $params = [
            'pp_card_id' => $virtualInfo['trans_id'],
        ];

        $this->sendLog('Start card block', [
            'params' => $params
        ], 'info');

        $result = $this->gateway->block($params);

        $this->sendLog('result card block', [
            'result' => $result
        ], 'info');

        if (isset($result['code']) && $result['code'] == Support::RESPONSE_CODE_SUCCESS) {
            // 更新卡状态
            $cardVirtualRes = CardVirtual::where('virtual_id', $virtualInfo['virtual_id'])->update(['status' => $blockType]);

            if (!$cardVirtualRes) {
                $this->sendLog("block failed", [
                    'result' => $result
                ], 'warning');

                return false;
            }

            return true;
        }

        $this->sendLog("block failed", [
            'result' => $result
        ], 'warning');

        return false;
    }

    //解冻
    public function unblock($virtualInfo = []): bool
    {
        $params = [
            'pp_card_id' => $virtualInfo['trans_id'],
        ];

        $this->sendLog('Start card unblock', [
            'params' => $params
        ], 'info');

        $result = $this->gateway->unblock($params);

        $this->sendLog('result card unblock', [
            'result' => $result
        ], 'info');

        if (isset($result['code']) && $result['code'] == Support::RESPONSE_CODE_SUCCESS) {
            // 更新卡状态
            $cardVirtualRes = CardVirtual::where('virtual_id', $virtualInfo['virtual_id'])->update(['status' => CardVirtual::ACTIVATION]);

            if (!$cardVirtualRes) {
                $this->sendLog("unblock failed", [
                    'result' => $result
                ], 'warning');

                return false;
            }

            return true;
        }

        $this->sendLog("unblock failed", [
            'result' => $result
        ], 'warning');

        return false;
    }

    //销卡更新
    public function destroyUpdate($virtualInfo = [], $res = [])
    {
        if ($res['code'] != '6666') {
            $this->sendLog('Destroy Update Error', $res, 'warning');
            return ['code' => 0, 'message' => $res['message']];
        }

        $cardVirtualUpdate = CardVirtual::where('virtual_id', $virtualInfo['virtual_id'])->update([
            'date_complete_cancel' => now(),
            'status'               => CardVirtual::DESTROY,
        ]);

        if (!$cardVirtualUpdate) {
            $this->sendLog('Destroy Update Status Error', $res, 'warning');
            return ['code' => 0, 'message' => 'Failed to modify the destruction status of the card'];
        }

        return ['code' => 200, 'message' => $res['message']];
    }

    public function shareCardLimit($cardBatch = []): void
    {
        $params = [
            'pp_card_id' => $cardBatch['trans_id'],
            'amount'     => $cardBatch['day_amount_limit'],
            'type'       => 'DAY' // 目前只有按天限额、但是渠道那边 还有其他type类型 如：TRANSACTION(单次限额)、WEEK(按周限额)、MONTH(按月限额)、LIFETIME(终身限额) 注：限额的起始时间都是 00:00:00 UTC.
        ];
        $this->sendLog('Start Share card Limit', $params, 'info');

        $result = $this->gateway->shareCardLimit($params);
        if ($result["code"] == Support::RESPONSE_CODE_SUCCESS) {
            // 限额新到虚拟卡表中
            $cardVirtualRes = CardVirtual::where('trans_id', $cardBatch['trans_id'])->update(['day_amount_limit' => $cardBatch["day_amount_limit"]]);
            if (!$cardVirtualRes) {
                $this->sendLog("shareCardLimit failed", [
                    'result' => $result
                ], 'warning');
            }
        } else {
            // 执行失败log
            $this->sendLog("shareCardLimit failed", [
                'result' => $result
            ], 'warning');
        }
    }

    /**
     * 卡转账通知
     * @param array $content
     * $content = [
     *      'trans_id'         => '',
     *      'card_ticket_type' => CardTicket::TYPE_CHARGE_OUT,
     *      'remarks'          => '',
     *      'amount'           => 0,
     *      'currency'         => 'USD',
     *      'transfer_date'    => '',
     *   ];
     * @return array
     */
    public function cardTransferNotify(array $cardVirtualInfo, array $content): array
    {
        $res = ['respond' => 'fail', 'card_ticket_data' => []];
        // 非USD数据无法处理
        if ($content['currency'] != 'USD') {
            $this->sendLog("CardTransferNotify non USD data", [
                'result' => $content
            ], 'warning');
            return $res;
        }

        // 充值数据不处理（这个接口为销卡通知接口，不会有充值数据，防止错误处理）
        if ($content['card_ticket_type'] == CardTicket::TYPE_RECHARGE) {
            $this->sendLog("CardTransferNotify type recharge data", [
                'result' => $content
            ], 'warning');
            return $res;
        }
        // 匹配card_tickets是否已经退值过了
        $cardTicket = CardTicket::where([
            'virtual_id' => $cardVirtualInfo['virtual_id'],
            'type'       => $content['card_ticket_type'],
            'amount'     => $content['amount'],
            'currency'   => $content['currency'],
            'remarks'    => $content['remarks'],
            'created_at' => $content['transfer_date'],
        ])->first();

        if (!empty($cardTicket)) {
            $this->sendLog("CardTransferNotify repeat", [
                'result' => $content
            ], 'info');
            $res['respond'] = 'success';
            return $res;
        }
        // 插入数据
        DB::beginTransaction();
        try {
            $uniqueId = CardTicket::getUniqueId();
            // 插入数据
            $cardTicketData = [
                'unique_id'       => $uniqueId,
                'unique_order_id' => $uniqueId,
                'merchant_id'     => $cardVirtualInfo['merchant_id'],
                'merchant_name'   => $cardVirtualInfo['merchant_name'],
                'cards_id'        => $cardVirtualInfo['cards_id'],
                'card_bin_id'     => $cardVirtualInfo['card_bin_id'],
                'bin'             => $cardVirtualInfo['bin'],
                'bin_name'        => $cardVirtualInfo['bin_name'],
                'virtual_id'      => $cardVirtualInfo['virtual_id'],
                'card_number'     => $cardVirtualInfo['card_number'],
                'amount'          => $content['amount'],
                'currency'        => $content['currency'],
                'type'            => $content['card_ticket_type'],
                'originator'      => CardTicket::ORIGINATOR_SYSTEM,
                'status'          => CardTicket::STATUS_SUCCESS,
                'remarks'         => $content['remarks'],
                'created_at'      => $content['transfer_date'],
                'updated_at'      => date('Y-m-d H:i:s'),
            ];

            $isInsertTicket = CardTicket::insert($cardTicketData);

            if (!$isInsertTicket) {
                DB::rollBack();
                $this->sendLog('CardTransferNotify insert cardTicket Error', ['params' => $cardTicketData], 'error');
                return $res;
            }

            // 更新CID余额
            DB::update("UPDATE merchant_cards SET `balance` = `balance` + '{$cardTicketData['amount']}' WHERE `cards_id` = '{$cardTicketData['cards_id']}'");

            //增加结算对冲
            $settleDetailCardData = [
                'unique_id'     => $cardTicketData['unique_id'],
                'cards_id'      => $cardTicketData['cards_id'],
                'merchant_id'   => $cardTicketData['merchant_id'],
                'merchant_name' => $cardTicketData['merchant_name'],
                'virtual_id'    => $cardTicketData['virtual_id'],
                'card_number'   => $cardTicketData['card_number'],
                'amount'        => $cardTicketData['amount'],
                'type'          => SettleDetailCard::SETTLE_DETAIL_CARDS_TYPE_11,
                'settle_at'     => get_settle_date(),
                'balance'       => MerchantCard::where('cards_id', $cardTicketData['cards_id'])->value('balance'),
            ];

            $isInsertSettle = SettleDetailCard::create($settleDetailCardData);

            if (!$isInsertSettle) {
                DB::rollBack();
                $this->sendLog('CardTransferNotify insert settleDetailCard Error', ['params' => $settleDetailCardData], 'error');
                return $res;
            }

            //更新卡余额
            dispatch(new CardBalanceQuery($cardVirtualInfo, 5)); // 添加异步查卡余额任务
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $this->sendLog('CardTransferNotify Error:' . $e->getMessage(), [], 'error');
        }

        $res['respond']          = 'success';
        $res['card_ticket_data'] = $cardTicketData;

        return $res;
    }

    public function setFraud(array $cardBlackInfo): int
    {
        $param       = [
            'mcc'           => $cardBlackInfo['mcc'],
            'merchant_name' => $cardBlackInfo['merchant_name'],
        ];
        $updateCount = 0;
        $result      = $this->gateway->setFraud($param);
        if ($result['code'] == Support::RESPONSE_CODE_SUCCESS) {
            if (isset($cardBlackInfo['is_all'])) {
                // 更新黑名单表 已同步到渠道
                $updateCount = CardVirtualBlack::where('status', CardVirtualBlack::CARD_BLACK_STATUS_ENABLE)->update([
                    'is_sync' => CardVirtualBlack::CARD_BLACK_IS_SYNC_YES,
                ]);
            }
        } else {
            // 执行失败log
            $this->sendLog("setFraud failed", [
                'result' => $result
            ], 'warning');
        }

        return $updateCount;
    }

    // 创建持卡人
    public function createHolder(array $cardHolderInfo): array
    {
        if (!empty($this->getCardHolderReport($cardHolderInfo['reports'] ?? []))) {
            return ['code' => '000', 'msg' => '持卡人已报备'];
        }

        $pinYinService = new PinyinService();
        $param         = [
            'budget_id'       => '',
            'calling_prefix'  => "+" . $cardHolderInfo['calling_prefix'],
            'mobile'          => $cardHolderInfo['phone'],
            'email'           => $cardHolderInfo['email'],
            'first_name'      => str_replace(" ", "", $pinYinService->str2pys($cardHolderInfo['first_name'])),
            'last_name'       => str_replace(" ", "", $pinYinService->str2pys($cardHolderInfo['last_name'])),
            'date_of_birth'   => date('Y-m-d', strtotime($cardHolderInfo['birth_date'])),
            'address_line'    => $cardHolderInfo['address'],
            'city'            => str_replace(" ", "", $pinYinService->str2pys($cardHolderInfo['city'])),
            'state'           => str_replace(" ", "", $pinYinService->str2pys($cardHolderInfo['province'])),
            'post_code'       => $cardHolderInfo['postal_code'],
            'country_code'    => $cardHolderInfo['country'],
            'security_index'  => (string)$cardHolderInfo['security_index'],
            'security_answer' => str_replace(" ", "", $pinYinService->str2pys($cardHolderInfo['security_answer'])),
        ];
        $result        = $this->gateway->createHolder($param);
        $returnData    = [
            'payer_id'           => $result['data']['budget_id'] ?? '',
            'report_supplier_id' => $result['data']['card_holder_id'] ?? '',
            'supplier_type'      => CardHolderChannelReports::CHANNEL_REPORT_SUPPLIER_TYPE_PP,
            'report_result'      => CardHolderChannelReports::CHANNEL_REPORT_RESULT_SUCCESS,
        ];
        if (!isset($result['code']) || $result['code'] != Support::RESPONSE_CODE_SUCCESS) {
            // 执行失败log
            $this->sendLog("createHolder failed", [
                'result' => $result
            ], 'info');
            $returnData['report_result'] = CardHolderChannelReports::CHANNEL_REPORT_RESULT_FAIL;
            return ['code' => '500', 'data' => $returnData, 'message' => $result['message']];
        }

        return ['code' => '200', 'data' => $returnData];
    }

    public function bindCardHolder(array $virtualInfo): array
    {
        $cardHolderId = CardHolderChannelReports::where('cardholder_id', $virtualInfo['cardholder_id'])
            ->where('supplier_type', CardHolderChannelReports::CHANNEL_REPORT_SUPPLIER_TYPE_PP)
            ->where('report_result', CardHolderChannelReports::CHANNEL_REPORT_RESULT_SUCCESS)
            ->value('report_supplier_id');
        if (empty($cardHolderId)) {
            return ['code' => '500', 'message' => 'cardholder_id not found'];
        }

        $param  = [
            'pp_card_id'     => $virtualInfo['trans_id'],
            'card_holder_id' => $cardHolderId,
        ];
        $result = $this->gateway->bindCardHolder($param);
        if (!isset($result['code']) || $result['code'] != Support::RESPONSE_CODE_SUCCESS) {
            // 执行失败log
            $this->sendLog("Bind Card Holder failed", [
                'result' => $result
            ], 'warning');

            return ['code' => '500', 'message' => $result['message']];
        }

        return ['code' => '200', 'message' => 'success'];
    }

    //针对卡Bin最低剩余0.01
    public function checkBinQuota($virtualInfo, $balance)
    {
        $binArr = ['428837'];
        if (in_array($virtualInfo['bin'], $binArr)) {
            $balance -= 0.01;
        }

        return $balance;
    }

    public function setGatewayConfig(string $config = ''): VirtualServiceInterface
    {
        $this->gateway->setConfig($config);
        return $this;
    }

    private function sendLog(string $message, array $data, string $level): void
    {
        Log::$level($message, $data);
        dispatch(new SendSlsLog(
            ['message' => $message],
            $data,
            $level,
            'virtual_card',
        ));
    }

    /*
     * 递归修改数组key值  目前用于虚拟卡卡号 日记卡掩码显示
     * @param array $haystack
     * @param string $needle
     * @return array
     */
    private function modifyValueOfKey($haystack, $needle)
    {
        return array_map(function ($key, $value) use ($needle) {
            if (is_array($value)) {
                return $this->modifyValueOfKey($value, $needle);
            }

            return $key === $needle ? get_markcard(DES3::encrypt($value, env('DES3_CARD_VIRTUAL'))) : $value;
        }, array_keys($haystack), $haystack);
    }

    public function getCardHolderReport(array $reports = []): array
    {
        foreach ($reports as $report) {
            if ($report['supplier_type'] == CardHolderChannelReports::CHANNEL_REPORT_SUPPLIER_TYPE_PP) {
                return $report;
            }
        }
        return [];
    }
}
