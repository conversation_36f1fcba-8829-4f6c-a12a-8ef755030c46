<?php


namespace App\Services\Virtual;

class VirtualServiceFacade
{
    /**
     * 获取虚拟卡服务
     *
     * @param string $name
     * @return VirtualServiceInterface
     */
    public static function getService(string $name): VirtualServiceInterface
    {
        $cardApplyServiceClass = "\App\Services\Virtual\\" . $name . "Service";
        $cardApplyService      = app($cardApplyServiceClass);
        if ($cardApplyService instanceof VirtualServiceInterface) {
            return $cardApplyService;
        }
        throw new \Exception('getService error');
    }
}
