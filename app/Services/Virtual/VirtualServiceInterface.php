<?php


namespace App\Services\Virtual;

interface VirtualServiceInterface
{
    /**
     * 批量申请开卡
     *
     * @param array $params
     * @param integer $num
     * @return array
     */
    public function batchApplyCard(array $params = [], int $num = 0): array;

    /**
     * 查询卡余额
     *
     * @param array $virtual
     * @return array
     */
    public function inquireBalance(array $virtual = []): array;

    /**
     * 查询共享卡余额
     *
     * @param array $virtual
     * @return array
     */
    public function inquireShareBalance(array $virtual = []): array;

    /**
     * 查询卡结算信息
     *
     * @param array $data
     * @return array
     */
    public function inquirySettlement(array $data = []): array;

    /**
     * 查询卡交易信息
     *
     * @param array $data
     * @return array
     */
    public function inquiryTrade(array $data = []): array;

    /**
     * 充值
     *
     * @param array $cardTicketData
     * @return array
     */
    public function recharge(array $cardTicketData = []): array;

    /**
     * 卡退值
     *
     * @param array $cardTicketData
     * @return array
     */
    public function refund(array $cardTicketData = []): array;

    /**
     * 销卡
     *
     * @param array $virtual
     * @return array
     */
    public function destroy(array $virtual = []): array;

    /**
     * 冻结
     *
     * @param array $virtual
     * @param int $blockType
     * @return bool
     */
    public function block(array $virtual = [], int $blockType = 0): bool;

    /**
     * 解冻
     *
     * @param array $virtual
     * @param int $blockType
     * @return bool
     */
    public function unblock(array $virtual = []): bool;

    /**
     * 查询卡操作
     *
     * @param array $virtual
     * @return array
     */
    public function inquiryCardOperation(array $virtual = []): array;

    /**
     * 设置网关配置
     *
     * @param string $config
     * @return self
     */
    public function setGatewayConfig(string $config = ''): VirtualServiceInterface;

    /**
     * 卡信息查询
     * @param array $cardVirtualInfo [ids1,ids2]
     * @return void
     */
    public function batchGetCardInfo(array $transIds = []): void;

    /**
     * 共享卡限制
     *
     * @param array $params
     * @return void
     * @throws \App\Classes\Card\Exceptions\CardException
     */
    public function shareCardLimit(array $params): void;

    /**
     * 账户余额查询
     *
     * @param array $params
     * @return array
     * @throws \App\Classes\Card\Exceptions\CardException
     */
    public function accountBalance(array $params = []): array;

    /**
     * 卡交易通知
     * @param array $content
     * @return string
     */
    public function cardTradeNotify(array $content): string;

    /**
     * 卡操作通知
     * @param array $content
     * @return string
     */
    public function cardOperateNotify(array $cardTickets, array $content): string;

    /**
     * 卡转账通知
     * @param array $content
     * @return array ['respond' => 'fail', 'card_ticket_data' => []]
     */
    public function cardTransferNotify(array $cardVirtualInfo, array $content): array;

    /**
     * 获取黑名单
     * @return int
     */
    public function getFraudList(): int;

    /**
     * 设置黑名单
     * @param array $cardBlackInfo
     * @return int
     */
    public function setFraud(array $cardBlackInfo): int;

    /**
     * 创建持卡人
     * @param array $cardHolder
     * @return array
     */
    public function createHolder(array $cardHolder): array;

    /**
     * 绑定持卡人
     * @param array $virtualInfo
     * @return array
     */
    public function bindCardHolder(array $virtualInfo): array;
}
