<?php
namespace App\Services;

use App\Models\CardWhiteList;
use App\Models\Order;
use App\Models\OrderCard;
use App\Models\OrderRelation;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;

class ChannelWhiteListService
{
    /**
     * 处理数据
     * @param array $data
     * @param array $cardMasks
     * @param Carbon $startTimeDay
     * @param Carbon $endTimeDay
     * @param Carbon $marchTimeDay
     * @return void
     */
    public function handelDate(&$data, &$cardMasks, $startTimeDay, $endTimeDay): void
    {
        if (!count($cardMasks)) {
            return;
        }

        $cardId = OrderCard::whereIn('card_mask', array_keys($cardMasks))->whereBetween('created_at', [$startTimeDay, $endTimeDay])->pluck('card_mask', 'id')->toArray();

        if (!count($cardId)) {
            $cardMasks = [];
            return;
        }

        $orders = Order::select('order_id', 'card_id', 'amount', 'address_id', 'status', 'created_at')
            ->with(['address:id,bill_first_name,bill_last_name,bill_email,bill_address', 'relation:order_id,is_refund,is_chargeback'])
            ->whereHas('relation', function (Builder $query) {
                $query->where('is_chargeback', OrderRelation::IS_CHARGEBACK_NOT);
            })
            ->whereIn('card_id', array_keys($cardId))
            ->where('status', Order::STATUS_APPROVED)
            ->get();

        foreach ($orders as $order) {
            if (isset($data[$cardId[$order->card_id]]) && count($data[$cardId[$order->card_id]]) == 3) {
                unset($cardMasks[$order->card->card_mask]);
                continue;
            }

            $data[$cardId[$order->card_id]][] = [
                'request_date'     => $order->created_at->toDateTimeString(),
                'status'           => 'Approved',
                'amount'           => $order->amount,
                'ccn'              => $order->card->card_mask,
                'customer_name'    => $order->address->bill_last_name,
                'customer_surname' => $order->address->bill_first_name,
                'customer_address' => $order->address->bill_address,
                'customer_email'   => $order->address->bill_email,
                'refund'           => (optional($order->relation)->is_refund == 0) ? 0 : 1,
                'chargeback'       => 0,
            ];
        }

        $cardMasks = array_intersect_key($cardMasks, $data);
    }

    /**
     * 补充缺失数据
     * @param array $data
     * @param array $cardMasks
     * @param Carbon $startTimeDay
     * @param Carbon $endTimeDay
     * @param Carbon $marchTimeDay
     * @return void
     */
    public function supplementaryData(&$data, &$cardMasks, $startTimeDay, $endTimeDay, &$logCardMask): void
    {
        if (!count($cardMasks)) {
            return;
        }

        // 查看要补充的卡掩码是否在卡号白名单，只补充商户171031004584662的数据
        $cardId = CardWhiteList::query()->whereIn('card_mask', array_keys($cardMasks))->where('merchant_ids', 'like', '%171031004584662%')->pluck('card_mask', 'id')->toArray();
        $count  = count($cardId);
        if (!$count) {
            $cardMasks = [];
            return;
        }

        $ordersSql = Order::select('order_id', 'card_id', 'amount', 'address_id', 'status', 'created_at')
            ->with(['address:id,bill_first_name,bill_last_name,bill_email,bill_address', 'relation:order_id,is_refund,is_chargeback'])
            ->whereHas('relation', function (Builder $query) {
                $query->where('is_chargeback', OrderRelation::IS_CHARGEBACK_NOT);
            })
            ->whereBetween('created_at', [$startTimeDay, $endTimeDay])
            ->where('status', Order::STATUS_APPROVED);

        // 获取上次查询最后的订单ID，如果存在则从上次查询的最后一条开始查询
        $key     = 'Channel_White_List_Order_Id';
        $orderId = Cache::get($key);
        if ($orderId) {
            $ordersSql->where('order_id', '>', $orderId);
        }

        $orders = $ordersSql->limit(($count * 4))
            ->get();

        if (!count($orders)) {
            // 查询不到数据，清空缓存记录并且重新查询
            Cache::forget($key);

            $orders = Order::select('order_id', 'card_id', 'amount', 'address_id', 'status', 'created_at')
                ->with(['address:id,bill_first_name,bill_last_name,bill_email,bill_address', 'relation:order_id,is_refund,is_chargeback'])
                ->whereHas('relation', function (Builder $query) {
                    $query->where('is_chargeback', OrderRelation::IS_CHARGEBACK_NOT);
                })
                ->whereBetween('created_at', [$startTimeDay, $endTimeDay])
                ->where('status', Order::STATUS_APPROVED)->limit(($count * 4))
                ->get();
        }

        $orderCount  = count($orders); // 订单数量
        if ($orderCount) {
            $now = Carbon::now();
            // 计算6个月前的日期
            $sixMonthsAgo = $now->copy()->subMonths(6);
            // 计算90天前的日期
            $threeMonthsAgo = $now->copy()->subDays(90);

            // 计数器
            $count = 0;
            foreach ($cardId as $cardMask) {
                for ($i = 0; $i < 3; $i++) {
                    if (!isset($orders[$count]))  $count = 0; // 数据不够，重新从第一条开始

                    $order       = $orders[$count];
                    // 随机生成90天前到现在的日期
                    $requestDate = Carbon::createFromTimestamp(rand($now->timestamp, $threeMonthsAgo->timestamp));
                    if ($i == 0) {
                        // 生成6个月前到90天前之间的随机日期，因为成功交易至少要有一笔90天之前的
                        $requestDate = Carbon::createFromTimestamp(rand($threeMonthsAgo->timestamp, $sixMonthsAgo->timestamp));
                    }

                    if (isset($data[$cardMask][0])) {
                        $temp = $data[$cardMask][0];
                        $lastName  = $temp['customer_name'];
                        $firstName = $temp['customer_surname'];
                        $address   = $temp['customer_address'];
                        $email     = $temp['customer_email'];
                    } else {
                        $lastName  = $order->address->bill_last_name;
                        $firstName = $order->address->bill_first_name;
                        $address   = $order->address->bill_address;
                        $email     = $order->address->bill_email;
                    }

                    $data[$cardMask][] = [
                        'request_date'     => $requestDate->toDateTimeString(),
                        'status'           => 'Approved',
                        'amount'           => $order->amount,
                        'ccn'              => $cardMask,
                        'customer_name'    => $lastName,
                        'customer_surname' => $firstName,
                        'customer_address' => $address,
                        'customer_email'   => $email,
                        'refund'           => (optional($order->relation)->is_refund == 0) ? 0 : 1,
                        'chargeback'       => 0,
                    ];

                    $logCardMask[$cardMask][] = $order->order_id; // 记录对应日志
                    $count++; // 计数器加1
                }
            }

            // 最后一条订单数据
            $lastElement = $orders->last();
            Cache::put($key, $lastElement->order_id);

            $cardMasks = array_intersect_key($cardMasks, $data);
        }
    }
}