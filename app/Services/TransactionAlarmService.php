<?php

namespace App\Services;

use App\Jobs\SendNotice;
use App\Jobs\SendSlsLog;
use App\Models\Channel;
use App\Models\ChannelPid;
use App\Models\DirectoryDictionary;
use App\Models\Order;
use Illuminate\Support\Facades\Cache;

class TransactionAlarmService
{

	// 连续失败告警(固定10笔) 缓存key
	public static $continuousFailAlarmCacheKey = 'Order_transaction_failed';

	/**
	 * @description: 连续失败告警 => 商户
	 * @author: zqc
	 * @date: 2023/10/9
	 **/
	public static function continuousFailAlarmToMerchant($order, $cacheData = [])
	{
		$continuousFailNumber  = DirectoryDictionary::where('type', '连续失败告警')->where('name', '商户失败监测')->value('remarks') ?? 10;
		$isSendAlarmToMerchant = false;

		// 判断订单状态 -> 失败
		if ($order->status == Order::STATUS_DECLINED) {
			// 需要进行缓存的数据
			$cacheData[$order->merchant_id]['order_data'][]                                                     = $order->order_id;
			$cacheData[$order->merchant_id]['business_data'][$order->business_id]['order_data'][]               = $order->order_id;
			$cacheData[$order->merchant_id]['business_data'][$order->business_id]['url_data'][$order->url_id][] = $order->order_id;

			// 发生MID告警
			if (!$isSendAlarmToMerchant) {
				$sendOrderIds = self::checkContinuousFailAlarm([$cacheData[$order->merchant_id]['order_data']], $continuousFailNumber);
				if (!empty($sendOrderIds)) {
					// 发送告警
					$isSendAlarmToMerchant = true;
					// 发送告警故障值
					$sendStr  = $order->merchant_id;
					$orderIds = $sendOrderIds;
					// 获取该次满足条件发生的order_id
					$tempMerchantOrderId = $cacheData[$order->merchant_id]['order_data'];
					// 重置缓存值
					$cacheData[$order->merchant_id]['order_data'] = [];
					self::unsetInvalidData($cacheData[$order->merchant_id]['business_data'], $tempMerchantOrderId);
				}
			}

			// 发生BID告警
			if (!$isSendAlarmToMerchant) {
				$sendOrderIds = self::checkContinuousFailAlarm([$cacheData[$order->merchant_id]['business_data'][$order->business_id]['order_data']], $continuousFailNumber);
				if (!empty($sendOrderIds)) {
					// 发送告警
					$isSendAlarmToMerchant = true;
					// 发送告警故障值
					$sendStr  = $order->business_id;
					$orderIds = $sendOrderIds;
					// 获取该次满足条件发生的order_id
					$tempBusinessId    = $cacheData[$order->merchant_id]['business_data'][$order->business_id]['order_data'];
					$merchantOrderData = $cacheData[$order->merchant_id]['order_data'] ?? [];
					// 取差集 更新数据
					$merchantOrderIds                                                     = array_diff($merchantOrderData, $tempBusinessId);
					$cacheData[$order->merchant_id]['order_data']                         = array_values($merchantOrderIds);
					$cacheData[$order->merchant_id]['business_data'][$order->business_id] = [];
					self::unsetInvalidData($cacheData[$order->merchant_id]['business_data'][$order->business_id]['url_data'], $tempBusinessId);
				}
			}

			// 发生URL告警
			if (!$isSendAlarmToMerchant) {
				$sendOrderIds = self::checkContinuousFailAlarm([$cacheData[$order->merchant_id]['business_data'][$order->business_id]['url_data'][$order->url_id]], $continuousFailNumber);
				if (!empty($sendOrderIds)) {
					// 发送告警
					$isSendAlarmToMerchant = true;
					// 发送告警故障值
					$sendStr  = $order->url_id;
					$orderIds = $sendOrderIds;
					// 找出成功订单的url的order_id
					$urlOrderIds = $cacheData[$order->merchant_id]['business_data'][$order->business_id]['url_data'][$order->url_id] ?? [];
					// 取差集
					$merchantOrderData = $cacheData[$order->merchant_id]['order_data'] ?? [];
					$businessOrderData = $cacheData[$order->merchant_id]['business_data'][$order->business_id]['order_data'] ?? [];
					$merchantOrderIds  = array_diff($merchantOrderData, $urlOrderIds);
					$businessOrderIds  = array_diff($businessOrderData, $urlOrderIds);
					// 重新写入
					$cacheData[$order->merchant_id]['order_data']                                                     = array_values($merchantOrderIds);
					$cacheData[$order->merchant_id]['business_data'][$order->business_id]['order_data']               = array_values($businessOrderIds);
					$cacheData[$order->merchant_id]['business_data'][$order->business_id]['url_data'][$order->url_id] = [];
				}
			}
		}

		// 判断订单状态 -> 成功
		if ($order->status == Order::STATUS_APPROVED) {
			// 更新缓存数据
			if (!empty($cacheData)) {
				$cacheData[$order->merchant_id]['order_data']                                                     = [];
				$cacheData[$order->merchant_id]['business_data'][$order->business_id]['order_data']               = [];
				$cacheData[$order->merchant_id]['business_data'][$order->business_id]['url_data'][$order->url_id] = [];
			}
		}

		Cache::put(self::$continuousFailAlarmCacheKey . $order->merchant_id, json_encode($cacheData), 24 * 60 * 60 * 15);

		// 判断是否需要进行 交易告警
		if ($isSendAlarmToMerchant) {
			$data = [
				'level'             => 1,
				'contents'          =>
					'[交易告警]' . PHP_EOL .
					'故障MID/BID/URL:' . $sendStr . PHP_EOL .
					'故障详情:' . '连续失败' . $continuousFailNumber . '笔' . PHP_EOL .
					'关联商户:' . $order->merchant_name . PHP_EOL .
					'关联订单:' . PHP_EOL . implode(PHP_EOL, $orderIds) . PHP_EOL .
					'来源:' . config('app.url'),
				'notice_user_roles' => 'Administrator,Operate Supervisor',
				'type'              => 3,
				'status'            => 2,
			];

			logger()->channel('intercept')->info(
				'连续失败告警',
				['sendData' => $data['contents'], 'time' => time()]
			);

			dispatch(new SendSlsLog(
				['message' => '连续失败告警'],
				['sendData' => $data['contents'], 'time' => time()],
				'info',
				'intercept'
			));
			dispatch(new SendNotice($data, 5));
		}
	}

	/**
	 * @description: 连续失败告警 => 渠道
	 * @author: zqc
	 * @date: 2023/10/9
	 **/
	public static function continuousFailAlarmToChannel($order, $cacheData, $supplierData)
	{
		$continuousFailNumber = DirectoryDictionary::where('type', '连续失败告警')->where('name', '渠道失败监测')->value('remarks') ?? 10;
		$isSendAlarmToChannel = false;
		$channelPidId         = $supplierData['channel_pid'];
		$supplierId           = $supplierData['supplier_id'];
		$supplierName         = $supplierData['supplier_name'];

		// 判断订单状态 -> 失败
		if ($order->status == Order::STATUS_DECLINED) {
			// 需要进行缓存的数据
			$cacheData[$supplierId]['order_data'][]                                                   = $order->order_id;
			$cacheData[$supplierId]['channel_pid'][$channelPidId]['order_data'][]                     = $order->order_id;
			$cacheData[$supplierId]['channel_pid'][$channelPidId]['channel_id'][$order->channel_id][] = $order->order_id;

			// 发送渠道告警
			if (!$isSendAlarmToChannel) {
				$sendOrderIds = self::checkContinuousFailAlarm([$cacheData[$supplierId]['order_data']], $continuousFailNumber);
				// 单发的情况  穿插成功
				if (!empty($sendOrderIds)) {
					// 发送告警
					$isSendAlarmToChannel = true;
					// 发送告警故障值
					$sendStr  = $supplierName;
					$orderIds = $sendOrderIds;
					// 获取该次满足条件发生的order_id
					$supplierOrderData = $cacheData[$supplierId]['order_data'];
					// 重置
					$cacheData[$supplierId]['order_data'] = [];
					self::unsetInvalidData($cacheData[$supplierId]['channel_pid'], $supplierOrderData);
				}
			}

			// 发送pid告警
			if (!$isSendAlarmToChannel) {
				$sendOrderIds = self::checkContinuousFailAlarm([$cacheData[$supplierId]['channel_pid'][$channelPidId]['order_data']], $continuousFailNumber);
				// 单发的情况  穿插成功
				if (!empty($sendOrderIds)) {
					// 发送告警
					$isSendAlarmToChannel = true;
					// 发送告警故障值
					$channelPid = ChannelPid::where('id', $channelPidId)->value('channel_pid');
					$sendStr    = $channelPid;
					$orderIds   = $sendOrderIds;
					// 获取该次满足条件发生的order_id
					$tempChannelPid    = $cacheData[$supplierId]['channel_pid'][$channelPidId]['order_data'];
					$supplierOrderData = $cacheData[$supplierId]['order_data'] ?? [];
					// 取差集 更新数据
					$supplierOrderIds                                     = array_diff($supplierOrderData, $tempChannelPid);
					$cacheData[$supplierId]['order_data']                 = array_values($supplierOrderIds);
					$cacheData[$supplierId]['channel_pid'][$channelPidId] = [];
					self::unsetInvalidData($cacheData[$supplierId]['channel_pid'][$channelPidId]['channel_id'], $tempChannelPid);
				}
			}

			// 发送账单告警
			if (!$isSendAlarmToChannel) {
				$sendOrderIds = self::checkContinuousFailAlarm([$cacheData[$supplierId]['channel_pid'][$channelPidId]['channel_id'][$order->channel_id]], $continuousFailNumber);
				if (!empty($sendOrderIds)) {
					// 发送告警
					$isSendAlarmToChannel = true;
					// 发送告警故障值
					$channel  = Channel::where('id', $order->channel_id)->value('channel');
					$sendStr  = $channel;
					$orderIds = $sendOrderIds;
					// 找出成功订单的url的order_id
					$channelIdToOrderIds = $cacheData[$supplierId]['channel_pid'][$channelPidId]['channel_id'][$order->channel_id] ?? [];
					// 取差集
					$supplierOrderData   = $cacheData[$supplierId]['order_data'] ?? [];
					$channelPidOrderData = $cacheData[$supplierId]['channel_pid'][$channelPidId]['order_data'] ?? [];
					$supplierOrderIds    = array_diff($supplierOrderData, $channelIdToOrderIds);
					$channelPidOrderIds  = array_diff($channelPidOrderData, $channelIdToOrderIds);
					// 重新写入
					$cacheData[$supplierId]['order_data']                                                   = array_values($supplierOrderIds);
					$cacheData[$supplierId]['channel_pid'][$channelPidId]['order_data']                     = array_values($channelPidOrderIds);
					$cacheData[$supplierId]['channel_pid'][$channelPidId]['channel_id'][$order->channel_id] = [];
				}
			}
		}

		// 判断订单状态 -> 成功
		if ($order->status == Order::STATUS_APPROVED) {
			// 更新缓存数据
			if (!empty($cacheData)) {
				$cacheData[$supplierId]['order_data']                                                   = [];
				$cacheData[$supplierId]['channel_pid'][$channelPidId]['order_data']                     = [];
				$cacheData[$supplierId]['channel_pid'][$channelPidId]['channel_id'][$order->channel_id] = [];
			}
		}

		Cache::put(self::$continuousFailAlarmCacheKey . $supplierId, json_encode($cacheData), 24 * 60 * 60 * 15);

		// 判断是否需要进行 交易告警
		if ($isSendAlarmToChannel) {
			$data = [
				'level'             => 1,
				'contents'          =>
					'[交易告警]' . PHP_EOL .
					'故障渠道/PID/账单:' . $sendStr . PHP_EOL .
					'故障详情:' . '连续失败' . $continuousFailNumber . '笔' . PHP_EOL .
					'关联渠道:' . $supplierName . PHP_EOL .
					'关联订单:' . PHP_EOL . implode(PHP_EOL, $orderIds) . PHP_EOL .
					'来源:' . config('app.url'),
				'notice_user_roles' => 'Administrator,Operate Supervisor',
				'type'              => 3,
				'status'            => 2,
			];
			logger()->channel('intercept')->info(
				'连续失败告警',
				['sendData' => $data['contents'], 'time' => time()]
			);
			dispatch(new SendSlsLog(
				['message' => '连续失败告警'],
				['sendData' => $data['contents'], 'time' => time()],
				'info',
				'intercept'
			));
			dispatch(new SendNotice($data, 5));
		}
	}

	/**
	 * @description: 递归判断次数 并且获取对应的订单id
	 * @author: zqc
	 * @date: 2023/10/8
	 **/
	private static function checkContinuousFailAlarm($cacheData, $failNumber = 10)
	{
		if (empty($cacheData) || empty($failNumber)) {
			return [];
		}

		// 递归判断次数
		$returnData = [];
		$cacheNum   = ToolService::countElements($cacheData);
		if ($cacheNum >= $failNumber) {
			foreach ($cacheData as $array) {
				foreach ($array as $value) {
					$returnData[] = $value;
				}
			}
			return $returnData;
		}

		return $returnData;
	}

	/**
	 * @description: 递归删除
	 * @author: zqc
	 * @date: 2023/10/31
	 **/
	private static function unsetInvalidData(&$array, $validArray)
	{
		if (empty($array) || empty($validArray)) {
			return $array;
		}

		foreach ($array as $key => &$value) {
			if (is_array($value)) {
				self::unsetInvalidData($value, $validArray);
			} elseif (in_array($value, $validArray)) {
				unset($array[$key]);
			}
		}
	}

}