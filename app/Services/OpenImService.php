<?php

namespace App\Services;

use App\Classes\Supports\Traits\HasHttpRequest;
use App\Models\OpenImUser;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class OpenImService
{
    use HasHttpRequest;

    protected $config;
    protected $adminToken;

    public function __construct()
    {
        $this->config     = config('open_im');
        $this->adminToken = $this->getAdminUserToken();
    }

    /**
     * 请求方法
     * @param $header
     * @param $url
     * @param $req
     * @param mixed $port
     * @return array
     */
    private function requestMethod($header, $url, $req,$port = ''): array
    {
        if (!$port) {
            $port = $this->config['api_port'];
        }
        $resUrl = config('open_im.api_url') . $port . $url;
        return $this->post($resUrl, [], ['json' => $req, 'headers' => $header]);
    }

    /**
     * 获取管理员用户token
     * @return mixed
     */
    public function getAdminUserToken()
    {
        $key   = 'OPEN_IM_ADMIN_USER_TOKEN';
        $token = Cache::get($key);
        if ($token) {
            return $token;
        }

        $header = [
            'Operationid'  => now()->timestamp,
            'Content-Type' => 'application/json',
        ];

        $data = [
            'secret'     => $this->config['secret'],
            'platformID' => 10,
            'userID'     => $this->config['admin_user_id'],
        ];

        $res = $this->requestMethod($header, '/auth/user_token', $data);
        if ($res['errCode'] !== 0) {
            return null;
        }

        $token = $res['data']['token'];
        Cache::put($key, $token, $res['data']['expireTimeSeconds'] - 5);
        return $token;
    }

    /**
     * 注册用户
     * @param array $openImUser
     * @param int $merchantId
     * @param string $merchantName
     * @return string
     */
    public function register(array $openImUser, int $merchantId = 0, string $merchantName = ''): string
    {
        // 验证是否已经注册
        $imUserInfo = OpenImUser::query()
            ->where('user_id', $openImUser['userId'])
            ->where('user_type', $openImUser['userType'])
            ->first();
        if ($imUserInfo) {
            return $imUserInfo->open_im_id;
        }

        $header = [
            'Operationid'  => now()->timestamp,
            'Content-Type' => 'application/json',
            'Token'        => $this->adminToken,
        ];

        // 手机号码区分系统和商户
        // 根据不同的APP_NAME设置不同的手机号码
        switch (config('app.name')) {
            case 'Embracy':
                $mobileAccessCode = '17';
                break;
            case 'PunctualPay':
                $mobileAccessCode = '18';
                break;
            case 'PeachyPay':
                $mobileAccessCode = '19';
                break;
            default:
                $mobileAccessCode = '16';
                Log::warning('APP_NAME is not set ' . config('app.name'));
                break;
        }

        // 补齐用户id
        $baseStr = $this->padString($openImUser['userId'], 0, 5, true);

        // 补齐手机号码
        $phoneNumber = $this->padString($mobileAccessCode . $openImUser['userType'] . $baseStr, null, 11);

        $data = [
            'platform'   => 5, // 平台ID 1：IOS，2：Android，3：Windows，4：OSX，5：Web，6：MiniWeb，7：Linux，8：Android Pad，9：IPad，10：admin
            'verifyCode' => '666666', // 验证码
            'user'       => [
                "nickname"    => $openImUser['nickname'],
                "faceURL"     => "",
                "birth"       => null,
                "gender"      => 1, // 性别 1：男，2：女
                "areaCode"    => "+86",
                "phoneNumber" => $phoneNumber,
                "password"    => $openImUser['password']
            ],
        ];

        $res = $this->requestMethod($header, '/account/register', $data,$this->config['chat_port']);
        if ($res['errCode'] !== 0) {
            Log::error('register open im user error', $res);
            return '';
        }

        OpenImUser::create([
            'open_im_id'         => $res['data']['userID'],
            'user_id'            => $openImUser['userId'],
            'user_type'          => $openImUser['userType'],
            'phone'              => $phoneNumber,
            'username'           => $phoneNumber,
            'password'           => $openImUser['password'],
            'nickname'           => $openImUser['nickname'],
            'merchant_id'        => $merchantId,
            'merchant_name'      => $merchantName,
            'allow_add_group'    => $openImUser['addGroup'],
            'allow_add_friend'   => $openImUser['addFriend'],
            'allow_create_group' => $openImUser['createGroup'],
            'default_add_group'  => $openImUser['defaultAddGroup'],
            'group_role_level'   => $openImUser['groupRoleLevel'],
            'by_added'           => $openImUser['userName'],
            'by_modified'        => $openImUser['userName'],
        ]);

        return $res['data']['userID'];
    }

    /**
     * 导入好友
     * @param string $ownerId
     * @param string $friendId
     * @return bool
     */
    public function addFriend(string $ownerId, string $friendId): bool
    {
        $header = [
            'Operationid'  => now()->timestamp,
            'Content-Type' => 'application/json',
            'Token'        => $this->adminToken,
        ];

        $data = [
            'ownerUserID'   => $ownerId,
            'friendUserIDs' => [$friendId],
        ];

        $res = $this->requestMethod($header, '/friend/import_friend', $data);
        if ($res['errCode'] !== 0) {
            return false;
        }

        return true;
    }

    /**
     * 创建群组
     * @param string $ownerId
     * @param string $groupName
     * @param array $memberUserIDs
     * @param array $adminUserIDs
     * @return bool
     */
    public function createGroup(string $groupName, array $memberUserIDs, array $adminUserIDs = [], string $ownerId = ''): bool
    {
        if (empty($ownerId)) {
            $ownerId = OpenImUser::query()->select('open_im_id')
                ->where('status',OpenImUser::STATUS_NORMAL)
                ->where('user_type',OpenImUser::USER_TYPE_SYSTEM)
                ->where('group_role_level',OpenImUser::ROLE_LEVEL_OWNER)
                ->where('default_add_group',OpenImUser::DEFAULT_ADD_GROUP)
                ->value('open_im_id');
            if (!$ownerId) {
                return false;
            }
        }

        if (empty($adminUserIDs)) {
            $adminUserIDs = OpenImUser::query()->select('open_im_id')
                ->where('status',OpenImUser::STATUS_NORMAL)
                ->where('user_type',OpenImUser::USER_TYPE_SYSTEM)
                ->where('group_role_level',OpenImUser::ROLE_LEVEL_ADMIN)
                ->where('default_add_group',OpenImUser::DEFAULT_ADD_GROUP)
                ->pluck('open_im_id')
                ->toArray();
        }

        $header = [
            'Operationid'  => now()->timestamp,
            'Content-Type' => 'application/json',
            'Token'        => $this->adminToken,
        ];

        $data = [
            'memberUserIDs' => $memberUserIDs,
            'adminUserIDs'  => $adminUserIDs,
            'ownerUserID'   => $ownerId,
            'groupInfo'     => [
                'groupName'         => $groupName,
                'notification'      => '',
                'introduction'      => '',
                'faceUrl'           => '',
                'ex'                => '',
                'groupType'         => 2, // 群类型	固定为 2
                'needVerification'  => 1, // 进群是否需要验证	0：申请加入群需要同意，成员邀请可直接进群、1：所有人进群需要验证，除了群主管理员邀请进群、2：直接进群
                'lookMemberInfo'    => 0, // 是否能查看其他群成员信息	0：允许查看群成员信息、1：不允许查看群成员信息
                'applyMemberFriend' => 1, // 群成员是否能添加好友	0：允许从群成员处添加好友、1：不允许添加
            ],
        ];

        $res = $this->requestMethod($header, '/group/create_group', $data);
        if ($res['errCode'] !== 0) {
            Log::error('create group error', $res);
            return false;
        }

        return true;
    }

    /**
     * 删除用户所有消息
     * @param string $userId
     * @return bool
     */
    public function deleteAllMsg(string $userId): bool
    {
        $header = [
            'Operationid'  => now()->timestamp,
            'Content-Type' => 'application/json',
            'Token'        => $this->adminToken,
        ];

        $data = [
            'userID' => $userId,
        ];

        $res = $this->requestMethod($header, '/msg/user_clear_all_msg', $data);
        if ($res['errCode'] !== 0) {
            return false;
        }

        return true;
    }

    /**
     * 强制用户下线
     *
     */
    public function forceLogout(string $userId): bool
    {
        $header = [
            'Operationid'  => now()->timestamp,
            'Content-Type' => 'application/json',
            'Token'        => $this->adminToken,
        ];

        $data = [
            'platformID' => 5,
            'userID'     => $userId,
        ];

        $res = $this->requestMethod($header, '/auth/force_logout', $data);
        if ($res['errCode'] !== 0) {
            return false;
        }

        return true;
    }

    /**
     * 填充
     * @param string $input 需要填充的字符串
     * @param int|null $padDigit 指定填充数字
     * @param int $targetLength 目标长度
     * @param bool $padLeft 填充方向
     * @return string
     */
    private function padString(string $input, int $padDigit = null, int $targetLength = 5, bool $padLeft = false): string
    {
        while (strlen($input) < $targetLength) {
            $paddingDigit = $padDigit !== null ? $padDigit : mt_rand(0, 9);
            $input        = $padLeft ? $paddingDigit . $input : $input . $paddingDigit;
        }

        return $input;
    }
}
