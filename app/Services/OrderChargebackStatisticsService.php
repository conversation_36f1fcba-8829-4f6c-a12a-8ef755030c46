<?php


namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;
use App\Models\StatOrderChargeback;
use App\Classes\Supports\Logger;
use App\Jobs\SendSlsLog;
use App\Models\OrderRelation;
use App\Models\Order;
use App\Models\Refund;
use Illuminate\Support\Facades\Cache;

class OrderChargebackStatisticsService
{
    /**
     * @var
     */
    protected $log;

    public function __construct()
    {
        // 初始化日志
        $this->log = new Logger();
        $config    = ['file' => storage_path('logs/consoleTask.log')];
        $this->log->setConfig($config);
    }

    //手动同步
    public function syncStatistics(): bool
    {
        try {
            $status = $this->commonStatistics();
        } catch (\Throwable $th) {
            $this->log->error('手动同步拒付统计异常信息：' . $th->getMessage());
            dispatch(new SendSlsLog(
                ['message' => '手动同步拒付统计异常信息：' . $th->getMessage()],
                [],
                'error',
            ));
            
            $status = false;
        }

        return $status;
    }

    //定时任务同步
    public function timingStatistics($defaultDate = null): bool
    {
        try {
            $status = $this->commonStatistics($defaultDate);
        } catch (\Throwable $th) {
            $this->log->error('定时同步拒付统计异常信息：' . $th->getMessage());
            dispatch(new SendSlsLog(
                ['message' => '定时同步拒付统计异常信息：' . $th->getMessage()],
                [],
                'error',
            ));
            
            $status = false;
        }

        return $status;
    }


    /**
     * 同步零点至当前时间数据
     *
     * @return void
     */
    private function commonStatistics($defaultDate = null): bool
    {
        $lock = Cache::lock('OrderChargebackStatistics_Lock', 10);

        if (!$lock->get()) {
            return false;
        }

        $date             = $defaultDate ? date('Y-m-d', strtotime($defaultDate)) : date('Y-m-d');
        $defaultDateMonth = date('Ym', strtotime($date));
        $timeInterval     = [$date . ' 00:00:00', date('Y-m-d 23:59:59', strtotime($date))];
        $orderList        = [];
        $data             = [];

        //获取拒付记录
        $chargeback = DB::table('chargeback_history')
            ->select('order_id', 'type')
            ->whereBetween('chargeback_at', $timeInterval)
            ->get();

        $chargebackOrderIds = $chargeback->pluck('order_id');
        //获取拒付的订单
        $chargebackOrder = $this->getOrder($chargebackOrderIds);

        $this->getDimensionData($chargebackOrder, $orderList, 'dishonour_qty', $defaultDateMonth);
        $this->getDimensionData($chargebackOrder, $orderList, 'dishonour_qty1');

        //特殊情况统计拒付的订单中是否有已经拒付预警的情况，如果有减去那一天的拒付预警和还原月拒付预警数量
        $correctCaseDate = DB::table('chargeback_cases')->selectRaw('order_id, date(created_at) as correct_date')
            ->whereIn('order_id', $chargebackOrderIds)
            ->whereRaw('date(created_at) <> ' . $date)
            ->get()->pluck('correct_date', 'order_id')->toArray();

        if (count($correctCaseDate)) {
            $correctOrders = $this->getOrder(array_keys($correctCaseDate));
            $this->correctionDishonorCase($correctOrders, $correctCaseDate);
        }

        //获取拒付预警记录
        $chargebackCase = DB::table('chargeback_cases as c')
            ->select('c.order_id as order_id')
            ->join('order_relations as o', 'o.order_id', '=', 'c.order_id')
            ->where('o.is_chargeback', OrderRelation::IS_CHARGEBACK_NOT)
            ->whereBetween('c.created_at', $timeInterval)
            ->get();
        //获取拒付预警的订单
        $chargebackCaseOrder = $this->getOrder($chargebackCase->pluck('order_id'));

        $this->getDimensionData($chargebackCaseOrder, $orderList, 'dishonour_case_qty', $defaultDateMonth);
        $this->getDimensionData($chargebackCaseOrder, $orderList, 'dishonour_case_qty1');

        //获取退款记录
        $refund = DB::table('refunds')
            ->whereIn('status', [Refund::STATUS_APPROVED, Refund::STATUS_REVIEW])
            ->whereBetween('completed_at', $timeInterval)
            ->pluck('refund_id');

        //获取退款的订单
        $refundOrder = $this->getRefundsOrder($refund);

        $this->getDimensionData($refundOrder, $orderList, 'refund_qty', $defaultDateMonth);
        $this->getDimensionData($refundOrder, $orderList, 'refund_qty1');

        //当月交易数量
        $orders = DB::table('orders as o')
            ->leftJoin('order_cards as c', 'o.card_id', '=', 'c.id')
            ->leftJoin('order_settlements as s', 'o.order_id', '=', 's.order_id')
            ->select('o.order_id', 'o.status', 'o.type', 'o.completed_at', 'o.merchant_id', 'o.business_id', 'o.channel_id', 'o.url_id', 'o.d_mcc_id', 'o.currency', 'c.cc_type', 'c.card_country', 'o.is_3d', 's.amount_usd', 'o.card_bill')
            ->where('o.status', '=', Order::STATUS_APPROVED)
            ->whereIn('o.type', [Order::TYPES_SALE, Order::TYPES_CAPTURE])
            ->whereBetween('o.completed_at', $timeInterval)
            ->get();

        $this->getDimensionData($orders, $orderList, 'transaction_qty', $defaultDateMonth);
        //查出已经统计的数据（当天）
        $toDayDataStat   = DB::table('stat_order_chargebacks')->where('date_stat', $date)->get();
        $updateCount     = 0;
        $isUpdate        = false;
        //判断是否需要更新
        if ($toDayDataStat->isNotEmpty()) {
            $isUpdate = true;
        }

        //统计
        foreach ($orderList as $mid => $bList) {
            foreach ($bList as $bid => $channel) {
                foreach ($channel as $channelId => $res) {
                    foreach ($res as $cardBill => $value) {
                        foreach ($value as $url => $merchant) {
                            foreach ($merchant as $dMccId => $payment) {
                                foreach ($payment as $currency => $card) {
                                    foreach ($card as $ccType => $end) {
                                        foreach ($end as $country => $dateMonths) {
                                            foreach ($dateMonths as $dateMonth => $item) {
                                                $usd = $transaction_amount_usd = 0;

                                                foreach ($item['transaction_qty'] ?? [] as $v) {
                                                    if ($v['is_3d']) {
                                                        $usd += $v['amount_usd'];
                                                    }
                                                    $transaction_amount_usd += $v['amount_usd'];
                                                }

                                                $chargebackFraud = 0;
												$chargebackFraudAmount = 0;
												$chargebackAmount = 0;
                                                foreach ($item['dishonour_qty'] ?? [] as $v) {
                                                    if (count($chargeback->where('type', '=', 0)->where('order_id', '=', $v['order_id']))) {
                                                        $chargebackFraud++;
														$chargebackFraudAmount += $v['amount_usd'];
                                                    }
													$chargebackAmount += $v['amount_usd'];

                                                }

                                                $chargebackFraud1 = 0;
                                                foreach ($item['dishonour_qty1'] ?? [] as $v) {
                                                    if (count($chargeback->where('type', '=', 0)->where('order_id', '=', $v['order_id']))) {
                                                        $chargebackFraud1++;
                                                    }
                                                }

                                                $refundAmountUsd = 0;
                                                foreach ($item['refund_qty'] ?? [] as $v) {
                                                    $refundAmountUsd += $v['amount_usd'];
                                                }

                                                $refundAmountUsd1 = 0;
                                                foreach ($item['refund_qty1'] ?? [] as $v) {
                                                    $refundAmountUsd1 += $v['amount_usd'];
                                                }

                                                $judgeData = [
                                                    'dishonour_qty'             => count($item['dishonour_qty'] ?? []),
                                                    'dishonour_qty1'            => count($item['dishonour_qty1'] ?? []),
                                                    'dishonour_case_qty'        => count($item['dishonour_case_qty'] ?? []),
                                                    'dishonour_case_qty1'       => count($item['dishonour_case_qty1'] ?? []),
                                                    'refund_qty'                => count($item['refund_qty'] ?? []),
                                                    'refund_qty1'               => count($item['refund_qty1'] ?? []),
                                                    'fraud_qty'                 => $chargebackFraud,
                                                    'fraud_qty1'                => $chargebackFraud1,
                                                    'transaction_qty'           => count($item['transaction_qty'] ?? []),
                                                    '3d_transaction_amount_usd' => $usd,
                                                    'transaction_amount_usd'    => $transaction_amount_usd,
                                                    'refund_amount_usd'         => $refundAmountUsd,
                                                    'refund_amount_usd1'        => $refundAmountUsd1,
													'fraud_amount_usd'  		=> $chargebackFraudAmount,
													'chargeback_amount_usd'     => $chargebackAmount,
                                                ];

                                                if ($isUpdate) {
                                                    $where = [
                                                        'merchant_id'     => $mid,
                                                        'business_id'     => $bid,
                                                        'channel_id'      => $channelId,
                                                        'card_bill'       => $cardBill,
                                                        'url_id'          => $url,
                                                        'd_mcc_id'        => $dMccId,
                                                        'currency'        => $currency,
                                                        'cc_type'         => $ccType,
                                                        'card_country'    => $country,
                                                        'date_stat'       => $date,
                                                        'date_stat_month' => $dateMonth,
                                                    ];

                                                    $updateResult = $this->updatePreparation($toDayDataStat, $where, $judgeData, $updateCount);

                                                    if ($updateResult) {
                                                        continue;
                                                    }
                                                }

                                                $data[] = [
                                                    'merchant_id'               => $mid,
                                                    'business_id'               => $bid,
                                                    'channel_id'                => $channelId,
                                                    'card_bill'                 => $cardBill,
                                                    'url_id'                    => $url,
                                                    'd_mcc_id'                  => $dMccId,
                                                    'currency'                  => $currency,
                                                    'cc_type'                   => $ccType,
                                                    'card_country'              => $country,
                                                    'dishonour_qty'             => $judgeData['dishonour_qty'],
                                                    'dishonour_qty1'            => $judgeData['dishonour_qty1'],
                                                    'dishonour_case_qty'        => $judgeData['dishonour_case_qty'],
                                                    'dishonour_case_qty1'       => $judgeData['dishonour_case_qty1'],
                                                    'refund_qty'                => $judgeData['refund_qty'],
                                                    'refund_qty1'               => $judgeData['refund_qty1'],
                                                    'fraud_qty'                 => $judgeData['fraud_qty'],
                                                    'fraud_qty1'                => $judgeData['fraud_qty1'],
                                                    'transaction_qty'           => $judgeData['transaction_qty'],
                                                    '3d_transaction_amount_usd' => $judgeData['3d_transaction_amount_usd'],
                                                    'transaction_amount_usd'    => $judgeData['transaction_amount_usd'],
                                                    'refund_amount_usd'         => $judgeData['refund_amount_usd'],
                                                    'refund_amount_usd1'        => $judgeData['refund_amount_usd1'],
													'fraud_amount_usd'  		=> $judgeData['fraud_amount_usd'],
													'chargeback_amount_usd'     => $judgeData['chargeback_amount_usd'],
                                                    'date_stat'                 => $date,
                                                    'date_stat_month'           => $dateMonth,
                                                    'created_at'                => date('Y-m-d H:i:s'),
                                                ];
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        //插入新增数据
        if (count($data)) {
            StatOrderChargeback::insert($data);
        }

        $lock->release();
        $this->log->info(sprintf("同步拒付统计日期：%s 添加总数：%s条 更新总数：%s条", $date, count($data), $updateCount));
        return true;
    }

    /**
     * @param $ids
     * @return Collection
     */
    public function getOrder($ids): Collection
    {
        return DB::table('orders as o')
            ->leftJoin('order_cards as c', 'o.card_id', '=', 'c.id')
            ->leftJoin('order_settlements as s', 'o.order_id', '=', 's.order_id')
            ->select('o.order_id', 'o.status', 'o.type', 'o.completed_at', 'o.merchant_id', 'o.business_id', 'o.channel_id', 'o.url_id', 'o.d_mcc_id', 'o.currency', 'c.cc_type', 'c.card_country', 'o.card_bill', 's.amount_usd')
            ->whereIn('o.order_id', $ids)
            ->get();
    }

    /**
     * @param $ids
     * @return Collection
     */
    public function getRefundsOrder($ids): Collection
    {
        return DB::table('refunds as r')
            ->leftJoin('orders as o', 'r.order_id', '=', 'o.order_id')
            ->leftJoin('order_cards as c', 'o.card_id', '=', 'c.id')
            ->leftJoin('order_settlements as s', 'r.refund_id', '=', 's.order_id')
            ->select('o.order_id', 'o.status', 'o.type', 'o.completed_at', 'o.merchant_id', 'o.business_id', 'o.channel_id', 'o.url_id', 'o.d_mcc_id', 'o.currency', 'c.cc_type', 'c.card_country', 'o.card_bill', 's.amount_usd')
            ->whereIn('r.refund_id', $ids)
            ->get();
    }
    /**
     * @param $approved
     * @param $orderList
     * @param $type
     *
     * 维度数据处理
     */
    public function getDimensionData($approved, &$orderList, $type, $dateMonth = null): void
    {
        foreach ($approved as $value) {
            $value = (array)$value;

            $mid       = $value['merchant_id'];
            $bid       = $value['business_id'];
            $channelId = $value['channel_id'] ?? 0;
            $cardBill  = $value['card_bill'];
            $urlId     = $value['url_id'] ?? 0;
            $dMccId    = $value['d_mcc_id'] ?? 0;
            $currency  = $value['currency'];
            $ccType    = $value['cc_type'];
            $country   = $value['card_country'];

            $correctMonth = $dateMonth ?? date('Ym', strtotime($value['completed_at']));
            $orderList[$mid][$bid][$channelId][$cardBill][$urlId][$dMccId][$currency][$ccType][$country][$correctMonth][$type][] = $value;
        }
    }

    /**
     * 更新今日已经统计过的数据
     *
     * @param Collection $toDayDataStat 今日已统计数据
     * @param array $wheres
     * @param array $judgeData
     * @param [type] $updateCount
     * @return boolean
     */
    public function updatePreparation(Collection $toDayDataStat, array $wheres, array $judgeData, &$updateCount): bool
    {
        $data  = $toDayDataStat;
        foreach ($wheres as $key => $where) {
            $data = $data->where($key, $where);
        }

        $data     = $data->first();
        $isUpdate = false;

        if ($data) {
            foreach ($judgeData as $field => $value) {
                if ($data->{$field} != $value) {
                    $isUpdate = true;
                    break;
                }
            }

            if ($isUpdate) {
                DB::table('stat_order_chargebacks')->where('id', $data->id)->update($judgeData);
                $updateCount++;
            }

            return true;
        }

        return false;
    }


    /**
     * 特殊情况修正以往的拒付预警数据
     * （如先预警，过后又拒付的情况下，要把先前预警的数量和还原月数量减少）
     * @param [type] $caseOrder 拒付预警订单
     * @param array $correctDate 修正日期
     * [
     *   'order_id'=>'date_stat'
     * ]
     * @return void
     */
    public function correctionDishonorCase($caseOrders, array $correctDate): void
    {
        $caseKey        = 'Statistics_Cases';
        $excludeOrderId = Cache::get($caseKey) ?? [];

        foreach ($caseOrders as $order) {
            if (in_array($order->order_id, $excludeOrderId)) {
                continue;
            }

            $value = (array)$order;
            $this->log->info(sprintf("日期：%s 有拒付预警修正情况order_id：%s", $correctDate[$value['order_id']], $value['order_id']));

            $where = [
                'merchant_id'  => $value['merchant_id'],
                'business_id'  => $value['business_id'],
                'channel_id'   => $value['channel_id'],
                'card_bill'    => $value['card_bill'],
                'url_id'       => $value['url_id'],
                'd_mcc_id'     => $value['d_mcc_id'],
                'currency'     => $value['currency'],
                'cc_type'      => $value['cc_type'],
                'card_country' => $value['card_country'],
                'date_stat'    => $correctDate[$value['order_id']],
            ];

            StatOrderChargeback::where($where)
                ->where('date_stat_month', date('Ym', strtotime($correctDate[$value['order_id']])))
                ->where('dishonour_case_qty', '>', 0)
                ->decrement('dishonour_case_qty');

            StatOrderChargeback::where($where)
                ->where('date_stat_month', date('Ym', strtotime($value['completed_at'])))
                ->where('dishonour_case_qty1', '>', 0)
                ->decrement('dishonour_case_qty1');

            $excludeOrderId[] = $order->order_id;
        }

        Cache::put($caseKey, $excludeOrderId, 24 * 60 * 60);
    }
}
