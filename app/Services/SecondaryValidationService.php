<?php

namespace App\Services;

use App\Models\SystemLogin;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Cookie;

class SecondaryValidationService
{
    public static function allowLogin($ip, $systemType = SystemLogin::SYSTEM_TYPE_ADMIN)
    {
        $list = SystemLogin::query()->where('ip', $ip)
            ->where('system_type', $systemType)
            ->where('status', SystemLogin::STATUS_FAILED)
            ->select('created_at')
            ->orderByRaw('created_at desc')->get()->toArray();

        $num = count($list);
        $n   = ceil($num / 5);

        if ($num >= 5 && $num % 5 == 0) {
            $limitationTime = strtotime($list[0]['created_at']) + pow($n, 3) * 60;
            $time           = time();

            if ($time < $limitationTime) {
                $divide = ceil(($limitationTime - $time) / 60);

                return ['status' => false, 'divide' => $divide, 'num' => $num];
            }
        }

        return ['status'=> true, 'num'=> 0, 'divide' => 0];
    }

    public static function secondaryVerify($user, $admin = true)
    {
        $return = '';

        if ($user) {
            if ($admin) {
                $str = [
                    'logout'    => 'logout',
                    'google2fa' => 'google2fa'
                ];
            } else {
                $str = [
                    'logout'    => 'merchantLogout',
                    'google2fa' => 'merchantGoogle2fa'
                ];
            }

            //判断是否登录超时
            $logout = session($str['logout']);

            if ($logout < time()) {
                //退出登录
                $return = 'logout';
            }

            //判断是否要验证谷歌身份验证器
            $key      = md5($str['google2fa'] . $user->id);
            $redisKey = Cookie::get($key);

            //获取缓存
            $row = Cache::get($redisKey);

            if (!$row) {
                $return = 'google2fa';
            }

            //判断是否要修改密码
            if (strtotime($user->password_valid_at) <= time()) {
                $return = 'password';
            }
        }

        return $return;
    }
}
