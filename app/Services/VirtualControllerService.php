<?php


namespace App\Services;

use App\Classes\Card\Exceptions\CardException;
use App\Events\CardBatchAsync;
use App\Events\Virtual;
use App\Models\CardBatch;
use App\Models\CardBin;
use App\Models\CardHolder;
use App\Models\CardTicket;
use App\Models\CardTransaction;
use App\Models\CardVirtual;
use App\Models\DirectoryCurrency;
use App\Models\Merchant;
use App\Models\MerchantApiWebhook;
use App\Models\MerchantApiNoticeTask;
use App\Models\MerchantCard;
use App\Models\MerchantCardTicket;
use App\Models\SettleDetailCard;
use App\Services\Virtual\VirtualServiceFacade;
use App\Jobs\CardBatchSyncApply;
use App\Services\Virtual\PingPongService;
use Illuminate\Support\Facades\DB;
use Dcat\Admin\Form;
use DES3;

class VirtualControllerService
{
    /**
     * 获取虚拟卡详情
     * @param array $request
     * @return array
     */
    //    public static function getCardVirtualDetail(array $request): array
    //    {
    //        $virtualId       = $request['virtual_id'] ?? '';
    //        $merchantId      = $request['mid'];
    //        $resultData      = ['isSuccess' => true, 'message' => '', 'data' => []];
    //        $cardVirtualInfo = CardVirtual::where('merchant_id', $merchantId)
    //            ->where('virtual_id', $virtualId)
    //            ->first();
    //        if (empty($cardVirtualInfo)) {
    //            $resultData['isSuccess'] = false;
    //            $resultData['message']   = admin_trans('card-virtual.fields.virtual_info.card_not_exist');
    //            return $resultData;
    //        }
    //
    //        $returnData['virtual_id']             = $cardVirtualInfo->virtual_id;
    //        $returnData['cards_id']               = $cardVirtualInfo->cards_id;
    //        $returnData['batch_id']               = $cardVirtualInfo->batch_id;
    //        $returnData['card_number']            = DES3::decrypt($cardVirtualInfo->card_number, env('DES3_CARD_VIRTUAL'));
    //        $returnData['cvv']                    = DES3::decrypt($cardVirtualInfo->cvv, env('DES3_CARD_VIRTUAL'));
    //        $returnData['expiration_year']        = DES3::decrypt($cardVirtualInfo->expiration_year, env('DES3_CARD_VIRTUAL'));
    //        $returnData['expiration_month']       = DES3::decrypt($cardVirtualInfo->expiration_month, env('DES3_CARD_VIRTUAL'));
    //        $returnData['created_at']             = !empty($cardVirtualInfo->created_at) ? $cardVirtualInfo->created_at->toDateTimeString() : '';
    //        $returnData['card_type']              = CardVirtual::$cardTypeApiMap[$cardVirtualInfo->card_type];
    //        $returnData['status']                 = CardVirtual::$internalMerchantStatusMapToEn[$cardVirtualInfo->status];
    //        $returnData['allow_card_out']         = $cardVirtualInfo->allow_card_out == CardVirtual::ALLOW_CARD_OUT_ALLOW ? true : false;
    //        $returnData['is_transaction']         = $cardVirtualInfo->is_transaction == CardVirtual::IS_TRANSACTION_ALLOW ? true : false;
    //        $returnData['apply_for_cancellation'] = $cardVirtualInfo->date_apply_cancel ? true : false; // 申请销卡
    //
    //        if ($cardVirtualInfo->card_type == CardVirtual::SHARED_CARD) {
    //            $returnData['day_amount_limit'] = $cardVirtualInfo->day_amount_limit; // 交易限额
    //        }
    //
    //        $resultData['data'] = $returnData;
    //        return $resultData;
    //    }

    /**
     * 获取虚拟卡卡交易信息
     * @param array $request
     * @return array
     */
    public static function getCardTransactions(array $request): array
    {
        $page       = $request['start_page'] ?? 1;
        $pageSize   = $request['row'] ?? 20;
        $resultData = ['isSuccess' => true, 'message' => '', 'data' => []];
        $whereData  = ['merchant_id' => $request['mid']];

        if (isset($request['virtual_id'])) {
            $whereData['virtual_id'] = $request['virtual_id'];
        }

        if (isset($request['start_date'])) {
            $whereData[] = ['date_comtplete', '>=', date('Y-m-d H:i:s', $request['start_date'])];
        }

        if (isset($request['end_date'])) {
            $whereData[] = ['date_comtplete', '<=', date('Y-m-d H:i:s', $request['end_date'])];
        }

        $cardTransactionInfo = CardTransaction::where($whereData)->orderBy('id', 'desc')->paginate($pageSize, ['*'], 'page', $page);
        $returnData          = [];
        foreach ($cardTransactionInfo as $key => $value) {
            $returnData[$key]['authorization_id']   = $value->unique_id;
            $returnData[$key]['virtual_id']         = $value->virtual_id;
            $returnData[$key]['card_number']        = get_markcard(DES3::decrypt($value->card_number, env('DES3_CARD_VIRTUAL')));
            $returnData[$key]['amount']             = $value->amount;
            $returnData[$key]['currency']           = $value->currency;
            $returnData[$key]['transaction_type']   = CardTransaction::$transactionTypeMapToEn[$value->transaction_type];
            $returnData[$key]['transaction_status'] = CardTransaction::$transactionStatusMapEn[$value->transaction_status];
            $returnData[$key]['fail_reason']        = $value->fail_reason;
            $returnData[$key]['authorization_time'] = $value->date_comtplete;  // 授权时间
        }

        $resultData['data'] = [
            'total' => $cardTransactionInfo->total(),
            'list'  => $returnData,
        ];
        return $resultData;
    }

    /**
     * 撤销销卡
     * @param array $request
     * @return array
     */
    public static function cardRevoke(array $request): array
    {
        $resultData  = ['isSuccess' => true, 'message' => '', 'data' => []];
        $virtualId   = $request['virtual_id'] ?? '';
        $merchantId  = $request['mid'];
        $virtualInfo = CardVirtual::where('merchant_id', $merchantId)->find($virtualId);

        if (empty($virtualInfo)) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('card-virtual.fields.virtual_info.card_not_exist');
            return $resultData;
        }

        if ($virtualInfo->status != CardVirtual::DESTROYPENDING) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('card-virtual.fields.virtual_info.irrevocable');
            return $resultData;
        }

        $virtualInfo->status = CardVirtual::ACTIVATION;
        $save                = $virtualInfo->save();
        if (!$save) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('card-virtual.labels.撤回') . trans('admin.failed');
            return $resultData;
        }

        $resultData['message'] = admin_trans('card-virtual.labels.撤回') . trans('admin.succeeded');
        return $resultData;
    }

    /**
     * 销卡
     * @param array $request
     * @return array
     */
    public static function cardDestroy(array $request): array
    {
        $resultData  = ['isSuccess' => true, 'message' => '', 'data' => []];
        $virtualId   = $request['virtual_id'] ?? '';
        $merchantId  = $request['mid'];
        $virtualInfo = CardVirtual::where('merchant_id', $merchantId)->find($virtualId);

        if (empty($virtualInfo)) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('card-virtual.fields.virtual_info.card_not_exist');
            return $resultData;
        }

        if ($virtualInfo->status != CardVirtual::ACTIVATION) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('card-virtual.fields.virtual_info.card_status_unavailable');
            return $resultData;
        }

        $virtualInfo->date_apply_cancel = now();
        $virtualInfo->status            = CardVirtual::DESTROYPENDING;
        $res                            = $virtualInfo->save();
        if (!$res) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('card-virtual.labels.提交销卡审核') . trans('admin.failed');
            return $resultData;
        }

        $resultData['message'] = trans('admin.submit') . trans('admin.succeeded') . ',' . admin_trans('card-virtual.labels.待审核');
        return $resultData;
    }

    /**
     * 冻结
     * @param array $request
     * @return array
     */
    public static function cardBlock(array $request): array
    {
        $resultData  = ['isSuccess' => true, 'message' => '', 'data' => []];
        $virtualId   = $request['virtual_id'] ?? '';
        $blockType   = $request['block_type'] ?? CardVirtual::MERCHANT_BLOCK;
        $merchantId  = $request['mid'];
        $virtualInfo = CardVirtual::where('merchant_id', $merchantId)->with('cardBin.CardBinSupplier')->find($virtualId);

        if (empty($virtualInfo)) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('card-virtual.fields.virtual_info.card_not_exist');
            return $resultData;
        }

        if ($virtualInfo->status != CardVirtual::ACTIVATION) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('card-virtual.fields.virtual_info.card_status_unavailable');
            return $resultData;
        }

        // 请求渠道冻结
        $res = VirtualServiceFacade::getService($virtualInfo->cardBin->CardBinSupplier->file_name)->setGatewayConfig($virtualInfo->cardBin->config)->block($virtualInfo->toArray(), $blockType);

        if (!$res) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('card-virtual.labels.冻结卡失败');
            return $resultData;
        }

        $resultData['message'] = trans('admin.succeeded');
        return $resultData;
    }

    /**
     * 解冻
     * @param array $request
     * @return array
     */
    public static function cardUnBlock(array $request): array
    {
        $resultData  = ['isSuccess' => true, 'message' => '', 'data' => []];
        $virtualId   = $request['virtual_id'] ?? '';
        $blockType   = $request['block_type'] ?? CardVirtual::MERCHANT_BLOCK;
        $merchantId  = $request['mid'];
        $virtualInfo = CardVirtual::where('merchant_id', $merchantId)->with('cardBin.CardBinSupplier')->find($virtualId);

        if (empty($virtualInfo)) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('card-virtual.fields.virtual_info.card_not_exist');
            return $resultData;
        }

        if (in_array($virtualInfo->status, [CardVirtual::MERCHANT_BLOCK, CardVirtual::SYSTEM_BLOCK])) {
            // 系统冻结
            if ($virtualInfo->status == CardVirtual::SYSTEM_BLOCK && $blockType != CardVirtual::SYSTEM_BLOCK) {
                $resultData['isSuccess'] = false;
                $resultData['message']   = admin_trans('card-virtual.fields.virtual_info.card_status_please_contact');
                return $resultData;
            }
            // 请求渠道解冻
            $res = VirtualServiceFacade::getService($virtualInfo->cardBin->CardBinSupplier->file_name)->setGatewayConfig($virtualInfo->cardBin->config)->unblock($virtualInfo->toArray(), $blockType);

            if (!$res) {
                $resultData['isSuccess'] = false;
                $resultData['message']   = admin_trans('card-virtual.labels.冻结卡失败');
                return $resultData;
            }

            $resultData['message'] = trans('admin.succeeded');
            return $resultData;
        }

        $resultData['isSuccess'] = false;
        $resultData['message']   = admin_trans('card-virtual.fields.virtual_info.card_status_unavailable');
        return $resultData;
    }

    /**
     * 虚拟卡额度限制
     * @param array $request
     * @return array
     */
    public static function cardLimitAmount(array $request): array
    {
        $virtualId   = $request['virtual_id'];
        $amount      = $request['amount'] ?? '';
        $merchantId  = $request['mid'];
        $resultData  = ['isSuccess' => true, 'message' => '请求成功', 'data' => []];
        $virtualInfo = CardVirtual::where('merchant_id', $merchantId)->find($virtualId);

        if (empty($virtualInfo)) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('card-virtual.fields.virtual_info.card_not_exist');
            return $resultData;
        }

        if ($virtualInfo->card_type != CardVirtual::SHARED_CARD) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('card-virtual.fields.virtual_info.shared_card_limit');
            return $resultData;
        }

        if ($amount < 100) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('card-virtual.fields.virtual_info.shared_card_low_info');
            return $resultData;
        }

        $bin     = CardBin::with('CardBinSupplier')->find($virtualInfo->card_bin_id);
        $service = VirtualServiceFacade::getService($bin->CardBinSupplier->file_name)->setGatewayConfig($bin->config);
        $service->shareCardLimit([
            'card_type'        => $virtualInfo->card_type,
            'trans_id'         => $virtualInfo->trans_id,
            'day_amount_limit' => $amount,
        ]);

        if (!CardVirtual::where('virtual_id', $virtualInfo->virtual_id)->where('day_amount_limit', $amount)->exists()) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('cards.fields.operation_failed');
            return $resultData;
        }


        return $resultData;
    }

    /**
     * 虚拟卡余额查询
     * @param array $request
     * @return array
     */
    public static function cardBalanceCheck(array $request): array
    {
        $virtualId   = $request['virtual_id'];
        $merchantId  = $request['mid'];
        $resultData  = ['isSuccess' => true, 'message' => '', 'data' => []];
        $virtualInfo = CardVirtual::where('merchant_id', $merchantId)->find($virtualId);

        if (empty($virtualInfo)) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('card-virtual.fields.virtual_info.card_not_exist');
            return $resultData;
        }

        if ($virtualInfo->status != CardVirtual::ACTIVATION) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('card-virtual.fields.virtual_info.card_not_activated');
            return $resultData;
        }

        $bin = CardBin::with('CardBinSupplier')->find($virtualInfo->card_bin_id);
        // 请求渠道
        $cardApplyService = VirtualServiceFacade::getService($bin->CardBinSupplier->file_name)->setGatewayConfig($bin->config);
        if ($virtualInfo->card_type) { //共享卡
            $res = $cardApplyService->inquireShareBalance($virtualInfo->toArray());
        } else {
            $res = $cardApplyService->inquireBalance($virtualInfo->toArray());
        }

        if (isset($res['code']) && $res['code'] == '200') {
            if ($virtualInfo->card_type) {
                // 共享卡更新到卡余额
                $virtualInfo->available_balance = $res['balance'] ?? 0;
                $virtualInfo->date_api_balance  = now();
                $virtualInfo->save();
            }

            $resultData['isSuccess'] = true;
            $resultData['data']      = [
                'card_number'       => get_markcard(DES3::decrypt($virtualInfo->card_number, env('DES3_CARD_VIRTUAL'))),
                'available_balance' => $virtualInfo->available_balance,
            ];
            $resultData['message']   = admin_trans('card-virtual.labels.查询') . trans('admin.succeeded');
        } else {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('card-virtual.labels.查询') . trans('admin.failed');
        }

        return $resultData;
    }

    /**
     * 获取卡订单(充值/退值)信息
     * @param array $request
     * @return array
     */
    public static function getCardRechargeInfo(array $request): array
    {
        $merchantId = $request['mid'];
        $page       = $request['start_page'] ?? 1;
        $pageSize   = $request['row'] ?? 20;
        $resultData = ['isSuccess' => true, 'message' => '', 'data' => []];

        $whereData = [
            'merchant_id' => $merchantId,
        ];
        // 可选条件
        if (isset($request['unique_order_id'])) {
            $whereData['unique_order_id'] = $request['unique_order_id'];
        }

        if (isset($request['virtual_id'])) {
            $whereData['virtual_id'] = $request['virtual_id'];
        }

        if (isset($request['status'])) {
            [$IsPass, $value] = self::verificationPass($request['status'], CardTicket::$statusMapToEn);
            if ($IsPass) {
                $whereData['status'] = $value;
            }
        }

        if (isset($request['type'])) {
            [$IsPass, $value] = self::verificationPass($request['type'], CardTicket::$apiTypeMapToEn);
            if ($IsPass) {
                $whereData['type'] = $value;
            }
        }

        // 只允许查询转入和转出
        $cardTicket = CardTicket::where($whereData)->whereIn('status', [CardTicket::TYPE_RECHARGE, CardTicket::TYPE_CHARGE_OUT])->orderBy('id', 'desc')->paginate($pageSize, ['*'], 'page', $page);
        if (empty($cardTicket)) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('cards.fields.data_not_exist');
            return $resultData;
        }

        $returnData = [];
        foreach ($cardTicket as $key => $value) {
            $returnData[$key]['unique_id']       = $value->unique_id;
            $returnData[$key]['unique_order_id'] = $value->unique_order_id;
            $returnData[$key]['virtual_id']      = $value->virtual_id;
            $returnData[$key]['cards_id']        = $value->cards_id;
            $returnData[$key]['card_number']     = get_markcard(DES3::decrypt($value->card_number, env('DES3_CARD_VIRTUAL')));
            $returnData[$key]['amount']          = $value->amount;
            $returnData[$key]['currency']        = $value->currency;
            $returnData[$key]['status']          = CardTicket::$statusMapToEn[$value->status];
            $returnData[$key]['type']            = CardTicket::$apiTypeMapToEn[$value->type];
        }

        $resultData['data'] = [
            'total' => $cardTicket->total(),
            'list'  => $returnData,
        ];
        return $resultData;
    }

    /**
     * 虚拟卡退值
     * @param array $request
     * @return array
     * @throws \Exception
     */
    public static function cardVirtualRefund(array $request): array
    {
        $uniqueId      = CardTicket::getUniqueId();
        $amount        = $request['amount'];
        $virtualId     = $request['virtual_id'] ?? '';
        $uniqueOrderId = $request['unique_order_id'] ?? $uniqueId;
        $merchantId    = $request['mid'];
        $resultData    = ['isSuccess' => true, 'message' => '', 'data' => []];
        $virtualInfo   = CardVirtual::where('merchant_id', $merchantId)->find($virtualId);

        if (empty($virtualInfo)) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('card-virtual.fields.virtual_info.card_not_exist');
            return $resultData;
        }

        if ($virtualInfo->status != CardVirtual::ACTIVATION) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('card-virtual.fields.virtual_info.card_not_activated');
            return $resultData;
        }

        if (!$virtualInfo->allow_card_out) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('card-virtual.fields.virtual_info.card_ban_transferred_out');
            return $resultData;
        }

        if ($virtualInfo->card_type) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('card-virtual.fields.virtual_info.shared_card_not_refunded');
            return $resultData;
        }

        if (CardTicket::where('merchant_id', $virtualInfo->merchant_id)->where('unique_order_id', $uniqueOrderId)->exists()) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('cards.fields.not_repeatable');
            return $resultData;
        }

        $cidInfo = MerchantCard::find($virtualInfo->cards_id);
        if ($cidInfo->status != 1) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = 'CID ' . admin_trans('card-virtual.fields.virtual_info.not_available');
            return $resultData;
        }

        $bin = CardBin::with('CardBinSupplier')->find($virtualInfo->card_bin_id);
        if ($bin->status != 1) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = 'BIN' . admin_trans('card-virtual.fields.virtual_info.not_available');
            return $resultData;
        }

        $cardTicketData = [
            'unique_id'       => $uniqueId,
            'unique_order_id' => $uniqueOrderId,
            'merchant_id'     => $virtualInfo->merchant_id,
            'merchant_name'   => $virtualInfo->merchant_name,
            'cards_id'        => $virtualInfo->cards_id,
            'card_bin_id'     => $virtualInfo->card_bin_id,
            'bin'             => $virtualInfo->bin,
            'bin_name'        => $virtualInfo->bin_name,
            'virtual_id'      => $virtualInfo->virtual_id,
            'card_number'     => $virtualInfo->card_number,
            'amount'          => $amount,
            'currency'        => 'USD',
            'type'            => 1,
            'originator'      => 1,
            'status'          => 2, // 待处理
            'remarks'         => '',
        ];

        $cardTicket = CardTicket::create($cardTicketData);
        if (!$cardTicket) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('card-virtual.fields.virtual_info.failed_to_create_retreat');
            return $resultData;
        }

        $cardTicketData['trans_id'] = $virtualInfo->trans_id;
        // 请求渠道退值
        $result = VirtualServiceFacade::getService($bin->CardBinSupplier->file_name)->setGatewayConfig($bin->config)->refund($cardTicketData);
        if ($result['code'] != 200) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('card-virtual.labels.退值') . trans('admin.failed');
            return $resultData;
        }

        $resultData['data']    = [
            'unique_id' => $uniqueOrderId,
        ];
        $resultData['message'] = admin_trans('card-virtual.labels.退值') . trans('admin.succeeded');
        return $resultData;
    }

    /**
     * 虚拟卡充值
     * @param array $request
     * @return array
     * @throws \Exception
     */
    public static function cardVirtualRecharge(array $request): array
    {
        $uniqueId      = CardTicket::getUniqueId();
        $amount        = $request['amount'] ?? '';
        $virtualId     = $request['virtual_id'] ?? '';
        $uniqueOrderId = $request['unique_order_id'] ?? $uniqueId;
        $merchantId    = $request['mid'];
        $resultData    = ['isSuccess' => true, 'message' => '', 'data' => []];
        $virtualInfo   = CardVirtual::where('merchant_id', $merchantId)->find($virtualId);

        if (empty($virtualInfo)) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('card-virtual.fields.virtual_info.card_not_exist');
            return $resultData;
        }

        if ($virtualInfo->status != CardVirtual::ACTIVATION) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('card-virtual.labels.卡') . admin_trans('card-virtual.fields.virtual_info.not_available');
            return $resultData;
        }

        if ($virtualInfo->card_type) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('card-virtual.fields.virtual_info.shared_card_not_refunded');
            return $resultData;
        }

        if ($amount <= 0) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('card-virtual.labels.recharge_tip');
            return $resultData;
        }

        $cidInfo = MerchantCard::find($virtualInfo->cards_id);
        if ($cidInfo->status != 1) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = 'CID ' . admin_trans('card-virtual.fields.virtual_info.not_available');
            return $resultData;
        }

        $bin = CardBin::with('CardBinSupplier')->find($virtualInfo->card_bin_id);
        if ($bin->status != 1) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = 'BIN' . admin_trans('card-virtual.fields.virtual_info.not_available');
            return $resultData;
        }

        if (CardTicket::where('merchant_id', $virtualInfo->merchant_id)->where('unique_order_id', $uniqueOrderId)->exists()) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('cards.fields.not_repeatable');
            return $resultData;
        }

        $balance = MerchantCard::getAvailableBalance($virtualInfo->cards_id);
        if ($balance < $amount) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('card-virtual.fields.virtual_info.balance_low');
            return $resultData;
        }

        // 开启事务
        DB::beginTransaction();
        //充值表
        $cardTicketData = [
            'unique_id'       => $uniqueId,
            'unique_order_id' => $uniqueOrderId,
            'merchant_id'     => $virtualInfo->merchant_id,
            'merchant_name'   => $virtualInfo->merchant_name,
            'cards_id'        => $virtualInfo->cards_id,
            'card_bin_id'     => $virtualInfo->card_bin_id,
            'bin'             => $virtualInfo->bin,
            'bin_name'        => $virtualInfo->bin_name,
            'virtual_id'      => $virtualInfo->virtual_id,
            'card_number'     => $virtualInfo->card_number,
            'amount'          => $amount,
            'currency'        => 'USD',
            'type'            => 0,
            'originator'      => 1,
            'status'          => CardTicket::STATUS_PENDING, // 待处理
            'remarks'         => '',
        ];

        $cardTicket = CardTicket::create($cardTicketData);
        if (!$cardTicket) {
            DB::rollBack();
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('card-virtual.fields.virtual_info.failed_to_create');
            return $resultData;
        }

        // 更新cid余额
        DB::update("UPDATE merchant_cards SET `balance` = `balance` - '{$amount}' WHERE `cards_id` = '{$cidInfo->cards_id}'");
        // 添加cid结算明细
        SettleDetailCard::create([
            'unique_id'     => $cardTicketData['unique_id'],
            'cards_id'      => $cardTicketData['cards_id'],
            'merchant_id'   => $cardTicketData['merchant_id'],
            'merchant_name' => $cardTicketData['merchant_name'],
            'virtual_id'    => $virtualInfo->virtual_id,
            'card_number'   => $virtualInfo->card_number,
            'amount'        => bcmul(-1, $amount, 2),
            'type'          => SettleDetailCard::SETTLE_DETAIL_CARDS_TYPE_00,
            'settle_at'     => get_settle_date(),
            'balance'       => $cidInfo->balance, // 上次余额，充值前余额
        ]);

        // 事务提交前再次确认余额 不会小于0
        $balance = MerchantCard::getAvailableBalance($virtualInfo->cards_id);
        if ($balance < 0) {
            DB::rollBack();
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('card-virtual.fields.virtual_info.balance_low') . '！';
            return $resultData;
        }

        DB::commit();
        // 提交渠道进行充值
        $cardTicketData['trans_id'] = $virtualInfo->trans_id;
        $result                     = VirtualServiceFacade::getService($bin->CardBinSupplier->file_name)->setGatewayConfig($bin->config)->recharge($cardTicketData);
        if ($result['code'] != 200) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('card-virtual.labels.提交充值') . trans('admin.failed');
            return $resultData;
        }

        $resultData['data']    = [
            'unique_id' => $uniqueId,
        ];
        $resultData['message'] = admin_trans('card-virtual.labels.提交充值') . trans('admin.succeeded');
        return $resultData;
    }

    /**
     * 获取虚拟卡信息
     * @param array $request
     * @return array
     */
    public static function getCardVirtualInfo(array $request): array
    {
        $merchantId = $request['mid'];
        $page       = $request['start_page'] ?? 1;
        $pageSize   = $request['row'] ?? 20;
        $resultData = ['isSuccess' => true, 'message' => '', 'data' => []];
        $whereData  = [
            'merchant_id' => $merchantId,
        ];
        // 可选条件
        if (isset($request['cards_id'])) {
            $whereData['cards_id'] = $request['cards_id'];
        }

        if (isset($request['batch_id'])) {
            $whereData['batch_id'] = $request['batch_id'];
        }

        if (isset($request['virtual_id'])) {
            $whereData['virtual_id'] = $request['virtual_id'];
        }

        $cardVirtualInfo = CardVirtual::where($whereData)->orderBy('created_at', 'desc')->paginate($pageSize, ['*'], 'page', $page);
        if (empty($cardVirtualInfo)) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('card-virtual.fields.virtual_info.card_not_exist');
            return $resultData;
        }

        $returnData = [];
        foreach ($cardVirtualInfo as $key => $value) {
            $returnData[$key]['virtual_id']             = $value->virtual_id;
            $returnData[$key]['cards_id']               = $value->cards_id;
            $returnData[$key]['bin']                    = $value->bin;
            $returnData[$key]['batch_id']               = $value->batch_id;
            $returnData[$key]['card_type']              = CardVirtual::$cardTypeApiMap[$value->card_type];
            $returnData[$key]['card_number']            = !empty($value->card_number) ? DES3::decrypt($value->card_number, env('DES3_CARD_VIRTUAL')) : '';
            $returnData[$key]['cvv']                    = !empty($value->cvv) ? DES3::decrypt($value->cvv, env('DES3_CARD_VIRTUAL')) : '';
            $returnData[$key]['expiration_year']        = !empty($value->expiration_year) ? DES3::decrypt($value->expiration_year, env('DES3_CARD_VIRTUAL')) : '';
            $returnData[$key]['expiration_month']       = !empty($value->expiration_month) ? DES3::decrypt($value->expiration_month, env('DES3_CARD_VIRTUAL')) : '';
            $returnData[$key]['allow_card_out']         = $value->allow_card_out == CardVirtual::ALLOW_CARD_OUT_ALLOW ? true : false;
            $returnData[$key]['is_transaction']         = $value->is_transaction == CardVirtual::IS_TRANSACTION_ALLOW ? true : false;
            $returnData[$key]['apply_for_cancellation'] = $value->date_apply_cancel ? true : false; // 申请销卡
            $returnData[$key]['status']                 = CardVirtual::$internalMerchantStatusMapToEn[$value->status];
            $returnData[$key]['created_at']             = !empty($value->created_at) ? $value->created_at->toDateTimeString() : '';


            if ($value->card_type == CardVirtual::SHARED_CARD) {
                $returnData[$key]['day_amount_limit'] = $value->day_amount_limit; // 交易限额
            }
        }

        $resultData['data'] = [
            'total' => $cardVirtualInfo->total(),
            'list'  => $returnData,
        ];

        return $resultData;
    }

    /**
     * 处理卡批量异步申请（囤卡申请逻辑）
     *
     * @param array $cardBatchData
     * @param array $virtualIdInfo （可选）
     * @return array 返回处理结果的数组
     * @throws CardException
     */
    public static function HandleCardBatchAsyncApply(array $cardBatchData, array $virtualIdInfo = []): array
    {
        $resultData  = ['isSuccess' => false, 'successNum' => 0];
        $detailsData = [];
        $totalFee    = 0.00;
        // 根据批次号 获取 cards_id、merchant_id
        $merchantCard = MerchantCard::select('cards_id', 'card_bin_id', 'merchant_name', 'merchant_id', 'bin', 'opening_fee', 'balance')
            ->where('cards_id', $cardBatchData['cards_id'])
            ->where('merchant_id', $cardBatchData['merchant_id'])
            ->first();

        // 获取可以需要进行开卡的数量
        $cardVirtualQuery = CardVirtual::where('cards_id', '')
            ->where('status', CardVirtual::ACTIVATION)
            ->where('bin', $merchantCard->bin)
            ->limit($cardBatchData['quantity']);

        if (!empty($virtualIdInfo)) {
            $cardVirtualQuery->whereIn('virtual_id', $virtualIdInfo);
        }

        // 判断使用虚拟卡
        $cardVirtual = $cardVirtualQuery->get();
        $virtualIds  = $cardVirtual->pluck('virtual_id');
        // 收取开卡费
        $openFee = $merchantCard->opening_fee;
        if ($openFee != 0.00) {
            // 开卡费结算
            foreach ($cardVirtual as $value) {
                $detailsData[] = [
                    'unique_id'     => CardTicket::getUniqueId(),
                    'cards_id'      => $cardBatchData['cards_id'],
                    'merchant_id'   => $merchantCard->merchant_id,
                    'merchant_name' => $merchantCard->merchant_name,
                    'virtual_id'    => $value->virtual_id,
                    'card_number'   => $value->card_number,
                    'amount'        => bcmul(-1, $openFee, 2),
                    'type'          => SettleDetailCard::SETTLE_DETAIL_CARDS_TYPE_20,
                    'settle_at'     => get_settle_date(),
                    'balance'       => $merchantCard->balance,
                    'created_at'    => now(),
                ];
                // 费用汇总
                $totalFee = bcadd($totalFee, $openFee, 2);
            }
        }

        DB::beginTransaction();
        if (!empty($detailsData)) {
            $cardId          = $merchantCard->cards_id;
            $merchantCardRes = MerchantCard::where('cards_id', $cardId)
                ->update(['balance' => $merchantCard->balance - $totalFee]);
            if (!$merchantCardRes) {
                DB::rollBack();
                return $resultData;
            }

            $settleDetailCardRes = SettleDetailCard::insert($detailsData);
            if (!$settleDetailCardRes) {
                DB::rollBack();
                return $resultData;
            }
        }

        $newTime = now();
        // 将商户信息更新回虚拟卡表里
        $cardVirtualRes = CardVirtual::whereIn('virtual_id', $virtualIds)->update([
            'merchant_id'   => $merchantCard->merchant_id,
            'merchant_name' => $merchantCard->merchant_name,
            'cards_id'      => $merchantCard->cards_id,
            'batch_id'      => $cardBatchData['batch_id'],
            'created_at'    => $newTime,
            'updated_at'    => $newTime,
        ]);
        if (!$cardVirtualRes) {
            DB::rollBack();
            return $resultData;
        }

        $cardBinRes = CardBatch::where('batch_id', $cardBatchData['batch_id'])->update([
            'status'          => CardBatch::PROCESS_SUCCESS,
            'success_number'  => count($virtualIds),
            'processing_time' => date('Y-m-d H:i:s'),
        ]);

        if (!$cardBinRes) {
            DB::rollBack();
            return $resultData;
        }

        $resultData = ['isSuccess' => true, 'successNum' => $cardVirtualRes];
        DB::commit();

//        self::BatchAsyncFollowUp($cardBatchData, $cardVirtual);
        event(new CardBatchAsync($cardBatchData, $cardVirtual));
        return $resultData;
    }

    /**
     * 处理异步卡批次后续步骤
     *
     * @param array $virtualIds
     * @return array 返回处理结果的数组
     * @throws CardException
     */
//    public static function BatchAsyncFollowUp(array $cardBatchData, $cardVirtual): void
//    {
//        // 设置卡日限额
//        $bin = CardBin::with('CardBinSupplier')->find($cardBatchData['card_bin_id']);
//
//        if ($bin->card_type == CardVirtual::SHARED_CARD) {
//            $service = VirtualServiceFacade::getService($bin->CardBinSupplier->file_name)->setGatewayConfig($bin->config);
//            foreach ($cardVirtual as $virtualInfo) {
//                $service->shareCardLimit([
//                    'card_type'        => $cardBatchData['card_type'],
//                    'trans_id'         => $virtualInfo->trans_id,
//                    'day_amount_limit' => $cardBatchData['day_amount_limit'],
//                ]);
//            }
//        }
//
//        if (!empty($cardBatchData['cardholder_ids'])) {
//            dispatch(new CardBindCardholders($cardBatchData, $cardVirtual->pluck('trans_id')));
//        }
//    }

    /**
     * 处理同步卡批次申请（不囤卡申请逻辑）
     *
     * @param array $cardBatchData
     * @return array 返回处理结果的数组
     * @throws CardException
     */
    public static function HandleCardBatchSyncApply(array $cardBatchData): void
    {
        dispatch(new CardBatchSyncApply($cardBatchData));
    }

    /**
     * 批量开卡验证
     * @param Form $form
     * @return array
     */
    public static function HandleCardBatchApplyVerify(Form &$form): array
    {
        $resultData    = ['isSuccess' => true, 'message' => ''];
        $initialAmount = 0.00;
        $cidData       = MerchantCard::where('cards_id', $form->cards_id)->where('merchant_id', $form->merchant_id)->first();

        if (empty($cidData)) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = 'CID ' . admin_trans('card-virtual.fields.virtual_info.not_available');
            return $resultData;
        }

        //验证批次提交
        if ($cidData->status != 1) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = 'CID ' . admin_trans('card-virtual.fields.virtual_info.status_unavailable');
            return $resultData;
        }

        if ($cidData->card_limit && $cidData->number_cards >= 0 && $cidData->number_cards < $form->quantity) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('card-virtual.fields.virtual_info.opened_most') . $cidData->number_cards . admin_trans('card-virtual.fields.virtual_info.card') . '。';
            return $resultData;
        }

        if ($cidData->pause_card_apply) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = 'CID ' . admin_trans('card-virtual.fields.virtual_info.stopped_card_info') . '。';
            return $resultData;
        }

        $cardBin = CardBin::where('id', $cidData->card_bin_id)->with(['CardBinClass', 'CardBinSupplier'])->first();

        if (empty($cardBin) || !$cardBin->status || empty($cardBin->CardBinClass) || !$cardBin->CardBinClass->status) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('card-virtual.fields.virtual_info.card_bin_unavailable');
            return $resultData;
        }

        // 不使用默认持卡人则要校验传入的持卡人数据
        if ($form->is_use_default_cardholder == CardBatch::DEFAULT_CARDHOLDER_NO) {
            $cardholderIds   = array_filter(explode(',', $form->cardholder_ids), function ($value) {
                return ($value !== null && $value !== "");
            });
            $cardholderCount = count($cardholderIds);
            // 如果是pp渠道 且没有选择持卡人 则不需要校验持卡人
            if (!($cardBin->CardBinSupplier->file_name == PingPongService::CHANNEL_SERVICE_CODE && $cardholderCount == 0)) {
                if ($cardholderCount == 0 || $cardholderCount != $form->quantity) {
                    $resultData['isSuccess'] = false;
                    $resultData['message']   = admin_trans('card-batch.labels.card_holders_count_error') . '。';
                    return $resultData;
                }

                if ($cardholderCount != CardHolder::where('merchant_id', $form->merchant_id)->where('status', CardHolder::CARD_HOLDER_STATUS_ENABLE)->where('audit_status', CardHolder::CARD_HOLDER_AUDIT_STATUS_PASS)->whereIn('id', $cardholderIds)->count()) {
                    $resultData['isSuccess'] = false;
                    $resultData['message']   = admin_trans('card-batch.labels.card_holders_error') . '。';
                    return $resultData;
                };
            }
        }

        if ($form->is_use_default_cardholder == CardBatch::DEFAULT_CARDHOLDER_YES) {
            $defaultCardholder = CardHolder::where('merchant_id', $form->merchant_id)->where('status', CardHolder::CARD_HOLDER_STATUS_ENABLE)->where('audit_status', CardHolder::CARD_HOLDER_AUDIT_STATUS_PASS)->where('is_default_cardholder', CardHolder::CARD_HOLDER_IS_DEFAULT_CARDHOLDER_YES)->first();

            if (empty($defaultCardholder)) {
                $resultData['isSuccess'] = false;
                $resultData['message']   = admin_trans('card-batch.labels.card_holders_unknown') . '。';
                return $resultData;
            }
            $form->cardholder_ids = $defaultCardholder->id;
        }

        if (!$cardBin->card_type) {
            $config = json_decode($cardBin->config, true);
            foreach ($config as $value) {
                if ($value['key'] == 'initial_amount') {
                    $initialAmount = $value['value'];
                }
            }
        }

        $form->batch_id           = self::findAvailableNo();
        $form->card_bin_id        = $cidData->card_bin_id;
        $form->bin                = $cidData->bin;
        $form->bin_name           = $cidData->bin_name;
        $form->card_type          = $cardBin->card_type;
        $form->batch_total_amount = bcmul($form->quantity, $form->initial_amount, 2);
        $form->initial_amount     = $initialAmount;

        if ($cardBin->card_type == 0) { //常规卡
            $form->day_amount_limit = 0; // 重置日交易限额
        } else {
            if ($form->day_amount_limit < 100) {
                $resultData['isSuccess'] = false;
                $resultData['message']   = admin_trans('card-virtual.fields.virtual_info.shared_card_low_info');
                return $resultData;
            }
        }

        //费用计算
        $cardOpeningFee = bcmul($form->quantity, $cidData->opening_fee, 2); //开卡费
        $balance        = MerchantCard::getAvailableBalance($cidData->cards_id);

        if ($balance < $form->batch_total_amount + $cardOpeningFee) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('card-virtual.fields.virtual_info.balance_low');
            return $resultData;
        }

        return $resultData;
    }


    /**
     *************CID相关接口*************************
     */

    /**
     * CID转出
     * @param array $request
     * @return array
     */
    public static function cidAmountTransfer(array $request): array
    {
        // 数据校验
        $merchantId    = $request['mid'];
        $amount        = $request['amount'];
        $cid           = $request['cards_id'];
        $uniqueOrderId = $request['unique_order_id'] ?? CardTicket::getUniqueId();
        $resultData    = ['isSuccess' => true, 'message' => '', 'data' => []];

        if ($amount <= 0) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('ticket.labels.转出') . admin_trans('ticket.labels.金额太少');
            return $resultData;
        }

        $cardsData = MerchantCard::where('merchant_id', $merchantId)->where('cards_id', $cid)->where('status', '1')->first();

        if (empty($cardsData)) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = 'CID ' . admin_trans('cards.fields.not_exist');
            return $resultData;
        }

        if ($cardsData->balance < $amount) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = 'CID ' . admin_trans('ticket.labels.余额不足');
            return $resultData;
        }

        if (MerchantCardTicket::where('merchant_id', $merchantId)->where('unique_order_id', $uniqueOrderId)->exists()) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('cards.fields.not_repeatable');
            return $resultData;
        }

        // 计算手续费
        $fee = amount_format($amount * $cardsData['charges'] / 100);
        // 添加充值工单--转出(需要审核,防止余额击穿)
        $data = [
            'unique_order_id' => $uniqueOrderId,
            'merchant_id'     => $cardsData->merchant_id,
            'merchant_name'   => $cardsData->merchant_name,
            'cards_id'        => $cid,
            'type'            => MerchantCardTicket::MERCHANT_TICKET_TYPE_OUT,
            'currency'        => 'USD',
            'amount'          => $amount,
            'arrival_amount'  => $amount,
            'fee'             => $fee,
            'status'          => MerchantCardTicket::MERCHANT_TICKET_STATUS_CHECK,
            'created_at'      => date_create(),
        ];

        $insertRes = MerchantCardTicket::insert($data);
        if (!$insertRes) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = 'CID ' . admin_trans('cards.fields.operation_failed');
            return $resultData;
        }

        DB::beginTransaction();
        // 更新cid余额
        DB::update("UPDATE merchant_cards SET `balance` = `balance` - '{$amount}' WHERE `cards_id` = '{$cid}'");

        // 获取余额验证
        $tempCardsData = MerchantCard::getAvailableBalance($cid);

        if ($tempCardsData < 0) {
            DB::rollBack();
            $resultData['isSuccess'] = false;
            $resultData['message']   = sprintf('CID %s %s', admin_trans('cards.fields.balance_not_enough'), $amount);
            return $resultData;
        }

        // 添加结算明细
        $details = [
            'cards_id'      => $cid,
            'unique_id'     => SettleDetailCard::getUniqueId(),
            'merchant_id'   => $cardsData->merchant_id,
            'merchant_name' => $cardsData->merchant_name,
            'virtual_id'    => '0',
            'card_number'   => '-',
            'amount'        => amount_format((-1) * $amount),
            'type'          => SettleDetailCard::SETTLE_DETAIL_CARDS_TYPE_01,
            'settle_at'     => get_settle_date(),
            'balance'       => $cardsData['balance'],
            'created_at'    => date_create(),
        ];

        if (SettleDetailCard::insert($details)) {
            DB::commit();
            $resultData['isSuccess'] = true;
            $resultData['message']   = 'CID ' . admin_trans('cards.fields.operation_success');
            $resultData['data']      = [
                "unique_order_id" => $uniqueOrderId,
                'status'          => MerchantCardTicket::$merchantTicketStatusMapToEn[MerchantCardTicket::MERCHANT_TICKET_STATUS_CHECK],
            ];
            return $resultData;
        }

        DB::rollBack();
        $resultData['isSuccess'] = false;
        $resultData['message']   = 'CID ' . admin_trans('cards.fields.operation_failed');

        return $resultData;
    }

    /**
     * CID充值
     * @param array $request
     * @return array
     */
    public static function cidRecharge(array $request): array
    {
        $merchantId    = $request['mid'];
        $cid           = $request['cards_id'];
        $currency      = $request['currency'];
        $amount        = $request['amount'];
        $uniqueOrderId = $request['unique_order_id'] ?? CardTicket::getUniqueId();
        $resultData    = ['isSuccess' => true, 'message' => '', 'data' => []];
        $merchant      = Merchant::firstWhere('merchant_id', $merchantId);

        if (empty($merchant)) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = 'MID ' . admin_trans('cards.fields.not_exist');
            return $resultData;
        }

        $cardsData = MerchantCard::where('merchant_id', $merchantId)->where('cards_id', $cid)->where('status', '1')->first();

        if (empty($cardsData)) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = 'CID ' . admin_trans('cards.fields.not_exist');
            return $resultData;
        }

        if ($amount <= 0) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('ticket.labels.充值') . admin_trans('ticket.labels.金额太少');
            return $resultData;
        }

        if (MerchantCardTicket::where('merchant_id', $merchantId)->where('unique_order_id', $uniqueOrderId)->exists()) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('cards.fields.not_repeatable');
            return $resultData;
        }

        // 验证mid可提现金额
        $amountList = MerchantService::getAvailableAmountData($merchantId);
        $amountData = $amountList[$currency] ?? [];

        if (empty($amountData)) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = sprintf('mid %s, %s %s ', $merchantId, $currency, admin_trans('cards.fields.currency_info_exist'));
            return $resultData;
        }

        // 计算手续费
        $fee = amount_format($amount * $cardsData['charges'] / 100);

        if (($amountData['available_amount'] - ($amount + $fee)) < 0) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = sprintf('mid %s, %s %s', $merchantId, $currency, admin_trans('cards.fields.currency_balance_not_enough'));
            return $resultData;
        }

        // 获取币种对应汇率 计算预计转入金额
        $rateList                   = self::getCurrencyRateList();
        $request['transfer_amount'] = amount_format($amount / $rateList[$currency]);
        DB::beginTransaction();
        // 添加充值工单
        $data = [
            'unique_order_id' => $uniqueOrderId,
            'merchant_id'     => $cardsData->merchant_id,
            'merchant_name'   => $cardsData->merchant_name,
            'cards_id'        => $cid,
            'type'            => MerchantCardTicket::MERCHANT_TICKET_TYPE_IN,
            'currency'        => $currency,
            'amount'          => $amount,
            'arrival_amount'  => $request['transfer_amount'],
            'fee'             => $fee,
            'created_at'      => date_create(),
        ];

        if (!empty($merchant['is_credit']) && $merchant['is_credit'] == 1) {
            //信用卡收单商户，CID充值要审核
            $data['status'] = MerchantCardTicket::MERCHANT_TICKET_STATUS_CHECK;
            $returnStatus   = MerchantCardTicket::$merchantTicketStatusMapToEn[MerchantCardTicket::MERCHANT_TICKET_STATUS_CHECK];
        } else {
            $data['status']    = MerchantCardTicket::MERCHANT_TICKET_STATUS_SUCCESS;
            $returnStatus      = MerchantCardTicket::$merchantTicketStatusMapToEn[MerchantCardTicket::MERCHANT_TICKET_STATUS_SUCCESS];
            $data['settle_at'] = get_settle_date();

            // 更新cid余额
            DB::update("UPDATE merchant_cards SET `balance` = `balance` + '{$request['transfer_amount']}' WHERE `cards_id` = '{$cid}'");

            // 添加cid结算明细
            $details = [
                'unique_id'     => SettleDetailCard::getUniqueId(),
                'cards_id'      => $cid,
                'merchant_id'   => $cardsData->merchant_id,
                'merchant_name' => $cardsData->merchant_name,
                'virtual_id'    => '0',
                'card_number'   => '-',
                'amount'        => $request['transfer_amount'],
                'type'          => SettleDetailCard::SETTLE_DETAIL_CARDS_TYPE_00,
                'settle_at'     => get_settle_date(),
                'balance'       => $cardsData['balance'],
                'created_at'    => date_create(),
            ];

            if (!SettleDetailCard::insert($details)) {
                DB::rollBack();
                $resultData['isSuccess'] = false;
                $resultData['message']   = 'CID ' . admin_trans('ticket.labels.充值') . trans('admin.failed');
                return $resultData;
            }
        }

        if (MerchantCardTicket::insert($data)) {
            DB::commit();
            $resultData['isSuccess'] = true;
            $resultData['message']   = 'CID ' . admin_trans('ticket.labels.充值') . trans('admin.succeeded');
            $resultData['data']      = [
                "unique_order_id" => $uniqueOrderId,
                'status'          => $returnStatus,
            ];
            return $resultData;
        } else {
            DB::rollBack();
            $resultData['isSuccess'] = false;
            $resultData['message']   = 'CID ' . admin_trans('ticket.labels.充值') . trans('admin.failed');
            return $resultData;
        }
    }

    /**
     * 获取mid余额
     * @param string $merchantId
     * @return array
     */
    public static function getMerchantBalanceInfo(string $merchantId): array
    {
        $resultData = ['isSuccess' => true, 'message' => '', 'data' => []];
        $amountData = MerchantService::getAvailableAmountData($merchantId);

        if (empty($amountData)) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('cards.fields.data_not_exist');
            return $resultData;
        }

        $returnData = [];
        foreach ($amountData as $currency => $amount) {
            $returnData[] = [
                "currency"          => $currency,
                "available_balance" => $amount["available_balance"],
                "available_amount"  => $amount["available_amount"],
            ];
        }

        $resultData['data'] = $returnData;

        return $resultData;
    }

    /**
     * 获取CID结算明细
     * @param array $request
     * @return array
     */
    public static function getAllCidSettleInfo(array $request): array
    {
        $merchantId = $request['mid'];
        $page       = $request['start_page'] ?? 1;
        $pageSize   = $request['row'] ?? 20;
        $resultData = ['isSuccess' => true, 'message' => '', 'data' => []];
        $whereData  = [
            'merchant_id' => $merchantId,
        ];
        // 可选条件
        if (isset($request['cards_id'])) {
            $whereData['cards_id'] = $request['cards_id'];
        }

        $settleDetailCard = SettleDetailCard::where($whereData)->orderBy('id', 'desc')->paginate($pageSize, ['*'], 'page', $page);

        if (empty($settleDetailCard)) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('card-virtual.fields.virtual_info.card_not_exist');
            return $resultData;
        }

        $returnData = [];
        foreach ($settleDetailCard as $key => $value) {
            $returnData[$key]['unique_id']   = $value->unique_id;
            $returnData[$key]['cards_id']    = $value->cards_id;
            $returnData[$key]['card_number'] = $value->card_number != '-' ? get_markcard(DES3::decrypt($value->card_number, env('DES3_CARD_VIRTUAL'))) : '-';
            $returnData[$key]['amount']      = $value->amount;
            $returnData[$key]['type']        = SettleDetailCard::$settleDetailCardsTypeMapToEn[$value->type];
            $returnData[$key]['currency']    = 'USD';
            $returnData[$key]['remarks']     = $value->remarks;
            $returnData[$key]['settle_at']   = !empty($value->settle_at) ? date('Y-m-d H:i:s', strtotime($value->settle_at)) : '';
            $returnData[$key]['created_at']  = !empty($value->created_at) ? $value->created_at->toDateTimeString() : '';
        }

        $resultData['data'] = [
            'total' => $settleDetailCard->total(),
            'list'  => $returnData,
        ];

        return $resultData;
    }

    /**
     * 获取所有CID充值信息
     * @param array $request
     * @return array
     */
    public static function getAllCidRechargeInfo(array $request): array
    {
        $merchantId = $request['mid'];
        $page       = $request['start_page'] ?? 1;
        $pageSize   = $request['row'] ?? 20;
        $resultData = ['isSuccess' => true, 'message' => '', 'data' => []];

        $whereData = [
            'merchant_id' => $merchantId,
        ];
        // 可选条件
        if (isset($request['unique_order_id'])) {
            $whereData['unique_order_id'] = $request['unique_order_id'];
        }

        if (isset($request['cards_id'])) {
            $whereData['cards_id'] = $request['cards_id'];
        }

        if (isset($request['status'])) {
            [$IsPass, $value] = self::verificationPass($request['status'], MerchantCardTicket::$merchantTicketStatusMapToEn);
            if ($IsPass) {
                $whereData['status'] = $value;
            }
        }

        $merchantCardTicketData = MerchantCardTicket::where($whereData)->orderBy('id', 'desc')->paginate($pageSize, ['*'], 'page', $page);

        if (empty($merchantCardTicketData)) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('card-virtual.fields.virtual_info.card_not_exist');
            return $resultData;
        }

        $returnData = [];
        foreach ($merchantCardTicketData as $key => $value) {
            $returnData[$key]['cards_id']        = $value->cards_id;
            $returnData[$key]['unique_order_id'] = $value->unique_order_id;
            $returnData[$key]['type']            = MerchantCardTicket::$mercahntTicketTypeMapToEn[$value->type];
            $returnData[$key]['currency']        = $value->currency;
            $returnData[$key]['amount']          = $value->amount;
            $returnData[$key]['arrival_amount']  = $value->arrival_amount;
            $returnData[$key]['fee']             = $value->fee;
            $returnData[$key]['status']          = MerchantCardTicket::$merchantTicketStatusMapToEn[$value->status];
            $returnData[$key]['remark']          = $value->remark;
            $returnData[$key]['created_at']      = !empty($value->created_at) ? $value->created_at->toDateTimeString() : '';
        }

        $resultData['data'] = [
            'total' => $merchantCardTicketData->total(),
            'list'  => $returnData,
        ];

        return $resultData;
    }

    /**
     * 查询CID信息
     * @param string $cid
     * @return array
     */
    public static function getCidInfo(string $cid, string $mid): array
    {
        $resultData   = ['isSuccess' => true, 'message' => '', 'data' => []];
        $merchantCard = MerchantCard::where('cards_id', $cid)->where('merchant_id', $mid)->first();

        if (empty($merchantCard)) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('cards.fields.data_not_exist');
            return $resultData;
        }

        $returnData = [
            'cards_id'   => $merchantCard->cards_id,
            'status'     => MerchantCard::$internalStatusMapToEn[$merchantCard->status] ?? '',
            'balance'    => $merchantCard->balance,
            'created_at' => !empty($merchantCard->created_at) ? $merchantCard->created_at->toDateTimeString() : '',
        ];

        $resultData['data'] = $returnData;

        return $resultData;
    }

    /**
     * 添加CID
     * @param string $merchantId
     * @return array
     */
    public static function handleAddCid(string $merchantId): array
    {
        $resultData   = ['isSuccess' => true, 'message' => '', 'data' => []];
        $merchantData = Merchant::find($merchantId);

        if (empty($merchantData)) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('cards.fields.data_not_exist');
            return $resultData;
        }

        $cardsData = MerchantCard::withTrashed()->select(DB::raw('COUNT(cards_id) total'))->firstWhere('merchant_id', $merchantId);
        $total     = !empty($cardsData) && !empty($cardsData->total) ? $cardsData->total : '0';

        $cards = [
            'cards_id'      => 'C' . $merchantId . str_pad($total + 1, 3, '0', STR_PAD_LEFT),
            'merchant_id'   => $merchantId,
            'merchant_name' => $merchantData->merchant_name,
            'created_at'    => now(),
            'updated_at'    => now(),
        ];

        $merchantCardCount = MerchantCard::where([
            ['merchant_id', '=', $merchantId],
            ['status', '=', 2],
        ])->count();

        if ($merchantCardCount) {
            $resultData['isSuccess'] = false;
            $resultData['message']   = admin_trans('cards.labels.not_processed');
            return $resultData;
        }

        if (MerchantCard::insert($cards)) {
            $resultData['isSuccess'] = true;
            $resultData['data']      = [
                'cards_id' => $cards['cards_id'],
                'status'   => MerchantCard::$internalStatusMapToEn[MerchantCard::STATUS_PENDING],
            ];
            $resultData['message']   = admin_trans('cards.labels.add_to_cid') . trans('admin.succeeded');
            return $resultData;
        }

        $resultData['isSuccess'] = false;
        $resultData['message']   = admin_trans('cards.labels.add_to_cid') . trans('admin.failed');
        return $resultData;
    }

    public static function findAvailableNo()
    {
        $no   = date('YmdHis');
        $time = explode(' ', microtime());
        $no   = $no . str_pad(intval($time[0] * 100000), 5, '0', STR_PAD_LEFT);

        return $no;
    }

    /**
     * 获取币种汇率
     * @return array
     */
    public static function getCurrencyRateList()
    {
        return DirectoryCurrency::all()->pluck('rate', 'code')->toArray();
    }

    /**
     * 验证映射值
     * @return array
     */
    private static function verificationPass(string $value, array $mapData): array
    {
        $reverseArr = array_flip($mapData);
        return [
            isset($reverseArr[$value]),
            $reverseArr[$value] ?? '',
        ];
    }

    public static function createNoticeTask(string $mid, int $messageType, array $content, int $type = 1): void
    {
        // 查询是否有配置回调地址
        $webhook = MerchantApiWebhook::where([
            'merchant_id' => $mid,
            'type'        => $type,
            'status'      => MerchantApiWebhook::STATUS_ON,
        ])->with('merchantApi')->first();

        if (empty($webhook)) {
            return;
        }

        $returnData = [
            'code'   => 200,
            'msg'    => 'OK',
            'result' => $content,
        ];

        // 加签加密
        $returnData['result']['data']['sign'] = hash_hmac('sha256', json_encode($content), $webhook->merchantApi->secret);
        $returnData['result']                 = DES3::encrypt(json_encode($returnData['result']));
        // 保存
        $task               = new MerchantApiNoticeTask();
        $task->notify_url   = $webhook->url;
        $task->merchant_id  = $mid;
        $task->type         = $type;
        $task->message_type = $messageType;
        $task->content      = json_encode($returnData);
        $task->save();
    }
}
