<?php
namespace App\Services;

use App\Models\Channel;
use App\Models\Order;
use App\Http\Controllers\Traits\PaymentController;
use App\Classes\Pay\Pay;
use App\Models\AbnormalRefund;
use Illuminate\Support\Arr;

class AbnormalRefundService
{
    use PaymentController;
    public function createAbnormalRefund(Order $order)
    {
        // 验证该订单是否存在异常退款记录
        $abnormalRefund = AbnormalRefund::where('order_id', $order->order_id)->whereIn('status', [AbnormalRefund::STATUS_APPROVED, AbnormalRefund::STATUS_PENDING, AbnormalRefund::STATUS_REVIEW])->first();
        if ($abnormalRefund) {
            // 有完成或进行中的异常退款记录
            return;
        }

        $refundId = Order::findAvailableNo();
        // 获取账单标识信息
        $channelData = Channel::with('channelSupplier')->find($order->channel_id);

        $refund = new AbnormalRefund();
        $refund->refund_id    = $refundId;
        $refund->order_id     = $order->order_id;
        $refund->completed_at = now();
        $refund->currency     = $order->currency;
        $refund->amount       = $order->amount;
        $refund->status       = AbnormalRefund::STATUS_RECEIVED;
        $refund->code         = '';
        $refund->result       = '';
        $refund->remark       = '';
        $refund->save();

        $refundAmount = $order->paymentOrder->amount;

        // 根据币种对金额进行向上取整
        $refundAmount = $this->_roundUpAmountByCurrency($order->paymentOrder->currency, $refundAmount);

        // 保存网关订单信息
        $refund->abnormalPaymentRefund()->create([
            'order_number' => $refund->refund_id,
            'currency'     => $order->paymentOrder->currency,
            'amount'       => $refundAmount,
        ]);

        $refund->load('paymentOrder', 'abnormalPaymentRefund');

        // 获取账单标识信息
        $channelData = Channel::with('channelSupplier')->where('channel', $order->channel)->first();

        // 获取渠道名
        $channelSupplierName = strtolower($channelData->channelSupplier->file_name);

        // 获取账单标识配置信息
        $config = $this->_getConfig($channelData->config, $channelSupplierName);

        $channelSupplier = Pay::$channelSupplierName($config);

        $tempRefund = $refund->toArray();
        $tempRefund['payment_refund'] = $tempRefund['abnormal_payment_refund'];
        unset($tempRefund['abnormal_payment_refund']);
        $refundResult = $channelSupplier->refund($tempRefund);

        // 更新渠道返回
        $refundResult['refund'] = Arr::add($refundResult['refund'], 'completed_at', now()); // 完成时间
        $refund->abnormalPaymentRefund()->update($refundResult['payment_refund']);
        $refund->update($refundResult['refund']);
    }
}