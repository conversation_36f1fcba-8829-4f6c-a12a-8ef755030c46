<?php

namespace App\Services;

use App\Models\StatOrderChargeback;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class ProcessingSnapshotService
{
    /**
     * 获取统计信息
     * @param $params
     * @return array
     */
    public function getProcessingSnapshot($params): array
    {
        $merchantId = $params['merchant_id'] ?? null;
        if (empty($merchantId)) {
            return [];
        }

        $businessId = $params['business_id'] ?? null;
        $urlId = $params['url_id'] ?? null;

        $monthTypeSuffix = $params['switch_month_type'] ?? 'qty';
        $refundAmountUsd = ($monthTypeSuffix == 'qty') ? 'refund_amount_usd' : 'refund_amount_usd1';

        $baseDate = Carbon::now();

        $months = [];
        for ($i = 0; $i <= 11; $i++) {
            $months[] = $baseDate->copy()->subMonths(11 - $i)->format('Ym');
        }

        $statOrderChargebackQuery = StatOrderChargeback::query()
            ->whereBetween('date_stat_month', [$months[0], $months[11]])
            ->where('merchant_id', $merchantId);
        if (!empty($businessId)) {
            $statOrderChargebackQuery = $statOrderChargebackQuery->where('business_id', $businessId);
        }
        if (!empty($urlId)) {
            $statOrderChargebackQuery = $statOrderChargebackQuery->where('url_id', $urlId);
        }

        // 构造查询字段
        $selectFiled = [
            'date_stat_month',
            DB::raw('SUM(transaction_qty) as transaction_qty'),
            DB::raw('SUM(transaction_amount_usd) as transaction_amount_usd'),
            DB::raw('TRUNCATE(SUM(transaction_amount_usd) / NULLIF(SUM(transaction_qty), 0), 2) AS avg_transaction_price'),
            DB::raw('SUM(refund_' . $monthTypeSuffix . ') as refund_qty'),
            DB::raw('SUM(' . $refundAmountUsd . ') as refund_amount_usd'),
            DB::raw('SUM(dishonour_' . $monthTypeSuffix . ') as dishonour_qty'),
        ];

        if ($monthTypeSuffix == 'qty') {
            $selectFiled = array_merge($selectFiled, [
                DB::raw('TRUNCATE((SUM(refund_amount_usd) / NULLIF(SUM(transaction_amount_usd), 0)) * 100, 2) AS refund_rate'),
                DB::raw('SUM(chargeback_amount_usd) as chargeback_amount_usd'),
                DB::raw('TRUNCATE((SUM(dishonour_qty) / NULLIF(SUM(transaction_qty), 0)) * 100, 2) AS dishonour_rate_by_count'),
                DB::raw('TRUNCATE(SUM(chargeback_amount_usd) / NULLIF(SUM(transaction_amount_usd), 0), 2) AS dishonour_rate_by_amount'),
                DB::raw('TRUNCATE(SUM(chargeback_amount_usd) / NULLIF(SUM(dishonour_qty), 0), 2) AS avg_chargeback_amount'),
            ]);
        }

        $list = $statOrderChargebackQuery->select($selectFiled)
            ->groupBy('date_stat_month')
            ->get()
            ->toArray();

        // 补全缺失月份数据
        $indexedData = [];
        foreach ($list as $item) {
            $indexedData[$item['date_stat_month']] = $item;
        }

        $completeList = [];
        foreach ($months as $month) {
            if (isset($indexedData[$month])) {
                $completeList[] = $indexedData[$month];
            } else {
                $completeList[] = [
                    'date_stat_month' => $month,
                    'transaction_qty' => 0,
                    'transaction_amount_usd' => 0.00,
                    'avg_transaction_price' => 0.00,
                    'refund_qty' => 0,
                    'refund_rate' => 0.00,
                    'refund_amount_usd' => 0.00,
                    'dishonour_qty' => 0,
                    'chargeback_amount_usd' => 0.00,
                    'dishonour_rate_by_count' => 0.00,
                    'dishonour_rate_by_amount' => 0.00,
                    'avg_chargeback_amount' => 0.00,
                ];
            }
        }

        // 汇总数据
        $total = [
            'total_transaction_qty' => 0,
            'total_transaction_amount_usd' => 0.00,
            'total_refund_qty' => 0,
            'total_refund_rate' => 0.00,
            'total_refund_amount_usd' => 0.00,
            'total_dishonour_qty' => 0,
            'total_chargeback_amount_usd' => 0.00,
            'total_dishonour_rate_by_count' => 0.00,
            'total_dishonour_rate_by_amount' => 0.00,
            'total_avg_chargeback_amount' => 0.00,
        ];

        foreach ($completeList as $item) {
            $total['total_transaction_qty'] += $item['transaction_qty'];
            $total['total_transaction_amount_usd'] = bcadd($total['total_transaction_amount_usd'], $item['transaction_amount_usd'], 2);
            $total['total_refund_qty'] += $item['refund_qty'];
            $total['total_refund_amount_usd'] = bcadd($total['total_refund_amount_usd'], $item['refund_amount_usd'], 2);
            $total['total_dishonour_qty'] += $item['dishonour_qty'];

            if ($monthTypeSuffix == 'qty') {
                $total['total_chargeback_amount_usd'] = bcadd($total['total_chargeback_amount_usd'], $item['chargeback_amount_usd'], 2);
            }
        }

        // 安全计算汇总指标
        $total['total_avg_transaction_price'] = $this->safeDivide($total['total_transaction_amount_usd'], $total['total_transaction_qty']);

        if ($monthTypeSuffix == 'qty') {
            $total['total_refund_rate'] = bcmul($this->safeDivide($total['total_refund_amount_usd'], $total['total_transaction_amount_usd'], 4), '100', 2);
            $total['total_dishonour_rate_by_count'] = bcmul($this->safeDivide($total['total_dishonour_qty'], $total['total_transaction_qty'], 4), '100', 2);
            $total['total_dishonour_rate_by_amount'] = bcmul($this->safeDivide($total['total_chargeback_amount_usd'], $total['total_transaction_amount_usd'], 4), '100', 2);
            $total['total_avg_chargeback_amount'] = $this->safeDivide($total['total_chargeback_amount_usd'], $total['total_dishonour_qty']);
        }

        // 年化交易量 = 总交易金额 / 12
        $total['annualized_volume'] = $this->safeDivide($total['total_transaction_amount_usd'], 12);

        return [
            'data' => $completeList,
            'total' => $total,
        ];
    }

    /**
     * 安全除法函数（避免除以0）
     *
     * @param float|string $numerator 分子
     * @param float|string $denominator 分母
     * @param int $scale 小数位数
     * @return string
     */
    protected function safeDivide($numerator, $denominator, int $scale = 2): string
    {
        $numerator = (float)$numerator;
        $denominator = (float)$denominator;

        if ($denominator === 0.0) {
            return '0.00';
        }

        return bcdiv((string)$numerator, (string)$denominator, $scale);
    }
}
