<?php
namespace App\Services;

use App\Models\OrderRelation;
use Dcat\Admin\Admin;

class OrderRelationService
{
    /**
     * 订单关系表创建
     *
     * @param integer $orderId
     * @return object | bool
     */
    public static function orderRelationCreate(int $orderId)
    {
        $orderRelations               = new OrderRelation();
        $orderRelations->order_id     = $orderId;
        $orderRelations->custom_token = md5($orderId);

        if ($orderRelations->save()) {
            return $orderRelations;
        }

        return;
    }

}