<?php
namespace App\Services\Shield;

use App\Classes\Supports\Collection;
use App\Models\ChargebackCase;
use App\Models\DirectoryCurrency;
use App\Classes\Supports\Log;
use App\Jobs\SendSlsLog;
use App\Models\Order;
use App\Models\OrderRelation;
use App\Models\Refund;
use App\Services\AlertService;
use App\Services\ChargebackCaseService;
use App\Services\ShieldService;
use App\Services\TransactionService;
use App\Classes\Pay\Contracts\Support;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Cache;

class EmbracyShieldService extends ShieldService
{

    protected $supplierName = 'embracyShield';

    public function notice($request)
    {
        //判断case_id是否存在
        if (ChargebackCase::where('case_id', $request['caseId'])->exists()) {
            //重复报警
            Log::warning('EmbracyShield 重复预警', ['caseId' => $request['caseId']]);
            dispatch(new SendSlsLog(
                ['message' => 'EmbracyShield 重复预警'],
                ['caseId' => $request['caseId']],
                'warning',
                'EmbracyShield'
            ));

            return;
        }

        //匹配交易
        $order = $this->matchingOrder($request);
        $lock  = Cache::lock('Chargeback_Case_Orders_Lock' . $order->order_id, 30);

        if (!$lock->get()) {
            //说明该订单已经被匹配，把当前匹配结果清空
            $order = new Collection();
        }

        //预警数据初始化
        $addData = [
            'case_id'             => $request['caseId'],
            'business_history_id' => 0,
            'alert_type'          => $request['alertType'] == 0 ? ChargebackCase::CONFIRMED_FRAUD : ChargebackCase::CUSTOMER_DISPUTE,
            'reason_code'         => $request['reasonCode'],
            'result'              => 0,
            'dishonour_warn_info' => $request,
            'remarks'             => '',
            'warn_currency'       => $request['alertCurrency'],
            'warn_amount'         => $request['alertAmount'],
            'alert_from'          => ChargebackCase::ALERT_FROM_EMBRACY_SHIELD,
            'by_alert_id'         => '1',
            'by_alert'            => 'admin',
            'date_complete'       => '1970-01-01',
            'date_settle'         => '1970-01-01',
        ];

		// 处理截止时间
		if (isset($request['timeOut']) && $request['timeOut']) {
			$timestamp = strtotime($request['timeOut']);
			if ($timestamp) {
				$addData['time_out'] = date('Y-m-d H:i:s', $timestamp);
			}
		}

        switch ($request['alertServe']) {
            case 0 :
                $addData['chargeback_from'] = ChargebackCase::FROM_CDRN;
                break;
            case 1 :
                $addData['chargeback_from'] = ChargebackCase::FROM_RDR;
                break;
            case 2 :
                $addData['chargeback_from'] = ChargebackCase::FROM_ETHOCA;
                break;
        }

        //无或多条订单记录需人工处理该次预警
        $addDataUp = [
            'order_id'      => '000000000000000000',
            'order_number'  => '000000000000000000',
            'merchant_id'   => 0,
            'merchant_name' => '无',
            'business_id'   => 0,
            'channel_id'    => 0,
            'channel'       => '无',
            'is_meddle'     => 0,
            'reply_status'  => ChargebackCase::REPLY_STATUS_UNHANDLED,
        ];

        if ($order->count()) {
            if (empty($order->relation) || empty($order->paymentOrder)) {
                //特殊情况(只找到订单，还需对比币种、金额)，不回复预警平台
                Log::error('交易订单未找到关联数据', ['caseId' => $request['caseId'], 'orderId' => $order->order_id]);
                dispatch(new SendSlsLog(
                    ['message' => '交易订单未找到关联数据'],
                    ['caseId' => $request['caseId'], 'orderId' => $order->order_id],
                    'error',
                    'EmbracyShield'
                ));

                return;
            }

            $resultTemp = $this->dataHandle($order);
            $addDataUp  = $resultTemp['addDataUp'];
        } else {
			// 匹配失败，归入异常预警
			$addDataUp['is_normal']     = ChargebackCase::IS_NORMAL_FALSE;
			$addDataUp['abnormal_type'] = ChargebackCase::ABNORMAL_TYPE_UNMATCHED;

            //未匹配发送告警
            $warnData = [
                'alert_from'      => $addData['alert_from'],
                'chargeback_from' => $addData['chargeback_from'],
                'case_id'         => $addData['case_id'],
            ];
            $this->sendWarning($warnData, ChargebackCase::WARNING_TYPE_MATCHING_FAILED);
        }

        //保存拒付预警数据
        $addData = array_merge($addData, $addDataUp);

        //匹配订单已拒付预警
        if ($addDataUp['is_normal'] != ChargebackCase::IS_NORMAL_FALSE && $order->relation->is_chargeback_warn) {
            //a021 重复报警
            $data = [
                'caseId' => $request['caseId'],
                'data'   => [
                    'merchantId' => $request['merchantId'],
                    'shieldId'   => $request['shieldId'],
                    'caseId'     => $request['caseId'],
                    'statusCode' => $addData['chargeback_from'] == ChargebackCase::FROM_RDR ? 'Solved' : 'a021'
                ]
            ];

            //补充：录入预警记录且类型为异常
            $addData['is_normal']     = ChargebackCase::IS_NORMAL_FALSE;
            $addData['abnormal_type'] = ChargebackCase::ABNORMAL_TYPE_REPLY_CODE;
            $addData['reply_status']  = ChargebackCase::REPLY_STATUS_NOTICE;

            //回复前先判断已预警记录，预警类型是否与此预警相同，不同则回复a011 or a017
            $chargebackRow = ChargebackCase::select('case_id', 'chargeback_from')->where('order_id', $order->order_id)->where('case_id', '<>', $request['caseId'])->orderBy('id', 'desc')->first();

            if ($chargebackRow['chargeback_from'] != $request['alertServe']) {
                $refundOrder = $order->refund->sortByDesc('completed_at')->whereIn('status', [Refund::STATUS_APPROVED, Refund::STATUS_REVIEW])->first();

                if (empty($refundOrder)) {
                    //无退款信息则不回复
                    Log::info('交易订单暂无成功退款数据 ', ['caseId' => $request['caseId'], 'orderId' => $order->order_id]);
                    dispatch(new SendSlsLog(
                        ['message' => '交易订单暂无成功退款数据'],
                        ['caseId' => $request['caseId'], 'orderId' => $order->order_id],
                        'error',
                        'EmbracyShield'
                    ));

                    return;
                }

                $refundOrder = $refundOrder->toArray();

                $data['data']['statusCode']     = $request['alertType'] == ChargebackCase::CONFIRMED_FRAUD ? 'a011' : 'a017';
                $data['data']['refundNo']       = $refundOrder['refund_id'];
                $data['data']['refundDate']     = $refundOrder['completed_at']; //最后退款时间
                $data['data']['refundAmount']   = $order->paymentOrder->amount;
                $data['data']['refundCurrency'] = $order->paymentOrder->currency;
            } else {
                //回复a021时，如果Ethoca类型则备注填写被重复预警的case ID
                if ($addData['chargeback_from'] == ChargebackCase::FROM_ETHOCA) {
                    $data['data']['comments'] = $chargebackRow['case_id'];
                }
            }

            if ($chargebackRow) {
                ChargebackCase::create($addData);
                $this->sendNotice($data, 1);//查询到已预警数据才回复
                Log::info('EmbracyShield 重复预警', ['caseId' => $request['caseId'], 'orderId' => $order->order_id]);
                dispatch(new SendSlsLog(
                    ['message' => 'EmbracyShield 重复预警'],
                    ['caseId' => $request['caseId'], 'orderId' => $order->order_id],
                    'info',
                    'EmbracyShield'
                ));
            } else {
                Log::info('EmbracyShield 重复预警，订单已拒付预警但无相关拒付预警数据', ['caseId' => $request['caseId'], 'orderId' => $order->order_id]);
                dispatch(new SendSlsLog(
                    ['message' => 'EmbracyShield 重复预警，订单已拒付预警但无相关拒付预警数据'],
                    ['caseId' => $request['caseId'], 'orderId' => $order->order_id],
                    'info',
                    'EmbracyShield'
                ));
            }

            return;
        }

        $chargebackCase = ChargebackCase::create($addData);

        if ($chargebackCase && $addData['reply_status'] == ChargebackCase::REPLY_STATUS_NOTICE) {
            $data = [
                'merchantId'      => $request['merchantId'],
                'shieldId'        => $request['shieldId'],
                'caseId'          => $addData['case_id'],
                'alert_type'      => $addData['alert_type'],
                'alert_from'      => $addData['alert_from'],
                'chargeback_from' => $addData['chargeback_from'],
                'is_normal'       => ChargebackCase::IS_NORMAL_TRUE
            ];
            $this->chargebackCasesHandle($order, $data);
        }
    }

    /**
     * 处理保存的拒付预警数据
     *
     * @param object $order
     * @return array
     */
    public function dataHandle(object $order): array
    {
        $addDataUp = [
            'order_id'            => $order->order_id,
            'business_history_id' => $order->settlements->business_history_id,
            'order_number'        => $order->order_number,
            'merchant_id'         => $order->merchant_id,
            'merchant_name'       => $order->merchant_name,
            'business_id'         => $order->business_id,
            'channel_id'          => $order->channel_id,
            'channel'             => $order->channel,
            'date_complete'       => $order->completed_at,
            'date_settle'         => get_settle_date(),
            'is_meddle'           => 0,
            'is_normal'           => ChargebackCase::IS_NORMAL_TRUE,
            'reply_status'        => ChargebackCase::REPLY_STATUS_NOTICE
        ];

        return ['addDataUp' => $addDataUp];
    }

    /**
     * 拒付预警新增后续处理
     *
     * @param object $order
     * @param array $data
     */
    public function chargebackCasesHandle(object $order, array $data)
    {
        //更新关联表拒付预警字段
        $order->relation->update(['is_chargeback_warn' => 1]);

        $handleData = [
            'updateData' => [
                'result'  => ChargebackCase::NOT_REFUNDED,
                'remarks' => '未处理',
            ],
        ];

        switch ($order->status) {
            case Order::STATUS_APPROVED:
                //成功处理
                //获取可退款最高金额
                $availableAmount = TransactionService::getAvailableRefundAmount($order);

                //调取对应预警服务的退款
                $refundFun = strtolower(ChargebackCase::$from[$data['chargeback_from']]) . 'refund';
                $result    = $this->{$refundFun}($order, $availableAmount, $data);

                $handleData['updateData']['result']  = $result['result'];
                $handleData['updateData']['remarks'] = $result['remarks'];

                if ($order->type == Order::TYPES_AUTH) {
                    $handleData['updateData']['remarks'] = 'Auth交易预警';
                }

                //完全退款才做回复
                if ($result['is_refund'] == 1) {
                    //回复预警系统信息
                    $dataTemp = [
                        'caseId' => $data['caseId'],
                        'data'   => [
                            'merchantId' => $data['merchantId'],
                            'shieldId'   => $data['shieldId'],
                            'caseId'     => $data['caseId']
                        ]
                    ];

                    //预警前已拒付
                    if ($order->relation->is_chargeback) {
                        $dataTemp['data']['statusCode'] = 'a013';
                    } else {
                        //欺诈类型：a010 成功处理并退款、a011 在收到预警前已经退款； 争议类型：a016 成功处理并退款、a017 在收到预警前已经退款
                        $codeArr  = [['a010', 'a011'], ['a016', 'a017']];
                        $refunded = $order->relation->is_refund == OrderRelation::IS_REFUND_FULL ? OrderRelation::IS_REFUND_FULL : OrderRelation::IS_REFUND_NOT;

                        $dataTemp['data']['statusCode'] = $codeArr[$data['alert_type']][$refunded];

                        if (
                            isset($result['returnData']['refundNo'])
                            && isset($result['returnData']['refundDate'])
                            && isset($result['returnData']['refundAmount'])
                            && isset($result['returnData']['refundCurrency'])
                        ) {
                            $dataTemp['data']['refundNo']       = $result['returnData']['refundNo']; //最后退款单号
                            $dataTemp['data']['refundDate']     = $result['returnData']['refundDate']; //最后退款时间
                            $dataTemp['data']['refundAmount']   = $result['returnData']['refundAmount'];
                            $dataTemp['data']['refundCurrency'] = $result['returnData']['refundCurrency'];
                        }
                    }

                    $handleData['dataTemp'] = $dataTemp;
                } else {
                    //待审核退款把处理状态改为待通知
                    $handleData['updateData']['reply_status'] = ChargebackCase::REPLY_STATUS_PROCESSING;

                    if ($result['remarks'] != '退款待审核'){
                        $chargebackCase        = ChargebackCase::where('case_id', $data['caseId'])->first();
                        $chargebackCaseService = new ChargebackCaseService();
                        $chargebackCaseService->AutoFail($chargebackCase);
                        return;
                    }
                }
                break;

            case Order::STATUS_DECLINED:
            case Order::STATUS_EXPIRED:
                //失败处理
                $handleData['updateData']['remarks'] = '订单状态：' . Order::$statusMap[$order->status];

                //回复预警系统信息
                $handleData['dataTemp'] = [
                    'caseId' => $data['caseId'],
                    'data'   => [
                        'merchantId' => $data['merchantId'],
                        'shieldId'   => $data['shieldId'],
                        'caseId'     => $data['caseId'],
                        'statusCode' => $data['alert_type'] == ChargebackCase::CONFIRMED_FRAUD ? 'a012' : 'a018'
                    ]
                ];
                break;
        }

        //更新预警信息
        if (isset($handleData['dataTemp']['data']['statusCode']) && in_array($handleData['dataTemp']['data']['statusCode'], ['a010', 'a016', 'a011', 'a017', 'a013'])) {
            $handleData['updateData']['is_normal']     = ChargebackCase::IS_NORMAL_TRUE;
            $handleData['updateData']['abnormal_type'] = ChargebackCase::ABNORMAL_TYPE_NORMAL;
            $handleData['updateData']['date_settle']   = get_settle_date();
        } else {
            $handleData['updateData']['is_normal']     = ChargebackCase::IS_NORMAL_FALSE;
            $handleData['updateData']['abnormal_type'] = ChargebackCase::ABNORMAL_TYPE_REPLY_CODE;
        }

        ChargebackCase::where('case_id', $data['caseId'])->update($handleData['updateData']);

        //有回复信息则回复渠道
        if (isset($handleData['dataTemp'])) {
            $result = $this->sendNotice($handleData['dataTemp'], 1);

            //回复渠道失败则把处理状态改为待通知
            if (!in_array($result['status'], ['0', '1'])) {
                ChargebackCase::where('case_id', $handleData['dataTemp']['data']['caseId'])->update([
                    'reply_status' => ChargebackCase::REPLY_STATUS_PROCESSING
                ]);
            }
        }
    }

    public function sendNotice($data, $autoReply = 0)
    {
        $sendData = $data['data'];

		// 拒付预警信息
		$caseInfo = ChargebackCase::where(['case_id' => $sendData['caseId']])->first();

        if ($caseInfo) {
            //自动应答时，先写入应答码;手动处理则回复成功后才补充应答码
            if ($autoReply == 1 && !$caseInfo->reply_code) {
                ChargebackCase::where('case_id', $data['data']['caseId'])->update(['reply_code' => $data['data']['statusCode']]);
            }

            //为RDR时回复统一应答码Solved
            if (isset($caseInfo->chargeback_from) && $caseInfo->chargeback_from == ChargebackCase::FROM_RDR) {
                $sendData = [
                    'merchantId' => $data['data']['merchantId'],
                    'shieldId'   => $data['data']['shieldId'],
                    'caseId'     => $data['data']['caseId'],
                    'statusCode' => 'Solved'
                ];
            }
        }

        $postData = [
            'header' => [
                'Content-Type'  => 'application/x-www-form-urlencoded',
                'Authorization' => 'Bearer ' . $this->noticeConfig['api_token'],
            ],
            'data' => $sendData,
        ];

        $result = [];
        $url    = $this->noticeConfig['api_domain'] . '/api/v1/merchantAlert';

        Log::info('EmbracyShield Request API', [$url, $postData]);
        dispatch(new SendSlsLog(
            ['message' => 'EmbracyShield Request API'],
            [$url, $postData],
            'info',
            'EmbracyShield'
        ));

        try {
            $result = $this->post($url, $postData['data'], ['headers' => $postData['header']]);
        } catch (\Exception $e) {
            //记录日志
            Log::warning($e->getMessage());
            dispatch(new SendSlsLog(
                ['message' => $e->getMessage()],
                [],
                'warning',
                'EmbracyShield'
            ));
        }

        Log::info('EmbracyShield Result API', $result);
        dispatch(new SendSlsLog(
            ['message' => 'EmbracyShield Result API'],
            $result,
            'info',
            'EmbracyShield'
        ));

        $status = $result['status'] ?? '';

        //回复预警平台失败，推送告警
        if (empty($status) && $status != 0) {
            $warnData = [
                'alert_from'      => $caseInfo['alert_from'],
                'chargeback_from' => $caseInfo['chargeback_from'],
                'case_id'         => $caseInfo['case_id'],
            ];

            $this->sendWarning($warnData, ChargebackCase::WARNING_TYPE_RESPONSE_FAILED);
        }

        $result['status'] = $status;//重新赋值，供其它地方判断
        return $result;
    }

    /**
     * 发送预警工单告警
     *
     * @param $warnData
     * @param int $warningType
     */
    public function sendWarning($warnData, int $warningType = ChargebackCase::WARNING_TYPE_PROCESSING_FAILED)
    {
        $tempData = [
            'alert_from'      => $warnData['alert_from'],
            'chargeback_from' => $warnData['chargeback_from'],
            'case_id'         => $warnData['case_id'],
            'warning_type'    => $warningType
        ];

        AlertService::sendAlertWarning($tempData);
    }

    /**
     * 根据预警通知数据匹配交易
     *
     * @param $request 通知数据
     * @return collection
     */
    public function matchingOrder($request)
    {
        $order = new Collection();

        //匹配交易
        $caseTime  = strtotime($request['transactionDate']);
        $days      = 2 * 24 * 3600; //浮动时间
        $orderSql  = Order::query()->with(['settlements:order_id,business_history_id', 'paymentOrder:order_id,amount,currency', 'refund:order_id,refund_id,status,completed_at', 'relation'])
            ->whereBetween('completed_at', [date('Y-m-d H:i:s', $caseTime - $days), date('Y-m-d H:i:s', $caseTime + $days)])
            ->whereIn('status', [Order::STATUS_DECLINED, Order::STATUS_APPROVED, Order::STATUS_EXPIRED])
            ->whereHas('card', function(Builder $query) use ($request) {
                $query->where('card_mask', get_markcard($request['cardNumber']))->whereIn('cc_type', ['V', 'M']);
            });

        //匹配ARN
        $arnRow = (clone $orderSql)->whereHas('paymentOrder', function(Builder $query) use ($request) {
            $query->where('arn', $request['arn']);
        })->get();
        if ($arnRow->count() == 1) {
            //ARN匹配单个订单
            $order = $arnRow[0];

            return $order;
        }

        //匹配auth_code
        $authCodeRow = (clone $orderSql)->whereHas('paymentOrder', function(Builder $query) use ($request) {
            $query->where('auth_code', $request['authCode']);
        })->get();
        if ($authCodeRow->count() == 1) {
            //auth_code匹配单个订单
            $order = $authCodeRow[0];

            return $order;
        }

        //同币种匹配情况
        $sameCurrencyOrderSql = (clone $orderSql)->whereHas('paymentOrder', function(Builder $query) use ($request) {
            $minAmount = $request['alertAmount'] > 2 ? $request['alertAmount'] - 2 : $request['alertAmount']; //金额大于2时才做最低差额
            $query->where('currency', $request['alertCurrency']);
            $query->whereBetween('amount', [$minAmount, $request['alertAmount'] + 2]); //同币种，金额相差2以内视为匹配
        });

        //同币种并且卡账单完整匹配
        $cardBillRow = (clone $sameCurrencyOrderSql)->where('card_bill', $request['cardBill'])->get();
        if ($cardBillRow->count() == 1) {
            //同币种并且卡账单完整匹配单个订单
            $order = $cardBillRow[0];

            return $order;
        }

        //同币种并且卡账单模糊匹配
        $likeCardBillRow = (clone $sameCurrencyOrderSql)->where('card_bill', 'like', "%{$request['cardBill']}%")->get();
        if ($likeCardBillRow->count() == 1) {
            //同币种并且卡账单模糊匹配单个订单
            $order = $likeCardBillRow[0];

            return $order;
        }

        //不同币种匹配情况
        $differentCurrencyList = (clone $orderSql)->whereHas('paymentOrder', function(Builder $query) use ($request) {
            $query->where('currency', '!=', $request['alertCurrency']);
        })->get();

        if ($differentCurrencyList->count()) {
            //偏差金额匹配订单
            $deviationAmountOrders = [];

            //根据币种找出汇率数据
            $currencyData         = $differentCurrencyList->pluck('paymentOrder.currency')->all();
            $currencyData[]       = $request['alertCurrency'];
            $directoryCurrencyArr = DirectoryCurrency::whereIn('code', $currencyData)->get()->pluck('rate', 'code');

            foreach ($differentCurrencyList as $val) {
                $orderCurrency = $val->paymentOrder['currency'];
                $alertCurrency = $request['alertCurrency'];

                if (isset($directoryCurrencyArr[$orderCurrency]) && isset($directoryCurrencyArr[$alertCurrency])) {
                    $chargebackAmount = Support::amount_format($request['alertAmount'] * ($directoryCurrencyArr[$orderCurrency] / $directoryCurrencyArr[$alertCurrency]));
                    $difference       = abs($val->paymentOrder['amount'] - $chargebackAmount); //金额差值
                    $percentage       = round(($difference / $val->paymentOrder['amount']) * 100, 2); //交易授权金额差值比

                    //差比小于等于5%为正常
                    if ($percentage <= 5) {
                        $deviationAmountOrders[] = $val;
                    }
                }
            }

            if (count($deviationAmountOrders)) {
                $tempCollection  = collect($deviationAmountOrders);
                //不同币种并且卡账单完整匹配
                $cardBillRow = $tempCollection->where('card_bill', $request['cardBill']);
                if ($cardBillRow->count() == 1) {
                    //不同币种并且卡账单完整匹配单个订单
                    $order = $cardBillRow->first();

                    return $order;
                }

                //不同币种并且卡账单模糊匹配
                $likeCardBillRow = $tempCollection->filter(function ($item) use ($request) {
                    return stripos($item['card_bill'], $request['cardBill']) !== false;
                });
                if ($likeCardBillRow->count() == 1) {
                    //不同币种并且卡账单模糊匹配单个订单
                    $order = $likeCardBillRow->first();

                    return $order;
                }
            }
        }

        return $order;
    }
}
