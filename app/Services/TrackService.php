<?php
namespace App\Services;

use App\Classes\Track\Track;
use App\Models\DirectoryCarrier;
use App\Models\OrderTrack;
use App\Models\OrderTrackTask;
use Illuminate\Database\Eloquent\Collection;

class TrackService
{
    /**
     * 添加运单添加任务
     *
     * @param OrderTrack $orderTrack
     * @return mixed
     */
    public static function createTrackTask(OrderTrack $orderTrack)
    {
        return $orderTrack->tracking_type != 'other' ? self::getClassByType($orderTrack->api_type)->addTrackAddTask($orderTrack) : [];
    }

    /**
     * 添加运单更新任务
     *
     * @param OrderTrack $orderTrack
     * @param array $update
     * @return bool|mixed
     */
    public static function createTrackUpdateTask(OrderTrack $orderTrack, array &$update)
    {
        // 未修改运单号、运单类型
        if ($update['tracking_type'] == $orderTrack->tracking_type
            && $update['tracking_number'] == $orderTrack->tracking_number) {
            $update['is_repeat'] = $orderTrack->is_repeat;

            return false; // 无需添加运单任务
        }

        // 修改了运单号或运单类型,重置状态
        $update['is_delivered']       = '0';
        $update['is_activated']       = '0';
        $update['api_result_status']  = OrderTrack::API_RESULT_STATUS_WAITING;
        $update['api_result_content'] = null;
        $update['api_result_date']    = get_default_date() . ' 00:00:00';

        // todo 修改后为other类型，删除警示信息
        if ($update['tracking_type'] == 'other') {
            return false;
        }

        // 修改运单号
        $update['api_type'] = DirectoryCarrier::firstWhere('code', $update['tracking_type'])->api_type;

        if ($update['tracking_number'] != $orderTrack->tracking_number) {
            $orderTrack->tracking_number = $update['tracking_number'];
            $orderTrack->tracking_type   = $update['tracking_type'];
            $orderTrack->api_type        = $update['api_type'];

            return self::createTrackTask($orderTrack);
        }

        // 只修改运单类型
        if ($update['tracking_type'] != $orderTrack->tracking_type) {
            $update['is_repeat'] = $orderTrack->is_repeat;

            if ($orderTrack->tracking_type == 'other' || $update['api_type'] != $orderTrack->api_type) {
                $orderTrack->tracking_number = $update['tracking_number'];
                $orderTrack->tracking_type   = $update['tracking_type'];
                $orderTrack->api_type        = $update['api_type'];

                return self::createTrackTask($orderTrack);
            }

            return self::getClassByType($orderTrack->api_type)->addTrackUpdateTask($orderTrack, $update);
        }
    }

    /**
     * 执行添加运单任务
     *
     * @param array $orderTaskList
     * @param int $apiType
     * @return mixed
     */
    public static function executeAddTrackTask(array $orderTaskList, int $apiType)
    {
        $resultList = self::getClassByType($apiType)->taskAddTrack($orderTaskList);
        $returnData = ['succes' => 0, 'failure' => 0];

        foreach ($orderTaskList as $value) {
            // 更新
            $contents = json_decode($value['content'], true);
            $update   = [
                'status' => OrderTrackTask::STATUS_SUCCESS,
                'result' => ''
            ];

            if (empty($contents) || !isset($contents['tracking_number'])) {
                $update['status'] = OrderTrackTask::STATUS_INVALID;
                $update['result'] = '任务内容错误';
            } else {
                $trackData = isset($resultList['success'][$contents['tracking_number']]) ? $resultList['success'][$contents['tracking_number']] : [];
                $errorData = isset($resultList['error'][$contents['tracking_number']]) ? $resultList['error'][$contents['tracking_number']] : [];
            }

            if (empty($trackData)) {
                $returnData['failure']++;

                if (!empty($errorData)) {
                    $update['result'] = json_encode($errorData);
                } else {
                    $update['status'] = OrderTrackTask::STATUS_INVALID;
                }
            } else {
                $returnData['succes']++;
            }

            OrderTrackTask::where('id', $value['id'])->update($update);
        }

        return $returnData;
    }

    /**
     * 执行更新运单任务
     *
     * @param array $orderTaskList
     * @param int $apiType
     * @return mixed
     */
    public static function executeUpdateTrackTask(array $orderTaskList, int $apiType)
    {
        $resultList = self::getClassByType($apiType)->taskUpdateTrack($orderTaskList);
        $returnData = ['succes' => 0, 'failure' => 0];

        foreach ($orderTaskList as $value) {
            $trackData = isset($resultList['success'][$value['id']]) ? $resultList['success'][$value['id']] : [];
            $errorData = isset($resultList['error'][$value['id']]) ? $resultList['error'][$value['id']] : [];

            // 更新
            $update = [
                'status' => OrderTrackTask::STATUS_SUCCESS,
                'result' => ''
            ];

            if (empty($trackData)) {
                $returnData['failure']++;

                if (!empty($errorData)) {
                    $update['result'] = json_encode($errorData);
                } else {
                    $update['status'] = OrderTrackTask::STATUS_INVALID;
                }
            } else {
                $returnData['succes']++;
            }

            OrderTrackTask::where('id', $value['id'])->update($update);
        }

        return $returnData;
    }

    /**
     * 运单追踪
     *
     * @param Collection $orderTrackList
     * @return array
     */
    public static function track(Collection $orderTrackList)
    {
        if (empty($orderTrackList)) {
            return ['error' => true, 'msg' => '运单信息不存在!'];
        }

        $orderTrackData  = current($orderTrackList->toArray());
        $apiTrackResults = self::getClassByType($orderTrackData['api_type'])->track($orderTrackList->toArray());

        if ($apiTrackResults['error']) {
            return ['error' => true, 'msg' => $apiTrackResults['msg']];
        }

        $apiTrackList = isset($apiTrackResults['data']) ? $apiTrackResults['data'] : array();

        if (empty($apiTrackList)) {
            return ['error' => true, 'msg' => 'api运单追踪无物流信息!'];
        }

        // 有效期限制
        $dateExpire = date('Y-m-d H:i:s', strtotime("-3 month"));
        $orderIds   = [];

        // 业务逻辑处理
        foreach ($orderTrackList as $track) {
            $trackingNumberList = explode(',', $track->tracking_number);
            $trackList          = [];
            $statusList         = [];
            $status             = OrderTrack::API_RESULT_STATUS_LAUNCHED;

            foreach ($trackingNumberList as $trackingNumber) {
                $contentList = isset($apiTrackList[$trackingNumber]) ? $apiTrackList[$trackingNumber] : [];

                if (empty($contentList)) {
                    (new OrderTrackTask())->insert(self::createTrackTask($track));
                    continue;  // 暂无物流信息
                }

                $content                    = !empty($contentList['api_result_content']) ? $contentList['api_result_content'] : [];
                $content['tracking_number'] = $trackingNumber;
                $trackList[]                = $content;
                $statusList[]               = $contentList['api_result_status'];
            }

            // 去重
            array_unique($statusList);

            if (count($statusList) == 1) {
                $status = current($statusList);
            }

            if ($status == OrderTrack::API_RESULT_STATUS_DELIVERED) {
                $track->is_delivered = '1';
            }

            if (in_array($status, array(OrderTrack::API_RESULT_STATUS_DELIVERED, OrderTrack::API_RESULT_STATUS_LAUNCHED))) {
                $track->is_activated = '1';  // 更新生效状态
                $statusDate          = date('Y-m-d H:i:s', strtotime('-30 days'));

                if ($track->created_at > $statusDate || $track->is_delivered == '1') {
                    $orderIds[] = $track->order_id;
                }
            }

            if ($status != OrderTrack::API_RESULT_STATUS_DELIVERED && $dateExpire >= $track->created_at) {  // 有效期判断
                $status = OrderTrack::API_RESULT_STATUS_EXPIRED;
            }

            // 更新数据
            $track->api_result_count   = $track->api_result_count + 1;
            $track->api_result_code    = 200;
            $track->api_result_date    = now();
            $track->api_result_content = json_encode($trackList);
            $track->api_result_status  = $status;

            $track->save();
        }

        // todo 删除物流警示、发送运单确认邮件
    }

    /**
     * 取消妥投
     * @param int $orderId
     * @return array|void
     */
   public static function cancelDelivered(int $orderId) {
       $query = OrderTrack::find($orderId);

       if (empty($query)) {
           return ['error' => true, 'msg' => '运单信息不存在!'];
       }

       if ($query->is_delivered != OrderTrack::IS_DELIVERED_DELIVERED) {
           return ['error' => true, 'msg' => '运单信息' . OrderTrack::$isDeliveredMap[$query->is_delivered]];
       }

       $query->api_result_status = 0;
       $query->is_delivered = 0;
       $query->is_activated = 0;
       $query->api_result_date = now();
       if (!$query->save()) {
           return ['error' => true, 'msg' => '取消妥投失败!'];
       }
   }

    /**
     * 获取加载的接口类
     *
     * @param int $apiType
     * @return mixed
     */
    private static function getClassByType(int $apiType)
    {
        $trackType = isset(DirectoryCarrier::$typeMap[$apiType]) ? DirectoryCarrier::$typeMap[$apiType] : DirectoryCarrier::$typeMap[0];

        return Track::$trackType();
    }
}
