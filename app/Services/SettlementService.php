<?php
namespace App\Services;

use App\Classes\Pay\Log;
use App\Jobs\SendSlsLog;
use App\Models\Channel;
use App\Models\DirectoryCurrency;
use App\Models\MerchantBusiness;
use App\Models\OrderSettlement;
use Dcat\Admin\Admin;
use App\Models\Merchant;
use App\Models\ChannelOrderUprate;
use App\Models\ChannelParitiesUprate;
use App\Models\MerchantBusinessHistory;
use App\Models\Order;
use DateTime;
use DES3;

class SettlementService
{
    /**
     * 计算结算日期
     *
     * @param $businessId
     * @return false|string
     */
    public static function getSettleDate($businessId)
    {
        // todo 节假日待后续添加
        $date     = date('Y-m-d');
        $business = MerchantBusiness::find($businessId);

        if (empty($business)) {
            Log::error('结算日期计算', ['BID信息不存在' . $businessId]);
            dispatch(new SendSlsLog(
                ['message' => '结算日期计算，BID信息不存在' . $businessId],
                [],
                'error',
            ));

            return $date;
        }
        //周期0默认不结算
        if ($business->settle_cycle_cc == 0) {
            return get_default_date();
        }
        // 获取结算日
        $settleDayList = json_decode($business->settle_day, true) ?? [1, 2, 3, 4, 5];

        // 获取不属于节假日的结算时间
        $i = 1;

        while ($i <= $business->settle_cycle_cc) {
            $date = date('Y-m-d', strtotime("{$date} +1 day"));
            $i++;
        }

        // 获取属于结算日的结算时间
        if (!empty($settleDayList)) {
            while (!in_array(date('w', strtotime($date)), $settleDayList)) {
                $date = date('Y-m-d', strtotime("{$date} +1 day"));
            }
        } else {
            // 添加错误日志
            Log::error('结算日期计算', [sprintf('%s : 商户结算日期列表为空，获取商户结算日期 Error', $business->business_id)]);
            dispatch(new SendSlsLog(
                ['message' => '结算日期计算'],
                [sprintf('%s : 商户结算日期列表为空，获取商户结算日期 Error', $business->business_id)],
                'error',
            ));
        }

        return $date;
    }

    /**
     * 获取结算金额
     *
     * @param $order
     * @return array
     */
    public static function getSettleAmount($order, $cardNumber = '')
    {
        if (!empty($cardNumber)) {
            $cardNumber = DES3::decrypt($cardNumber);
        }

        $channel = Channel::with('channelPid')->firstWhere('id', $order->channel_id);
        $settle  = [];

        // 判定支付币种是否在渠道结算列表中
        $settleCurrency = $order->currency;

        if (!empty($channel->channelPid)
            && !empty($channel->channelPid->settle_currencies)
            && !empty($channel->channelPid->settle_default_currency)) {
            $channelSettleCurrencyList = explode(',', $channel->channelPid->settle_currencies);
            $settleCurrency            = in_array($order->currency, $channelSettleCurrencyList) ? $order->currency : $channel->channelPid->settle_default_currency;
        }

        // BID 信息
        $business = MerchantBusiness::find($order->business_id);

        // 判定渠道结算币种是否在商户结算列表中
        $settleCurrencyList        = explode(',', $business->settle_currencies);
        $settle['settle_currency'] = in_array($settleCurrency, $settleCurrencyList) ? $settleCurrency : $business->settle_default_currency;

        // 获取货币汇率
        $currencyRate        = DirectoryCurrency::firstWhere('code', $order->currency);
        $settleCurrencyRate  = DirectoryCurrency::firstWhere('code', $settle['settle_currency']);

        // 获取加价使用比例
        $paritiesUprate   = 1.00;
        $merchantBusiness = MerchantBusiness::firstWhere('business_id', $order->business_id);

        $tempData  = [
            'card_number' => $cardNumber,
            'business_id' => $order->business_id,
            'merchant_id' => $order->merchant_id,
        ];
        if ($merchantBusiness && SettlementService::priceIncreaseJudgment($tempData)) {
            $type                           = in_array($order->type, [OrderSettlement::TYPE_REFUND, OrderSettlement::TYPE_CHARGEBACK]) ? '-' : '+';
            $merchantBusinessParitiesUprate = $merchantBusiness->parities_uprate;
            $tempSupplier                   = Channel::where('id', $order->channel_id)->first();

            if ($tempSupplier) {
                $tempChannelParitiesUprate = ChannelParitiesUprate::where('channel_supplier_id', $tempSupplier->channel_supplier_id)
                    ->where('currency_before', $order->currency)
                    ->where('currency_after', $settle['settle_currency'])
                    ->first();

                if ($tempChannelParitiesUprate) {
                    $channelParitiesUprate = $tempChannelParitiesUprate->uprate;

                    if ($merchantBusinessParitiesUprate > 0.00 && $channelParitiesUprate > 0.00) {
                        $uprate = $merchantBusinessParitiesUprate * $channelParitiesUprate / 10000.00;

                        if ($type == '+') {
                            $paritiesUprate = 1.00 - $uprate;
                        } else {
                            $paritiesUprate = 1.00 + $uprate;
                        }
                    }
                }
            }
        }

        // 订单金额转美元（USD）
        if ($order->currency == 'USD') {
            $settle['amount_usd'] = $order->amount;
        } else {
            $settle['amount_usd'] = amount_format($order->amount / $currencyRate->rate, 2);
        }

        // 初始化
        $settle['payment_amount_usd'] = $settle['amount_usd'];

        if (isset($order->payment_currency) && isset($order->payment_amount_usd)) {
            // 渠道金额转美元（USD）
            if ($order->payment_currency == 'USD') {
                $settle['payment_amount_usd'] = $order->payment_amount_usd;
            } else {
                $paymentCurrencyRate          = DirectoryCurrency::firstWhere('code', $order->payment_currency);
                $settle['payment_amount_usd'] = amount_format($order->payment_amount_usd / $paymentCurrencyRate->rate, 2);
            }
        }

        // 计算商户结算金额
        if ($settle['settle_currency'] == $order->currency) {
            $settle['rate']          = '1.0000';
            $settle['settle_amount'] = $order->amount;
        } else {
            $settle['rate']          = amount_format($settleCurrencyRate->rate / $currencyRate->rate * $paritiesUprate, 4);
            $settle['settle_amount'] = amount_format($order->amount * $settle['rate'], 2);
        }

        return $settle;
    }

    /**
     * @param OrderSettlement $orderSettlement
     * @param array $input
     * @param string $blendBy
     */
    public static function blend(OrderSettlement $orderSettlement, array $input, $blendBy = 'system')
    {
        // 计算到账数据
        $blendData = [
            'is_blend'                => $orderSettlement->blend_status,
            'merchant_id'             => $orderSettlement->merchant_id,
            'business_id'             => $orderSettlement->business_id,
            'channel_id'              => $orderSettlement->channel_id,
            'currency'                => $orderSettlement->currency,
            'amount'                  => $orderSettlement->amount,
            'settle_currency'         => $orderSettlement->settle_currency,
            'settle_amount'           => $orderSettlement->settle_amount,
            'payment_currency'        => $orderSettlement->payment_currency,
            'payment_amount'          => $orderSettlement->payment_amount,
            'payment_settle_currency' => $orderSettlement->payment_settle_currency,
            'payment_settle_amount'   => $orderSettlement->payment_settle_amount,
            'arrival_currency'        => $input['arrival_currency'],
            'arrival_amount'          => $input['arrival_amount'],
            'type'                    => in_array($orderSettlement->type, [OrderSettlement::TYPE_REFUND, OrderSettlement::TYPE_CHARGEBACK]) ? '-' : '+'
        ];

        if (in_array($orderSettlement->type, [OrderSettlement::TYPE_SALE, OrderSettlement::TYPE_AUTH])) {
            $orderId = $orderSettlement->order_id;
        } else {
            $orderId = $orderSettlement->parent_order_id;
        }

        $order = Order::with(['card'])->find($orderId);
        if (!empty($order)) {
            $blendData['card_number'] = $order->card->card_number;
        }

        // 更新数组
        $tempData = self::calculateSettle($blendData);
        $data     = [
            'settle_currency'         => $tempData['settle_currency'],
            'settle_amount'           => $tempData['settle_amount'],
            'payment_settle_currency' => $tempData['payment_settle_currency'],
            'payment_settle_amount'   => $tempData['payment_settle_amount'],
            'rate'                    => $tempData['rate'],
            'arrival_currency'        => $tempData['arrival_currency'],
            'arrival_amount'          => $tempData['arrival_amount'],
            'arrival_status'          => OrderSettlement::ARRIVAL_STATUS_SUCCESS,
            'arrival_at'              => now()->toDateTimeString()
        ];

        if (!$orderSettlement->blend_status) {
            $data['blend_status'] = OrderSettlement::BLEND_STATUS_SUCCESS;
            $data['blend_by']     = !empty(Admin::user()) ? Admin::user()->name : $blendBy;
            $data['blend_at']     = get_settle_date();

            // 退款需要添加结算日期
            if ($orderSettlement->type == OrderSettlement::TYPE_REFUND) {
                $data['settle_at'] = $data['blend_at'];
            }
        }

        // TODO 待后续添加拒付到账数据

        // 到账更新
        $orderSettlement->update($data);
    }

    /**
     * 勾兑结算金额
     *
     * @param $blendData
     * @return array
     */
    public static function calculateSettle($blendData)
    {
        if ($blendData['arrival_currency'] == $blendData['payment_currency']) {
            $blendData['arrival_amount'] = $blendData['payment_amount'];
        }

        // 初始化返回数组
        $data = [
            'currency'                => $blendData['currency'],
            'amount'                  => $blendData['amount'],
            'settle_currency'         => $blendData['settle_currency'],
            'settle_amount'           => $blendData['settle_amount'],
            'payment_currency'        => $blendData['payment_currency'],
            'payment_amount'          => $blendData['payment_amount'],
            'payment_settle_currency' => $blendData['payment_settle_currency'],
            'payment_settle_amount'   => $blendData['payment_settle_amount'],
            'arrival_currency'        => $blendData['arrival_currency'],
            'arrival_amount'          => $blendData['arrival_amount'],
            'rate'                    => '1.0000'
        ];

        if (!$blendData['is_blend'] && $blendData['settle_currency'] != $blendData['currency']) {
            // 获取货币汇率
            $paymentSettleCurrencyRate = DirectoryCurrency::firstWhere('code', $data['payment_settle_currency']);
            $settleCurrencyRate        = DirectoryCurrency::firstWhere('code', $data['settle_currency']);

            // 获取加价使用比例
            $orderUprate      = 1.00;
            $paritiesUprate   = 1.00;
            $merchantBusiness = MerchantBusiness::firstWhere('business_id', $blendData['business_id']);

            $cardNumber = '';
            if (isset($blendData['card_number'])) {
                $cardNumber = DES3::decrypt($blendData['card_number']);
            }

            $tempData  = [
                'card_number' => $cardNumber,
                'business_id' => $blendData['business_id'],
                'merchant_id' => $blendData['merchant_id'],
            ];
            if ($merchantBusiness && SettlementService::priceIncreaseJudgment($tempData)) {
                $merchantBusinessOrderUprate    = $merchantBusiness->order_uprate;
                $merchantBusinessParitiesUprate = $merchantBusiness->parities_uprate;
                $tempSupplier                   = Channel::where('id', $blendData['channel_id'])->first();
                if ($tempSupplier) {
                    $tempChannelOrderUprate = ChannelOrderUprate::where('channel_supplier_id', $tempSupplier->channel_supplier_id)
                        ->where('currency', $data['currency'])
                        ->first();

                    $tempChannelParitiesUprate = ChannelParitiesUprate::where('channel_supplier_id', $tempSupplier->channel_supplier_id)
                        ->where('currency_before', $data['payment_settle_currency'])
                        ->where('currency_after', $data['settle_currency'])
                        ->first();

                    if ($tempChannelOrderUprate) {
                        $channelOrderUprate = $tempChannelOrderUprate->uprate;

                        if ($merchantBusinessOrderUprate > 0.00 && $channelOrderUprate > 0.00) {
                            $uprate = $merchantBusinessOrderUprate * $channelOrderUprate / 10000.00;

                            if ($blendData['type'] == '+') {
                                $orderUprate = 1.00 + $uprate;
                            } else {
                                $orderUprate = 1.00 - $uprate;
                            }
                        }
                    }

                    if ($tempChannelParitiesUprate) {
                        $channelParitiesUprate = $tempChannelParitiesUprate->uprate;

                        if ($merchantBusinessParitiesUprate > 0.00 && $channelParitiesUprate > 0.00) {
                            $uprate = $merchantBusinessParitiesUprate * $channelParitiesUprate / 10000.00;

                            if ($blendData['type'] == '+') {
                                $paritiesUprate = 1.00 - $uprate;
                            } else {
                                $paritiesUprate = 1.00 + $uprate;
                            }
                        }
                    }
                }
            }

            // 将渠道结算金额转化成美元
            $tempAmount   = $data['payment_settle_amount'] / $paymentSettleCurrencyRate->rate / $orderUprate;
            $settleAmount = amount_format($tempAmount * $settleCurrencyRate->rate * $paritiesUprate, 2);

            if ($blendData['type'] == '+') {
                $data['settle_amount'] = min($settleAmount, $blendData['settle_amount']);
            } else {
                $data['settle_amount'] = max($settleAmount, $blendData['settle_amount']);
            }

            // 修改汇率
            $data['rate'] = amount_format($data['settle_amount'] / $data['amount'], 4);
        }

        return $data;
    }

    /**
     * 计算延迟结算日期
     *
     * @param $businessId
     * @return false|string
     */
    public static function getDelaySettleDates($businessId)
    {
        // todo 节假日待后续添加
        $date     = date('Y-m-d', strtotime("+1 day"));
        $business = MerchantBusiness::find($businessId);

        if (empty($business)) {
            Log::error('结算日期计算', ['BID信息不存在' . $businessId]);
            dispatch(new SendSlsLog(
                ['message' => '结算日期计算， BID信息不存在' . $businessId],
                [],
                'error',
            ));

            return date('Y-m-d', strtotime($date . '+1 day'));
        }

        $settleDayList = json_decode($business->settle_day, true) ?? [1, 2, 3, 4, 5];

        // 获取属于结算日的结算时间
        if (!empty($settleDayList)) {
            while (!in_array(date('w', strtotime($date)), $settleDayList)) {
                $date = date('Y-m-d', strtotime("{$date} +1 day"));
            }
        } else {
            // 添加错误日志
            Log::error('结算日期计算', [sprintf('%s : 商户结算日期列表为空，获取商户结算日期 Error', $business->business_id)]);
            dispatch(new SendSlsLog(
                ['message' => '结算日期计算'],
                [sprintf('%s : 商户结算日期列表为空，获取商户结算日期 Error', $business->business_id)],
                'error',
            ));

            return date('Y-m-d', strtotime($date . '+1 day'));
        }

        return $date;
    }

    /**
     * 计算保证金预计返回时间
     * @param string $businessId
     * @param string $order_completed_at
     * @param string $settleDate
     * @param string $cc_type
     * @return string
     */
    public static function getDepositSettleDates($businessId, $order_completed_at, $settleDate, $cc_type)
    {
        $date     = $settleDate;
        $business = MerchantBusiness::find($businessId);

        if (empty($business)) {
            Log::error('结算日期计算', ['BID信息不存在' . $businessId]);
            dispatch(new SendSlsLog(
                ['message' => '结算日期计算，BID信息不存在' . $businessId],
                [],
                'error',
            ));

            return $date;
        }

        //信用卡结算周期为0直接返回
        if ($business->settle_cycle_cc == 0) {
            return $date;
        }

        //处理保证金数据
        $tempDeposit = [];
        if (!empty($business['deposit'])) {
            foreach ($business['deposit'] as $deposit) {
                $tempDeposit[$deposit['deposit_type']][$deposit['deposit_card']] = $deposit;
            }

            $deposit = $tempDeposit[MerchantBusiness::DEPOSIT_TYPE_ACTIVITY][$cc_type] ?? $tempDeposit[MerchantBusiness::DEPOSIT_TYPE_ACTIVITY]['*'] ?? [];

            if (!empty($deposit) && isset($deposit['deposit_cycle']) && $deposit['deposit_cycle'] > 0 && !empty($order_completed_at)) {
                // 有循环保证金 并且周期大于 0
                $date = date('Y-m-d', strtotime("{$order_completed_at} + {$deposit['deposit_cycle']} days"));
            }
        }

        return $date;
    }

    /**
     * 计算退款，拒付，拒付申述结算日期
     * @param string $businessId
     * @return string
     */
    public static function getNonTradingSettleDate($businessId)
    {
        $date     = get_settle_date();
        $business = MerchantBusiness::select('settle_cycle_cc')->find($businessId);

        if (empty($business)) {
            Log::error('退款，拒付，拒付申述结算日期计算', ['BID信息不存在' . $businessId]);
            dispatch(new SendSlsLog(
                ['message' => '退款，拒付，拒付申述结算日期计算， BID信息不存在' . $businessId],
                [],
                'error',
            ));
            return $date;
        }

        if ($business->settle_cycle_cc == 0) {
            $date = get_default_date();
        }

        return $date;
    }

    /**
     * 获取当前生效历史条款id
     * @param string $businessId
     * @return string
     */
    public static function getBusinessHistoryId($businessId): string
    {
        return MerchantBusinessHistory::where([
            'business_id' => $businessId,
            'is_effect'   => MerchantBusinessHistory::TAKE_EFFECT,
        ])->orderByDesc('take_effect_at')->limit(1)->value('id') ?? '0';
    }

    /**
     * 获取原始订单生效历史条款id
     * @param string $businessId
     * @return string
     */
    public static function getOrderBusinessHistoryId($orderId): string
    {
        return OrderSettlement::where('order_id', $orderId)->limit(1)->value('business_history_id') ?? '0';
    }

    public static function mixDate($date1, $date2)
    {
        $date1 = new DateTime($date1);
        $date2 = new DateTime($date2);
        // 使用 min 函数来获取两者间的最小日期
        $minDate = min($date1, $date2);

        // 输出最小日期
        return $minDate->format('Y-m-d');
    }

    public static function maxDate($date1, $date2)
    {
        $date1 = new DateTime($date1);
        $date2 = new DateTime($date2);
        // 使用 max 函数来获取两者间的最大日期
        $maxDate = max($date1, $date2);

        // 输出最大日期
        return $maxDate->format('Y-m-d');
    }

    public static function priceIncreaseJudgment($data)
    {
        if (
            (in_array($data['card_number'], ['***************', '***************', '****************', '****************'])
                && in_array($data['business_id'], ['168612884744097007', '164791825957292007', '168612884744097004']))
            || (in_array($data['card_number'], ['****************', '****************', '****************','***************']) && in_array($data['merchant_id'], ['168612884744097']))
            || (in_array($data['card_number'], ['****************', '****************','***************']) && in_array($data['merchant_id'], ['164791825957292', '166520855232531', '160103346595807']))
        ) {
            return true;
        }

        return false;
    }
}
