<?php


namespace App\Services;

use <PERSON><PERSON>_Log_Client;
use <PERSON>yun_Log_Exception;
use <PERSON>yun_Log_Models_LogItem;
use Aliyun_Log_Models_PutLogsRequest;

class SlsLogService
{
    protected $endpoint, $accessKey, $accessKeyId, $project, $logstore, $client, $switch;
    
    public function __construct()
    {
        $this->endpoint    = env('SLS_ENDPOINT', '');
        $this->accessKeyId = env('SLS_ACCESS_KEY_ID', '');
        $this->accessKey   = env('SLS_ACCESS_KEY', '');
        $this->project     = env('SLS_PROJECT', '');
        $this->logstore    = env('SLS_LOGSTORE', '');
        $this->switch      = env('SLS_SWITCH', false);
        $this->client      = new Aliyun_Log_Client($this->endpoint, $this->accessKeyId, $this->accessKey);
    }
    
    public function createLog ($topic, $contents = []) {
        if (!$this->switch) {
            return;
        }
        
        $logItem = new <PERSON>yun_Log_Models_LogItem();
        $logItem->setTime($contents['time']);
        // 格式化时间
        $contents['time'] = date('Y-m-d H:i:s', $contents['time']);
        $logItem->setContents($contents);
        $logitems = [$logItem];
        
        $request  = new Aliyun_Log_Models_PutLogsRequest($this->project, $this->logstore, $topic, null, $logitems);
        
        try {
            $this->client->putLogs($request);
        } catch (Aliyun_Log_Exception $ex) {
            logger()->channel('single')->warning('Sls error', ['error' => $ex->getMessage()]);
        } catch (\Exception $ex) {
            logger()->channel('single')->warning('Sls error', ['error' => $ex->getMessage()]);
        }
    }
}
