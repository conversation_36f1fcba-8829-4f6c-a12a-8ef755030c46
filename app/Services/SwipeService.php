<?php

namespace App\Services;

use App\Classes\Pay\Contracts\Support;
use App\Classes\Supports\Traits\HasHttpRequest;
use App\Models\Merchant;
use App\Models\MerchantUrl;
use App\Models\Order;
use App\Models\CardSource;

class SwipeService
{
    use HasHttpRequest;

    /**
     * 刷卡
     *
     * @param $sourceId
     * @param $transactionUrl
     * @param $amount
     * @param string $channelId
     * @param string $productName
     * @return array
     */
    public function swipeCard($sourceId, $transactionUrl, $amount, $channelId = '', $productName = 'coupon')
    {
        $result = array('error' => false, 'msg' => array());

        // 获取卡信息
        $cardSource = CardSource::with('cardInfo')->find($sourceId);

        if (empty($cardSource) || $cardSource->used_times >= $cardSource->limite_times) {
            $result['error'] = true;
            $result['msg'][] = '卡台账不存在或者不可用(次数超限)';

            return $result;
        }

        if ($cardSource->status != '1') {
            $result['error'] = true;
            $result['msg'][] = '卡台账状态不合法!';

            return $result;
        }

        if (empty($cardSource->cardInfo)) {
            $result['error'] = true;
            $result['msg'][] = '卡信息不存在';

            return $result;
        }

        $businessId = config('swipe.bid');
        // 获取商户网址信息
        $merchantUrl = MerchantUrl::where('business_id', $businessId)
        ->where('url_status', MerchantUrl::STATUS_UP)
        ->where('url_name', $transactionUrl)->first();

        if (empty($merchantUrl)) {
            $result['error'] = true;
            $result['msg'][] = '网址信息不合法';

            return $result;
        }

        $reqReserved = json_encode(array(
            'transaction_amount' => $amount,
            'source_id'          => $sourceId,
            'channel_id'         => empty($channelId) ? 0 : $channelId
        ));

        // 验证商户信息
        $merchant = Merchant::where('status', Merchant::STATUS_ENABLE)->find($merchantUrl->merchant_id);

        if (empty($merchant)) {
            $result['error'] = true;
            $result['msg'][] = '商户信息不合法';

            return $result;
        }

        $orderNumber = Order::findAvailableNo();

        $data = [
            'merchantId'      => $merchantUrl->merchant_id,
            'businessId'      => $merchantUrl->business_id,
            'orderNumber'     => $orderNumber,
            'transactionType' => 'sale',
            'urlName'         => $merchantUrl->url_name,
            'currency'        => 'USD',
            'amount'          => Support::amount_format($amount),
            'card'            => [
                'cardNumber'      => $cardSource->card_number,
                'expirationMonth' => $cardSource->expiration_month,
                'expirationYear'  => $cardSource->expiration_year,
                'cvv'             => $cardSource->cvv,
            ],
            'address'         => [
                'billFirstName'    => $cardSource->cardInfo->first_name,
                'billLastName'     => $cardSource->cardInfo->last_name,
                'billEmail'        => $cardSource->cardInfo->email,
                'billAddress'      => $cardSource->cardInfo->address,
                'billCity'         => $cardSource->cardInfo->city,
                'billState'        => $cardSource->cardInfo->state,
                'billPostcode'     => $cardSource->cardInfo->post_code,
                'billCountryIsoa2' => $cardSource->cardInfo->country,
                'billPhone'        => $cardSource->cardInfo->phone,
                'shipFirstName'    => $cardSource->cardInfo->first_name,
                'shipLastName'     => $cardSource->cardInfo->last_name,
                'shipEmail'        => $cardSource->cardInfo->email,
                'shipAddress'      => $cardSource->cardInfo->address,
                'shipCity'         => $cardSource->cardInfo->city,
                'shipState'        => $cardSource->cardInfo->state,
                'shipPostcode'     => $cardSource->cardInfo->post_code,
                'shipCountryIsoa2' => $cardSource->cardInfo->country,
                'shipPhone'        => $cardSource->cardInfo->phone,
                'ip'               => $cardSource->cardInfo->ip,
            ],
            'product'         => [
                [
                    'name'  => $productName,
                    'price' => '12.00',
                    'qty'   => '1',
                    'url'   => 'www.coupon.com',
                    'sku'   => 'coupon',
                ]
            ],
            'attach'          => $reqReserved,
            'notifyUrl'       => '',
            'returnUrl'       => \route('api.v1.swipeResult'),
        ];

        $data['returnUrl'] = str_replace('https://client', 'https://www', $data['returnUrl']);
        $url               = route('api.v1.orders.index');
        $url               = str_replace('https://client', 'https://www', $url);
        $headers           = ['headers' => ['Authorization' => 'Bearer ' . $merchant->api_token]];
        $tempResult        = '';

        try {
            $tempResult = $this->post($url, $data, $headers);
        } catch (\Exception $e) {
            $result['error'] = true;
            $result['msg'][] = '刷单出错';
            $result['msg'][] = $e->getMessage();
        }

        if (empty($tempResult) || isset($tempResult['errors'])) {
            $result['error'] = true;
            $result['msg'][] = '刷单出错';
            $result['msg'][] = $tempResult;

            return $result;
        }

        if (!empty($tempResult)) {
            if (isset($tempResult['status']) && $tempResult['status'] == Order::STATUS_APPROVED) {
                $cardSource->last_transaction_amount = $data['amount'];
                $cardSource->available_amount        = $cardSource->available_amount - $data['amount'];
                $cardSource->used_times              = $cardSource->used_times + 1;
                $cardSource->total_used_times        = $cardSource->total_used_times + 1;
                $result['msg'][]                     = '成功';

                if($cardSource->available_amount == 0) {
                    $cardSource->status = CardSource::STATUS_DISABLE;
                }
            } else {
                if (isset($tempResult['code']) && $tempResult['code'] == get_system_code('084')) { // 余额不足
                    $cardSource->status = CardSource::STATUS_DISABLE;
                } else {
                    $cardSource->status = CardSource::STATUS_SUSPEND;
                }
            }

            $cardSource->date_last_time = now();
            $cardSource->save();
        }

        return $result;
    }
}
