<?php
namespace App\Services;

use App\Classes\Pay\Contracts\Support;
use App\Models\Order;
use App\Models\Refund;
use App\Models\ChargebackHistory;
use App\Models\DirectoryCurrency;

class TransactionService
{
	/**
	 * 计算可退款金额
	 * @param $order
	 * @return float|int
	 */
	public static function getAvailableRefundAmount(Order $order)
	{
		$totalRefundAmount = 0.00;

		if ($order->refund) {
			$refundArr         = $order->refund->whereIn('status', [Refund::STATUS_APPROVED, Refund::STATUS_REVIEW, Refund::STATUS_RECEIVED, Refund::STATUS_PENDING])->pluck('amount', 'refund_id')->toArray();
			$totalRefundAmount = array_sum($refundArr);
		}

		$chargebackAmount = self::getChargebackAmount($order);

		return Support::amount_format($order->amount - $totalRefundAmount - $chargebackAmount);
	}

	/**
	 * 计算拒付金额
	 * @param $order
	 * @return float|int
	 */
	public static function getChargebackAmount(Order $order)
	{
		$chargebackAmount = 0.00;

		if (!$order->relation['is_chargeback']) {
			return $chargebackAmount;
		}

		$chargeback = $order->chargeback->where("use_flag",0)->toArray();

        if (!count($chargeback)) {
			return $chargebackAmount;
		}

		$chargebackHistory = ChargebackHistory::where('order_id', $order->order_id)
			->where('chargeback_id', current($chargeback)['chargeback_id'])
			->first(['currency', 'amount']);

        if (!$chargebackHistory) {
            return $chargebackAmount;
        }

		if ($order->currency === $chargebackHistory->currency) {
			$chargebackAmount = $chargebackHistory->amount;
		}else{
			// 拒付货币汇率转换
			$directoryCurrencyArr	= DirectoryCurrency::whereIn('code', [$order->currency, $chargebackHistory->currency])->get()->pluck('rate', 'code');
			$chargebackAmount		= Support::amount_format($chargebackHistory->amount * ($directoryCurrencyArr[$order->currency] / $directoryCurrencyArr[$chargebackHistory->currency]));
		}

		return $chargebackAmount;
	}
}