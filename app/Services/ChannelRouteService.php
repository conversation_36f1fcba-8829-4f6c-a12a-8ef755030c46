<?php

namespace App\Services;

use Dcat\Admin\Admin;

class ChannelRouteService
{

    /**
     * 路由弹窗适配手机
     * @return void
     */
    public static function loadPhoneJs(): void
    {
        Admin::script(
            <<<JS
                (()=>{
                    if (isMobile()) {
                        var mengvalue  = -1;
                        var phoneWidth = parseInt(window.screen.width);
                        var phoneScale = phoneWidth / 750;

                        var ua = navigator.userAgent;
                        if (/Android (\d+\.\d+)/.test(ua)) {
                            var version = parseFloat(RegExp.$1);
                            if (version > 2.3) {
                                $("head").append('<meta name="viewport" content="width=800, minimum-scale = ' + phoneScale + ', maximum-scale = ' + phoneScale + ', target-densitydpi=device-dpi">') 
                            } else {
                                $("head").append('<meta name="viewport" content="width=800, target-densitydpi=device-dpi">')
                            }
                        } else {
                            $("head").append('<meta name="viewport" content="width=800, user-scalable=no, target-densitydpi=device-dpi">')
                        }
                    }
                })();
                function isMobile() {
                    var userAgentInfo = navigator.userAgent;
                    var mobileAgents  = ["micromessenger", "Android", "iPhone", "SymbianOS", "Windows Phone", "iPad", "iPod", "BlackBerry", "IEMobile", "Opera Mini"];
                    var mobileFlag   = false;
                
                    for (var v = 0; v < mobileAgents.length; v++) {
                        if (userAgentInfo.indexOf(mobileAgents[v]) > 0) {
                            mobileFlag = true;
                            break;
                        }
                    }
                    var screenWidth  = window.screen.width;
                    var screenHeight = window.screen.height;

                    if (screenWidth > 325 && screenHeight < 750) {
                        mobileFlag = true;
                    }

                    return mobileFlag;
                }
            JS
        );
    }

    /**
     * 路由弹窗适配手机
     * @return void
     */
    public static function loadPhonePopupJs($formId = 1): void
    {
        Admin::style(
            <<<css
                @media screen and (max-width: 1200px) { 
                    .copy {display: none} 
                    .layui-layer.layui-layer-page {height:60% !important;}
                    #{$formId} {margin-top:200px}
                    .select2.select2-container.select2-container--default {min-width:200px!important;max-width:200px!important;}
                    .select2-selection__rendered {padding-left: 0px !important;padding-right: 0px !important;}
                    .layui-layer-setwin .layui-layer-close1:before {font-size: 2rem;}
                    .layui-layer-setwin .layui-layer-close1 {margin-right: 20px;}
                } 
            css
        );
        Admin::script(
            <<<JS
                (()=>{
                    if (isMobile()) {
                        let maxOb     = document.getElementsByClassName("layui-layer-max");
                        let maxlength = maxOb.length - 1;
                        let minOb     = document.getElementsByClassName("layui-layer-min");
                        let minLength = minOb.length - 1;
                        
                        maxOb[maxlength].hidden = true;
                        minOb[minLength].hidden = true;
                    }
                })();
                function isMobile() {
                    var userAgentInfo = navigator.userAgent;
                    var mobileAgents  = ["micromessenger", "Android", "iPhone", "SymbianOS", "Windows Phone", "iPad", "iPod", "BlackBerry", "IEMobile", "Opera Mini"];
                    var mobileFlag   = false;
                
                    for (var v = 0; v < mobileAgents.length; v++) {
                        if (userAgentInfo.indexOf(mobileAgents[v]) > 0) {
                            mobileFlag = true;
                            break;
                        }
                    }
                    var screenWidth  = window.screen.width;
                    var screenHeight = window.screen.height;

                    if (screenWidth > 325 && screenHeight < 750) {
                        mobileFlag = true;
                    }

                    return mobileFlag;
                }
            JS
        );
    }
}
