<?php
namespace App\Services;

use App\Classes\Pay\Contracts\Support;
use App\Classes\Supports\Log;
use App\Classes\Supports\Logger;
use App\Jobs\SendSlsLog;
use App\Models\ChargebackCase as ChargebackCaseModel;
use App\Models\DirectoryCurrency;
use App\Models\DirectoryDictionary as Dictionary;
use App\Classes\Supports\Traits\HasHttpRequest;
use App\Http\Controllers\Traits\PaymentController;
use App\Models\Merchant;
use App\Models\Order;
use App\Models\OrderRelation;
use App\Models\PaymentRefund;
use App\Models\Refund;
use Illuminate\Support\Facades\Cache;

class ShieldService
{
    use PaymentController;
    use HasHttpRequest;

    protected $noticeConfig = [];
    protected $supplierName = '';

    public function __construct()
    {
        if (empty($this->supplierName)) {
            return;
        }

        $log    = new Logger();
        $config = ['file' => storage_path('logs/' . $this->supplierName . 'Notice.log')];
        $log->setConfig($config);
        Log::setInstance($log);

        //设置回复渠道配置
        $noticeConfig = Cache::get('Risk_Config:' . $this->supplierName);

        if (empty($noticeConfig)) {
            $noticeConfig = Dictionary::where('type', '=', $this->supplierName . '_config')->pluck('remarks', 'name');
            //存储回复配置缓存
            Cache::add('Risk_Config:' . $this->supplierName, $noticeConfig, 24 * 3600);
        }

        $this->noticeConfig = $noticeConfig;
    }

    /**
     * 接收预警：rdr发起退款
     *
     * @param $order
     * @param $availableAmount
     * @return array
     */
    public function rdrRefund($order, $availableAmount): array
    {
        $return = [
            'is_refund'  => 0, //0未完全退款 1完全退款
            'result'     => ChargebackCaseModel::NOT_REFUNDED,
            'returnData' => [],
            'remarks'    => ''
        ];

        if ($order->relation->is_refund == OrderRelation::IS_REFUND_FULL) {
            $return['is_refund'] = 1;
            $return['result']    = ChargebackCaseModel::REFUNDED;
            $return['remarks']   = '需要人工确认是否有退款中';

            //订单已完全退款则填充退款信息
            $refundArr = $order->refund->sortByDesc('completed_at')->whereIn('status', [Refund::STATUS_APPROVED, Refund::STATUS_REVIEW])->first()->toArray();

            $return['returnData']['refundNo']       = $refundArr['refund_id'];
            $return['returnData']['refundDate']     = $refundArr['completed_at'];
            $return['returnData']['refundAmount']   = $order->paymentOrder->amount;
            $return['returnData']['refundCurrency'] = $order->paymentOrder->currency;
        } elseif ($order->relation->is_chargeback) {
            $return['is_refund'] = 1;
            $return['remarks']   = '已拒付';
        } else {
            //创建退款数据
            if ($availableAmount <= 0.00) {
                $return['remarks'] = '可退款金额为0, 请核对！';
            }
        }

        if ($return['remarks']) {
            return $return;
        }

        $refund = new Refund();
        $refund->fill([
            'refund_id'    => Order::findAvailableNo(),
            'order_id'     => $order->order_id,
            'currency'     => $order->currency,
            'amount'       => $availableAmount,
            'status'       => Refund::STATUS_RECEIVED,
            'code'         => '',
            'result'       => '',
            'remark'       => '',
            'completed_at' => now(),
        ]);

        $refund->save();

        //计算提交渠道退款金额
        if ($refund->amount == $order->amount) {
            //全额退款
            $refundAmount = $order->paymentOrder->amount;
        } else {
            //部分退款
            if ($order->currency == $order->paymentOrder->currency) {
                if ($refund->amount == $availableAmount) {
                    //同币种 剩余金额全额退款 已退款渠道交易金额
                    $refundIds                  = $order->refund->pluck('refund_id', 'refund_id');
                    $channelRefundedArr         = PaymentRefund::whereIn('refund_id', $refundIds)->get()->pluck('amount', 'id')->toArray();
                    $channelRefundedTotalAmount = array_sum($channelRefundedArr);
                    $refundAmount               = $order->paymentOrder->amount - $channelRefundedTotalAmount;
                } else {
                    //同币种 部分退款
                    $refundAmount = $refund->amount;
                }
            } else {
                // 不同币种
                if ($refund->amount == $availableAmount) {
                    //不同币种 剩余金额全额退款 已退款渠道交易金额
                    $refundIds                  = $order->refund->pluck('refund_id', 'refund_id');
                    $channelRefundedArr         = PaymentRefund::whereIn('refund_id', $refundIds)->get()->pluck('amount', 'id')->toArray();
                    $channelRefundedTotalAmount = array_sum($channelRefundedArr);
                    $refundAmount               = $order->paymentOrder->amount - $channelRefundedTotalAmount;
                } else {
                    //不同币种 部分退款
                    $directoryCurrencyArr = DirectoryCurrency::whereIn('code', [$order->currency, $order->paymentOrder->currency])->get()->pluck('rate', 'code');
                    $refundAmount         = Support::amount_format($refund->amount * ($directoryCurrencyArr[$order->paymentOrder->currency] / $directoryCurrencyArr[$order->currency]));
                }
            }
        }

        //根据币种对金额进行向上取整
        $refundAmount = $this->_roundUpAmountByCurrency($order->paymentOrder->currency, $refundAmount);

        //保存网关订单信息
        $refund->paymentRefund()->create([
            'order_number' => $refund->refund_id,
            'currency'     => $order->paymentOrder->currency,
            'amount'       => $refundAmount,
        ]);

        //组装成功退款状态
        $refundResult['refund'] = [
            'status' => Refund::STATUS_APPROVED,
            'code'   => get_system_code('000'),
            'result' => 'Transaction is approved',
            'remark' => '交易成功',
        ];

        $refundResult['payment_refund'] = [
            'payment_refund_id' => '0',
            'code'              => '0000',
            'result'            => 'Transaction is approved',
            'remark'            => '',
            'status'            => PaymentRefund::STATUS_APPROVED
        ];

        $res = $this->updateRefund($refund, $refundResult);

        if (!empty($res)) {
            if (isset($res['status']) && $res['status'] == Refund::STATUS_APPROVED) {
                $return['remarks']   = '创建退款成功数据成功！';
                $return['is_refund'] = 1;
                $return['result']    = ChargebackCaseModel::REFUNDED;

                //填充退款信息返回
                $return['returnData']['refundNo']       = $refund->refund_id;
                $return['returnData']['refundDate']     = $refund->completed_at->toDateTimeString();
                $return['returnData']['refundAmount']   = $order->paymentOrder->amount;
                $return['returnData']['refundCurrency'] = $order->paymentOrder->currency;
            }
        }

        return $return;
    }

    public function cdrnRefund($order, $availableAmount, $data): array
    {
        return $this->_requestRefund($order, $availableAmount, $data);
    }

    public function ethocaRefund($order, $availableAmount, $data): array
    {
        return $this->_requestRefund($order, $availableAmount, $data);
    }

    /**
     * 接收预警：cdrn、ethoca发起退款
     *
     * @param $order
     * @param $availableAmount
     * @param $data
     * @return array
     */
    protected function _requestRefund($order, $availableAmount, $data): array
    {
        $return = [
            'is_refund'  => 0, //0未完全退款 1完全退款
            'result'     => ChargebackCaseModel::NOT_REFUNDED,
            'returnData' => [],
            'remarks'    => ''
        ];

        if ($order->relation->is_refund == OrderRelation::IS_REFUND_FULL) {
            $return['is_refund'] = 1;
            $return['result']    = ChargebackCaseModel::REFUNDED;
            $return['remarks']   = '已退款';

            //订单已完全退款则填充退款信息
            $refundArr = $order->refund->sortByDesc('completed_at')->whereIn('status', [Refund::STATUS_APPROVED, Refund::STATUS_REVIEW])->first()->toArray();

            $return['returnData']['refundNo']       = $refundArr['refund_id'];
            $return['returnData']['refundDate']     = $refundArr['completed_at'];
            $return['returnData']['refundAmount']   = $order->paymentOrder->amount;
            $return['returnData']['refundCurrency'] = $order->paymentOrder->currency;

        } elseif ($order->is_3d && !$data['alert_type'] && $data['chargeback_from'] == ChargebackCaseModel::FROM_CDRN) {
            $return['remarks'] = '该订单属于3d交易属于欺诈类预警需要跟CDRN申诉';
        } elseif ($order->relation->is_chargeback) {
            $return['is_refund'] = 1;
            $return['remarks']   = '已拒付';
        } else {
            //添加退款任务
            if ($availableAmount <= 0.00) {
                $return['remarks'] = '可退款金额为0, 请核对！';
            }
        }

        if ($return['remarks']) {
            return $return;
        }

        //获取商户api_token
        $merchant = Merchant::find($order->merchant_id);
        $data     = [
            'merchantId'      => $order->merchant_id,
            'businessId'      => $order->business_id,
            'orderId'         => $order->order_id,
            'transactionType' => 'refund',
            'currency'        => $order->currency,
            'amount'          => Support::amount_format($availableAmount),
        ];

        $url     = route('api.v1.refund');
        $url     = str_replace('https://client', 'https://www', $url);
        $headers = ['headers' => ['Authorization' => 'Bearer ' . $merchant->api_token]];

        try {
            $resArr = $this->post($url, $data, $headers);
        } catch (\Exception $e) {
            //记录日志
            logger()->channel('intercept')->warning(
                'ChargebackCase',
                ['action' => 'Refund', 'order_id' => $order->order_id, 'error' => ['info' => $data, 'msg' => $e->getMessage()]]
            );
            dispatch(new SendSlsLog(
                ['message' => 'ChargebackCase'],
                ['action' => 'Refund', 'order_id' => $order->order_id, 'error' => ['info' => $data, 'msg' => $e->getMessage()]],
                'warning'
            ));
        }

        if (!empty($resArr)) {
            $result = '退款失败!';

            if (isset($resArr['status'])) {
                if ($resArr['status'] == Refund::STATUS_APPROVED) {
                    $result              = '退款成功';
                    $return['is_refund'] = 1;
                    $return['result']    = ChargebackCaseModel::REFUNDED;

                    //填充退款信息返回
                    $return['returnData']['refundNo']       = $resArr['refundId'];
                    $return['returnData']['refundDate']     = $resArr['completeDate'];
                    $return['returnData']['refundAmount']   = $order->paymentOrder->amount;
                    $return['returnData']['refundCurrency'] = $order->paymentOrder->currency;
                } elseif ($resArr['status'] == Refund::STATUS_PENDING) {
                    $result = '退款待审核';
                }
            }
        } else {
            $result = '退款请求超时！';
        }

        $return['remarks'] = $result;

        return $return;
    }

}
