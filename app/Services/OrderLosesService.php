<?php

namespace App\Services;

use App\Models\OrderLoses;
use Illuminate\Support\Facades\DB;

class OrderLosesService
{

	/**
	 * @description: 批量更新丢失订单状态
	 * @author: zqc
	 * @date: 2023/8/7
	 **/
	public function batchUpdateStatus(array $data)
	{
		// 数据判空
		if (empty($data) || !is_array($data)) {
			return [
				'isPass'  => false,
				'message' => '参数不正确~'
			];
		}

		// 校验待处理的数据是否存在
		$orderLosesData = OrderLoses::query()
						->where('status', OrderLoses::PROCESSED_STATUS)
						->whereIn('id', $data)
						->exists();

		if ($orderLosesData) {
			return [
				'isPass'  => false,
				'message' => '选中交易存在“已处理”,无法进行更新~'
			];
		}

		$batchUpdateRet = OrderLoses::whereIn('id', $data)->where('status',OrderLoses::UNTREATED_STATUS)->update(['status' => OrderLoses::PROCESSED_STATUS]);
		if ($batchUpdateRet) {
			return [
				'isPass'  => true,
				'message' => '成功更新' . $batchUpdateRet . '条数据',
			];
		}
		return [
			'isPass'  => false,
			'message' => '更新失败,请检查重试~'
		];
	}

}