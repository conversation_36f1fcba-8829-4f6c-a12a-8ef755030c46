<?php
namespace App\Services\Alert;

use App\Classes\Supports\Logger;
use App\Classes\Alert\Contracts\GateWay;
use App\Classes\Supports\Log;
use App\Models\ChargebackCase;
use App\Models\Order;
use App\Models\OrderRelation;
use App\Classes\Supports\Traits\HasHttpRequest;
use App\Http\Controllers\Traits\PaymentController;
use App\Jobs\SendSlsLog;
use App\Models\DirectoryChargebackCode;
use App\Models\DirectoryDictionary;
use App\Services\AlertService;
use App\Services\RefundService;
use App\Services\TransactionService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Cache;

class CdrnService
{
    use PaymentController;
    protected $gateway;
    use HasHttpRequest;

    public function __construct()
    {
        $log    = new Logger();
        $config = ['file' => storage_path('logs/cdrnNotice.log')];
        $log->setConfig($config);
        Log::setInstance($log);

        $param = Cache::get('Cdrn_Config_Data');

        if (empty($param)) {
            // 从数据库获取CDRN配置
            $list = DirectoryDictionary::where('type', 'CDRN配置')->pluck('remarks', 'name');

            if (count($list) == 0) {
                Log::info('Cdrn配置参数为空');
                dispatch(new SendSlsLog(
                    ['message' => 'Cdrn配置参数为空'],
                    [],
                    'info',
                    'cdrn'
                ));

                echo 'success';die;
            }
            // 验证配置参数
            $param    = [];
            $nameData = ['partnerId', 'secretKey', 'version', 'url'];

            foreach($nameData as $vo) {
                if (isset($list[$vo])) {
                    $param[$vo] = $list[$vo];
                } else {
                    Log::info('缺少Cdrn配置参数' . $vo);
                    dispatch(new SendSlsLog(
                        ['message' => '缺少Cdrn配置参数' . $vo],
                        [],
                        'info',
                        'cdrn'
                    ));

                    echo 'success';die;
                }
            }

            // 存储CDRN配置缓存
            Cache::add('Cdrn_Config_Data', $param, 30 * 24 * 3600);
        }

        $gatewayClass = '\App\Classes\Alert\Gateways\Cdrn';
        $gateway      = new $gatewayClass($param);

        if ($gateway instanceof GateWay) {
            $this->gateway = $gateway;
        }
    }

    public function notice($request)
    {
        // 应第一时间把对应案例状态改为PROCESSING
        $result = $this->gateway->noticeModifyStatus($request['caseid'], ['status' => 'PROCESSING']);

        if (isset($result['code']) && $result['code'] == '202') {
            //根据caseid获取案例详情
            $caseInfo = $this->gateway->getCaseInfo($request);
            $arr      = ['accountNumber', 'referenceTransactionCurrency', 'referenceTransactionAmount', 'referenceTransactionDate', 'type', 'reasonCode', 'merchantId'];
            // 验证所需参数
            foreach ($arr as $vo) {
                if (!isset($caseInfo[$vo])) {
                    Log::info('Cdrn ' .$vo. '参数为空', ['caseid' => $request['caseid'], 'data' => $request]);
                    dispatch(new SendSlsLog(
                        ['message' => 'Cdrn ' .$vo. '参数为空'],
                        ['caseid' => $request['caseid'], 'data' => $request],
                        'info',
                        'cdrn'
                    ));

                    $this->sendWarning($request['caseid']);
                    return;
                }
            }

            $caseTime  = strtotime($caseInfo['referenceTransactionDate']);
            $days      = 2 * 24 * 3600; // 浮动时间
            $orderList = Order::with(['settlements', 'paymentOrder', 'refund', 'relation', 'card'])->whereHas('card', function(Builder $query) use ($caseInfo) {
                $query->where('card_mask', $caseInfo['accountNumber']);
            })->whereHas('paymentOrder', function(Builder $query) use ($caseInfo) {
                $query->where('currency', $caseInfo['referenceTransactionCurrency']);
                $query->where('amount', $caseInfo['referenceTransactionAmount']);
            })->whereBetween('completed_at', [date('Y-m-d H:i:s', $caseTime - $days), date('Y-m-d H:i:s', $caseTime + $days)])
            ->where('status', Order::STATUS_APPROVED)->get();

            // 预警数据初始化
            $addData = [
                'case_id'             => $request['caseid'],
                'business_history_id' => 0,
                'alert_type'          => 0,
                'reason_code'         => $caseInfo['reasonCode'],
                'result'              => 0,
                'dishonour_warn_info' => $caseInfo,
                'remarks'             => '',
                'warn_currency'       => $caseInfo['referenceTransactionCurrency'],
                'warn_amount'         => $caseInfo['referenceTransactionAmount'],
                'by_alert_id'         => '1',
                'by_alert'            => 'admin',
                'chargeback_from'     => ChargebackCase::FROM_CDRN,
                'alert_from'          => ChargebackCase::ALERT_FROM_VERIFI,
                'date_complete'       => '1970-01-01',
                'date_settle'         => '1970-01-01',
            ];

            if ($orderList->count() != 1) {
                //无或多条订单记录需人工处理该次预警
                $addDataUp = [
                    'order_id'      => '000000000000000000',
                    'order_number'  => '000000000000000000',
                    'merchant_id'   => 0,
                    'merchant_name' => '无',
                    'business_id'   => 0,
                    'channel_id'    => 0,
                    'channel'       => '无',
                    'is_meddle'     => 0,
                    'reply_status'  => ChargebackCase::REPLY_STATUS_UNHANDLED,
                ];
                $availableAmount = 0.00;
                $flag            = 0;     // 0:无操作 1:创建退款成功数据 2:请求退款

                $this->sendWarning($request['caseid'], ChargebackCase::WARNING_TYPE_MATCHING_FAILED);
            } else {
                $order = $orderList[0];

                // 未找到关联数据
                if (empty($order->relation) || empty($order->paymentOrder)) {
                    //DECLINED 902
                    $this->gateway->noticeModifyStatus($request['caseid'], ['status' => 'DECLINED', 'statusCode' => '902']);
                    Log::info('Cdrn 未找到关联数据', ['caseid' => $request['caseid'], 'orderId' => $order->order_id]);
                    dispatch(new SendSlsLog(
                        ['message' => 'Cdrn 未找到关联数据'],
                        ['caseid' => $request['caseid'], 'orderId' => $order->order_id],
                        'info',
                        'cdrn'
                    ));

                    return;
                }

                // 判断是否重复预警
                if ($order->relation->is_chargeback_warn) {
                    // DECLINED 940
                    $this->gateway->noticeModifyStatus($request['caseid'], ['status' => 'DECLINED', 'statusCode' => '940']);
                    Log::info('Cdrn 重复预警', ['caseid' => $request['caseid'], 'orderId' => $order->order_id]);
                    dispatch(new SendSlsLog(
                        ['message' => 'Cdrn 重复预警'],
                        ['caseid' => $request['caseid'], 'orderId' => $order->order_id],
                        'info',
                        'cdrn'
                    ));

                    return;
                }

                // 处理保存数据
                $resultTemp      = $this->dataHandle($order, $caseInfo['reasonCode']);
                $addDataUp       = $resultTemp['addDataUp'];
                $availableAmount = $resultTemp['availableAmount'];
                $flag            = $resultTemp['flag'];
            }

            // 保存拒付预警数据
            $addData        = array_merge($addData, $addDataUp);
            $chargebackCase = ChargebackCase::create($addData);

            if ($chargebackCase && $addData['reply_status'] == ChargebackCase::REPLY_STATUS_NOTICE) {
                $data = [
                    'flag'            => $flag,
                    'availableAmount' => $availableAmount,
                    'caseid'          => $request['caseid'],
                    'caseInfo'        => $caseInfo
                ];
                $this->chargebackCasesHandle($order, $data);
            }
        } else {
            Log::info('Cdrn 修改案例状态为PROCESSING失败', ['caseid' => $request['caseid'], 'data' => $request]);
            dispatch(new SendSlsLog(
                ['message' => 'Cdrn 修改案例状态为PROCESSING失败'],
                ['caseid' => $request['caseid'], 'data' => $request],
                'info',
                'cdrn'
            ));

            $this->sendWarning($request['caseid']);
        }
    }

    /**
     * 处理保存的拒付预警数据
     *
     * @param object $order
     * @param string $reasonCode
     * @return array
     */
    public function dataHandle($order, $reasonCode)
    {
        $flag               = 0;// 0:无操作 1:创建退款成功数据 2:请求退款
        $availableAmount    = 0.00;
        $chargebackCode     = [];
        $chargebackCodeList = DirectoryChargebackCode::selectRaw('cc_type,code')->get()->toArray();

        foreach ($chargebackCodeList as $vo) {
            $chargebackCode[$vo['cc_type']][] = $vo['code'];
        }

        $alertType = in_array($reasonCode, $chargebackCode[$order->card->cc_type]) ? '1' : '0';
        $addDataUp = [
            'order_id'            => $order->order_id,
            'business_history_id' => $order->settlements->business_history_id,
            'alert_type'          => $alertType,
            'order_number'        => $order->order_number,
            'merchant_id'         => $order->merchant_id,
            'merchant_name'       => $order->merchant_name,
            'business_id'         => $order->business_id,
            'channel_id'          => $order->channel_id,
            'channel'             => $order->channel,
            'date_complete'       => $order->completed_at,
            'date_settle'         => get_settle_date(),
            'is_meddle'           => 0,
            'reply_status'        => ChargebackCase::REPLY_STATUS_NOTICE
        ];

        // 判断原始交易是否退款、拒付
        if ($order->relation->is_refund == OrderRelation::IS_REFUND_FULL || $order->relation->is_chargeback) {
            $addDataUp['result']  = 1;
            $addDataUp['remarks'] = '已退款或已拒付';
        } elseif ($order->is_3d && !$addDataUp['alert_type']) {
            $addDataUp['result']  = 0;
            $addDataUp['remarks'] = '该订单属于3d交易属于欺诈类预警需要跟CDRN申诉';
        } else {
            $addDataUp['result']  = 1;

            // 获取可退款最高金额
            $availableAmount = TransactionService::getAvailableRefundAmount($order);

            // 添加退款任务
            if ($availableAmount > 0.00) {
                $flag = 2;
            } else {
                $addDataUp['remarks'] = '可退款金额为0, 请核对！';
            }
        }

        if ($order->type == Order::TYPES_AUTH) {
            $addDataUp['remarks'] = 'Auth交易预警';
        }

        return ['addDataUp' => $addDataUp, 'availableAmount' => $availableAmount, 'flag' => $flag];
    }

    /**
     * 拒付预警新增后续处理
     *
     * @param object $order
     * @param array $data
     */
    public function chargebackCasesHandle($order, $data)
    {
        // 更新关联表拒付预警字段
        $order->relation->update(['is_chargeback_warn' => 1]);

        if ($data['flag'] == 2) { // 请求退款
            $refundService = new RefundService();
            $result        = $refundService->_requestRefund($order, $data['availableAmount']);
            if (!empty($result)) {
                // 有退款把处理状态改为待通知
                ChargebackCase::where('case_id', $data['caseid'])->update(['remarks' => $result, 'reply_status' => ChargebackCase::REPLY_STATUS_PROCESSING]);
            }
        } else {
            // 不需要退款处理的直接通知CDRN处理完成
            $noticeData = [
                'merchantId' => $data['caseInfo']['merchantId'],
                'type'       => $data['caseInfo']['type'],
                'amount'     => $data['caseInfo']['referenceTransactionAmount'],
                'currency'   => $order->currency,
                'caseid'     => $data['caseid'],
            ];
            $this->sendNotice($noticeData);
        }
    }

    /**
     * 通知CDRN处理完成
     *
     * @param array $noticeData
     * @return void
     */
    public function sendNotice($noticeData)
    {
        $data = [
            'merchantId' => $noticeData['merchantId'],
        ];

        if ($noticeData['type'] == 'CANCEL') {
            $data['status']         = 'CANCELLED';
            $data['statusCode']     = '130';
            $data['activityAmount'] = 0.00;
        } else {
            $data['status']         = 'RESOLVED';
            $data['statusCode']     = '100';
            $data['activityAmount'] = $noticeData['amount'];
        }

        $data['activityCurrency'] = $noticeData['currency'];
        $data['activityDate']     = date('Y-m-d');

        return $this->gateway->noticeModifyStatus($noticeData['caseid'], $data);
    }

    /**
     * 发送预警工单告警
     *
     * @param $caseId
     */
    public function sendWarning($caseId, $warningType = ChargebackCase::WARNING_TYPE_PROCESSING_FAILED)
    {
        $tempData = [
            'alert_from'      => ChargebackCase::ALERT_FROM_VERIFI,
            'chargeback_from' => ChargebackCase::FROM_CDRN,
            'case_id'         => $caseId,
            'warning_type'    => $warningType
        ];

        AlertService::sendAlertWarning($tempData);
    }
}
