<?php
namespace App\Services\Alert;

use App\Classes\Supports\Logger;
use App\Classes\Alert\Contracts\GateWay;
use App\Classes\Supports\Log;
use App\Models\ChargebackCase;
use App\Models\Order;
use App\Models\OrderRelation;
use App\Http\Controllers\Traits\PaymentController;
use App\Jobs\SendSlsLog;
use App\Models\DirectoryBinbase;
use App\Models\DirectoryDictionary;
use App\Models\Refund;
use App\Services\AlertService;
use App\Services\RefundService;
use App\Services\TransactionService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Cache;

class WintranxService
{
    use PaymentController;
    protected $gateway;

    private $handleString = 'WINSHIELD_';

    public function __construct()
    {
        $log    = new Logger();
        $config = ['file' => storage_path('logs/wintranxNotice.log')];
        $log->setConfig($config);
        Log::setInstance($log);

        $param = Cache::get('Wintranx_Config_Data');

        if (empty($param)) {
            // 从数据库获取Wintranx配置
            $list = DirectoryDictionary::where('type', 'Wintranx配置')->pluck('remarks', 'name');

            if (count($list) == 0) {
                Log::info('Wintranx配置参数为空');
                dispatch(new SendSlsLog(
                    ['message' => 'Wintranx配置参数为空'],
                    [],
                    'info',
                    'wintranx'
                ));

                echo 'SUCCESS';die;
            }
            // 验证配置参数
            $param    = [];
            $nameData = ['userName', 'secretKey', 'password', 'url'];

            foreach($nameData as $vo) {
                if (isset($list[$vo])) {
                    $param[$vo] = $list[$vo];
                } else {
                    Log::info('缺少Wintranx配置参数' . $vo);
                    dispatch(new SendSlsLog(
                        ['message' => '缺少Wintranx配置参数' . $vo],
                        [],
                        'info',
                        'wintranx'
                    ));

                    echo 'SUCCESS';die;
                }
            }

            // 存储CDRN配置缓存
            Cache::add('Wintranx_Config_Data', $param, 30 * 24 * 3600);
        }

        $gatewayClass = '\App\Classes\Alert\Gateways\Wintranx';
        $gateway      = new $gatewayClass($param);

        if ($gateway instanceof GateWay) {
            $this->gateway = $gateway;
        }
    }

    public function notice($request)
    {
        $request['winshieldId'] = str_replace($this->handleString, '', $request['winshieldId']);
        // 判断case_id是否存在
        if (ChargebackCase::where('case_id', $request['winshieldId'])->exists()) {
            // 521 重复报警
            $data = [
                'winshieldId' => $request['winshieldId'],
                'data' => [
                    'winshieldId' => $request['winshieldId'],
                    'outCome'     => '521'
                ]
            ];
            $this->sendNotice($data);
            Log::info('Wintranx 重复预警', ['winshieldId' => $request['winshieldId']]);
            dispatch(new SendSlsLog(
                ['message' => 'Wintranx 重复预警'],
                ['winshieldId' => $request['winshieldId']],
                'info',
                'Wintranx'
            ));

            return;
        }

        //匹配交易
        $caseTime  = strtotime($request['transactionTime']);
        $days      = 2 * 24 * 3600; //浮动时间
        $cardMask  = get_markcard($request['cardNumber']); //卡掩码
        $orderList = Order::with(['settlements', 'paymentOrder', 'refund', 'relation', 'card'])->whereHas('card', function(Builder $query) use ($cardMask) {
            $query->where('card_mask', $cardMask)->where('cc_type', 'M');
        })->whereHas('paymentOrder', function(Builder $query) use ($request) {
            $query->where('currency', $request['currency']);
            $query->where('amount', $request['amount']);
        })->whereBetween('completed_at', [date('Y-m-d H:i:s', $caseTime - $days), date('Y-m-d H:i:s', $caseTime + $days)])
        ->where('status', Order::STATUS_APPROVED)
        ->where('card_bill', $request['descriptor'])->get();

        //预警数据初始化
        $alertType  = $request['alertType'] === 'CONFIRMED_FRAUD' ? ChargebackCase::CONFIRMED_FRAUD : ChargebackCase::CUSTOMER_DISPUTE;
        $reasonCode = $this->getReasonCode($alertType, $cardMask);
        $addData    = [
            'case_id'             => $request['winshieldId'],
            'business_history_id' => 0,
            'alert_type'          => $alertType,
            'reason_code'         => $reasonCode,
            'result'              => 0,
            'dishonour_warn_info' => $request,
            'remarks'             => '',
            'warn_currency'       => $request['currency'],
            'warn_amount'         => $request['amount'],
            'by_alert_id'         => '1',
            'by_alert'            => 'admin',
            'chargeback_from'     => ChargebackCase::FROM_ETHOCA,
            'alert_from'          => ChargebackCase::ALERT_FROM_WINTRANX,
            'date_complete'       => '1970-01-01',
            'date_settle'         => '1970-01-01',
        ];

        if ($orderList->count() != 1) {
            //无或多条订单记录需人工处理该次预警
            $addDataUp = [
                'order_id'      => '000000000000000000',
                'order_number'  => '000000000000000000',
                'merchant_id'   => 0,
                'merchant_name' => '无',
                'business_id'   => 0,
                'channel_id'    => 0,
                'channel'       => '无',
                'is_meddle'     => 0,
                'reply_status'  => ChargebackCase::REPLY_STATUS_UNHANDLED,
            ];
            $availableAmount = 0.00;
            $flag            = 0;     // 0:无操作 1:创建退款成功数据 2:请求退款

            $this->sendWarning($request['winshieldId'], ChargebackCase::WARNING_TYPE_MATCHING_FAILED);
        } else {
            $order = $orderList[0];

            // 未找到关联数据
            if (empty($order->relation) || empty($order->paymentOrder)) {
                // 514 没有找到对应的交易
                $data = [
                    'winshieldId' => $request['winshieldId'],
                    'data' => [
                        'winshieldId' => $request['winshieldId'],
                        'outCome'     => '514'
                    ]
                ];
                $this->sendNotice($data);
                Log::info('Wintranx 订单未找到关联数据', ['winshieldId' => $request['winshieldId'], 'orderId' => $order->order_id]);
                dispatch(new SendSlsLog(
                    ['message' => 'Wintranx 订单未找到关联数据'],
                    ['winshieldId' => $request['winshieldId'], 'orderId' => $order->order_id],
                    'info',
                    'Wintranx'
                ));

                return;
            }

            // 判断是否重复预警
            if ($order->relation->is_chargeback_warn) {
                // 521 重复报警
                $data = [
                    'winshieldId' => $request['winshieldId'],
                    'data' => [
                        'winshieldId' => $request['winshieldId'],
                        'outCome'     => '521'
                    ]
                ];
                $this->sendNotice($data);
                Log::info('Wintranx 重复预警', ['winshieldId' => $request['winshieldId'], 'orderId' => $order->order_id]);
                dispatch(new SendSlsLog(
                    ['message' => 'Wintranx 重复预警'],
                    ['winshieldId' => $request['winshieldId'], 'orderId' => $order->order_id],
                    'info',
                    'Wintranx'
                ));

                return;
            }

            // 处理保存数据
            $resultTemp      = $this->dataHandle($order, $request['authCode']);
            $addDataUp       = $resultTemp['addDataUp'];
            $availableAmount = $resultTemp['availableAmount'];
            $flag            = $resultTemp['flag'];
        }

        // 保存拒付预警数据
        $addData        = array_merge($addData, $addDataUp);
        $chargebackCase = ChargebackCase::create($addData);

        if ($chargebackCase && $addData['reply_status'] == ChargebackCase::REPLY_STATUS_NOTICE) {
            $data = [
                'flag'            => $flag,
                'availableAmount' => $availableAmount,
                'winshieldId'     => $request['winshieldId'],
                'alert_type'      => $addData['alert_type'],
            ];
            $this->chargebackCasesHandle($order, $data);
        }
    }

    /**
     * 处理保存的拒付预警数据
     *
     * @param object $order
     * @return array
     */
    public function dataHandle($order)
    {
        $flag            = 0;     // 0:无操作 1:创建退款成功数据 2:请求退款 3:预警前已退款
        $availableAmount = 0.00;
        $addDataUp       = [
            'order_id'            => $order->order_id,
            'business_history_id' => $order->settlements->business_history_id,
            'order_number'        => $order->order_number,
            'merchant_id'         => $order->merchant_id,
            'merchant_name'       => $order->merchant_name,
            'business_id'         => $order->business_id,
            'channel_id'          => $order->channel_id,
            'channel'             => $order->channel,
            'date_complete'       => $order->completed_at,
            'date_settle'         => get_settle_date(),
            'is_meddle'           => 0,
            'reply_status'        => ChargebackCase::REPLY_STATUS_NOTICE
        ];

        // 判断原始交易是否退款、拒付
        if ($order->relation->is_refund == OrderRelation::IS_REFUND_FULL || $order->relation->is_chargeback) {
            $addDataUp['result']  = 1;
            $addDataUp['remarks'] = '已退款或已拒付';
            $flag                 = 3;
        } else {
            $addDataUp['result']  = 1;

            // 获取可退款最高金额
            $availableAmount = TransactionService::getAvailableRefundAmount($order);

            // 添加退款任务
            if ($availableAmount > 0.00) {
                $flag = 2;
            } else {
                $addDataUp['remarks'] = '可退款金额为0, 请核对！';
            }
        }

        if ($order->type == Order::TYPES_AUTH) {
            $addDataUp['remarks'] = 'Auth交易预警';
        }

        return ['addDataUp' => $addDataUp, 'availableAmount' => $availableAmount, 'flag' => $flag];
    }

    /**
     * 拒付预警新增后续处理
     *
     * @param object $order
     * @param array $data
     */
    public function chargebackCasesHandle($order, $data)
    {
        // 更新关联表拒付预警字段
        $order->relation->update(['is_chargeback_warn' => 1]);

        switch ($data['flag']) {
            case '2':
                $refundService = new RefundService();
                $result        = $refundService->_requestRefund($order, $data['availableAmount']);
                if (!empty($result)) {
                    // 有退款把处理状态改为待通知
                    ChargebackCase::where('case_id', $data['winshieldId'])->update(['remarks' => $result, 'reply_status' => ChargebackCase::REPLY_STATUS_PROCESSING]);
                }
                break;
            case '3':
                // 预警前已经退款或拒付； 511 欺诈类型在收到预警前已经退款； 517 争议类型在收到预警前已经退款
                $dataTemp = [
                    'winshieldId' => $data['winshieldId'],
                    'data' => [
                        'winshieldId' => $data['winshieldId']
                    ]
                ];
                if ($data['alert_type'] == ChargebackCase::CONFIRMED_FRAUD) {
                    $dataTemp['data']['outCome'] = '511';
                    // 计算已退款金额
                    $refundArr = Refund::with('paymentRefund')->where(['order_id' => $order->order_id])
						->whereIn('status', [Refund::STATUS_APPROVED, Refund::STATUS_REVIEW])
                        ->get();

                    if (empty($refundArr)) {
                        return ;
                    }

                    $dataTemp['data']['creditAmount'] = $refundArr->sum('paymentRefund.amount');
                    $dataTemp['data']['creditDate']   = $refundArr->max('completed_at')->toDateTimeString();
                } else {
                    $dataTemp['data']['outCome'] = '517';
                }

                $this->sendNotice($dataTemp);
                break;
        }
    }

    public function sendNotice($data)
    {
        $tempWinshieldId = '';
        if (isset($data['data']['winshieldId']) && substr($data['data']['winshieldId'], 0, strlen($this->handleString)) != $this->handleString) {
            $tempWinshieldId = $data['data']['winshieldId'];
            $data['data']['winshieldId'] = $this->handleString . $data['data']['winshieldId'];
        }

        return $this->gateway->noticeModifyStatus($tempWinshieldId, $data['data']);
    }

    /**
     * 发送预警工单告警
     *
     * @param $caseId
     */
    public function sendWarning($caseId, $warningType = ChargebackCase::WARNING_TYPE_PROCESSING_FAILED)
    {
        $tempData = [
            'alert_from'      => ChargebackCase::ALERT_FROM_WINTRANX,
            'chargeback_from' => ChargebackCase::FROM_ETHOCA,
            'case_id'         => $caseId,
            'warning_type'    => $warningType
        ];

        AlertService::sendAlertWarning($tempData);
    }

    /**
     * 根据Wintranx渠道通知内容获取系统原因
     *
     * @param int $alertType
     * @param string $cardMask
     * @return string
     */
    public function getReasonCode(int $alertType, string $cardMask): string
    {
        //卡种获取
        $cardBin          = substr($cardMask, 0, 6);
        $ccType           = '';
        $directoryBinbase = DirectoryBinbase::firstWhere('bin', $cardBin);
        if (!empty($directoryBinbase['brand'])) {
            $brand  = strtoupper($directoryBinbase['brand']);
            $ccType = $this->_brandList[$brand] ?? substr($brand, 0, 1);
        } else {
            $ccType = get_cc_type($cardMask);
        }

        $tempData = [
            ChargebackCase::CONFIRMED_FRAUD => [
                'V' => '10.4',
                'M' => '4837'
            ],
            ChargebackCase::CUSTOMER_DISPUTE => [
                'V' => 'VW',
                'M' => 'MW'
            ],
        ];

        return $tempData[$alertType][$ccType] ?? 'ALERT';
    }
}
