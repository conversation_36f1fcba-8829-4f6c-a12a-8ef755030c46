<?php
namespace App\Services;

use Illuminate\Support\Facades\Cache;

class RateLimiterServices {
    protected static $maxRequests = 1;
    protected static $timeWindow  = 1;

    public static function canMakeRequest($name)
    {
        $key = 'request_limit_' . $name;

        // 检查请求是否超过了限制
        if (Cache::has($key)) {
            $requests = Cache::get($key);

            // 如果请求次数超过限制，获取剩余时间并延迟
            if ($requests >= self::$maxRequests) {
                $resetTime = Cache::get($key . '_reset');
                $remaining = $resetTime - time();
                if ($remaining > 0) {
                    sleep(1);
                }
            }
        } else {
            $requests = 0;
        }

        // 更新缓存中的请求次数
        Cache::put($key, $requests + 1, self::$timeWindow);
        Cache::put($key . '_reset', time() + self::$timeWindow, self::$timeWindow);
    }
}