<?php

namespace App\Services;

use App\Models\CardTransaction;
use Auth;

class CardTransactionService
{

    /**
     * @description:
     * CID卡交易金额统计、CID交易量统计、CID失败率
     * 统计表 card_transactions 中 预授权 的数据
     * @author: zqc
     * @date: 2024/6/24
     */
    public static function getCardTradeStatAmountData($searchData, $type): array
    {
        $startDate  = date('Y-m-d', strtotime('-7 day'));
        $endDate    = now()->format('Y-m-d');
        $merchantId = Auth::user()->merchant_id;
        if (isset($searchData['date'])) {
            switch ($searchData['date']) {
                case '7_days_ago':
                    $startDate = now()->subDays(7)->format('Y-m-d');
                    break;
                case '10_days_ago':
                    $startDate = now()->subDays(10)->format('Y-m-d');
                    break;
                case '28_days_ago':
                    $startDate = now()->subDays(28)->format('Y-m-d');
                    break;
                case '30_days_ago':
                    $startDate = now()->subDays(30)->format('Y-m-d');
                    break;
            }
        }

        $searchDate = [$startDate, $endDate];
        // 获取交易数据
        $cardTransactionList = CardTransaction::where('merchant_id', $merchantId)
            ->whereBetween('date_comtplete', $searchDate)
            ->where('transaction_type', CardTransaction::TRANSACTION_TYPE_AUTH)
            ->get();
        if (empty($cardTransactionList)) {
            return [];
        }

        // 返回数据
        $returnData = [];
        switch ($type) {
            case 'amount':
                $returnData = self::HandleAmount($cardTransactionList);
                break;
            case 'count':
                $returnData = self::HandleCount($cardTransactionList);
                break;
            case 'fail_rate':
                $returnData = self::HandleFailRate($cardTransactionList);
                break;
            case 'table':
                break;
        }

        return $returnData;
    }


    /**
     * @description:
     * 处理CID交易金额统计  目前默认都是USD 币种有变化 这里需要改变
     * @author: zqc
     * @date: 2024/6/24
     */
    private static function HandleAmount($cardTransactionInfo): array
    {
        $handelData = [];
        $statData   = [];
        // 处理数据
        $groupedTransactions = $cardTransactionInfo->groupBy('cards_id')->map(function ($group) {
            return $group->sum('amount');
        })->sortDesc();
        foreach ($groupedTransactions as $cardsId => $amount) {
            $cardsIdName   = $cardsId;
            $cardsIdAmount = $amount;

            if (count($handelData) > 4) {
                $cardsIdName   = 'Others';
                $cardsIdAmount += $handelData[$cardsIdName]['amount'] ?? 0;
            }

            $handelData[$cardsIdName] = [
                'cards_id' => $cardsIdName,
                'amount'   => $cardsIdAmount
            ];
        }

        foreach ($handelData as $data) {
            $statData[$data['cards_id'] . ':' . $data['amount'] . "$"] = $data;
        }

        return $statData;
    }

    /**
     * @description:
     * 处理CID交易量统计
     * @author: zqc
     * @date: 2024/6/25
     */
    private static function HandleCount($cardTransactionInfo): array
    {
        $handelData = [];
        $statData   = [];
        // 处理排序数据
        $countCardTransaction = $cardTransactionInfo->countBy('cards_id')->sortDesc();
        foreach ($countCardTransaction as $cardsId => $count) {
            $cardsIdName  = $cardsId;
            $cardsIdCount = $count;

            if (count($handelData) > 4) {
                $cardsIdName  = 'Others';
                $cardsIdCount += $handelData[$cardsIdName]['count'] ?? 0;
            }

            $handelData[$cardsIdName] = [
                'cards_id' => $cardsIdName,
                'count'    => $cardsIdCount
            ];
        }

        foreach ($handelData as $data) {
            $statData[$data['cards_id'] . ':' . $data['count']] = $data;
        }

        return $statData;
    }

    /**
     * @description:
     * 处理CID失败率
     * @author: zqc
     * @date: 2024/6/25
     */
    private static function HandleFailRate($cardTransactionInfo): array
    {
        $groupedTransactionsArr = $cardTransactionInfo->groupBy('cards_id')->map(function ($group) {
            return $group->count();
        })->toArray();

        $groupedFailTransactions = $cardTransactionInfo->where('transaction_status', CardTransaction::STATUS_FAIL)->groupBy('cards_id')->map(function ($group) {
            return $group->count();
        });

        $statData = [];
        foreach ($groupedFailTransactions as $cardsId => $failCount) {
            $cardsIdSuccessCount = $groupedTransactionsArr[$cardsId] ?? 0;
            $failRate            = $cardsIdSuccessCount > 0 ? ToolService::perCalculate($failCount ?? 0, $cardsIdSuccessCount, 2) : '0.00%';

            $statData[$cardsId . ':' . $failRate] = [
                'cards_id'  => $cardsId,
                'fail_rate' => $failRate
            ];
        }

        uasort($statData, function ($a, $b) {
            // 将百分比字符串转换为浮点数进行比较
            return ((float)str_replace('%', '', $b['fail_rate'])) - ((float)str_replace('%', '', $a['fail_rate']));
        });

        if (count($statData) > 5) {
            $returnData         = array_slice($statData, 0, 5);
            $otherTransactions  = array_slice($statData, 5);
            $otherFailRates     = array_map(function ($item) {
                return (float)str_replace('%', '', $item['fail_rate']);
            }, $otherTransactions);
            $totalOtherFailRate = array_sum($otherFailRates) / count($otherTransactions);
            // 计算其他项的平均失败率
            $failRate = number_format($totalOtherFailRate, 2) . "%";
            // 返回
            $returnData['Others' . ':' . $failRate] = [
                'cards_id'  => 'Others',
                'fail_rate' => $failRate,
            ];
            return $returnData;
        }

        return $statData;
    }
}