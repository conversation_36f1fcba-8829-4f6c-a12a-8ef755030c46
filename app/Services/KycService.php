<?php


namespace App\Services;

use App\Jobs\SendEmail;
use App\Jobs\SendSlsLog;
use App\Models\Merchant;
use App\Models\MerchantKyc;
use App\Models\OpenImUser;
use App\Models\User;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class KycService
{

    /**
     * 注册逻辑
     * @param array $data
     * @return array
     */
    public function application(array $data): array
    {
        DB::beginTransaction();
        try {
            $merchant_id             = Merchant::createMerchantId();
            $merchant                = new Merchant();
            $merchant->merchant_id   = $merchant_id;
            $merchant->api_token     = Merchant::getApiToken();
            $merchant->merchant_name = $data['company_name'];
            $merchant->phone         = $data['contacts_phone'];
            $merchant->email         = $data['contacts_email'];
            $merchant->status        = Merchant::STATUS_CHECK;
            $merchant->is_credit     = (isset($data['business_type']) && $data['business_type'] == MerchantKyc::BUSINESS_TYPE_ORDER) ? Merchant::STATUS_ENABLE : Merchant::STATUS_DISABLE;
            $merchant->is_virtual    = (isset($data['business_type']) && $data['business_type'] == MerchantKyc::BUSINESS_TYPE_VIRTUAL) ? Merchant::STATUS_ENABLE : Merchant::STATUS_DISABLE;
            $merchantStatus          = $merchant->save();

            if (!$merchantStatus) {
                throw new Exception('商户创建失败');
            }
            // 创建商户主用户
            $merchant->users()->createMany([[
                'type'              => User::TYPE_MAIN,
                'merchant_id'       => $merchant_id,
                'name'              => 'admin',
                'password'          => bcrypt($data['password']),
                'status'            => User::STATUS_ENABLE,
                'email'             => $data['contacts_email'],
                'email_verified_at' => now(),
                'password_valid_at' => now()->addMonths(3),
                '_remove_'          => '0'
            ]]);
            $data['merchant_id'] = $merchant_id;
            $kyc                 = MerchantKyc::create($data);
            // 创建kyc失败
            if (!$kyc) {
                throw new Exception('kyc创建失败');
            }

            if (config('open_im.open_im')) {
                // 根据mid 查询商户的admin用户
                $user = $merchant->users[0];
                // 注册openIm
                $openImUser = [
                    'userId'          => $user['id'],
                    'userType'        => OpenImUser::USER_TYPE_MERCHANT_MAIN,
                    'nickname'        => $merchant->merchant_name,
                    'password'        => $user['password'],
                    'addGroup'        => true,
                    'addFriend'       => false,
                    'createGroup'     => false,
                    'defaultAddGroup' => false,
                    'groupRoleLevel'  => OpenImUser::ROLE_LEVEL_MEMBER,
                    'userName'        => 'system',
                ];
                $openIm     = new OpenImService;
                $userId     = $openIm->register($openImUser, $merchant->merchant_id, $merchant->merchant_name);
                if ($userId) {
                    // 自动创建群组
                  $group =  $openIm->createGroup($merchant->merchant_name, [$userId]);
                }

                if (!$userId || !$group){
                    logger()->channel('intercept')->warning('open—im 注册异常 userid:'.$userId);
                    dispatch(new SendSlsLog(
                             ['message' => 'open—im 注册异常 userid:'.$userId],
                             [],
                             'warning',
                             'intercept'
                    ));
                }
            }

            DB::commit();
            //发送邮件
            $templateData = [
                'address' => $merchant->email,
                'user'    => $merchant->merchant_name,
                'view'    => 'KycRegister',
                'data'    => [
                    'company_name' => $merchant->merchant_name,
                    'merchant_id'  => $merchant->merchant_id,
                    'username'     => 'admin',
                ]
            ];
            // 添加邮件任务到队列
            dispatch(new SendEmail($templateData, 0));
            return ['status' => true, 'message' => trans('kyc.fields.tip.success')];
        } catch (\Throwable $th) {
            logger()->channel('intercept')->warning('商户申请Kyc异常:' . $th->getMessage());
            dispatch(new SendSlsLog(
                ['message' => '商户申请Kyc异常:' . $th->getMessage()],
                [],
                'warning',
                'intercept'
            ));

            DB::rollBack();
        }

        return ['status' => false, 'message' => trans('kyc.fields.tip.system_error')];
    }

    /**
     * 校验提交参数合法性
     * @param array $data
     * @return array
     */
    public function verifyData(array &$data): array
    {
        $rules = [
            'company_name'           => 'required|string|max:64',
            'address'                => 'required|string|max:256',
            'office_address'         => 'required|string|max:256',
            'office_phone'           => 'required|string|max:32',
            'certificate_no'         => 'required|string|max:128',
            'certificate_img'        => 'required|array|max:256',
            'enforcer_name'          => 'required|string|max:64',
            'enforcer_code'          => 'required|array|max:256',
            'enforcer_hold_code'     => 'required|array|max:256',
            'equity_certificate_img' => 'required|array|max:256',
            'bank_account_img'       => 'required|array|max:256',
            'merchant_apply_file'    => 'required|array|max:256',
            'shareholder_info'       => 'required|array',
            'type'                   => 'required|string|max:1',
            'business_type'          => 'required|string|max:1|in:' . MerchantKyc::BUSINESS_TYPE_ORDER . ',' . MerchantKyc::BUSINESS_TYPE_VIRTUAL,
            'contacts_name'          => 'required|string|max:64',
            'contacts_phone'         => 'required|string|max:32',
            'contacts_position'      => 'required|string|max:32',
            'contacts_email'         => 'required|email:rfc,dns',
            'password'               => 'required|min:8|regex:/^(?![a-zA-Z]+$)(?!\d+$)(?!\W_+$)[a-zA-Z\d\W_]{8,14}$/u',
        ];

        if (isset($data['type']) && $data['type'] == MerchantKyc::TYPE_CHINESE_HONGKONG) {
            $rules['register_img'] = 'required|array|max:256';
        }

        if (isset($data['shareholder_info'])) {
            foreach ($data['shareholder_info'] as $key => $shareholder) {
                if (!isset($shareholder['shareholder_name'], $shareholder['shareholder_code'])) {
                    unset($data['shareholder_info'][$key]);
                }
            }

            if (count($data['shareholder_info']) == 0) {
                unset($data['shareholder_info']);
            }
        }

        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            $errors = $validator->errors();
            return ['status' => false, 'message' => $errors->first()];
        }

        return ['status' => true];
    }

    /**
     * 是否已经申请kyc
     * @param array $data
     * @return array
     */
    public function isRegister(array $data): array
    {
        $isRegister = MerchantKyc::where([
            'company_name' => $data['company_name'],
        ])->count();

        $emailCount = User::where('email', $data['contacts_email'])->count();

        if ($isRegister > 0 || $emailCount > 0) {
            return ['status' => false, 'message' => trans('kyc.fields.already_registered')];
        }

        return ['status' => true];
    }

    /**
     * 上传图片
     * @param string $path
     * @return array
     */
    public function uploadImg($path = 'merchant', $prefix = 'images'): array
    {
        if (request()->hasFile('file_data')) {
            $tmp = request()->file('file_data');
            //限定格式为jpg,jpeg,png,bmp
            $fileTypes = ['image/jpeg', 'image/webp', 'image/png', 'image/x-ms-bmp', 'application/pdf'];

            if ($tmp) {
                if (in_array($tmp->getMimeType(), $fileTypes) && $tmp->isValid()) {
                    $FileType = $tmp->getClientOriginalExtension(); //获取文件后缀
                    $FilePath = $tmp->getRealPath(); //获取文件临时存放位置
                    $FileName = uniqid() . '.' . $FileType; //定义文件名
                    Storage::disk($path)->put('/' . $prefix . '/' . $FileName, file_get_contents($FilePath)); //存储文件

                    return [['status' => true, 'img' => $prefix . '/' . $FileName]];
                }
            }
        }

        return ['Not an img file!', 400];
    }

    /**
     * 上传图片
     * @param string $path
     * @return array
     */
    public function uploadXlsx($path = 'merchant', $prefix = 'xlsx'): array
    {
        if (request()->hasFile('file_data')) {
            $tmp       = request()->file('file_data');
            $fileTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'];

            if ($tmp) {
                if (($tmp->getClientOriginalExtension() == 'xlsx' || in_array($tmp->getMimeType(), $fileTypes)) && $tmp->isValid()) {
                    $FileType = $tmp->getClientOriginalExtension(); //获取文件后缀
                    $FilePath = $tmp->getRealPath(); //获取文件临时存放位置
                    $FileName = uniqid() . '.' . $FileType; //定义文件名
                    Storage::disk($path)->put('/' . $prefix . '/' . $FileName, file_get_contents($FilePath)); //存储文件

                    return [['status' => true, 'xlsx' => $prefix . '/' . $FileName]];
                }
            }
        }

        return ['Not an xlsx file!', 400];
    }
}
