<?php

namespace App\Services;

use App\Models\Chargeback;
use App\Models\ChargebackHistory;
use App\Models\DirectoryChargebackCode;
use Dcat\Admin\Admin;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class ChargebackService
{
    /**
     * 拒付申请
     *
     * @param $orderId
     * @param array $params
     * @return array | bool
     */
    public static function chargebackSave($orderId, array $params)
    {
        $chargeback = Chargeback::where('order_id', $orderId)->where('use_flag', 0)->first();

        if (!$chargeback) {
            $chargebackId                  = Chargeback::createChargebackId();
            $chargeback                    = new Chargeback();
            $chargeback->chargeback_id     = $chargebackId;
            $chargeback->status            = Chargeback::STATUS_PENDING;
            $chargeback->chargeback_status = Chargeback::CHARGEBACK_STATUS_ONE_TIME_REFUSAL;
            $chargeback->is_appeal_success = Chargeback::IS_APPEAL_SUCCESS_FALSE;  // TODO 暂无申诉功能, 暂默认设置值
        } else {
            $chargebackId = $chargeback->chargeback_id;
        }

        $chargeback->order_id            = $orderId;
        $chargeback->chargeback_deadline = $params['chargeback_deadline'];
        $chargeback->remarks             = $params['chargeback_remarks'];
        $chargeback->chargeback_at       = $params['chargeback_at'];

        $chargebackHistory = self::chargebackHistorySave($orderId, $chargebackId, $params);

        DB::beginTransaction();
        try {

            if (ChargebackHistory::where(['arn' => $params['arn']])->exists()) {
                throw new \Exception('该Arn已存在, 不允许录入');
            }

            if (!$chargeback->save()) {
                throw new \Exception('拒付工单保存失败');
            }

            if (!$chargebackHistory->save()) {
                throw new \Exception('拒付录入记录保存失败');
            }

            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            return ['success' => false, 'errMsg' => $exception->getMessage()];
        }

        return ['success' => true, 'chargeback_id' => $chargebackId, 'chargeback' => $chargeback, 'chargebackHistory' => $chargebackHistory];
    }

    /**
     * 拒付录入记录
     *
     * @param $orderId
     * @param $chargebackId
     * @param array $params
     * @param boolean $isError
     * @param string $errorMsg
     * @return object | bool
     */
    public static function chargebackHistorySave($orderId, $chargebackId, array $params, bool $isError = false, string $errorMsg = '')
    {
        $chargebackType    = DirectoryChargebackCode::where('code', $params['chargeback_code'])->value('type');
        $chargebackHistory = ChargebackHistory::where(['chargeback_id' => $chargebackId, 'order_id' => $orderId])->first();

        if (!$chargebackHistory) {
            $chargebackHistory                = new ChargebackHistory();
            $chargebackHistory->id            = ChargebackHistory::createChargebackHistoryId();
            $chargebackHistory->chargeback_id = $chargebackId;
            $chargebackHistory->order_id      = $orderId;
            $chargebackHistory->status        = ChargebackHistory::STATUS_ONE_TIME_REFUSAL;
        }

        $chargebackHistory->currency        = $params['chargeback_currency'];
        $chargebackHistory->amount          = $params['chargeback_amount'];
        $chargebackHistory->type            = $chargebackType;
        $chargebackHistory->chargeback_code = $params['chargeback_code'];
        $chargebackHistory->chargeback_at   = $params['chargeback_at'];
        $chargebackHistory->originator      = Admin::user()->isAdministrator() ? 1 : 0;
        $chargebackHistory->input_status    = ChargebackHistory::INPUT_STATUS_SUCCESS;
        $chargebackHistory->arn             = $params['arn'];

        if ($isError) {
            $chargebackHistory->input_fail_cause = $errorMsg;
        }

        return $chargebackHistory;
    }

	/*
	 * @description: 校验拒付录入(表格or手动)时 是否存在相同的缓存
	 * @author: zqc
	 * @date: 2023/9/22
	 **/
	public static function checkImport($value)
	{
        $key = 'chargebackImport' . $value;

        if (Cache::has($key)) {
            // 缓存已存在，跳过处理
            return true;
        }
        
        // 存储缓存
        Cache::add($key, 1, 1800);
        return false;
	}
}
