<?php
namespace App\Services;

use App\Jobs\SendNotice;
use App\Models\ChargebackCase;

class AlertService
{
    private static $services = [];

    public static function getService($type)
    {
        switch ($type) {
            case ChargebackCase::ALERT_FROM_WINTRANX:
                $className = 'WintranxService';
                break;

            default:
                $className = 'CdrnService';
                break;
        }

        if (!isset(self::$services[$className])) {
            $class = "\App\Services\Alert\\" . $className;
            self::$services[$className] = new $class();
        }

        return self::$services[$className];
    }

    /**
     * 发送预警工单异常告警
     *
     * @param $data
     */
    public static function sendAlertWarning($data)
    {
        if (!empty($data)) {
            $appName = env('APP_NAME', 'Laravel');
            $content  = sprintf(
                "[%s系统预警工单异常告警]\n告警系统: %s\n预警来源: %s\n预警服务: %s\nCase ID: %s\n异常类型: %s",
                $appName,
                $appName,
                ChargebackCase::$alertFromMap[$data['alert_from']] ?? '',
                ChargebackCase::$from[$data['chargeback_from']] ?? '',
                $data['case_id'] ?? '',
                ChargebackCase::$warningTypeMap[$data['warning_type']] ?? '',
            );

            $noticeData = [
                'level'             => 1,
                'contents'          => $content,
                'notice_user_roles' => 'Administrator,AlertWarning',
                'type'              => 3,
                'status'            => 2,
            ];

            dispatch(new SendNotice($noticeData, 5));
        }
    }
}
