<?php

namespace App\Services;

use App\Models\CardHolder;
use App\Models\DirectoryCc;
use App\Models\MerchantBusiness;
use DateTime;
use DateTimeZone;
use DES3;

class ToolService
{
    /*
     * 递归修改数组key值  目前用于虚拟卡卡号 日记卡掩码显示
     * @param array $haystack
     * @param string $needle
     * @return array
     */
    public static function modifyValueOfKey($haystack, $needle, $isEncrypt = true)
    {
        $result = [];
        foreach ($haystack as $key => $value) {
            if (is_array($value)) {
                $result[$key] = self::modifyValueOfKey($value, $needle, $isEncrypt);
            } else {
                $result[$key] = ($key === $needle) ? get_markcard($isEncrypt ? DES3::encrypt($value, env('DES3_CARD_VIRTUAL')) : $value) : $value;
            }
        }
        return $result;
    }

    public static function isAgeInRange($birthdate)
    {
        if (empty($birthdate)) {
            return false;
        }

        $birthdate = new DateTime($birthdate);
        $today     = new DateTime('now');
        $interval  = $birthdate->diff($today);
        return ($interval->y >= 18 && $interval->y <= 65);
    }

    public static function desensitizeIdNumber($idNumber, $type = ''): string
    {
        switch ($type) {
            case CardHolder::CARD_HOLDER_ID_TYPE_RESIDENT:
                // 身份证号脱敏
                if (strlen($idNumber) != 18) {
                    return 'Invalid ID card number';
                }
                $maskedNumber = substr($idNumber, 0, 6) . str_repeat('*', 8) . substr($idNumber, 14);
                break;
            // 其他证件全加密了
            default:
                $maskedNumber = get_mark_data($idNumber);
                break;
        }

        return $maskedNumber;
    }

    /**
     * @description:
     * 对手机号码进行脱敏处理，一般保留前三位和后四位，中间用星号代替
     * @param $phone
     * @return string
     * @author: zqc
     * @date: 2024/7/15
     */
    public static function desensitizePhone($phone): string
    {
        if (empty($phone)) {
            return '-';
        }

        return substr($phone, 0, 3) . str_repeat('*', 4) . substr($phone, 7);
    }

    /**
     * @description:
     * 将给定的东八区时间转换为系统时区时间。
     * @author: zqc
     * @date: 2024/6/27
     */
    public static function convertToSystemTimeZone(string $authorizationDate): string
    {
        if (empty($authorizationDate)) {
            return '';
        }

        $dateTime = new DateTime($authorizationDate, new DateTimeZone('Asia/Shanghai'));  // 假设原始时间为 东八区 时间
        // 获取系统时区
        $systemTimeZone = date_default_timezone_get();
        $dateTime->setTimezone(new DateTimeZone($systemTimeZone));
        return $dateTime->format('Y-m-d H:i:s');
    }

    /**
     * @description:
     * 判断文件是否是压缩文件
     * @author: zqc
     * @date: 2024/7/11
     */
    public static function isCompressedFile($filePath)
    {
        $info      = pathinfo($filePath);
        $extension = strtolower($info['extension']);

        $compressExtensions = ['zip', 'rar', '7z', 'gz'];

        return in_array($extension, $compressExtensions);
    }

    /**
     * @description: 用于从数组中删除指定的键
     * 接受一个数组和一个键作为参数，函数遍历数组中的每个元素。
     * 对于每个元素，它首先检查键是否与要删除的键匹配。
     * 如果匹配，则使用unset函数从数组中删除该键。
     * 如果键不匹配，并且该元素本身是一个数组，则递归地调用removeArrayByKey函数，以便在该子数组内查找和删除指定的键。
     * @author: zqc
     * @date: 2023/10/17
     **/
    public static function removeArrayByKey($array, $keyToRemove)
    {
        $values = [];
        if (!is_array($array) || empty($array) || empty($keyToRemove)) {
            return $values;
        }

        foreach ($array as $key => $value) {
            if ($key === $keyToRemove) {
                unset($array[$key]);
                $values = $array;
            } elseif (is_array($value)) {
                $values = array_merge($values, self::removeArrayByKey($value, $keyToRemove));
            }
        }
        return $values;
    }

    /**
     * @description: 获取嵌套数组中指定某个键的值。
     * 接受一个数组和一个键作为参数，并递归地遍历数组中的每个元素。
     * 对于每个元素，它会检查键是否与当前元素的键匹配，如果是，则返回当前元素的值。
     * 如果它是一个数组，它会递归地调用getValuesByKeyName()方法来查找指定键的值。
     * 最后，如果找不到指定键的值，它会返回空数组。
     * @author: zqc
     * @date: 2023/10/8
     **/
    public static function getValuesByKeyName($array, $keyName)
    {
        $values = [];
        if (!is_array($array) || empty($array) || empty($keyName)) {
            return $values;
        }

        foreach ($array as $key => $value) {
            if ($key === $keyName) {
                $values[] = $value;
            } elseif (is_array($value)) {
                $values = array_merge($values, self::getValuesByKeyName($value, $keyName));
            }
        }
        return $values;
    }


    /**
     * @description: 计算嵌套数组中的元素数量
     * 计算给定数组中的元素数量。接受一个数组作为参数，并递归地遍历数组中的每个元素。
     * 对于每个元素，如果它是一个数组，它会递归地调用countElements()方法来计算子数组中的元素数量。
     * 如果元素不是数组，它会将计数器加1。
     * 最后，它返回数组中的元素数量。
     * @author: zqc
     * @date: 2023/10/8
     **/
    public static function countElements($array)
    {
        $count = 0;
        if (!is_array($array) || empty($array)) {
            return $count;
        }

        foreach ($array as $element) {
            if (is_array($element)) {
                $count += self::countElements($element);
            } else {
                $count++;
            }
        }
        return $count;
    }

    /**
     * @description:
     * 将给定的列索引转换为对应的字母表示，以便用于标识 Excel 表格中的列。
     * 0 => A列 1 => B列 以此类推
     * 如果 $pColumnIndex 小于26，它将简单地将索引与大写字母A相加，返回对应的字母。
     * 如果 $pColumnIndex 大于等于26但小于702，它将首先计算出第一个字母的索引，并将其与大写字母A相加，然后计算出第二个字母的索引，并将其与大写字母A相加。这样就能得到两个字母的组合表示。
     * 如果 $pColumnIndex 大于等于702，它将计算出第一个、第二个和第三个字母的索引，并分别将它们与大写字母A相加，以得到三个字母的组合表示。
     * @author: ysl
     * @date: 2023/9/19
     **/
    public static function stringFromColumnIndex(int $pColumnIndex = 0): string
    {
        if ($pColumnIndex < 26) {
            $index = chr(65 + $pColumnIndex);
        } elseif ($pColumnIndex < 702) {
            $index = chr(64 + intdiv($pColumnIndex, 26)) . chr(65 + $pColumnIndex % 26);
        } else {
            $index = chr(64 + (intdiv(($pColumnIndex - 676), 26))) .
                chr(65 + intdiv((($pColumnIndex - 26) % 676), 26)) .
                chr(65 + $pColumnIndex % 26);
        }
        return $index;
    }

    /**
     * @description:
     * 获取本月1号至今日00:00:00前,且上个月的时间不继承到本月,本月仅从1日00:00:00开始至当天00:00:00前截至
     * 例如今天是2023年9月1号,那就获取8月1号到8月31号的时间,如果今天是9月2号,那就获取9月1号到2号的时间。
     * @author: zqc
     * @date: 2023/9/11
     **/
    public static function getCurrentTimeRange()
    {
        $currentDateTime = new DateTime();
        $currentMonth    = $currentDateTime->format('m');
        $currentYear     = $currentDateTime->format('Y');
        // 获取本月1号的日期
        $firstDayOfMonth = $currentYear . '-' . $currentMonth . '-01';
        // 获取昨天的日期
        $yesterday = clone $currentDateTime;
        $yesterday->modify('-1 day');
        $yesterdayDate = $yesterday->format('Y-m-d');
        // 判断本月1号和昨天是否在同一个月内
        if ($currentMonth == $yesterday->format('m')) {
            $date = [$firstDayOfMonth, $yesterdayDate];
        } else {
            // 获取上个月的起始时间和截止时间
            $lastMonthStartDate = date('Y-m-01', strtotime('-1 month', strtotime($firstDayOfMonth)));
            $lastMonthEndDate   = date('Y-m-t', strtotime('-1 month', strtotime($firstDayOfMonth)));
            $date               = [$lastMonthStartDate, $lastMonthEndDate];
        }
        return $date;
    }

    /**
     * @description: 获取随机颜色
     * @author: zqc
     * @date: 2023/9/7
     **/
    public static function getRandomColor($length = 300)
    {
        $colors = [];
        for ($i = 0; $i < $length; $i++) {
            $randomColor = sprintf('#%06X', mt_rand(0, 0xFFFFFF));
            $colors[]    = $randomColor;
        }
        return $colors;
    }

    /**
     * MID过滤
     *
     * @param array $input
     * @return array
     */
    public static function merchantFilter(array $input): array
    {
        $filter = [];

        if (isset($input['merchant_id']) && strlen($input['merchant_id']) > 0) {
            $filter['merchant_id'] = $input['merchant_id'];
        }

        if (isset($input['business_id']) && strlen($input['business_id']) > 0) {
            $business = MerchantBusiness::find($input['business_id']);

            if (isset($filter['merchant_id'])) {
                if (empty($business) || $business->merchant_id != $filter['merchant_id']) {
                    $filter['merchant_id'] = 0;
                    $filter['business_id'] = 0;
                }
            } else {
                $filter['merchant_id'] = !empty($business) ? $business->merchant_id : '0';
                $filter['business_id'] = $input['business_id'];
            }
        }

        return $filter;
    }

    /**
     * @description: 百分比计算
     * @param: $molecular 分子
     * @param: $denominator 分母
     * @param: $prec 保留小数位数
     * @author: zqc
     * @date: 2023/8/28
     **/
    public static function perCalculate($molecular, $denominator, $prec = 4)
    {
        if (!$molecular || !floatval($denominator)) {
            $ret = '0.' . str_repeat('0', $prec) . '%';
        } else {
            $ret = number_format(round($molecular / $denominator * 100, $prec), $prec) . "%";
        }

        return $ret;
    }

    /**
     * @description: 获取两个日期之间的所有日期
     * 注意日期使用的是标准的 "Y-m-d" 格式。
     * @param: $startDate 起始日期
     * @param: $endDate 结束日期
     * @author: zqc
     * @date: 2023/8/28
     **/
    public static function getAllDaysInRange($startDate, $endDate): array
    {

        $dateArr = [$endDate];
        while ($startDate < $endDate) {
            $dateArr[] = $startDate;
            $startDate = date('Y-m-d', strtotime($startDate . ' +1 day'));
        }

        rsort($dateArr);
        return $dateArr;
    }

    /**
     * @description: 计算两个时间段之间的天数
     * @param: $startDate 开始日期
     * @param: $endDate 结束日期
     * @author: zqc
     * @date: 2023/8/28
     **/
    public static function getNumberOfDays($startDate, $endDate)
    {
        $startTimestamp = strtotime($startDate);
        $endTimestamp   = strtotime($endDate);
        $numberOfDays   = round(($endTimestamp - $startTimestamp) / (60 * 60 * 24));
        return (int)$numberOfDays + 1; // 加上最后一天
    }

    /**
     * 获取要显示的统计卡种
     *
     * @return array
     */
    public static function getCcType()
    {
        $ccTypeData = [];
        $ccTypeArr  = DirectoryCc::where('is_risk_control', DirectoryCc::IS_RISK_CONTROL_OPEN)
            ->select('cc_type')
            ->orderBy('id')
            ->get()->toArray();
        if (!empty($ccTypeArr)) {
            foreach ($ccTypeArr as $vo) {
                $ccType                                  = strtolower($vo['cc_type']);
                $ccTypeData["{$ccType}_amount_usd"]      = 0;
                $ccTypeData["{$ccType}_amount_usd_prec"] = 0;
            }
        }

        return $ccTypeData;
    }
}