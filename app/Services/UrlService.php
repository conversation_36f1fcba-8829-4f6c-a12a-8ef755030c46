<?php


namespace App\Services;

use Pdp\Rules;

class UrlService
{

    public static function _getMainUrlName(string $url): string
    {
        //判断主域名重复情况
        if (preg_match('/^http(s)?:\\/\\/.+/', $url)) {
            $host = rtrim(parse_url($url, PHP_URL_HOST), '/');
        } else {
            $host = rtrim($url, '/');
        }

        $rules = Rules::createFromPath(storage_path('app/public/public-suffix.dat'));

        return $rules->resolve($host)->getRegistrableDomain() ?? $url;
    }
}
