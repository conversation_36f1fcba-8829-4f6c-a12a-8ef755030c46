<?php
namespace App\Services;

use App\Models\CardWhiteList;
use App\Models\Channel;
use App\Models\ChannelPid;
use App\Models\Order;
use App\Models\OrderSettlement;
use App\Models\SettleDetail;
use Illuminate\Database\Eloquent\Builder;

class ExportService
{
    public static $functionData = [
        'orderExportQuery',
        'settlementOrderQuery',
        'settlementDetailQuery',
        'cardWhiteListExportQuery'
    ];

    public static $week = ['1', '2', '3', '4', '5'];

    public static $time = ['17:00:00', '19:00:00'];

    /**
     * 是否满足限制
     *
     * @param $function
     * @param $queryCriteria
     * @return boolean
     */
    public static function isLimitation($function, array $queryCriteria)
    {
        $tempTime = self::$time;

        if (in_array($function, self::$functionData)
            && in_array(date('w'), self::$week)
            &&  (date('H:i:s') >= $tempTime[0]  && date('H:i:s') <= $tempTime[1])
        ) {
            $query = self::$function($queryCriteria);
            $count = $query->count();

            if ($count > 50000) {
                return true;
            }
        }

        return false;
    }

    public static function orderExportQuery(array $queryCriteria)
    {
        $queryOrder = Order::with([
            'track:order_id,tracking_type,tracking_number',
            'card:id,cc_type,card_mask,card_country,card_number',
            'address:id,bill_name,bill_email,bill_country,bill_address,ship_name,ship_email,ship_country,ship_address,ip,bill_city,bill_state,bill_postcode,bill_phone,ship_city,ship_state,ship_postcode,ship_phone',
            'relation:order_id,is_refund,is_chargeback',
            'paymentOrder:order_id,order_number,payment_order_id,amount,currency,code,result,remark',
            'products:order_id,name,type,url,qty',
            'settlements:order_id,amount_usd,payment_amount_usd,is_settle,settle_at',
        ])->select('orders.*')->join('order_cards', 'orders.card_id', '=', 'order_cards.id');
        foreach ($queryCriteria['inputs'] as $key => $value) {
            switch ($key) {
                case 'main_bodys_id':
                    $channelIds     = [];
                    $channelPidInfo = ChannelPid::with('Channel:id,channel_pid_id')
                        ->select('id', 'main_bodys_id')
                        ->where('main_bodys_id', $value)
                        ->get()->toArray();

                    foreach ($channelPidInfo as $key => $channelData) {
                        foreach ($channelData['channel'] as $channelId) {
                            $channelIds[] = $channelId['id'];
                        }
                    }

                    if ($channelIds) {
                        $queryOrder->whereIn('channel_id', $channelIds);
                    }

                    break;
                case strpos($key, 'channelObj'):
                    $column = trim(strrchr($key, '.'), '.');
                    $queryOrder->whereHas('channelObj', function ($query) use ($value, $column) {
                        $query->where($column, $value);
                    });
                    break;
                case 'cc_type':
                    $queryOrder->where('order_cards.' . $key, $value);
                break;
                case 'card_mask':
                    if (strlen($value) == 6) {
                        $queryOrder->where('order_cards.' . $key, 'like', $value.'%');
                    } else {
                        $queryOrder->where('order_cards.' . $key, $value);
                    }
                    break;
                case 'created_at.end':
                    $queryOrder->where('orders.created_at', '<=', $value);
                    break;
                case 'created_at.start':
                    $queryOrder->where('orders.created_at', '>=',$value);
                    break;
                case strpos($key, 'address'):
                    $column = trim(strrchr($key, '.'), '.');
                    $queryOrder->whereHas('address', function ($query) use ($value, $column) {
                        $query->where('order_addresses.' . $column, $value);
                    });
                    break;
                case strpos($key, 'relation'):
                    $column = trim(strrchr($key, '.'), '.');
                    $queryOrder->whereHas('relation', function ($query) use ($value, $column) {
                        $query->where('order_relations.' . $column, $value);
                    });
                    break;
                case strpos($key, 'paymentOrder'):
                    $column = trim(strrchr($key, '.'), '.');
                    $queryOrder->whereHas('paymentOrder', function ($query) use ($value, $column) {
                        $query->where($column, $value);
                    });
                    break;
            }
        }

        $query = self::addConditions($queryOrder, $queryCriteria['filter'] ?? []);

        return $query;
    }

    public static function settlementOrderQuery(array $queryCriteria)
    {
        $queryOrderSettlement = OrderSettlement::query()->whereIn('type', [
            OrderSettlement::TYPE_SALE,
            OrderSettlement::TYPE_CAPTURE,
            OrderSettlement::TYPE_REFUND,
            OrderSettlement::TYPE_CHARGEBACK,
            OrderSettlement::TYPE_CHARGEBACK_REVERSAL
        ])->where('status', OrderSettlement::STATUS_APPROVED);

        foreach ($queryCriteria['inputs'] as $key => $value) {
            switch ($key) {
                case 'channel_id':
                    $channelList = Channel::where('channel_supplier_id', $value)->get();
                    $tempArr     = array();

                    foreach ($channelList as $channel) {
                        array_push($tempArr, $channel->id);
                    }

                    if (sizeof($tempArr) > 0) {
                        $queryOrderSettlement->whereIn('channel_id', $tempArr);
                    } else {
                        $queryOrderSettlement->where('channel_id', '-1');
                    }
                    break;

                case strpos($key, 'completed_at'):
                    $queryOrderSettlement->WithForcedIndex('order_settlements_completed_at_index');
                    break;
            }
        }

        $query = self::addConditions($queryOrderSettlement, $queryCriteria['filter'] ?? []);

        return $query;
    }

    public static function settlementDetailQuery(array $queryCriteria)
    {
        $querySettlementDetail = SettleDetail::query()->where('settle_amount', '<>', '0');

        $query = self::addConditions($querySettlementDetail, $queryCriteria['filter'] ?? []);

        return $query;
    }

    public static function cardWhiteListExportQuery(array $queryCriteria)
    {
        $queryCardVirtual = CardWhiteList::query();

        $query = self::addConditions($queryCardVirtual, $queryCriteria['filter'] ?? []);

        return $query;
    }

    /**
     * Add conditions to grid model.
     *
     * @param Builder $query
     * @param array $conditions
     *
     * @return Builder
     */
    public static function addConditions(Builder $query, array $conditions): Builder
    {
        sort($conditions);

        foreach ($conditions as $condition) {
            call_user_func_array([$query, key($condition)], current($condition));
        }
        return $query;
    }

    /**
     * 获取导出标题的中英翻译
     *
     * @param array $exportTitle
     * @return array
     */
    public static function getExportTitle(array $exportTitle, string $lang = 'zh_CN', string $file = 'export.fields.'): array
    {
        $result = [];

        if (count($exportTitle)) {
            foreach ($exportTitle as $value) {
                if ($lang == 'zh_CN') {
                    $result[] = admin_trans($file . $value, [], $lang) . ' / ' . admin_trans($file . $value, [], 'en');
                } else {
                    $result[] = admin_trans($file . $value, [], $lang);
                }
            }
        }

        return $result;
    }

    /**
     * 获取字段翻译
     *
     * @param string $field
     * @param string $lang
     * @return string
     */
    public static function getFieldTranslation(string $field, string $lang = 'zh_CN'): string
    {
        if (empty($field)) {
            return '';
        }

        return admin_trans('export.fields.' . $field, [], $lang);
    }

    /**
     * 获取当前语言
     */
    public static function getLang()
    {
        $lang = request()->session()->get('lang') ?? 'zh_CN';

        if (strtolower(mb_substr($lang, 0, 2)) == 'en') {
            $lang = 'en';
        }

        return $lang;
    }
}
