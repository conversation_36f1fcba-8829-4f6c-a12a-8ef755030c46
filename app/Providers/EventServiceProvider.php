<?php

namespace App\Providers;

use App\Events\CardBatchAsync;
use App\Events\ExportTask;
use App\Events\OrderPaid;
use App\Events\Notice;
use App\Events\RefundPaid;
use App\Events\TrackApplied;
use App\Events\ChargebackSettle;
use App\Events\ChargebackReversalSettle;
use App\Events\LocalOrderPaid;
use App\Events\LocalRefundPaid;
use App\Events\RiskWarn;
use App\Events\LocalRiskWarn;
use App\Listeners\BatchAsyncShareCardLimit;
use App\Listeners\CardBatchApiNotice;
use App\Listeners\CardBindCardHolder;
use App\Listeners\CreateExportTask;
use App\Listeners\CreateOrderRelations;
use App\Listeners\CreateOrderSettlements;
use App\Listeners\CreateTrackingTasks;
use App\Listeners\OrderChannelCount;
use App\Listeners\OrderConfirmedEmails;
use App\Listeners\OrderNoticeTasks;
use App\Listeners\OrderRefundComplaints;
use App\Listeners\OrderRelations;
use App\Listeners\OrderRefundNotice;
use App\Listeners\ShareCardLimit;
use App\Listeners\SysNotice;
use App\Listeners\Test;
use App\Listeners\UpdateChargebackCase;
use App\Listeners\UpdateOrderAddress;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;
use App\Events\Virtual;
use App\Listeners\CreateLocalOrderRelations;
use App\Listeners\LocalOrderNoticeTasks;
use App\Listeners\LocalOrderRefundNotice;
use App\Listeners\LocalOrderRelations;
use App\Listeners\LocalRefundSysNotice;
use App\Listeners\LocalSysNotice;
use App\Listeners\RefundSysNotice;
use App\Listeners\VirtualApply;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        Registered::class               => [
            SendEmailVerificationNotification::class,
        ],
        OrderPaid::class                => [
            // Test::class,
            // OrderNoticeTasks::class,
            OrderConfirmedEmails::class,
            CreateOrderRelations::class,
            CreateOrderSettlements::class,
            OrderChannelCount::class,
            UpdateOrderAddress::class,
        ],
        LocalOrderPaid::class           => [
            LocalOrderNoticeTasks::class,
            CreateLocalOrderRelations::class,
            // CreateOrderSettlements::class,
        ],
        RefundPaid::class               => [
            OrderRefundNotice::class,
            OrderRefundComplaints::class,
            OrderRelations::class,
            CreateOrderSettlements::class,
            RefundSysNotice::class,
            UpdateChargebackCase::class
        ],
        LocalRefundPaid::class          => [
            LocalOrderRefundNotice::class,
            // OrderRefundComplaints::class,
            LocalOrderRelations::class,
            // CreateOrderSettlements::class
            LocalRefundSysNotice::class
        ],
        TrackApplied::class             => [
            CreateTrackingTasks::class,
            OrderRelations::class
        ],
        ChargebackSettle::class         => [
            CreateOrderSettlements::class
        ],
        ChargebackReversalSettle::class => [
            CreateOrderSettlements::class
        ],
        Virtual::class                  => [
            VirtualApply::class,
        ],
        Notice::class                   => [
            OrderNoticeTasks::class,
        ],
        RiskWarn::class                 => [
            SysNotice::class,
        ],
        LocalRiskWarn::class            => [
            LocalSysNotice::class,
        ],
        CardBatchAsync::class => [
            CardBindCardHolder::class,
            ShareCardLimit::class,
            CardBatchApiNotice::class,
        ],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();

        //
    }
}
