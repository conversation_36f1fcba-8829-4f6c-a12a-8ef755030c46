<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Route;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * This namespace is applied to your controller routes.
     *
     * In addition, it is set as the URL generator's root namespace.
     *
     * @var string
     */
    protected $namespace = 'App\Http\Controllers';

    /**
     * The path to the "home" route for your application.
     *
     * @var string
     */
    public const HOME = '/home';

    /**
     * Define your route model bindings, pattern filters, etc.
     *
     * @return void
     */
    public function boot()
    {
        //

        parent::boot();
    }

    /**
     * Define the routes for the application.
     *
     * @return void
     */
    public function map()
    {
        $this->mapApiRoutes();

        $this->mapWebRoutes();

        $this->mapImRoutes();

        //
    }

    /**
     * Define the "web" routes for the application.
     *
     * These routes all receive session state, CSRF protection, etc.
     *
     * @return void
     */
    protected function mapWebRoutes()
    {
        $serviceType = config('app.service_type');

        if ($serviceType == 'm' || $serviceType == 'default') {
            Route::middleware('web')
                ->namespace($this->namespace)
                ->group(base_path('routes/web.php'));
        } else {
            Route::middleware('checkPermission')
                ->namespace($this->namespace)
                ->group(base_path('routes/web.php'));
        }


    }

    /**
     * Define the "api" routes for the application.
     *
     * These routes are typically stateless.
     *
     * @return void
     */
    protected function mapApiRoutes()
    {
        $serviceType = config('app.service_type');

        if ($serviceType == 'g' || $serviceType == 'default') {
            Route::prefix('api')
                ->middleware('api')
                ->namespace($this->namespace)
                ->group(base_path('routes/api.php'));
        } else {
            Route::prefix('api')
                ->middleware('checkApiPermission')
                ->namespace($this->namespace)
                ->group(base_path('routes/api.php'));
        }
    }

    protected function mapImRoutes()
    {
        $serviceType = config('app.service_type');

        if ($serviceType == 'l' || $serviceType == 'default') {
            Route::prefix('open_im')
                 ->middleware('im')
                 ->namespace($this->namespace)
                 ->group(base_path('routes/im.php'));
        } else {
            Route::prefix('open_im')
                 ->middleware('checkImPermission')
                 ->namespace($this->namespace)
                 ->group(base_path('routes/im.php'));
        }
    }
}
