<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Schema;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        \App\Models\DirectoryRegion::observe(\App\Observers\DirectoryRegionObserver::class);
        \App\Models\OrderCard::observe(\App\Observers\OrderCardObserver::class);
        \App\Models\OrderAddress::observe(\App\Observers\OrderAddressObserver::class);
        \App\Models\LocalOrderAddress::observe(\App\Observers\LocalOrderAddressObserver::class);
        \App\Models\User::observe(\App\Observers\UserObserver::class);
	    \App\Models\CardSource::observe(\App\Observers\CardSourceObserver::class);
	    \App\Models\OrderPost::observe(\App\Observers\OrderPostObserver::class);
	    \App\Models\DirectoryDictionary::observe(\App\Observers\DirectoryDictionaryObserver::class);
	    Schema::defaultStringLength(191);
    }
}
