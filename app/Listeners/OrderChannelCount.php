<?php

namespace App\Listeners;

use App\Classes\Risk\Risk;
use App\Events\OrderPaid;
use App\Jobs\SendSlsLog;
use App\Models\Channel;
use App\Models\MerchantBusiness;
use App\Models\Order;
use App\Services\TransactionAlarmService;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Cache;

class OrderChannelCount implements ShouldQueue
{

    public function handle(OrderPaid $event)
    {
        // 设置时区为UTC，因为队列时区为PRC，导致时间错误
		date_default_timezone_set('UTC');
        // 获取交易数据
        $order = $event->getOrder();

			//连续10笔失败规则
//        $this->channelTransactionFail($order);

		//卡，渠道24小时内不重复规则
		$this->channelCardLimit($order);

		//BID支付成功笔数限制
		$this->paymentsLimit($order);

		//同卡号24小时内失败3笔规则
		$this->cardContinuousFailure($order);

		//  连续失败告警(固定10笔)
		$this->continuousFailAlarm($order);
    }

	/**
	 * @description: 连续失败告警(固定10笔)
	 * @param Order $order
	 **@author: zqc
	 * @date: 2023/9/28
	 */
	public function continuousFailAlarm($order)
	{
		if (empty($order)) {
			return;
		}

		// 判断订单状态是否是 成功 or 失败  and 非系统风控拦截
		if (in_array($order->status, [Order::STATUS_DECLINED, Order::STATUS_APPROVED]) && ($order->risk_level == Order::RISK_LEVEL_LOW)) {
			// 根据账单id获取对应的渠道名跟PID
			$channelData = Channel::with(['channelSupplier:id,supplier_name', 'channelPid:id,channel_pid'])
				->select('id', 'channel_pid_id', 'channel_supplier_id')
				->where('id', $order->channel_id)
				->get()->toArray();

			$channelPidId = $channelData[0]['channel_pid']['id'] ?? '';
			$supplierId   = $channelData[0]['channel_supplier']['id'] ?? '';
			$supplierName = $channelData[0]['channel_supplier']['supplier_name'] ?? '';
			$supplierData = [
				'channel_pid'   => $channelPidId,
				'supplier_id'   => $supplierId,
				'supplier_name' => $supplierName,
			];

			$cacheMerchantData = Cache::get(TransactionAlarmService::$continuousFailAlarmCacheKey . $order->merchant_id);
			$cacheChannelData  = Cache::get(TransactionAlarmService::$continuousFailAlarmCacheKey . $supplierId);
			$cacheMerchantData = json_decode($cacheMerchantData, true);
			$cacheChannelData  = json_decode($cacheChannelData, true);

			try {
				TransactionAlarmService::continuousFailAlarmToChannel($order, $cacheChannelData, $supplierData);
				TransactionAlarmService::continuousFailAlarmToMerchant($order, $cacheMerchantData);
			} catch (Exception $exception) {
				logger()->channel('intercept')->warning(
					'连续失败告警',
					['exception' => $exception, 'time' => time()]
				);
				dispatch(new SendSlsLog(
					['message' => '连续失败告警'],
					['exception' => $exception, 'time' => time()],
					'warning',
					'intercept'
				));
			}
		}
	}


	/**
     * 连续10笔失败规则
     *
     * @param $order
     */
    public function channelTransactionFail($order)
    {
        if (in_array($order->status, [Order::STATUS_DECLINED, Order::STATUS_APPROVED]) && in_array($order->type, [Order::TYPES_SALE, Order::TYPES_AUTH])) {
            $cacheError  = Cache::get('Channel_Transaction_fail_State', []);
            $errorNumber = $cacheError[$order->channel] ?? 0;

            if ($order->status == Order::STATUS_DECLINED) {
                $errorNumber                 = $errorNumber + 1;
                $cacheError[$order->channel] = $errorNumber;

                if ($errorNumber >= 10) {
                    Channel::where('id', $order->channel_id)->update(['status' => Channel::STATUS_DISABLE]);
                    $cacheError[$order->channel] = 0;
                }
            }

            if ($order->status == Order::STATUS_APPROVED) {
                $cacheError[$order->channel] = 0;
            }

            Cache::put('Channel_Transaction_fail_State', $cacheError);
        }

    }

    /**
     * 卡，渠道24小时内不重复添加
     *
     * @param $order
     */
    public function channelCardLimit($order)
    {
        if ($order->status == Order::STATUS_APPROVED && in_array($order->type, [Order::TYPES_SALE, Order::TYPES_AUTH])) {
            $lock = Cache::lock('Card_Channel_Limit_Any_Lock_' . $order['order_id'], 5);

            if (!$lock->get()) {
                logger()->channel('intercept')->info(sprintf('触发卡，渠道24小时内不重复添加锁order_id: %s Key：%s', $order['order_id'], get_markcard($order['card']['card_number']) . '_' . $order['channel']));
                dispatch(new SendSlsLog(
                    ['message' => sprintf('触发卡，渠道24小时内不重复添加锁order_id: %s Key：%s', $order['order_id'], get_markcard($order['card']['card_number']) . '_' . $order['channel'])],
                    [],
                    'info',
                    'intercept'
                ));

                return;
            }

            $keyName    = 'Card_Channel_Limit_Any_' . $order['card']['card_number'] . '_' . $order['channel'];
            $cardLimits = $order['channelObj']['card_limits'];

            if (empty($cardLimits)) {
                $lock->release();
                return;
            }

            $completedAt = $order->completed_at->toDateTimeString();

            foreach ($cardLimits as $limit) {
                if (isset($limit['cc_type']) && $limit['cc_type'] == $order['card']['cc_type']
                    && isset($limit['card_limit']) && $limit['card_limit'] > 0) {
                    if (!isset($limit['card_limit_time']) || $limit['card_limit_time'] <= 0) {
                        $limit['card_limit_time'] = 1;
                    }

                    $cacheData = Cache::get($keyName);
                    $time      = strtotime($completedAt) - $limit['card_limit_time'] * 3600;

                    if ($cacheData) {
                        $cacheData = json_decode($cacheData, true);

                        if (count($cacheData) >= $limit['card_limit']) {
                            if ($time > strtotime(min($cacheData))) {
                                //删除缓存里最小时间，并插入新值
                                sort($cacheData);
                                array_shift($cacheData);
                                $cacheData[] = $completedAt;
                            }
                        } else {
                            $cacheData[] = $completedAt;
                        }
                    } else {
                        $cacheData = [$completedAt];
                    }

                    Cache::put($keyName, json_encode($cacheData), $limit['card_limit_time'] * 3600);
                }
            }
            $lock->release();
        }
    }

    /**
     * 验证BID成功笔数限制
     * @param $order
     * @return void
     */
    public function paymentsLimit($order)
    {
        if ($order->status == Order::STATUS_APPROVED && in_array($order->type, [Order::TYPES_SALE, Order::TYPES_AUTH])) {
            $lock = Cache::lock('Bid_Transaction_Card_Limit_Any_Lock_' . $order['order_id'], 5);

            if (!$lock->get()) {
                logger()->channel('intercept')->info(sprintf('触发验证BID成功笔数限制锁order_id: %s Key：%s', $order['order_id'], $order['business_id'] . md5($order['card']['card_number'])));

                dispatch(new SendSlsLog(
                    ['message' => sprintf('触发验证BID成功笔数限制锁order_id: %s Key：%s', $order['order_id'], $order['business_id'] . md5($order['card']['card_number']))],
                    [],
                    'info',
                    'intercept'
                ));

                return;
            }

            $bid        = MerchantBusiness::where('business_id', $order->business_id)->first();
            $timeLimits = $bid->time_limits;

            if (empty($timeLimits)) {
                $lock->release();
                return;
            }

            $completedAt = $order->completed_at->toDateTimeString();

            foreach ($timeLimits as $limit) {
                if (isset($limit['cc_type']) &&  $limit['cc_type'] == $order['card']['cc_type'] && isset($limit['times_limit']) && $limit['times_limit'] > 0) {
                    if (!isset($limit['limit_time']) || $limit['limit_time'] <= 0) {
                        $limit['limit_time'] = 1;
                    }

                    $keyName = 'Bid_Transaction_Card_Limit_Any_' . $order['business_id'] . md5($order['card']['card_number']);

                    if (Cache::has($keyName)) {
                        $cacheData = json_decode(Cache::get($keyName), true);

                        if (count($cacheData) >= $limit['times_limit']) {
                            $time = strtotime($completedAt) - $limit['limit_time'] * 3600;

                            if ($time > strtotime(min($cacheData))) {
                                //删除缓存里最小时间，并插入新值
                                sort($cacheData);
                                array_shift($cacheData);
                                $cacheData[] = $completedAt;
                            }

                        } else {
                            $cacheData[] = $completedAt;
                        }

                    } else {
                        $cacheData = [$completedAt] ;
                    }

                    Cache::put($keyName, json_encode($cacheData), $limit['limit_time'] * 3600);
                }
            }

            $lock->release();

//                $keyEmailName = 'Bid_Transaction_Email_Limit_' . $order->business_id . md5($order['address']['bill_email']);
//
//                if (Cache::has($keyEmailName)) {
//                    Cache::increment($keyEmailName);
//                } else {
//                    Cache::put($keyEmailName, 1, $bid->limit_time * 60 * 60);
//                }
        }
    }

    /**
     * 同卡号24小时内失败3笔规则
     *
     * @param $order
     */
    public function cardContinuousFailure($order)
    {
		//获取是否刷单商户标识
        $swipe = config('swipe.bid') == $order->business_id ? true : false;

		if ($swipe) {
			return;
		}

        $completedAt         = $order->completed_at->toDateTimeString();
        $keyName             = 'Card_Continuous_Failure_Any_' . $order->business_id . md5($order['card']['card_number']);
        list($number, $hour) = Risk::getCardContinuousFailure($order->business_id, $order->card->cc_type);

        //如果是抛投成功订单需要删除这次计数
        if ($order->throw_cnt >= 1 && $order->status == Order::STATUS_APPROVED && in_array($order->type, [Order::TYPES_SALE, Order::TYPES_AUTH])) {
            if (Cache::has($keyName)) {
                $cacheData = json_decode(Cache::get($keyName), true);
                if (isset($cacheData[$order['order_id']])) {
                    unset($cacheData[$order['order_id']]);
                    Cache::put($keyName, json_encode($cacheData), 24 * 3600);
                } else {
                    logger()->channel('intercept')->info(sprintf('同卡号24小时拦截不存在value,order_id: %s Key：%s', $order['order_id'], md5($order['card']['card_number'])));

                    dispatch(new SendSlsLog(
                        ['message' => sprintf('同卡号24小时拦截不存在value,order_id: %s Key：%s', $order['order_id'], md5($order['card']['card_number']))],
                        [],
                        'info',
                        'intercept'
                    ));
                }
            } else {
                logger()->channel('intercept')->info(sprintf('同卡号24小时拦截不存在key,order_id: %s Key：%s', $order['order_id'], md5($order['card']['card_number'])));

                dispatch(new SendSlsLog(
                    ['message' => sprintf('同卡号24小时拦截不存在key,order_id: %s Key：%s', $order['order_id'], md5($order['card']['card_number']))],
                    [],
                    'info',
                    'intercept'
                ));
            }
        }

        if (
            $order->throw_cnt == 0
            && $order->status == Order::STATUS_DECLINED
            && in_array($order->type, [Order::TYPES_SALE, Order::TYPES_AUTH])
            && $number > 0
        ) {
            if (Cache::has($keyName)) {
                $cacheData = json_decode(Cache::get($keyName), true);

                if (count($cacheData) >= $number) {
                    $time = strtotime($completedAt) - $hour * 3600;

                    if ($time > strtotime(min($cacheData))) {
                        //删除缓存里最小时间，并插入新值
                        $minKey = array_search(min($cacheData), $cacheData);
                        unset($cacheData[$minKey]);

                        $cacheData[$order['order_id']] = $completedAt;
                    }
                } else {
                    $cacheData[$order['order_id']] = $completedAt;
                }
            } else {
                $cacheData = [$order['order_id']=>$completedAt];
            }

            Cache::put($keyName, json_encode($cacheData), $hour * 3600);
        }
    }

}
