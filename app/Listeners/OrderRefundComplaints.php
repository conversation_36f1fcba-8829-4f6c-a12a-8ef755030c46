<?php

namespace App\Listeners;

use App\Events\RefundPaid;
use App\Models\OrderComplaint;
use App\Models\Refund;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class OrderRefundComplaints implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  RefundPaid  $event
     * @return void
     */
    public function handle(RefundPaid $event)
    {
        // 设置时区为UTC，因为队列时区为PRC，导致时间错误
		date_default_timezone_set('UTC');
        $order = $event->getOrder();

        if (empty($order->order_id)) {
            return false;
        }

	    // 判断退款状态是否是成功
	    if ($order->status != Refund::STATUS_APPROVED) {
		    return false;
	    }

        $orderComplaint = OrderComplaint::with(['order'])->firstWhere('order_id', $order->order_id);

        if (empty($orderComplaint)) {
            return false;
        }

        // 更新争议工单处理状态
        $orderComplaint->status = '1';

        $orderComplaint->save();
    }
}
