<?php

namespace App\Listeners;

use App\Events\OrderPaid;
use App\Models\OrderRelation;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class CreateOrderRelations implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  OrderPaid  $event
     * @return void
     */
    public function handle(OrderPaid $event)
    {
        // 设置时区为UTC，因为队列时区为PRC，导致时间错误
		date_default_timezone_set('UTC');
        $order = $event->getOrder();

        if (empty($order->order_id) || OrderRelation::where('order_id', '=', $order->order_id)->exists()) {
            return;
        }

        // 创建订单关系数据
        $orderRelations               = new OrderRelation();
        $orderRelations->order_id     = $order->order_id;
        $orderRelations->custom_token = md5($order->order_id);

        $orderRelations->save();
    }
}
