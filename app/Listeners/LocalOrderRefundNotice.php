<?php

namespace App\Listeners;

use App\Classes\Supports\Traits\HasHttpRequest;
use App\Events\LocalRefundPaid;
use App\Models\LocalOrderPost;
use App\Models\LocalRefund;
use App\Models\LocalRefundPost;
use Illuminate\Contracts\Queue\ShouldQueue;

class LocalOrderRefundNotice implements ShouldQueue
{
    use HasHttpRequest;

    public $timeout = '180';

    /**
     * Handle the event.
     *
     * @param LocalRefundPaid $event
     * @return void
     */
    public function handle(LocalRefundPaid $event)
    {
        // 设置时区为UTC，因为队列时区为PRC，导致时间错误
		date_default_timezone_set('UTC');
        // 获取交易数据
        $order      = $event->getOrder();
        $returnData = $event->getReturnData();

        // 获取商户提交数据
        $merchantPost       = LocalOrderPost::firstWhere('order_id', $order->order->order_id);
        $merchantRefundPost = LocalRefundPost::firstWhere('refund_id', $order->refund_id);
        $notify_url         = $merchantRefundPost->notify_url ?: $merchantPost->notify_url ?: '';

        // 判断是否需要添加异步通知
        if ($notify_url && $order->status == LocalRefund::STATUS_APPROVED) {
            try {
                $res = trim($this->post($notify_url, $returnData));
            } catch (\Exception $e) {
                $res = $e->getMessage();
            }

            $result = (strlen($res) > 255) ? mb_strcut($res, 0, 252) : $res;
            $status = '0';

            if (!empty($result) && $result == 'ok') {
                $status = '1';  // status=1 表示通知成功
            }

            // 添加通知任务
            $order->order->orderNoticeTask()->create([
                'order_id'   => $order->order_id,
                'content'    => json_encode($returnData),
                'notify_url' => $notify_url,
                'cnt'        => 1,
                'status'     => $status,
                'result'     => $result
            ]);
        }
    }
}
