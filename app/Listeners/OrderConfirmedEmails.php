<?php

namespace App\Listeners;

use App\Events\OrderPaid;
use App\Jobs\SendEmail;
use App\Mail\CommonTemplate;
use App\Models\Order;
use App\Notifications\OrderConfirmed;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;

class OrderConfirmedEmails implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param OrderPaid $event
     * @return void
     */
    public function handle(OrderPaid $event)
    {
        // 设置时区为UTC，因为队列时区为PRC，导致时间错误
		date_default_timezone_set('UTC');
        // 获取交易数据
        $order = $event->getOrder();
        $order->load('business');

        // 判断交易类型是否需要发送交易确认信
        if (!in_array($order->type, [Order::TYPES_SALE, Order::TYPES_CAPTURE])) {
            return false;
        }

        //是否开启接收邮件
        if (!$order->business->send_mail) {
            return false;
        }

        // 判断交易状态是否是成功
        if ($order->status != Order::STATUS_APPROVED) {
            return false;
        }

        if (empty($order->address->bill_email)) {
            return false;
        }

        // 组装替换数据
        $templateData = [
            'address' => $order->address->bill_email,
            'user'    => $order->address->bill_name,
            'view'    => 'OrderConfirm',
            'data'    => [
                'order_number'  => $order->order_number,
                'bill_name'     => $order->address['bill_name'],
                'url_name'      => $order->url_name,
                'date_complete' => $order->completed_at,
                'replace_type'  => 'common',
                'product_name'  => implode(',', $order->products->where('type', '0')->pluck('name')->toArray()),
                'service_url'   => route('customs.index', '0' . md5($order->order_id))
            ]
        ];

        // 添加邮件任务到队列
        dispatch(new SendEmail($templateData, 5));
    }
}
