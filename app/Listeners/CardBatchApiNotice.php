<?php

namespace App\Listeners;

use App\Models\CardVirtual;
use App\Models\MerchantApiNoticeTask;
use App\Models\MerchantCard;
use App\Services\VirtualControllerService;
use DES3;

class CardBatchApiNotice
{
    /**
     * Handle the event.
     *
     * @param object $event
     * @return void
     */
    public function handle($event)
    {

        $cardBatchData = $event->getCardBatchData();
        $cardVirtual   = $event->getCardVirtual();
        // 根据批次号 获取 cards_id、merchant_id
        $merchantCard = MerchantCard::select('cards_id', 'card_bin_id', 'merchant_name', 'merchant_id', 'bin', 'opening_fee', 'balance')
            ->where('cards_id', $cardBatchData['cards_id'])
            ->where('merchant_id', $cardBatchData['merchant_id'])
            ->first();
        $newTime      = now();
        $virtualIds   = $cardVirtual->pluck('trans_id');
        // api回调通知
        $apiReturnData = ['code' => '0000', 'message' => '成功', 'data' => ['total' => count($virtualIds)]];
        foreach ($cardVirtual as $cardVirtualInfo) {
            $data = [
                'virtual_id'             => $cardVirtualInfo->virtual_id,
                'cards_id'               => $merchantCard->cards_id,
                'batch_id'               => $cardBatchData['batch_id'],
                'card_number'            => DES3::decrypt($cardVirtualInfo->card_number, env('DES3_CARD_VIRTUAL')),
                'cvv'                    => DES3::decrypt($cardVirtualInfo->cvv, env('DES3_CARD_VIRTUAL')),
                'expiration_year'        => DES3::decrypt($cardVirtualInfo->expiration_year, env('DES3_CARD_VIRTUAL')),
                'expiration_month'       => DES3::decrypt($cardVirtualInfo->expiration_month, env('DES3_CARD_VIRTUAL')),
                'created_at'             => $newTime->toDateTimeString(),
                'card_type'              => CardVirtual::$cardTypeApiMap[$cardVirtualInfo->card_type],
                'status'                 => CardVirtual::$internalMerchantStatusMapToEn[$cardVirtualInfo->status],
                'allow_card_out'         => $cardVirtualInfo->allow_card_out == CardVirtual::ALLOW_CARD_OUT_ALLOW ? true : false,
                'is_transaction'         => $cardVirtualInfo->is_transaction == CardVirtual::IS_TRANSACTION_ALLOW ? true : false,
                'apply_for_cancellation' => $cardVirtualInfo->date_apply_cancel ? true : false,
            ];

            if ($cardVirtualInfo->card_type == CardVirtual::SHARED_CARD) {
                $data['day_amount_limit'] = $cardBatchData['day_amount_limit']; // 交易限额
            }
            $apiReturnData['data']['list'][] = $data;
        }

        // 添加回调任务
        VirtualControllerService::createNoticeTask($cardBatchData['merchant_id'], MerchantApiNoticeTask::WEBHOOK_MESSAGE_TYPE_VIRTUAL_INFO, $apiReturnData);
    }
}