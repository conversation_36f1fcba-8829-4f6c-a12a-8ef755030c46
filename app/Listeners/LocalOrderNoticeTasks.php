<?php

namespace App\Listeners;

use App\Classes\Supports\Traits\HasHttpRequest;
use App\Events\LocalOrderPaid;
use App\Models\LocalOrder;
use App\Models\LocalOrderPost;
use Illuminate\Contracts\Queue\ShouldQueue;

class LocalOrderNoticeTasks implements ShouldQueue
{
    use HasHttpRequest;

    public $timeout = '180';

    /**
     * Handle the event.
     *
     * @param LocalOrderPaid $event
     * @return void
     */
    public function handle(LocalOrderPaid $event)
    {
        // 设置时区为UTC，因为队列时区为PRC，导致时间错误
		date_default_timezone_set('UTC');
        // 获取交易数据
        $order      = $event->getOrder();
        $returnData = $event->getReturnData();
           
        // 获取商户提交数据
        $merchantPost = LocalOrderPost::firstWhere('order_id', $order->order_id);

        // 添加异步通知 1.存在notify_url 2.状态是终态
        $finalStatusList = [LocalOrder::STATUS_APPROVED, LocalOrder::STATUS_DECLINED];

        // 判断是否需要添加异步通知
        if ($merchantPost) {
            if ($merchantPost->notify_url
                && in_array($order->status, $finalStatusList)) {

                try {
                    $res = trim($this->post($merchantPost->notify_url, $returnData));
                } catch (\Exception $e) {
                    $res = $e->getMessage();
                }

                $result = (strlen($res) > 255) ? mb_strcut($res, 0, 252) : $res;
                $status = '0';

                if (!empty($result) && $result == 'ok') {
                    $status = '1';  // status=1 表示通知成功
                }

                // 添加通知任务
                $order->orderNoticeTask()->create([
                    'order_id'   => $order->order_id,
                    'content'    => json_encode($returnData),
                    'notify_url' => $merchantPost->notify_url,
                    'cnt'        => 1,
                    'status'     => $status,
                    'result'     => $result
                ]);
            }
        }
    }
}
