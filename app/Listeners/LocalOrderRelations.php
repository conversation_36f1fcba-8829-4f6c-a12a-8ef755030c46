<?php

namespace App\Listeners;

use App\Events\LocalRefundPaid;
use App\Models\LocalOrder;
use App\Models\LocalOrderRelation;
use App\Models\LocalRefund;
use App\Services\LocalTransactionService;
use App\Services\TransactionService;
use Illuminate\Contracts\Queue\ShouldQueue;

class LocalOrderRelations implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  LocalRefundPaid  $event
     * @return void
     */
    public function handle(LocalRefundPaid $event)
    {
        // 设置时区为UTC，因为队列时区为PRC，导致时间错误
		date_default_timezone_set('UTC');
        $order = $event->getOrder();

        if (empty($order->order_id)) {
            return false;
        }

	    // 更新相应的关系字段
	    $filed = $event->getField();
	    $value = '1';

        if ($filed == 'is_refund') {
	        // 判断退款状态是否是成功
	        if (!in_array($order->status, [LocalRefund::STATUS_APPROVED, LocalRefund::STATUS_REVIEW])) {
		        return false;
	        }

	        // 获取订单信息
	        $originOrder = LocalOrder::with(['refund'])->find($order->order_id);

	        // 获取可退款金额
	        $availableRefundAmount = LocalTransactionService::getAvailableRefundAmount($originOrder);
	        $value                 = $availableRefundAmount == '0.00' ? '1' : '2';
        }

        // 获取订单关系表数据
        $orderRelations = LocalOrderRelation::with(['order'])->firstWhere('order_id', $order->order_id);

        if (empty($orderRelations)) {
            return false;
        }

        $orderRelations->$filed = $value;

        $orderRelations->save();
    }
}
