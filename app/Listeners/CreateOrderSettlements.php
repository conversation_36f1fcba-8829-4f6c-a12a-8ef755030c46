<?php

namespace App\Listeners;

use App\Models\ChannelPid;
use App\Models\Order;
use App\Models\OrderSettlement;
use App\Models\Refund;
use App\Services\SettlementService;
use Dcat\Admin\Admin;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Carbon;

class CreateOrderSettlements //implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
    }

    /**
     * Handle the event.
     *
     * @param object $event
     * @return void
     */
    public function handle($event)
    {
        $order = $event->getOrder();

        switch ($event->getOrderType()) {
            case 'transaction':
                $this->transactionSettle($order);
            break;
            case 'refund':
                $this->refundSettle($order);
            break;
            case 'chargeback':
                $this->chargebackSettle($order);
            break;
            case 'chargeback_reversal':
                $this->chargebackReversalSettle($order);
            break;
        }
    }

    /**
     * 订单结算数据
     *
     * @param $order
     */
    protected function transactionSettle($order)
    {
        if (empty($order->order_id)) {
            return;
        }

        $settleOrder       = OrderSettlement::firstWhere('order_id', '=', $order->order_id);
        $settleDate        = SettlementService::getSettleDate($order->business_id);
        $depositDate       = SettlementService::getDepositSettleDates($order->business_id, $order->completed_at, $settleDate, $order->card->cc_type);
        $expectDepositDate = $depositDate;
        //保证金返还日期小于当前结算时间
        if (!empty($depositDate) && Carbon::parse($depositDate)->lt(Carbon::parse($settleDate))) {
            $expectDepositDate = date('Y-m-d', strtotime("{$settleDate} +1 days"));
        }

        $statusList = [Order::STATUS_DECLINED, Order::STATUS_APPROVED, Order::STATUS_EXPIRED];

        //抛投更新
        if (!empty($settleOrder) && $settleOrder['channel'] != $order['channel'] && in_array($order['status'], $statusList)) {

            // 补充数据计算结算金额
            $order->payment_amount_usd = $order->paymentOrder->amount;
            $order->payment_currency   = $order->paymentOrder->currency;

            $settleAmount = SettlementService::getSettleAmount($order, $order->card->card_number);

            //去掉补充数据
            unset($order->payment_amount_usd);
            unset($order->payment_currency);

            $settleOrder->channel_id              = $order->channel_id;
            $settleOrder->channel                 = $order->channel;
            $settleOrder->order_number            = $order->order_number;
            $settleOrder->type                    = $order->type;
            $settleOrder->status                  = $order->status == Order::STATUS_APPROVED ? '1' : '0';
            $settleOrder->currency                = $order->currency;
            $settleOrder->amount                  = $order->amount;
            $settleOrder->settle_currency         = $settleAmount['settle_currency'];
            $settleOrder->settle_amount           = $settleAmount['settle_amount'];
            $settleOrder->payment_currency        = $order->paymentOrder->currency;
            $settleOrder->payment_amount          = $order->paymentOrder->amount;
            $settleOrder->payment_settle_currency = $order->paymentOrder->currency;
            $settleOrder->payment_settle_amount   = $order->paymentOrder->amount;
            $settleOrder->rate                    = $settleAmount['rate'];
            $settleOrder->payment_at              = $order->created_at;
            $settleOrder->completed_at            = $order->completed_at;
            $settleOrder->amount_usd              = $settleAmount['amount_usd'];
            $settleOrder->payment_amount_usd      = $settleAmount['payment_amount_usd'];


            $settleOrder->save();
        }

        // 交易差错时,需要更新交易结算数据
        if (!empty($settleOrder)) {
            if (
                $settleOrder->status == OrderSettlement::ARRIVAL_STATUS_WAITING
                && $order->status == Order::STATUS_APPROVED
            ) {
                $settleOrder->status       = $order->status;
                $settleOrder->completed_at = $order->completed_at;

                if (
                    $settleOrder->blend_status == OrderSettlement::BLEND_STATUS_SUCCESS
                    || $settleOrder->settle_at != $settleDate
                ) {
                    $settleOrder->settle_at          = $settleDate;
                    $settleOrder->original_settle_at = $settleDate;
                    $settleOrder->blend_status       = OrderSettlement::BLEND_STATUS_WAITING;
                    $settleOrder->blend_by           = 'system';
                    $settleOrder->is_settle          = OrderSettlement::IS_SETTLE_WAITING;
                }

                $settleOrder->save(); // 更新结算数据
            }

            return;
        }

        if (!in_array($order->status, $statusList)) {
            return;
        }

        // 补充数据计算结算金额
        $order->payment_amount_usd = $order->paymentOrder->amount;
        $order->payment_currency   = $order->paymentOrder->currency;

        // 添加结算信息
        $settleAmount      = SettlementService::getSettleAmount($order, $order->card->card_number);
        //获取条款历史id
        $businessHistoryId = SettlementService::getBusinessHistoryId($order->business_id);

        //去掉补充数据
        unset($order->payment_amount_usd);
        unset($order->payment_currency);

        $data = [
            'order_id'                => $order->order_id,
            'payment_order_id'        => $order->paymentOrder->payment_order_id,
            'parent_order_id'         => $order->parent_order_id ?? '0',
            'business_history_id'     => $businessHistoryId,
            'merchant_id'             => $order->merchant_id,
			'cc_type'                 => $order->card->cc_type,
            'business_id'             => $order->business_id,
            'merchant_name'           => $order->merchant_name,
            'channel_id'              => $order->channel_id,
            'channel'                 => $order->channel,
            'order_number'            => $order->order_number,
            'type'                    => $order->type,
            'status'                  => $order->status == Order::STATUS_APPROVED ? '1' : '0',
            'currency'                => $order->currency,
            'amount'                  => $order->amount,
            'settle_currency'         => $settleAmount['settle_currency'],
            'settle_amount'           => $settleAmount['settle_amount'],
            'payment_currency'        => $order->paymentOrder->currency,
            'payment_amount'          => $order->paymentOrder->amount,
            'payment_settle_currency' => $order->paymentOrder->currency,
            'payment_settle_amount'   => $order->paymentOrder->amount,
            'rate'                    => $settleAmount['rate'],
            'original_settle_at'      => $settleDate,
            'settle_at'               => $settleDate,
            'deposit_return_at'       => $expectDepositDate,
            'deposit_expect_return_at'=> $depositDate,
            'payment_at'              => $order->created_at,
            'completed_at'            => $order->completed_at,
            'amount_usd'              => $settleAmount['amount_usd'],
            'payment_amount_usd'      => $settleAmount['payment_amount_usd']
        ];

        // 更新到缓存
        if (in_array($order->type, [Order::TYPES_SALE, Order::TYPES_CAPTURE])) {
            // 交易成功缓存处理
            if ($order->status == Order::STATUS_APPROVED) {
                $pidData = ChannelPid::whereHas('channel', function (Builder $query) use ($order) {
                    $query->where('channel', $order->channel);
                })->with('channelSupplier:id,timezone,effective_date')->first();

                $timezone = config('app.timezone');
                if ($pidData->channelSupplier->timezone){
                    // 使用timezone前需要判断当前app配置里的时区时间是否大于effective_date时间
                    $effectiveDate = Carbon::parse($pidData->channelSupplier->effective_date);
                    if (!$effectiveDate->gt(now())) {
                        $timezone = $pidData->channelSupplier->timezone;
                    }
                }
                $time = Carbon::now($timezone);

                $channelKey     = 'Transaction_' . MD5($order->channel) . '_Qty_And_Amount_' . $time->format('Ym');
                $channelLockKey = $channelKey . '_Lock';
                Cache::lock($channelLockKey, 10)->block(5, function () use ($channelKey, $settleAmount, $order) {
                    $transactionChannel = Cache::get($channelKey) ?? [];
                    $this->handelTransactionCacheData($transactionChannel, $settleAmount['payment_amount_usd'], 'day_amount', $order->card->cc_type);
                    $this->handelTransactionCacheData($transactionChannel, 1, 'day_qty', $order->card->cc_type);
                    $this->handelTransactionCacheData($transactionChannel, $settleAmount['payment_amount_usd'], 'month_amount', $order->card->cc_type);
                    $this->handelTransactionCacheData($transactionChannel, 1, 'month_qty', $order->card->cc_type);

                    foreach ($transactionChannel as $limitType => &$channelAmounts) {
                        if ($limitType == 'day_amount' || $limitType == 'month_amount') {
                            foreach ($channelAmounts as $ccType => $amount) {
                                $channelAmounts[$ccType] = amount_format($amount);
                            }
                        }
                    }

                    Cache::put($channelKey, $transactionChannel, 31 * 24 * 60 * 60);
                });

                // BID限额缓存
                $bidKey     = 'Transaction_' . $order->business_id . '_Amount_' . date('Ymd');
                $bidLockKey = $bidKey . '_Lock';
                Cache::lock($bidLockKey, 10)->block(5, function () use ($bidKey, $settleAmount, $order) {
                    $transactionBusiness = Cache::get($bidKey) ?? [];
                    $this->handelTransactionCacheData($transactionBusiness, $settleAmount['amount_usd'], 'day_amount', $order->card->cc_type);
                    $this->handelTransactionCacheData($transactionBusiness, $settleAmount['amount_usd'], 'month_amount', $order->card->cc_type);

                    foreach ($transactionBusiness as &$amounts) {
                        foreach ($amounts as $ccType => $amount) {
                            $amounts[$ccType] = amount_format($amount);
                        }
                    }

                    Cache::put($bidKey, $transactionBusiness, 24 * 60 * 60);
                });

                //PID限额缓存
                $pidKey     = 'Transaction_' . $pidData->channel_pid . '_Amount_' . $time->format('Ym');
                $pidLockKey = $pidKey . '_Lock';
                Cache::lock($pidLockKey, 10)->block(5, function () use ($pidKey, $settleAmount, $order) {
                    $transactionPid = Cache::get($pidKey) ?? [];
                    $this->handelTransactionCacheData($transactionPid, $settleAmount['payment_amount_usd'], 'day_amount', $order->card->cc_type);
                    $this->handelTransactionCacheData($transactionPid, $settleAmount['payment_amount_usd'], 'month_amount', $order->card->cc_type);

                    foreach ($transactionPid as &$pidAmounts) {
                        foreach ($pidAmounts as $ccType => $amount) {
                            $pidAmounts[$ccType] = amount_format($amount);
                        }
                    }

                    Cache::put($pidKey, $transactionPid, 31 * 24 * 60 * 60);
                });

                // 同卡同金额同网站120s成功交易（163953459255967 J卡为15秒）
                $key      = 'Order_Card_Mask_Amount_Url_Status_' . $order->card->card_mask . $order->amount . $order->url_name . Order::STATUS_APPROVED;
                $interval = ($order->merchant_id == '163953459255967' && $order->card->cc_type == 'J') ? '15' : '120';
                Cache::add($key, 1, $interval);
            }
        }

        OrderSettlement::firstOrCreate($data);
    }

    /**
     * 退款结算数据
     *
     * @param $order
     */
    protected function refundSettle($order)
    {
        if (empty($order->refund_id) || OrderSettlement::where('order_id', '=', $order->refund_id)->exists()) {
            return;
        }

        // 判断交易状态是否是成功
        if ($order->status != Refund::STATUS_APPROVED && $order->status != Refund::STATUS_REVIEW) {
            return;
        }

        // 获取原始交易信息
        $originalOrder = Order::with(['card'])->find($order->order_id);

        if (empty($originalOrder)) {
            return;
        }

        // 补充数据计算结算金额
        $order->channel_id         = $originalOrder->channel_id;
        $order->business_id        = $originalOrder->business_id;
        $order->payment_amount_usd = $order->paymentRefund->amount;
        $order->payment_currency   = $order->paymentRefund->currency;

        // 添加结算信息
        $settleAmount      = SettlementService::getSettleAmount($order, $originalOrder->card->card_number);
        //获取条款历史id
        $businessHistoryId = SettlementService::getOrderBusinessHistoryId($order->order_id);

        $data         = [
            'order_id'                => $order->refund_id,
            'payment_order_id'        => $order->paymentRefund->payment_refund_id,
            'parent_order_id'         => $order->order_id,
            'business_history_id'     => $businessHistoryId,
            'merchant_id'             => $originalOrder->merchant_id,
			'cc_type'                 => $originalOrder->card->cc_type,
            'business_id'             => $originalOrder->business_id,
            'merchant_name'           => $originalOrder->merchant_name,
            'channel_id'              => $originalOrder->channel_id,
            'channel'                 => $originalOrder->channel,
            'order_number'            => $originalOrder->order_number,
            'type'                    => OrderSettlement::TYPE_REFUND,
            'status'                  => OrderSettlement::STATUS_APPROVED,
            'currency'                => $order->currency,
            'amount'                  => $order->amount,
            'settle_currency'         => $settleAmount['settle_currency'],
            'settle_amount'           => $settleAmount['settle_amount'],
            'payment_currency'        => $order->paymentRefund->currency,
            'payment_amount'          => $order->paymentRefund->amount,
            'payment_settle_currency' => $order->paymentRefund->currency,
            'payment_settle_amount'   => $order->paymentRefund->amount,
            'rate'                    => $settleAmount['rate'],
            'payment_at'              => $order->created_at,
            'completed_at'            => $order->completed_at,
            'amount_usd'              => $settleAmount['amount_usd'],
            'payment_amount_usd'      => $settleAmount['payment_amount_usd']
        ];

        // 计算到账数据
        $blendData = array_merge($data, [
            'is_blend'         => OrderSettlement::BLEND_STATUS_WAITING,
            'arrival_currency' => $order->paymentRefund->currency,
            'arrival_amount'   => $order->paymentRefund->amount,
            'type'             => '-',
            'card_number'      => $originalOrder->card->card_number,
        ]);

        // 更新数组
        $tempData             = SettlementService::calculateSettle($blendData);
        $nonTradingSettleDate = SettlementService::getNonTradingSettleDate($originalOrder->business_id);

        $data['settle_currency']          = $tempData['settle_currency'];
        $data['settle_amount']            = $tempData['settle_amount'];
        $data['payment_settle_currency']  = $tempData['payment_settle_currency'];
        $data['payment_settle_amount']    = $tempData['payment_settle_amount'];
        $data['rate']                     = $tempData['rate'];
        $data['arrival_currency']         = $tempData['arrival_currency'];
        $data['arrival_amount']           = $tempData['arrival_amount'];
        $data['arrival_status']           = OrderSettlement::ARRIVAL_STATUS_SUCCESS;
        $data['arrival_at']               = now()->toDateTimeString();
        $data['blend_status']             = OrderSettlement::BLEND_STATUS_SUCCESS;
        $data['blend_by']                 = 'system';
        $data['blend_at']                 = $nonTradingSettleDate;
        $data['original_settle_at']       = $nonTradingSettleDate;
        $data['settle_at']                = $nonTradingSettleDate;
        $data['deposit_return_at']        = $nonTradingSettleDate;
        $data['deposit_expect_return_at'] = $nonTradingSettleDate;

        OrderSettlement::firstOrCreate($data);
    }

    /**
     * 拒付结算数据
     *
     * @param $order
     */
    protected function chargebackSettle($order)
    {
        // 判断是否存在拒付结算记录
        if (OrderSettlement::where(['order_id' => $order->order_id, 'type' => OrderSettlement::TYPE_CHARGEBACK])->exists()) {
            return;
        }

        // 判断订单是否可拒付
        if (Order::isChargeback($order->type . $order->status)) {
            return;
        }

        // 结算信息添加
        $orderResult          = Order::with(['paymentOrder', 'card'])->find($order->parent_order_id)->toArray();
        $nonTradingSettleDate = SettlementService::getNonTradingSettleDate($orderResult['business_id']);
        //获取条款历史id
        $businessHistoryId    = SettlementService::getOrderBusinessHistoryId($order->parent_order_id);

        $orderSettlement                           = $order;
        $orderSettlement->payment_order_id         = $orderResult['payment_order']['payment_order_id'];
        $orderSettlement->business_history_id      = $businessHistoryId;
        $orderSettlement->cc_type                  = $orderResult['card']['cc_type'];
        $orderSettlement->merchant_id              = $orderResult['merchant_id'];
        $orderSettlement->business_id              = $orderResult['business_id'];
        $orderSettlement->merchant_name            = $orderResult['merchant_name'];
        $orderSettlement->channel_id               = $orderResult['channel_id'];
        $orderSettlement->channel                  = $orderResult['channel'];
        $orderSettlement->order_number             = $orderResult['order_number'];
        $orderSettlement->type                     = OrderSettlement::TYPE_CHARGEBACK;
        $orderSettlement->status                   = OrderSettlement::STATUS_APPROVED;
        $orderSettlement->blend_status             = OrderSettlement::BLEND_STATUS_SUCCESS;
        $orderSettlement->blend_by                 = !empty(Admin::user()) ? Admin::user()->name : 'system';
        $orderSettlement->blend_at                 = $nonTradingSettleDate;
        $orderSettlement->original_settle_at       = $nonTradingSettleDate;
        $orderSettlement->settle_at                = $nonTradingSettleDate;
        $orderSettlement->deposit_return_at        = $nonTradingSettleDate;
        $orderSettlement->deposit_expect_return_at = $nonTradingSettleDate;
        $orderSettlement->payment_at               = $orderResult['created_at'];
        $orderSettlement->payment_amount_usd       = $orderResult['payment_order']['amount'];

        $settleAmountResult = SettlementService::getSettleAmount($orderSettlement, $orderResult['card']['card_number']);

        $orderSettlement->settle_currency    = $settleAmountResult['settle_currency'];
        $orderSettlement->settle_amount      = $settleAmountResult['settle_amount'];
        $orderSettlement->rate               = $settleAmountResult['rate'];
        $orderSettlement->amount_usd         = $settleAmountResult['amount_usd'];
        $orderSettlement->payment_amount_usd = $settleAmountResult['payment_amount_usd'];

        $orderSettlement->save();
    }

    /**
     * 拒付结算数据
     *
     * @param $order
     */
    protected function chargebackReversalSettle($order)
    {
        // 结算信息添加
        $orderResult          = Order::with(['paymentOrder', 'card'])->find($order->parent_order_id)->toArray();
        $nonTradingSettleDate = SettlementService::getNonTradingSettleDate($orderResult['business_id']);
        //获取条款历史id
        $businessHistoryId    = SettlementService::getOrderBusinessHistoryId($order->parent_order_id);

        $orderSettlement                           = $order;
        $orderSettlement->payment_order_id         = $orderResult['payment_order']['payment_order_id'];
        $orderSettlement->business_history_id      = $businessHistoryId;
        $orderSettlement->cc_type                  = $orderResult['card']['cc_type'];
        $orderSettlement->merchant_id              = $orderResult['merchant_id'];
        $orderSettlement->business_id              = $orderResult['business_id'];
        $orderSettlement->merchant_name            = $orderResult['merchant_name'];
        $orderSettlement->channel_id               = $orderResult['channel_id'];
        $orderSettlement->channel                  = $orderResult['channel'];
        $orderSettlement->order_number             = $orderResult['order_number'];
        $orderSettlement->type                     = OrderSettlement::TYPE_CHARGEBACK_REVERSAL;
        $orderSettlement->status                   = OrderSettlement::STATUS_APPROVED;
        $orderSettlement->blend_status             = OrderSettlement::BLEND_STATUS_SUCCESS;
        $orderSettlement->blend_by                 = !empty(Admin::user()) ? Admin::user()->name : 'system';
        $orderSettlement->blend_at                 = $nonTradingSettleDate;
        $orderSettlement->original_settle_at       = $nonTradingSettleDate;
        $orderSettlement->settle_at                = $nonTradingSettleDate;
        $orderSettlement->deposit_return_at        = $nonTradingSettleDate;
        $orderSettlement->deposit_expect_return_at = $nonTradingSettleDate;
        $orderSettlement->payment_at               = $orderResult['created_at'];
        $orderSettlement->payment_amount_usd       = $orderResult['payment_order']['amount'];

        $settleAmountResult = SettlementService::getSettleAmount($orderSettlement, $orderResult['card']['card_number']);

        $orderSettlement->settle_currency    = $settleAmountResult['settle_currency'];
        $orderSettlement->settle_amount      = $settleAmountResult['settle_amount'];
        $orderSettlement->rate               = $settleAmountResult['rate'];
        $orderSettlement->amount_usd         = $settleAmountResult['amount_usd'];
        $orderSettlement->payment_amount_usd = $settleAmountResult['payment_amount_usd'];
        $orderSettlement->save();
    }

    /**
     * 处理交易缓存
     * @param [type] $cacheData     处理的交易缓存
     * @param [type] $number        增加数量
     * @param string $handelType    处理类型
     * @param string $ccType        订单卡种
     * @return void
     */
    private function handelTransactionCacheData(&$cacheData, $number, string $handelType, string $ccType): void
    {
        if (!isset($cacheData[$handelType]['*'])) {
            $cacheData[$handelType]['*'] = 0;
        }

        if (!isset($cacheData[$handelType][$ccType])) {
            $cacheData[$handelType][$ccType] = 0;
        }

        $cacheData[$handelType]['*']     += $number;
        $cacheData[$handelType][$ccType] += $number;
    }
}
