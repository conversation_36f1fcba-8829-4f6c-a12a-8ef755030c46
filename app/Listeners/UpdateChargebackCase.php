<?php

namespace App\Listeners;

use App\Models\ChargebackCase;
use App\Models\Refund;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class UpdateChargebackCase
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  object  $event
     * @return void
     */
    public function handle($event)
    {
        // 设置时区为UTC，因为队列时区为PRC，导致时间错误
        date_default_timezone_set('UTC');

        // 退款信息
		$refund = $event->getOrder();

		// 终态对应更新预警备注和处理结果
		$chargebackCase = ChargebackCase::where('order_id', $refund->order_id)->first();
		if (!$chargebackCase) {
			return;
		}

		if (in_array($refund->status, [Refund::STATUS_APPROVED, Refund::STATUS_REVIEW])) {
			$chargebackCase->remarks = '退款成功';
			$chargebackCase->result  = ChargebackCase::REFUNDED;
			$chargebackCase->save();
		}
    }
}
