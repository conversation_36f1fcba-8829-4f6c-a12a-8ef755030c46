<?php


namespace App\Listeners;


use App\Jobs\SendNotice;
use App\Models\DirectoryExternalCode;
use App\Models\Order;
use Illuminate\Support\Facades\Cache;

class SysNotice
{
    /**
     * Handle the event.
     *
     * @param object $event
     * @return void
     * @throws \Psr\SimpleCache\InvalidArgumentException
     */
    public function handle($event)
    {
        $order        = $event->getOrder();
        $externalCode = DirectoryExternalCode::where('external_code', $order->code)->first();

        // 非终态直接返回
        if ($order->status == Order::STATUS_RECEIVED) {
            return;
        }

        // 连续次数
        $codeKey = 'Result_Check_' . $order->channel . '_' . $order->code;
        Cache::tags(['code', $order->channel])->increment($codeKey);

        $noticeCnt = Cache::tags(['code', $order->channel])->get($codeKey);

        // 当告警次数连续次数达到预警即发送通知
        if (!empty($externalCode->notice_cnt)) {
            if ($noticeCnt >= $externalCode->notice_cnt) {
                // 组装告警消息
                $content = sprintf(
                    "[交易告警]\n故障账单: %s\n故障详情: %s\n关联商户: %s\n关联订单: %s\n交易卡种: %s\n交易网址: %s\n来源: %s",
                    $order->channel,
                    "连续出现" . $noticeCnt . "笔" . $order->code . "[" . $externalCode->external_remarks . "]交易",
                    $order->merchant_name,
                    $order->paymentOrder->order_number,
                    $order->card->cc_type,
                    $order->url_name,
                    config('app.url')
                );

                // 组装告警数据
                $data = [
                    'level'             => 1,
                    'contents'          => $content,
                    'notice_user_roles' => 'Administrator,Operate Supervisor',
                    'type'              => 3,
                    'status'            => 2,
                ];

                dispatch(new SendNotice($data, 5));

                // 清空计数
                Cache::tags($order->channel)->flush();
            }
        } else {
            Cache::tags($order->channel)->flush();
        }
    }
}
