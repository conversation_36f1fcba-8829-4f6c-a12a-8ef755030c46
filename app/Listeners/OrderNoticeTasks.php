<?php

namespace App\Listeners;

use App\Classes\Supports\Traits\HasHttpRequest;
use App\Events\Notice;
use App\Models\Order;
use App\Models\OrderNoticeTask;
use App\Models\OrderPost;
use Illuminate\Contracts\Queue\ShouldQueue;

class OrderNoticeTasks implements ShouldQueue
{
    use HasHttpRequest;

    public $timeout = '180';

    /**
     * Handle the event.
     *
     * @param Notice $event
     * @return void
     */
    public function handle(Notice $event)
    {
        // 设置时区为UTC，因为队列时区为PRC，导致时间错误
        date_default_timezone_set('UTC');
        // 获取交易数据
        $order      = $event->getOrder();
        $returnData = $event->getReturnData();

        // 获取商户提交数据
        $merchantPost = OrderPost::firstWhere('order_id', $order->order_id);

        // 添加异步通知 1.存在notify_url 2.状态是终态
        $finalStatusList = [Order::STATUS_APPROVED, Order::STATUS_DECLINED];
        $notifyTypeList  = [Order::TYPES_AUTH, Order::TYPES_SALE];

        // 是否该订单号、不是退款通知并且状态不同异步通知
        $one = OrderNoticeTask::query()->where('order_id', $order->order_id)->whereNull('content->refundId')
            ->where('content->status', $order->status)->doesntExist();

        // 判断是否需要添加异步通知
        if ($merchantPost) {
            if (
                $merchantPost->notify_url
                && in_array($order->type, $notifyTypeList)
                && in_array($order->status, $finalStatusList)
                && $one
            ) {
                try {
                    $res = trim($this->post($merchantPost->notify_url, $returnData));
                } catch (\Exception $e) {
                    $res = $e->getMessage();
                }

                $result = (strlen($res) > 255) ? mb_strcut($res, 0, 252) : $res;
                $status = '0';

                if (!empty($result) && $result == 'ok') {
                    $status = '1';  // status=1 表示通知成功
                }

                // 添加通知任务
                $order->orderNoticeTask()->create([
                    'order_id'   => $order->order_id,
                    'content'    => json_encode($returnData),
                    'notify_url' => $merchantPost->notify_url,
                    'cnt'        => 1,
                    'status'     => $status,
                    'result'     => $result
                ]);
            }
        }
    }
}
