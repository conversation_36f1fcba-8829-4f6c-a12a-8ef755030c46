<?php

namespace App\Listeners;

use App\Events\LocalOrderPaid;
use App\Models\LocalOrderRelation;
use Illuminate\Contracts\Queue\ShouldQueue;

class CreateLocalOrderRelations implements ShouldQueue
{
    /**
     * Handle the event.
     *
     * @param  LocalOrderPaid  $event
     * @return void
     */
    public function handle(LocalOrderPaid $event)
    {
        // 设置时区为UTC，因为队列时区为PRC，导致时间错误
		date_default_timezone_set('UTC');
        $order = $event->getOrder();

        if (empty($order->order_id) || LocalOrderRelation::where('order_id', '=', $order->order_id)->exists()) {
            return;
        }

        // 创建订单关系数据
        $orderRelations               = new LocalOrderRelation();
        $orderRelations->order_id     = $order->order_id;
        $orderRelations->custom_token = md5($order->order_id);

        $orderRelations->save();
    }
}
