<?php

namespace App\Listeners;

use App\Jobs\SendNotice;
use App\Models\DirectoryExternalCode;
use App\Models\Refund;
use Illuminate\Support\Facades\Cache;

class RefundSysNotice
{
    /**
     * Handle the event.
     *
     * @param object $event
     * @return void
     * @throws \Psr\SimpleCache\InvalidArgumentException
     */
    public function handle($event)
    {
        $orderRefund  = $event->getOrder();
        $externalCode = DirectoryExternalCode::where('external_code', $orderRefund->code)->first();

        // 非终态直接返回
        if ($orderRefund->status == Refund::STATUS_RECEIVED) {
            return;
        }

        // 退款特殊告警返回代码
        $specialCodeList = [
            get_system_code('200') => 1,
            get_system_code('210') => 1,
        ];
        
        $externalCodeCnt = isset($specialCodeList[$orderRefund->code]) ? $specialCodeList[$orderRefund->code] : $externalCode->notice_cnt;

        // 连续次数
        $codeKey = 'Result_Check_Refund_' . $orderRefund->order->channel . '_' . $orderRefund->code;
        Cache::tags(['code', $orderRefund->order->channel])->increment($codeKey);

        $noticeCnt = Cache::tags(['code', $orderRefund->order->channel])->get($codeKey);

        // 当告警次数连续次数达到预警即发送通知
        if (!empty($externalCodeCnt)) {
            if ($noticeCnt >= $externalCodeCnt) {

                // 组装告警消息
                $content = sprintf(
                    "[退款告警]\n退款故障账单: %s\n退款故障详情: %s\n关联商户: %s\n原始订单: %s\n退款金额: %s\n是否全额退款: %s\n来源: %s",
                    $orderRefund->order->channel,
                    "连续出现" . $noticeCnt . "笔" . $orderRefund->code . "[" . $externalCode->external_remarks . "]交易",
                    $orderRefund->order->merchant_name,
                    $orderRefund->order->order_id,
                    $orderRefund->amount,
                    $orderRefund->paymentRefund->amount == $orderRefund->paymentOrder->amount ? '是' : '否',
                    config('app.url')
                );

                // 组装告警数据
                $data = [
                    'level'             => 1,
                    'contents'          => $content,
                    'notice_user_roles' => 'Operate Supervisor,Channel Operation',
                    'type'              => 3,
                    'status'            => 2,
                ];

                dispatch(new SendNotice($data, 5));

                // 清空计数
                Cache::tags($orderRefund->order->channel)->flush();
            }
        } else {
            Cache::tags($orderRefund->order->channel)->flush();
        }
    }
}
