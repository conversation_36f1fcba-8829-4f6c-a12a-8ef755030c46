<?php

namespace App\Listeners;

use App\Events\TrackApplied;
use App\Services\TrackService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class CreateTrackingTasks implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
    }

    /**
     * Handle the event.
     *
     * @param  TrackApplied  $event
     * @return void
     */
    public function handle(TrackApplied $event)
    {
        // 设置时区为UTC，因为队列时区为PRC，导致时间错误
		date_default_timezone_set('UTC');
        $orderTrack = $event->getOrder();

        if (empty($orderTrack->order_id)) {
            return false;
        }

        $orderTrack->task()->createMany(TrackService::createTrackTask($orderTrack));
    }
}
