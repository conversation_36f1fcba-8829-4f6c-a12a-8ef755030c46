<?php

namespace App\Listeners;


use App\Jobs\SendSlsLog;
use App\Models\CardBin;
use App\Models\CardVirtual;
use App\Services\Virtual\VirtualServiceFacade;

class CardBindCardHolder
{
    /**
     * Handle the event.
     *
     * @param object $event
     * @return void
     */
    public function handle($event)
    {
        $cardVirtual     = $event->getCardVirtual();
        $cardBatchData   = $event->getCardBatchData();
        $virtualTransIds = $cardVirtual->pluck('trans_id');
        if (!empty($cardBatchData['cardholder_ids'])) {
            $bin     = CardBin::with('CardBinSupplier')->find($cardBatchData['card_bin_id']);
            $service = VirtualServiceFacade::getService($bin->CardBinSupplier->file_name)->setGatewayConfig($bin->config);
            // 默认持卡人
            if ($cardBatchData['is_use_default_cardholder']) {
                foreach ($virtualTransIds as $transId) {
                    $res = $service->bindCardHolder(['trans_id' => $transId, 'cardholder_id' => $cardBatchData['cardholder_ids']]);
                    if ($res['code'] == '200') {
                        CardVirtual::where('trans_id', $transId)->update(['cardholder_id' => $cardBatchData['cardholder_ids']]);
                    } else {
                        dispatch(new SendSlsLog(
                            ['message' => $bin->CardBinSupplier->file_name . '批量绑定持卡人失败'],
                            ['trans_id' => $transId, 'cardholder_id' => $cardBatchData['cardholder_ids']],
                            'error',
                            'virtual_card',
                        ));
                    }
                }
            } else {
                $cardholderIds = array_filter(explode(',', $cardBatchData['cardholder_ids']), function ($value) {
                    return ($value !== null && $value !== "");
                });
                foreach ($virtualTransIds as $key => $transId) {
                    $res = $service->bindCardHolder(['trans_id' => $transId, 'cardholder_id' => $cardholderIds[$key]]);
                    if ($res['code'] == '200') {
                        CardVirtual::where('trans_id', $transId)->update(['cardholder_id' => $cardholderIds[$key]]);
                    } else {
                        dispatch(new SendSlsLog(
                            ['message' => $bin->CardBinSupplier->file_name . '批量绑定持卡人失败'],
                            ['trans_id' => $transId, 'cardholder_id' => $cardholderIds[$key]],
                            'error',
                            'virtual_card',
                        ));
                    }
                }
            }
        }
    }
}