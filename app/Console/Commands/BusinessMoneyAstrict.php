<?php

namespace App\Console\Commands;

use App\Models\Order as OrderModel;
use App\Models\OrderSettlement as OrderSettlementModel;
use App\Models\PaymentOrder as PaymentOrderModel;
use Carbon\Carbon;
use Illuminate\Console\Command;
use App\Classes\Supports\Logger;
use App\Jobs\SendSlsLog;
use Illuminate\Support\Facades\Cache;

class BusinessMoneyAstrict extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'task:businessMoneyAstrict';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        set_time_limit(0);

        // 初始化日志
        $log    = new Logger();
        $config = ['file' => storage_path('logs/consoleTask.log')];
        $log->setConfig($config);

        $log->info('更新Business交易金额缓存执行开始');
        dispatch(new SendSlsLog(
            ['message' => '更新Business交易金额缓存执行开始'],
            [],
            'info',
            'task'
        ));

        $transactionBusinessList = [];

        //日交易数据
        $startTimeDay = Carbon::now()->startOfDay();
        $endTimeDay   = Carbon::now()->endOfDay();
        $dayData      = OrderSettlementModel::WhereBetween('completed_at', [$startTimeDay, $endTimeDay])
            ->where('status', OrderModel::STATUS_APPROVED)
            ->whereIn('type', [PaymentOrderModel::TYPES_SALE, PaymentOrderModel::TYPES_CAPTURE])
            ->selectRaw('business_id, cc_type, ifnull(sum(amount_usd),0.00) as amount')
            ->groupBy('business_id', 'cc_type')
            ->get();


        foreach ($dayData as $day) {
            if (isset($transactionBusinessList[$day->business_id]['day_amount']['*'])) {
                $transactionBusinessList[$day->business_id]['day_amount']['*'] += $day->amount;
            } else {
                $transactionBusinessList[$day->business_id]['day_amount']['*'] = $day->amount;
            }

            if (isset($transactionBusinessList[$day->business_id]['day_amount'][$day->cc_type])) {
                $transactionBusinessList[$day->business_id]['day_amount'][$day->cc_type] += $day->amount;
            } else {
                $transactionBusinessList[$day->business_id]['day_amount'][$day->cc_type] = $day->amount;
            }
        }



        //月交易数据
        $startTimeMonth = Carbon::now()->startOfMonth();
        $endTimeMonth   = Carbon::now()->endOfMonth();
        $moonData       = OrderSettlementModel::WhereBetween('completed_at', [$startTimeMonth, $endTimeMonth])
            ->where('status', OrderModel::STATUS_APPROVED)
            ->whereIn('type', [PaymentOrderModel::TYPES_SALE, PaymentOrderModel::TYPES_CAPTURE])
            ->selectRaw('business_id, cc_type, ifnull(sum(amount_usd),0.00) as amount')
            ->groupBy('business_id', 'cc_type')
            ->get();

        foreach ($moonData as $moon) {
            if (isset($transactionBusinessList[$moon->business_id]['month_amount']['*'])) {
                $transactionBusinessList[$moon->business_id]['month_amount']['*'] += $moon->amount;
            } else {
                $transactionBusinessList[$moon->business_id]['month_amount']['*'] = $moon->amount;
            }

            if (isset($transactionBusinessList[$moon->business_id]['month_amount'][$moon->cc_type])) {
                $transactionBusinessList[$moon->business_id]['month_amount'][$moon->cc_type] += $moon->amount;
            } else {
                $transactionBusinessList[$moon->business_id]['month_amount'][$moon->cc_type] = $moon->amount;
            }
        }

        $time = date('Ymd');
        foreach ($transactionBusinessList as $bid => $transaction) {
            foreach ($transaction as &$amounts) {
                foreach ($amounts as $ccType => $amount) {
                    $amounts[$ccType] = amount_format($amount);
                }
            }
            Cache::put('Transaction_' . $bid . '_Amount_' . $time, $transaction, 24 * 60 * 60);
        }

        $log->info("更新Business交易金额缓存执行结束");
        dispatch(new SendSlsLog(
            ['message' => '更新Business交易金额缓存执行结束'],
            [],
            'info',
            'task'
        ));
    }
}
