<?php

namespace App\Console\Commands;

use App\Classes\Supports\Logger;
use App\Jobs\SendSlsLog;
use App\Models\Refund;
use App\Services\RefundService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class RefundQuery extends Command
{

	/**
	 * The name and signature of the console command.
	 *
	 * @var string
	 */
	protected $signature = 'task:refundQuery';

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'Refund Query';

	protected $cacheName = 'refund_query_';

	/**
	 * Create a new command instance.
	 *
	 * @return void
	 */
	public function __construct()
	{
		parent::__construct();
	}

	/**
	 * Execute the console command.
	 *
	 * @return int
	 */
	public function handle()
	{
		// 在命令行打印一行信息
		$this->info("退款非最终状态则自动查询开始");
		// 初始化日志
		$log    = new Logger();
		$config = ['file' => storage_path('logs/consoleTask.log')];
		$log->setConfig($config);

		// 需要关联  退款表(order_id)->订单表(channel_id)->账单表(channel_supplier_id)->渠道供应商表(file_name)
		$refundData = Refund::with(['order:order_id,channel_id', 'order.channelObj:id,channel_supplier_id', 'order.channelObj.channelSupplier:id,file_name'])
			->select('refund_id', 'order_id')
			->where('status', Refund::STATUS_PENDING)
			->whereIn('code', [get_system_code('200'), get_system_code('095')])
			->whereBetween('created_at',[date('Y-m-d H:i:s', strtotime('-1 months')),date('Y-m-d H:i:s', strtotime('-30 minutes'))])
			->get()
			->toArray();
		if (empty($refundData)) {
			return;
		}

		$refundQueryIds = [];
		$refundService  = new RefundService();
		foreach ($refundData as $refundInfo) {
			// 判断渠道是否满足退款查询 不满足直接跳过
			$fileName = $refundInfo['order']['channel_obj']['channel_supplier']['file_name'] ?? '';
			$fileName = strtolower($fileName);
			if (!in_array($fileName, Refund::$refundQueryChannel)) {
				continue;
			}

			$refundId = $refundInfo['refund_id'] ?? '';
			$keyName  = $this->cacheName . $refundId;

			$cacheData = Cache::get($keyName);
			$cacheData = json_decode($cacheData, true);

			// 当前任务执行次数
			$cnt = !empty($cacheData['cnt']) ? $cacheData['cnt'] : 1;
			// 如果超过执行次数限制或者未到下次执行时间。则跳过
			if ($cacheData['cnt'] > 3 || $cacheData['nextTime'] > time()) {
				continue;
			}

			// 满足执行退款查询的Id集合
			$refundQueryIds[] = $refundId;
			$cnt++;
			// 下次执行时间的幂值
			$nextTime = pow(10, $cnt);

			// 缓存数据
			$cacheData = [
				'cnt'      => $cnt,
				'nextTime' => strtotime("+ {$nextTime} min"),
			];
			// 根据单个时间 设置时区 去获取时间
			$firstQueryDateTime = now('PRC')->getTimestamp();
			$todayMidnight      = now('PRC')->setTime(23, 59, 59)->getTimestamp();
			// 设置缓存过期时间：当天第一次查询时间到当天零点
			$ttl = $todayMidnight - $firstQueryDateTime;
			Cache::put($keyName, json_encode($cacheData), $ttl);
		}

		// 执行退款查询
		if (!empty($refundQueryIds)) {
			$returnData = $refundService->refundQuery($refundQueryIds);
			if (!empty($returnData['refund_id'])) {
				$refundIds = $returnData['refund_id'];
				// 删除已经处理过的退款缓存数据
				foreach ($refundIds as $refundId) {
					Cache::forget($this->cacheName . $refundId);
				}
			}

			$log->info($returnData['msg']);
			dispatch(new SendSlsLog(
				['message' => '退款非最终状态则自动查询结束'],
				[$returnData['msg']],
				'info',
				'task'
			));
		}
	}
}
