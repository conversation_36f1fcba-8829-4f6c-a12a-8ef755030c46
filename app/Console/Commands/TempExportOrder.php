<?php

namespace App\Console\Commands;

use App\Classes\Supports\Traits\HasHttpRequest;
use App\Http\Controllers\Traits\PaymentController;
use App\Models\Order;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;

class TempExportOrder extends Command
{
    use PaymentController;
    use HasHttpRequest;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'task:tempExportOrder';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '根据CSV文件中的card_mask导出订单数据';

    /**
     * 分批处理大小
     */
    private const BATCH_SIZE = 1500;

    /**
     * Card mask 分批处理大小
     */
    private const CARD_MASK_BATCH_SIZE = 100;

    /**
     * IN子句最大参数数量限制
     */
    private const MAX_IN_CLAUSE_SIZE = 800;

    /**
     * 已使用订单ID数量阈值，超过此值使用临时表
     */
    private const TEMP_TABLE_THRESHOLD = 2000;

    /**
     * 已使用的订单ID集合（使用关联数组优化查找性能）
     */
    private array $usedOrderIds = [];

    /**
     * 已使用订单ID的数量统计
     */
    private int $usedOrderIdsCount = 0;

    /**
     * 导出文件句柄
     */
    private $exportFileHandle = null;

    /**
     * 临时表名（用于大量ID排除）
     */
    private ?string $tempTableName = null;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        set_time_limit(0);
        ini_set('memory_limit', '2048M'); // 降低内存限制，强制优化

        $this->info('开始执行订单导出任务...');

        try {
            // 1. 初始化导出文件
            $this->initializeExportFile();

            // 2. 初始化临时表（如果需要）
            $this->initializeTempTable();

            // 3. 读取CSV文件中的card_mask数据
            $cardMasks = $this->readCardMasksFromCsv();
            $this->info('从CSV文件中读取到 ' . count($cardMasks) . ' 个card_mask');

            // 4. 分批处理card_mask并导出订单
            $this->processCardMasksInBatches($cardMasks);

            // 5. 补齐不足的订单数量
            $this->fillRemainingOrders($cardMasks);

            // 6. 关闭导出文件和清理临时表
            $this->finalizeExportFile();
            $this->cleanupTempTable();

            $this->info('订单数据导出完成');

        } catch (\Exception $e) {
            $this->error('导出任务执行失败: ' . $e->getMessage());
            $this->cleanupOnError();
            return 1;
        }

        $this->info('订单导出任务执行完成');
        return 0;
    }

    /**
     * 初始化导出文件
     */
    private function initializeExportFile(): void
    {
        $exportPath = storage_path('app/exports');
        if (!is_dir($exportPath)) {
            mkdir($exportPath, 0755, true);
        }

        $fileName = 'selected_orders_' . date('Y-m-d_H-i-s') . '.csv';
        $filePath = $exportPath . '/' . $fileName;

        $this->exportFileHandle = fopen($filePath, 'w');
        if ($this->exportFileHandle === false) {
            throw new \Exception("无法创建导出文件: {$filePath}");
        }

        // 写入CSV头部
        $headers = [
            'CardNumber',    // card_mask (来自order_cards表)
            'Amount',        // payment_amount (来自order_settlements表)
            'Currency',      // payment_currency (来自order_settlements表)
            'Order Date',    // created_at (来自orders表)
            'SSO ID'         // order_id (来自orders表)
        ];
        fputcsv($this->exportFileHandle, $headers);

        $this->info("导出文件已创建: {$fileName}");
    }

    /**
     * 完成导出文件
     */
    private function finalizeExportFile(): void
    {
        if ($this->exportFileHandle) {
            fclose($this->exportFileHandle);
            $this->exportFileHandle = null;
        }
    }

    /**
     * 错误时清理资源
     */
    private function cleanupOnError(): void
    {
        if ($this->exportFileHandle) {
            fclose($this->exportFileHandle);
            $this->exportFileHandle = null;
        }

        $this->cleanupTempTable();
    }

    /**
     * 初始化临时表（用于大量订单ID排除）
     */
    private function initializeTempTable(): void
    {
        $this->tempTableName = 'temp_used_order_ids_' . uniqid();

        $sql = "
            CREATE TEMPORARY TABLE {$this->tempTableName} (
                order_id BIGINT PRIMARY KEY
            ) ENGINE=MEMORY
        ";

        DB::statement($sql);
        $this->info("临时表已创建: {$this->tempTableName}");
    }

    /**
     * 清理临时表
     */
    private function cleanupTempTable(): void
    {
        if ($this->tempTableName) {
            try {
                DB::statement("DROP TEMPORARY TABLE IF EXISTS {$this->tempTableName}");
                $this->info("临时表已清理: {$this->tempTableName}");
            } catch (\Exception $e) {
                $this->warn("清理临时表失败: " . $e->getMessage());
            }
            $this->tempTableName = null;
        }
    }

    /**
     * 添加订单ID到已使用列表（优化版）
     */
    private function addUsedOrderIds(array $orderIds): void
    {
        foreach ($orderIds as $orderId) {
            if (!isset($this->usedOrderIds[$orderId])) {
                $this->usedOrderIds[$orderId] = true;
                $this->usedOrderIdsCount++;
            }
        }

        // 当数量超过阈值时，批量插入到临时表
        if ($this->usedOrderIdsCount >= self::TEMP_TABLE_THRESHOLD && $this->tempTableName) {
            $this->syncUsedOrderIdsToTempTable();
        }
    }

    /**
     * 同步已使用订单ID到临时表
     */
    private function syncUsedOrderIdsToTempTable(): void
    {
        if (empty($this->usedOrderIds) || !$this->tempTableName) {
            return;
        }

        $orderIds = array_keys($this->usedOrderIds);
        $chunks = array_chunk($orderIds, 1000);

        foreach ($chunks as $chunk) {
            $values = implode(',', array_map(function($id) {
                return "({$id})";
            }, $chunk));

            $sql = "INSERT IGNORE INTO {$this->tempTableName} (order_id) VALUES {$values}";
            DB::statement($sql);
        }

        $this->info("已同步 " . count($orderIds) . " 个订单ID到临时表");

        // 清空内存中的数组，释放内存
        $this->usedOrderIds = [];
        $this->usedOrderIdsCount = count($orderIds);
    }

    /**
     * 从CSV文件中读取card_mask数据
     *
     * @return array
     */
    private function readCardMasksFromCsv(): array
    {
        $csvFile = app_path('Console/Commands/202506041120.csv');

        if (!file_exists($csvFile)) {
            throw new \Exception("CSV文件不存在: {$csvFile}");
        }

        $cardMasks = [];
        $handle = fopen($csvFile, 'r');

        if ($handle === false) {
            throw new \Exception("无法打开CSV文件: {$csvFile}");
        }

        // 跳过标题行
        $header = fgetcsv($handle);

        // 读取数据行
        while (($data = fgetcsv($handle)) !== false) {
            if (!empty($data[0])) {
                // 去除引号并添加到数组中
                $cardMask = trim($data[0], '"');
                if (!empty($cardMask)) {
                    $cardMasks[] = $cardMask;
                }
            }
        }

        fclose($handle);

        // 去重
        return array_unique($cardMasks);
    }

    /**
     * 分批处理card_mask
     */
    private function processCardMasksInBatches(array $cardMasks): void
    {
        $totalCardMasks = count($cardMasks);
        $batches = array_chunk($cardMasks, self::CARD_MASK_BATCH_SIZE);
        $totalBatches = count($batches);
        $processedCount = 0;

        $this->info("开始分批处理 {$totalCardMasks} 个card_mask，共 {$totalBatches} 批");

        foreach ($batches as $batchIndex => $cardMaskBatch) {
            $currentBatch = $batchIndex + 1;
            $this->info("处理第 {$currentBatch}/{$totalBatches} 批 card_mask...");

            // 为当前批次的card_mask查询订单
            $this->processCardMaskBatch($cardMaskBatch);

            $processedCount += count($cardMaskBatch);
            $this->info("已处理 {$processedCount}/{$totalCardMasks} 个card_mask");

            // 强制垃圾回收，释放内存
            if ($currentBatch % 10 === 0) {
                gc_collect_cycles();
                $this->info("内存使用: " . $this->formatBytes(memory_get_usage(true)));
            }
        }
    }

    /**
     * 处理单个card_mask批次（优化版：批量查询）
     */
    private function processCardMaskBatch(array $cardMaskBatch): void
    {
        $this->info("批量查询 " . count($cardMaskBatch) . " 个card_mask的订单...");

        // 批量查询所有card_mask的订单
        $allOrders = $this->getOrdersByCardMaskBatch($cardMaskBatch, 3);

        // 按card_mask分组处理订单
        $ordersByCardMask = $allOrders->groupBy('card_mask');

        foreach ($cardMaskBatch as $cardMask) {
            $orders = $ordersByCardMask->get($cardMask, collect());
            $foundCount = $orders->count();

            if ($orders->isNotEmpty()) {
                $this->writeOrdersToFile($orders);

                // 记录已使用的订单ID（优化版）
                $orderIds = $orders->pluck('order_id')->toArray();
                $this->addUsedOrderIds($orderIds);
            }

            // 如果该card_mask的订单数不足3笔，立即补齐
            if ($foundCount < 3) {
                $needed = 3 - $foundCount;
                $this->info("Card mask {$cardMask}: 找到 {$foundCount} 笔订单，需要补齐 {$needed} 笔");

                $additionalOrders = $this->getRandomOrdersForCardMask($cardMask, $needed);
                if ($additionalOrders->isNotEmpty()) {
                    $this->writeOrdersToFile($additionalOrders);

                    // 记录已使用的订单ID（优化版）
                    $additionalOrderIds = $additionalOrders->pluck('order_id')->toArray();
                    $this->addUsedOrderIds($additionalOrderIds);

                    $this->info("Card mask {$cardMask}: 已补齐 " . $additionalOrders->count() . " 笔随机订单");
                }

                // 释放变量
                unset($additionalOrders);
            } else {
                $this->info("Card mask {$cardMask}: 找到 {$foundCount} 笔订单，无需补齐");
            }

            // 监控已使用订单ID数量
            if ($this->usedOrderIdsCount % 1000 === 0 && $this->usedOrderIdsCount > 0) {
                $this->info("已使用订单ID数量: {$this->usedOrderIdsCount}");
            }
        }

        // 释放变量
        unset($allOrders, $ordersByCardMask);
    }

    /**
     * 补齐剩余订单数量（现在每个card_mask都会在处理时立即补齐，此方法作为备用检查）
     */
    private function fillRemainingOrders(array $cardMasks): void
    {
        $targetTotal = count($cardMasks) * 3;
        $currentCount = $this->usedOrderIdsCount;

        $this->info("目标订单总数: {$targetTotal}，实际导出订单数: {$currentCount}");

        if ($currentCount < $targetTotal) {
            $needed = $targetTotal - $currentCount;
            $this->info("警告：仍有 {$needed} 笔订单未补齐，这可能表示数据不足");
        } else {
            $this->info("所有card_mask的订单都已补齐完成");
        }
    }

    /**
     * 为特定card_mask获取随机订单进行补齐
     */
    private function getRandomOrdersForCardMask(string $cardMask, int $needed): Collection
    {
        // 获取随机订单，但在导出时将card_mask设置为指定值
        $query = Order::query()
            ->join('order_cards', 'orders.card_id', '=', 'order_cards.id')
            ->join('order_settlements', 'orders.order_id', '=', 'order_settlements.order_id')
            ->where('orders.status', Order::STATUS_APPROVED)
            ->where('orders.created_at', '<=', DB::raw('DATE_SUB(NOW(), INTERVAL 30 DAY)'))
            ->select(
                'orders.order_id',
                'orders.created_at',
                'order_settlements.payment_amount',
                'order_settlements.payment_currency'
            );

        // 排除已使用的订单ID（优化版）
        $this->applyUsedOrderIdsFilter($query, 'orders.order_id');

        $randomOrders = $query->inRandomOrder()->limit($needed)->get();

        // 为这些随机订单设置指定的card_mask
        foreach ($randomOrders as $order) {
            $order->card_mask = $cardMask;
        }

        return $randomOrders;
    }

    /**
     * 批量查询多个card_mask的订单（MySQL兼容版）
     */
    private function getOrdersByCardMaskBatch(array $cardMasks, int $limitPerCardMask = 3): Collection
    {
        if (empty($cardMasks)) {
            return collect();
        }

        // 尝试使用更高效的批量查询方案
        try {
            return $this->getOrdersByCardMaskBatchOptimized($cardMasks, $limitPerCardMask);
        } catch (\Exception $e) {
            // 如果优化方案失败，回退到分块查询
            $this->info("批量查询失败，回退到分块查询: " . $e->getMessage());
            return $this->getOrdersByCardMaskBatchFallback($cardMasks, $limitPerCardMask);
        }
    }

    /**
     * 优化的批量查询方案（使用简化的子查询方式）
     */
    private function getOrdersByCardMaskBatchOptimized(array $cardMasks, int $limitPerCardMask): Collection
    {
        // 简化的批量查询，避免复杂的子查询
        $query = DB::table('orders as o')
            ->join('order_cards as oc', 'o.card_id', '=', 'oc.id')
            ->join('order_settlements as os', 'o.order_id', '=', 'os.order_id')
            ->whereIn('oc.card_mask', $cardMasks)
            ->where('o.status', Order::STATUS_APPROVED)
            ->where('o.created_at', '<=', DB::raw('DATE_SUB(NOW(), INTERVAL 30 DAY)'))
            ->select(
                'o.order_id',
                'o.created_at',
                'oc.card_mask',
                'os.payment_amount',
                'os.payment_currency'
            )
            ->orderBy('oc.card_mask')
            ->orderBy('o.created_at', 'desc');

        // 排除已使用的订单ID（优化版）
        $this->applyUsedOrderIdsFilter($query, 'o.order_id');

        $allResults = $query->get();

        // 在PHP中按card_mask分组并限制每组的数量
        $groupedResults = $allResults->groupBy('card_mask');
        $limitedResults = collect();

        foreach ($groupedResults as $cardMask => $orders) {
            $limitedOrders = $orders->take($limitPerCardMask);
            $limitedResults = $limitedResults->merge($limitedOrders);
        }

        return $limitedResults;
    }

    /**
     * 回退方案：分块查询
     */
    private function getOrdersByCardMaskBatchFallback(array $cardMasks, int $limitPerCardMask): Collection
    {
        $allResults = collect();

        // 分批处理card_mask，避免IN子句过大
        $cardMaskChunks = array_chunk($cardMasks, 20); // 减少每次处理的数量

        foreach ($cardMaskChunks as $cardMaskChunk) {
            $results = $this->getOrdersByCardMaskChunk($cardMaskChunk, $limitPerCardMask);
            $allResults = $allResults->merge($results);
        }

        return $allResults;
    }

    /**
     * 查询单个card_mask块的订单（使用EXISTS子查询方式，兼容MySQL 5.7+）
     */
    private function getOrdersByCardMaskChunk(array $cardMaskChunk, int $limitPerCardMask): Collection
    {
        $results = collect();

        // 使用更简单的方式：为每个card_mask单独查询，但仍然比原来的逐个查询要好
        foreach ($cardMaskChunk as $cardMask) {
            $query = DB::table('orders')
                ->join('order_cards', 'orders.card_id', '=', 'order_cards.id')
                ->join('order_settlements', 'orders.order_id', '=', 'order_settlements.order_id')
                ->where('order_cards.card_mask', $cardMask)
                ->where('orders.status', Order::STATUS_APPROVED)
                ->where('orders.created_at', '<=', DB::raw('DATE_SUB(NOW(), INTERVAL 30 DAY)'))
                ->select(
                    'orders.order_id',
                    'orders.created_at',
                    'order_cards.card_mask',
                    'order_settlements.payment_amount',
                    'order_settlements.payment_currency'
                )
                ->orderBy('orders.created_at', 'desc')
                ->limit($limitPerCardMask);

            // 排除已使用的订单ID（优化版）
            $this->applyUsedOrderIdsFilter($query, 'orders.order_id');

            $cardMaskResults = $query->get();
            $results = $results->merge($cardMaskResults);
        }

        return $results;
    }

    /**
     * 单个card_mask查询（保留作为备用方法）
     */
    private function getOrdersByCardMaskOptimized(string $cardMask, int $limit = 3): Collection
    {
        $query = Order::query()
            ->join('order_cards', 'orders.card_id', '=', 'order_cards.id')
            ->join('order_settlements', 'orders.order_id', '=', 'order_settlements.order_id')
            ->where('order_cards.card_mask', $cardMask)
            ->where('orders.status', Order::STATUS_APPROVED)
            ->where('orders.created_at', '<=', DB::raw('DATE_SUB(NOW(), INTERVAL 30 DAY)'))
            ->select(
                'orders.order_id',
                'orders.created_at',
                'order_cards.card_mask',
                'order_settlements.payment_amount',
                'order_settlements.payment_currency'
            );

        // 排除已使用的订单ID（优化版）
        $this->applyUsedOrderIdsFilter($query, 'orders.order_id');

        return $query->limit($limit)->get();
    }

    /**
     * 应用已使用订单ID过滤器（性能优化版）
     */
    private function applyUsedOrderIdsFilter($query, string $orderIdColumn): void
    {
        if ($this->usedOrderIdsCount === 0) {
            return;
        }

        // 如果使用了临时表且订单ID数量超过阈值
        if ($this->tempTableName && $this->usedOrderIdsCount >= self::TEMP_TABLE_THRESHOLD) {
            // 先同步内存中的ID到临时表
            if (!empty($this->usedOrderIds)) {
                $this->syncUsedOrderIdsToTempTable();
            }

            // 使用NOT EXISTS子查询，性能更好
            $query->whereRaw("NOT EXISTS (
                SELECT 1 FROM {$this->tempTableName}
                WHERE {$this->tempTableName}.order_id = {$orderIdColumn}
            )");

            return;
        }

        // 如果订单ID数量较少，使用传统的whereNotIn
        $usedIds = array_keys($this->usedOrderIds);
        if (count($usedIds) <= self::MAX_IN_CLAUSE_SIZE) {
            $query->whereNotIn($orderIdColumn, $usedIds);
            return;
        }

        // 如果订单ID数量中等，分批使用whereNotIn
        $chunks = array_chunk($usedIds, self::MAX_IN_CLAUSE_SIZE);
        $query->where(function($subQuery) use ($chunks, $orderIdColumn) {
            foreach ($chunks as $chunk) {
                $subQuery->whereNotIn($orderIdColumn, $chunk);
            }
        });
    }

    /**
     * 将订单数据写入文件
     */
    private function writeOrdersToFile(Collection $orders): void
    {
        if (!$this->exportFileHandle) {
            throw new \Exception("导出文件句柄未初始化");
        }

        foreach ($orders as $order) {
            $row = [
                $order->card_mask ?? '',                    // CardNumber
                $order->payment_amount ?? '',               // Amount
                $order->payment_currency ?? '',             // Currency
                $order->created_at ?? '',                   // Order Date
                $order->order_id ?? ''                      // SSO ID
            ];
            fputcsv($this->exportFileHandle, $row);
        }
    }

    /**
     * 格式化字节数
     */
    private function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
