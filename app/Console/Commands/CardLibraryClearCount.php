<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\CardSource;

class CardLibraryClearCount extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'task:cardLibraryClearCount';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Card Library Clear daily count';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        set_time_limit(0);

        CardSource::where('status', CardSource::STATUS_ENABLE)->where('used_times', '>', 0)->update(['used_times' => 0]);
    }
}
