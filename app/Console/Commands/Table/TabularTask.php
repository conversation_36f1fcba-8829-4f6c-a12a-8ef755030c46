<?php

namespace App\Console\Commands\Table;

use App\Admin\Extensions\Table\ChargebackStatExport;
use App\Admin\Extensions\Table\TransactionStatExport;
use App\Admin\Extensions\Table\SuccessRateExport;
use App\Jobs\SendNotice;
use App\Jobs\SendSlsLog;
use App\Models\DirectoryDictionary;
use App\Services\TableService;
use App\Services\ToolService;
use DateTime;
use DateTimeZone;
use Illuminate\Console\Command;
use Maatwebsite\Excel\Facades\Excel;

class TabularTask extends Command
{
	/**
	 * The name and signature of the console command.
	 *
	 * @var string
	 */
	protected $signature = 'task:TabularTask';

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'New Correct StatOrderChargebacks Table';


	protected $halfPastNine = 0;


	/**
	 * Create a new command instance.
	 *
	 * @return void
	 */
	public function __construct()
	{
		parent::__construct();
	}

	/**
	 * Execute the console command.
	 *
	 * @return int
	 */
	public function handle()
	{
		if (in_array(env('APP_NAME', 'Laravel'), ['Laravel', 'PunctualPay', 'Hpaymerchants'])) {
			return;
		}

		$this->info("开始执行表格统计任务推送");
		// 计算延迟时间
		$this->getDelayMinutes();
		$currentTimeRange = ToolService::getCurrentTimeRange();
		$startDay         = $endDay = date('Y-m-d', strtotime('-1 day'));
		// 商户拒付统计情况 表格任务
		$this->chargebackStatMerchant($currentTimeRange);
		// 渠道拒付统计情况 表格任务
		$this->chargebackStatChannel($currentTimeRange);
		// 商户成功率 表格任务
		$this->successRateMerchant($startDay, $endDay);
		// 渠道成功率 表格任务
		$this->successRateChannel($startDay, $endDay);
		// 商户交易占比 表格任务
		$this->transactionStatMerchant($startDay, $endDay);
		// Turnover 类型表格任务
		$this->transactionStatTurnover($startDay, $endDay);

		$this->info("表格统计任务推送成功" . date('Y-m-d H:i:s'));
	}

	/**
	 * 获取延迟执行的分钟
	 *
	 */
	private function getDelayMinutes()
	{
		$tz                = new DateTimeZone('PRC');
		$now               = new DateTime('now', $tz);
		$halfPastNineToday = new DateTime('today 09:30:00', $tz);

		$this->halfPastNine = $now->diff($halfPastNineToday);
	}

	/**
	 * Turnover表格推送任务
	 *
	 * @return void
	 */
	private function transactionStatTurnover($startDay, $endDay)
	{
		// 从字典表获取查询条件
		$directoryData = DirectoryDictionary::select('remarks')->where('type', '表格统计任务')->where('name', 'turnover类型表格')->first();
		if (empty($directoryData)) {
			return;
		}

		$isEnable      = json_decode($directoryData['remarks'], true);
		if (empty($isEnable['isEnable'])) {
			return;
		}

		$system = env('APP_NAME', 'Laravel');
		$input  = [
			'inputs' => [
				'date_stat.start' => $startDay,
				'date_stat.end'   => $endDay,
			],
		];

		$fileName = date("m-d-H") . $system . 'Turnover' . '.xlsx';

		try {
			Excel::store(new TransactionStatExport($input, 'turnover'), '/export/' . $fileName, 'data');
		} catch (\Exception $e) {
			// 记录日志
			logger()->channel('intercept')->warning(
				'transactionStatExport',
				['action' => 'turnover', 'error' => $e->getMessage()]
			);
			dispatch(new SendSlsLog(
				['message' => 'transactionStatExport'],
				['action' => 'turnover', 'error' => $e->getMessage()],
				'warning',
				'task'
			));
		}

		$url         = asset('/data/export/' . $fileName);
		$pushContent = [
			'level'             => 1,
			'contents'          => '[Turnover 表格推送]' . PHP_EOL .
				'关联系统:' . $system . PHP_EOL .
				'推送日期:' . date('Y.m.d') . PHP_EOL .
				'数据时间范围:' . PHP_EOL .
				date('Y-m-d 00:00:00', strtotime($startDay)) . '至' . date('Y-m-d 23:59:59', strtotime($endDay)) . PHP_EOL .
				'下载地址:' . $url,
			'type'              => 3,            // 默认是3,暂时没有其他的了
			'status'            => 2,            // 等待通知
			'notice_user_roles' => 'Administrator,Operate Supervisor',
		];

		dispatch(new SendNotice($pushContent, $this->halfPastNine, 'workNotice'));
	}

	/**
	 * 商户交易占比推送任务
	 *
	 * @return void
	 */
	private function transactionStatMerchant($startDay, $endDay)
	{
		// 从字典表获取查询条件
		$directoryData = DirectoryDictionary::select('remarks')->where('type', '表格统计任务')->where('name', '商户交易占比统计')->first();
		if (empty($directoryData)) {
			return;
		}

		$isEnable = json_decode($directoryData['remarks'], true);
		if (empty($isEnable['isEnable'])) {
			return;
		}

		$system = env('APP_NAME', 'Laravel');
		// 获取固定条件和推送人
		$merchantId = TableService::$TransactionStatData[$system]['merchantTransactionStat']['merchant_id'] ?? [];
		if (empty($merchantId) || $system == 'Embracy') {
			return;
		}

		$input = [
			'inputs' => [
				'date_stat.start' => $startDay,
				'date_stat.end'   => $endDay,
				'merchant_id'     => $merchantId,
			],
		];

		$fileName = date("m-d-H") . '商户交易及占比' . '.xlsx';
		try {
			Excel::store(new TransactionStatExport($input, 'merchant'), '/export/' . $fileName, 'data');
		} catch (\Exception $e) {
			// 记录日志
			logger()->channel('intercept')->warning(
				'transactionStatExport',
				['action' => 'merchant', 'error' => $e->getMessage()]
			);
			dispatch(new SendSlsLog(
				['message' => 'transactionStatExport'],
				['action' => 'merchant', 'error' => $e->getMessage()],
				'warning',
				'task'
			));
		}

		$url         = asset('/data/export/' . $fileName);
		$pushContent = [
			'level'             => 1,
			'contents'          => '[交易占比统计 表格推送]' . PHP_EOL .
				'关联系统:' . $system . PHP_EOL .
				'推送日期:' . date('Y.m.d') . PHP_EOL .
				'数据时间范围:' . PHP_EOL .
				date('Y-m-d 00:00:00', strtotime($startDay)) . '至' . date('Y-m-d 23:59:59', strtotime($endDay)) . PHP_EOL .
				'下载地址:' . $url,
			'type'              => 3,            // 默认是3,暂时没有其他的了
			'status'            => 2,            // 等待通知
			'notice_user_roles' => 'Administrator,Operate Supervisor',
		];

		dispatch(new SendNotice($pushContent, $this->halfPastNine, 'workNotice'));
	}

	/*
	 * @description: 商户拒付统计
	 * @author: zqc
	 * @date: 2023/9/1
	 **/
	private function chargebackStatMerchant($date)
	{
		// 从字典表获取查询条件
		$directoryData = DirectoryDictionary::select('remarks')->where('type', '表格统计任务')->where('name', '商户拒付统计')->first();
		if (empty($directoryData)) {
			return;
		}

		$isEnable = json_decode($directoryData['remarks'], true);
		if (empty($isEnable['isEnable'])) {
			return;
		}

		$searchData = [
			'inputs' => [
				'date_stat.start' => $date[0],
				'date_stat.end'   => $date[1],
			],
		];

		$fileName = date('Y-m-d-H') . '-' . env('APP_NAME', 'Laravel') . '-' . '商户拒付统计' . '.xlsx';

		try {
			Excel::store(new ChargebackStatExport($searchData, 'business'), '/export/' . $fileName, 'data');
		} catch (\Exception $e) {
			// 记录日志
			logger()->channel('intercept')->warning(
				'chargebackStatExport',
				['action' => 'businessExport', 'time' => time(), 'error' => $e->getMessage()]
			);
			dispatch(new SendSlsLog(
				['message' => 'chargebackStatExport'],
				['action' => 'businessExport', 'time' => time(), 'error' => $e->getMessage()],
				'warning'
			));
		}
		$url         = asset('/data/export/' . $fileName);
		$pushTime    = date('Y-m-d H:i:s', strtotime(date('Y-m-d') . '10:00:00'));
		$pushContent = [
			'level'             => 1,
			'contents'          => '[商户拒付统计  表格推送]' . PHP_EOL .
				'关联系统:' . env('APP_NAME', 'Laravel') . PHP_EOL .
				'推送日期:' . $pushTime . PHP_EOL .
				'数据时间范围:' . PHP_EOL .
				date('Y-m-d 00:00:00', strtotime($date[0])) . '至' . date('Y-m-d 23:59:59', strtotime($date[1])) . PHP_EOL .
				'下载地址:' . $url,
			'type'              => 3, // 默认是3,暂时没有其他的了
			'status'            => 2, // 等待通知
			'notice_user_roles' => 'Administrator,Operate Supervisor',
		];

		dispatch(new SendNotice($pushContent, $this->halfPastNine, 'workNotice'));
	}

	/*
	 * @description: 渠道拒付统计
	 * @author: zqc
	 * @date: 2023/9/4
	 **/
	private function chargebackStatChannel($date)
	{
		// 从字典表获取查询条件
		$directoryData = DirectoryDictionary::select('remarks')->where('type', '表格统计任务')->where('name', '渠道拒付统计')->first();
		if (empty($directoryData)) {
			return;
		}

		$isEnable = json_decode($directoryData['remarks'], true);
		if (empty($isEnable['isEnable'])) {
			return;
		}

		$searchData = [
			'inputs' => [
				'date_stat.start' => $date[0],
				'date_stat.end'   => $date[1],
			],
		];

		$fileName = date('Y-m-d-H') . '-' . env('APP_NAME', 'Laravel') . '-' . '渠道拒付统计' . '.xlsx';

		try {
			Excel::store(new ChargebackStatExport($searchData, 'channel'), '/export/' . $fileName, 'data');
		} catch (\Exception $e) {
			// 记录日志
			logger()->channel('intercept')->warning(
				'chargebackStatExport',
				['action' => 'channelExport', 'time' => time(), 'error' => $e->getMessage()]
			);
			dispatch(new SendSlsLog(
				['message' => 'chargebackStatExport'],
				['action' => 'channelExport', 'time' => time(), 'error' => $e->getMessage()],
				'warning'
			));
		}
		$url         = asset('/data/export/' . $fileName);
		$pushTime    = date('Y-m-d H:i:s', strtotime(date('Y-m-d') . '10:00:00'));
		$pushContent = [
			'level'             => 1,
			'contents'          => '[渠道拒付统计  表格推送]' . PHP_EOL .
				'关联系统:' . env('APP_NAME', 'Laravel') . PHP_EOL .
				'推送日期:' . $pushTime . PHP_EOL .
				'数据时间范围:' . PHP_EOL .
				date('Y-m-d 00:00:00', strtotime($date[0])) . '至' . date('Y-m-d 23:59:59', strtotime($date[1])) . PHP_EOL .
				'下载地址:' . $url,
			'type'              => 3, // 默认是3,暂时没有其他的了
			'status'            => 2, // 等待通知
			'notice_user_roles' => 'Administrator,Operate Supervisor',
		];

		dispatch(new SendNotice($pushContent, $this->halfPastNine, 'workNotice'));
	}


	/*
	 * @description: 商户成功率统计
	 * @author: zqc
	 * @date: 2023/9/11
	 **/
	private function successRateMerchant($startDay, $endDay)
	{
		// 从字典表获取查询条件
		$directoryData = DirectoryDictionary::select('remarks')->where('type', '表格统计任务')->where('name', '商户成功率表格')->first();
		if (empty($directoryData)) {
			return;
		}

		$isEnable = json_decode($directoryData['remarks'], true);
		if (empty($isEnable['isEnable'])) {
			return;
		}
		$system            = env('APP_NAME', 'Laravel');
		$whereInMerchantId = TableService::$successRateData[$system]['successRateMerchant']['merchant_id'] ?? [];
		$whereInCcTye      = TableService::$successRateData[$system]['successRateMerchant']['cc_type'] ?? [];
		// 如果条件是空的就不执行了,就只在这两个系统执行
		if (empty($whereInMerchantId) || empty($whereInCcTye) || $system == 'Embracy') {
			return;
		}

		$fileName   = date('m-d') . '-peninsula123M card analysis.xlsx';
		$searchTime = [
			'inputs' => [
				'date_stat.start' => $startDay,
				'date_stat.end'   => $endDay,
				'merchant_id'     => $whereInMerchantId,
				'cc_type'         => $whereInCcTye,
			],
		];

		try {
			new SuccessRateExport($searchTime, 'business', $fileName);
		} catch (\Exception $e) {
			// 记录日志
			logger()->channel('intercept')->warning(
				'successRateExport',
				['action' => 'businessExport', 'time' => time(), 'error' => $e->getMessage()]
			);
			dispatch(new SendSlsLog(
				['message' => 'successRateExport'],
				['action' => 'businessExport', 'time' => time(), 'error' => $e->getMessage()],
				'warning'
			));
		}
		$url         = asset('/data/export/' . $fileName);
		$pushTime    = date('Y-m-d H:i:s', strtotime(date('Y-m-d') . '10:00:00'));
		$pushContent = [
			'level'             => 1,
			'contents'          => '[商户成功率  表格推送]' . PHP_EOL .
				'关联系统:' . env('APP_NAME', 'Laravel') . PHP_EOL .
				'推送日期:' . $pushTime . PHP_EOL .
				'数据时间范围:' . PHP_EOL .
				date('Y-m-d 00:00:00', strtotime($startDay)) . '至' . date('Y-m-d 23:59:59', strtotime($startDay)) . PHP_EOL .
				'下载地址:' . $url,
			'type'              => 3, // 默认是3,暂时没有其他的了
			'status'            => 2, // 等待通知
			'notice_user_roles' => 'Administrator,Operate Supervisor',
		];

		dispatch(new SendNotice($pushContent, $this->halfPastNine, 'workNotice'));
	}

	/*
	 * @description: 渠道成功率统计
	 * @author: zqc
	 * @date: 2023/9/11
	 **/
	private function successRateChannel($startDay, $endDay)
	{
		// 从字典表获取查询条件
		$directoryData = DirectoryDictionary::select('remarks')->where('type', '表格统计任务')->where('name', '渠道成功率表格')->first();
		if (empty($directoryData)) {
			return;
		}

		$isEnable = json_decode($directoryData['remarks'], true);
		if (empty($isEnable['isEnable'])) {
			return;
		}

		$search = [
			'inputs' => [
				'date_stat.start' => $startDay,
				'date_stat.end'   => $endDay,
			],
		];

		$fileName = env('APP_NAME', 'Laravel') . '-Channel Success Rate' . date('md') . '.xlsx';

		try {
			new SuccessRateExport($search, 'channel', $fileName);
		} catch (\Exception $e) {
			// 记录日志
			logger()->channel('intercept')->warning(
				'successRateExport',
				['action' => 'channelExport', 'time' => time(), 'error' => $e->getMessage()]
			);
			dispatch(new SendSlsLog(
				['message' => 'successRateExport'],
				['action' => 'channelExport', 'time' => time(), 'error' => $e->getMessage()],
				'warning'
			));
		}
		$url         = asset('/data/export/' . $fileName);
		$pushTime    = date('Y-m-d H:i:s', strtotime(date('Y-m-d') . '10:00:00'));
		$pushContent = [
			'level'             => 1,
			'contents'          => '[渠道成功率  表格推送]' . PHP_EOL .
				'关联系统:' . env('APP_NAME', 'Laravel') . PHP_EOL .
				'推送日期:' . $pushTime . PHP_EOL .
				'数据时间范围:' . PHP_EOL .
				date('Y-m-d 00:00:00', strtotime($startDay)) . '至' . date('Y-m-d 23:59:59', strtotime($endDay)) . PHP_EOL .
				'下载地址:' . $url,
			'type'              => 3, // 默认是3,暂时没有其他的了
			'status'            => 2, // 等待通知
			'notice_user_roles' => 'Administrator,Operate Supervisor',
		];

		dispatch(new SendNotice($pushContent, $this->halfPastNine, 'workNotice'));
	}
}
