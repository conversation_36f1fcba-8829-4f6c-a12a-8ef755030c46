<?php

namespace App\Console\Commands;

use App\Admin\Extensions\AbnormalRefundsExport;
use App\Admin\Extensions\AbnormalRefundsExportCsv;
use App\Admin\Extensions\BillSettlementExport;
use App\Admin\Extensions\CardCipherWhitelistExport;
use App\Admin\Extensions\CardCipherWhitelistExportCsv;
use App\Admin\Extensions\CardVirtualExport;
use App\Admin\Extensions\CardVirtualExportCsv;
use App\Admin\Extensions\CardWhiteListExport;
use App\Admin\Extensions\CardWhiteListExportCsv;
use App\Admin\Extensions\ChannelExternalCodeExport;
use App\Admin\Extensions\ChannelExternalCodeExportCsv;
use App\Admin\Extensions\ChannelWhiteListExport;
use App\Admin\Extensions\ChannelWhiteListExportCsv;
use App\Admin\Extensions\ChargebackCaseAbnormalExport;
use App\Admin\Extensions\ChargebackCaseAbnormalExportCsv;
use App\Admin\Extensions\ChargebackCaseExport;
use App\Admin\Extensions\ChargebackCaseExportCsv;
use App\Admin\Extensions\ChargebackExport;
use App\Admin\Extensions\ChargebackExportCsv;
use App\Admin\Extensions\ChargebackPenaltyExport;
use App\Admin\Extensions\ChargebackPenaltyExportCsv;
use App\Admin\Extensions\LocalOrderExport;
use App\Admin\Extensions\LocalOrderExportCsv;
use App\Admin\Extensions\LocalRefundErrorsExport;
use App\Admin\Extensions\LocalRefundErrorsExportCsv;
use App\Admin\Extensions\LocalRefundsInfoExport;
use App\Admin\Extensions\LocalRefundsInfoExportCsv;
use App\Admin\Extensions\OrderExport;
use App\Admin\Extensions\OrderExportCsv;
use App\Admin\Extensions\OrderLosesExport;
use App\Admin\Extensions\OrderLosesExportCsv;
use App\Admin\Extensions\RefundErrorsExport;
use App\Admin\Extensions\RefundErrorsExportCsv;
use App\Admin\Extensions\RefundsInfoExport;
use App\Admin\Extensions\RefundsInfoExportCsv;
use App\Admin\Extensions\RiskTransactionExport;
use App\Admin\Extensions\RiskTransactionExportCsv;
use App\Admin\Extensions\SettleDetailCardExport;
use App\Admin\Extensions\SettleDetailMerchantExport;
use App\Admin\Extensions\SettleDetailMerchantExportCsv;
use App\Admin\Extensions\SettlementDetailExport;
use App\Admin\Extensions\SettlementDetailExportCsv;
use App\Admin\Extensions\SettlementOrderExport;
use App\Admin\Extensions\SettlementOrderExportCsv;
use App\Admin\Extensions\ShellProductExport;
use App\Admin\Extensions\ShellProductExportCsv;
use App\Admin\Extensions\Table\ChargebackStatExport;
use App\Admin\Extensions\Table\SuccessRateExport;
use App\Admin\Extensions\Table\TransactionStatExport;
use App\Admin\Extensions\TrackOrdersExport;
use App\Admin\Extensions\TrackOrdersExportCsv;
use App\Admin\Extensions\TrackTracksExport;
use App\Admin\Extensions\TrackTracksExportCsv;
use App\Admin\Extensions\TransferTicketsExport;
use App\Admin\Extensions\TransferTicketsExportCsv;
use App\Classes\Supports\Logger;
use App\Classes\Supports\Str;
use App\Jobs\SendEmail;
use App\Jobs\SendSlsLog;
use App\Models\AbnormalRefund;
use App\Models\CardCipherWhitelist;
use App\Models\CardVirtual;
use App\Models\Channel;
use App\Models\ChannelExternalCode;
use App\Models\ChannelSupplier;
use App\Models\ChannelWhiteListed;
use App\Models\Chargeback;
use App\Models\ChargebackCase;
use App\Models\ChargebackPenalty;
use App\Models\DownloadCenter;
use App\Models\LocalOrder;
use App\Models\LocalRefund;
use App\Models\Order;
use App\Models\OrderLoses;
use App\Models\OrderRelation;
use App\Models\OrderRiskTransaction;
use App\Models\OrderTrack as OrderTrackModel;
use App\Models\Refund;
use App\Models\SettleDetailMerchant;
use App\Models\ShellProduct;
use App\Models\TransferTicket;
use App\Services\ExportService;
use Dcat\Admin\Models\Administrator;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use DES3;
use Maatwebsite\Excel\Facades\Excel;
use Nick\SecureSpreadsheet\Encrypt;

class ExportTask extends Command
{
    protected $signature = 'task:exportTask';

    protected $description = 'Dishonour Control';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        ini_set('memory_limit', '1280M');
        set_time_limit(3600);

        // 在命令行打印一行信息
        $this->info("导出任务开始");

        // 初始化日志
        $log    = new Logger();
        $config = ['file' => storage_path('logs/consoleTask.log')];
        $log->setConfig($config);

        $task = DownloadCenter::where('status', DownloadCenter::EXPORT_STATUS_NOT)->first();

        if (!$task) {
            return;
        }

        $task->status = DownloadCenter::EXPORT_STATUS_ON;
        $task->save();

        $function = $task['export_identity'] ?? 'defFunction';

        $res = $this->$function($task, $log);

        if ($res['file']) {
            $task->status = DownloadCenter::EXPORT_STATUS_SUCCESS;
            $task->url    = '/data/export/' . $res['file_name'];
        } else {
            $task->status = DownloadCenter::EXPORT_STATUS_ERROR;
            $log->warning('导出任务执行失败 id:' . $task->id);
            dispatch(new SendSlsLog(
                ['message' => '导出任务执行失败 id:' . $task->id],
                [],
                'warning',
                'task'
            ));
        }

        $task->save();

        $this->info("导出任务结束");
    }

    /*
     * @description: CID结算明细导出
     * @author: zqc
     * @date: 2023/8/29
     **/
    public function settleDetailCard($task): array
    {
        $fileName = $task->file_name . '.xlsx';
        $file     = Excel::store(new SettleDetailCardExport($task->query_criteria), '/export/' . $fileName, 'data');
        // 获取用户信息
        $userInfo = Administrator::select('username', 'email')->where('id', $task->admin_user_id)->first();
        // 加密文件
        $encrypt  = new Encrypt();
        $password = random_int(100000, 999999);
        $filePath = public_path('data/export/') . $fileName;
        $encrypt->input($filePath)
            ->password($password)
            ->output($filePath);
        // 组装email数据
        $templateData = [
            'address' => $userInfo->email,
            'user'    => $userInfo->username,
            'view'    => 'SettleDetailCard',
            'data'    => [
                'username'  => $userInfo->username,
                'file_name' => $fileName,
                'password'  => $password,
            ]
        ];
        // 添加邮件任务到队列
        dispatch(new SendEmail($templateData, 0));
        return ['file' => $file, 'file_name' => $fileName];
    }

	/*
	 * @description: 拒付情况统计表导出
	 * @author: zqc
	 * @date: 2023/8/29
	 **/
	public function chargebackStat($task)
	{
		$fileName = $task->file_name . '.xlsx';
		$titles   = $task->titles;
		$file     = Excel::store(new ChargebackStatExport($task->query_criteria, $titles), '/export/' . $fileName, 'data');
		return ['file' => $file, 'file_name' => $fileName];
	}

    public function transactionStat($task)
    {
		$fileName = $task->file_name . '.xlsx';
		$titles   = $task->titles;
		$file     = Excel::store(new TransactionStatExport($task->query_criteria, $titles), '/export/' . $fileName, 'data');

		return ['file' => $file, 'file_name' => $fileName];
    }

	/*
	 * @description: 成功率表格导出
	 * @author: zqc
	 * @date: 2023/9/7
	 **/
	public function successRate($task)
	{
		$fileName = $task->file_name . '.xlsx';
		$titles   = $task->titles;
		$file     = new SuccessRateExport($task->query_criteria, $titles, $fileName);
		return ['file' => $file, 'file_name' => $fileName];
	}

	/**
	 * 丢失订单导出
	 * @param $task
	 * @param $log
	 * @return array|false[]
	 */
	public function orderLoses($task, $log): array
	{
		$queryCriteria 	  = $task->query_criteria;
		$queryCardVirtual = OrderLoses::query()->with('merchant');
		$query 			  = $this->addConditions($queryCardVirtual, $queryCriteria['filter'] ?? []);
		$resultNumber	  = $query->count();

		if ($resultNumber <= 0) {
			return ['file' => false];
		}

		if ($resultNumber >= 20000) {
			// 创建空文件
			$fileName = $task->file_name . '.csv';
			$file = Excel::store(new OrderLosesExportCsv([]), '/export/' . $fileName, 'data');

			$this->queryCsv($fileName, $resultNumber, $log, $task, $query);
		} else {
			$fileName = $task->file_name . '.xlsx';
			$file = Excel::store(new OrderLosesExport($query), '/export/' . $fileName, 'data');
		}

		return ['file' => $file, 'file_name' => $fileName];
	}

    /**
     * @param $task
     * @param $log
     * @return array
     * 订单导出
     */
    public function orderExport($task, $log): array
    {
        $query        = ExportService::orderExportQuery($task->query_criteria);
        $resultNumber = $query->count();
        if ($resultNumber <= 0) {
            return ['file' => false];
        }

        if ($resultNumber >= 20000) {
            // 创建空文件
            $fileName = $task->file_name . '.csv';
            $file     = Excel::store(new OrderExportCsv([], $task->titles), '/export/' . $fileName, 'data');

            $channelSuppliers = Channel::query()->with('channelSupplier:id,supplier_name')->select('id', 'channel_supplier_id')->get()->pluck('channelSupplier.supplier_name', 'id');
            $customize        = [
                'channelSuppliers' => $channelSuppliers
            ];

            $this->queryCsv($fileName, $resultNumber, $log, $task, $query, $customize);
        } else {
            $fileName = $task->file_name . '.xlsx';
            $file     = Excel::store(new OrderExport($query, $task->titles), '/export/' . $fileName, 'data');
        }

        return ['file' => $file, 'file_name' => $fileName];
    }

    /**
     * @param $task
     * @param $log
     * @return array
     * @throws \Exception
     * 账单明细导出
     */
    public function settlement($task, $log): array
    {
        $fileName = $task->file_name ?? 'settlement-' . Str::random(10) . '.xlsx';
        $file     = Excel::store(new BillSettlementExport($task->query_criteria), '/export/' . $fileName, 'data');

        return ['file' => $file, 'file_name' => $fileName];
    }

    /**
     * @param $task
     * @param $log
     * @return array|false[]
     * 交易勾兑导出
     */
    public function settlementOrder($task, $log): array
    {
        $query = ExportService::settlementOrderQuery($task->query_criteria);

        $resultNumber = $query->count();

        if ($resultNumber <= 0) {
            return ['file' => false];
        }

        if ($resultNumber >= 20000) {
            // 创建空文件
            $fileName = $task->file_name . '.csv';
            $file     = Excel::store(new SettlementOrderExportCsv([]), '/export/' . $fileName, 'data');

            $this->queryCsv($fileName, $resultNumber, $log, $task, $query);
        } else {
            $fileName = $task->file_name . '.xlsx';
            $file     = Excel::store(new SettlementOrderExport($query), '/export/' . $fileName, 'data');
        }

        return ['file' => $file, 'file_name' => $fileName];
    }

    /**
     * @param $task
     * @param $log
     * @return array|false[]
     * 虚拟卡导出
     */
    public function cardVirtual($task, $log): array
    {
        $queryCriteria = $task->query_criteria;

        $queryCardVirtual = CardVirtual::query();

        $query = $this->addConditions($queryCardVirtual, $queryCriteria['filter'] ?? []);

        $resultNumber = $query->count();

        if ($resultNumber <= 0) {
            return ['file' => false];
        }

        if ($resultNumber >= 20000) {
            // 创建空文件
            $fileName = $task->file_name . '.csv';
            $file     = Excel::store(new CardVirtualExportCsv([]), '/export/' . $fileName, 'data');

            $this->queryCsv($fileName, $resultNumber, $log, $task, $query);
        } else {
            $fileName = $task->file_name . '.xlsx';
            $file     = Excel::store(new CardVirtualExport($query), '/export/' . $fileName, 'data');
        }

        return ['file' => $file, 'file_name' => $fileName];
    }

    /**
     * @param $task
     * @param $log
     * @return array|false[]
     * 卡号白名单导出
     */
    public function cardWhiteListExport($task, $log): array
    {
        $queryCriteria = $task->query_criteria;
        $query         = ExportService::cardWhiteListExportQuery($queryCriteria);
        $resultNumber  = $query->count();

        if ($resultNumber <= 0) {
            return ['file' => false];
        }

        if ($resultNumber >= 1000) {
            // 创建空文件
            $fileName = $task->file_name . '.csv';
            $file     = Excel::store(new CardWhiteListExportCsv([], $queryCriteria['cardType']), '/export/' . $fileName, 'data');

            $this->cardWhiteListQueryCsv($fileName, $resultNumber, $log, $task, $query, $queryCriteria['cardType']);
        } else {
            $fileName = $task->file_name . '.xlsx';
            $file     = Excel::store(new CardWhiteListExport($query, $queryCriteria['cardType']), '/export/' . $fileName, 'data');
        }

        return ['file' => $file, 'file_name' => $fileName];
    }

    /**
     * @param $task
     * @param $log
     * @return array|false[]
     * 渠道白名单库导出
     */
    public function channelWhiteListExport($task, $log): array
    {
        $queryCriteria = $task->query_criteria;

        $queryCardVirtual = ChannelWhiteListed::query();

        $query = $this->addConditions($queryCardVirtual, $queryCriteria['filter'] ?? []);

        $resultNumber = $query->count();

        if ($resultNumber <= 0) {
            return ['file' => false];
        }

        if ($resultNumber >= 20000) {
            // 创建空文件
            $fileName = $task->file_name . '.csv';
            $file     = Excel::store(new ChannelWhiteListExportCsv([]), '/export/' . $fileName, 'data');

            $this->queryCsv($fileName, $resultNumber, $log, $task, $query);
        } else {
            $fileName = $task->file_name . '.xlsx';
            $file     = Excel::store(new ChannelWhiteListExport($query), '/export/' . $fileName, 'data');
        }

        return ['file' => $file, 'file_name' => $fileName];
    }

    /**
     * @param $task
     * @param $log
     * @return array|false[]
     * 运单申请导出
     */
    public function trackOrders($task, $log): array
    {
        $queryCriteria = $task->query_criteria;

        $queryTrackOrders = Order::query()->with('paymentOrder')
            ->whereIn('type', [Order::TYPES_SALE, Order::TYPES_CAPTURE])
            ->where('status', Order::STATUS_APPROVED)
            ->whereHas('relation', function (Builder $query) {
                $query->where('order_relations.is_refund', OrderRelation::IS_REFUND_NOT)
                    ->where('order_relations.is_delivery', OrderRelation::IS_DELIVERY_NOT)
                    ->where('order_relations.is_chargeback', OrderRelation::IS_CHARGEBACK_NOT);
            });

        $query = $this->addConditions($queryTrackOrders, $queryCriteria['filter'] ?? []);

        $resultNumber = $query->count();

        if ($resultNumber <= 0) {
            return ['file' => false];
        }

        if ($resultNumber >= 20000) {
            // 创建空文件
            $fileName = $task->file_name . '.csv';
            $file     = Excel::store(new TrackOrdersExportCsv([]), '/export/' . $fileName, 'data');

            $this->queryCsv($fileName, $resultNumber, $log, $task, $query);
        } else {
            $fileName = $task->file_name . '.xlsx';
            $file     = Excel::store(new TrackOrdersExport($query), '/export/' . $fileName, 'data');
        }

        return ['file' => $file, 'file_name' => $fileName];
    }

    /**
     * @param $task
     * @param $log
     * @return array|false[]
     * 运单信息导出
     */
    public function trackTracks($task, $log): array
    {
        $queryCriteria = $task->query_criteria;

        $queryTrackTracks = OrderTrackModel::with('order');

        foreach ($queryCriteria['inputs'] as $key => $value) {
            switch ($key) {
                case 'order.order_id':
                case 'order.order_number':
                case 'order.merchant_id':
                    $column = trim(strrchr($key, '.'), '.');
                    $queryTrackTracks->whereHas('order', function ($query) use ($column, $value) {
                        $query->where($column, $value);
                    });
                    break;
                case 'order.relation.is_refund':
                case 'order.relation.is_chargeback':
                case 'order.relation.is_settle':
                    $column = trim(strrchr($key, '.'), '.');
                    $queryTrackTracks->whereHas('order.relation', function ($query) use ($column, $value) {
                        $query->where('order_relations.' . $column, $value);
                    });
                    break;
                case strpos($key, 'order.channel_id'):
                    $queryTrackTracks->whereHas('order', function ($query) use ($value) {
                        $query->where('channel_id', $value);
                    });
                    break;
            }
        }

        $query = $this->addConditions($queryTrackTracks, $queryCriteria['filter'] ?? []);

        $resultNumber = $query->count();

        if ($resultNumber <= 0) {
            return ['file' => false];
        }

        if ($resultNumber >= 20000) {
            // 创建空文件
            $fileName = $task->file_name . '.csv';
            $file     = Excel::store(new TrackTracksExportCsv([]), '/export/' . $fileName, 'data');

            $this->queryCsv($fileName, $resultNumber, $log, $task, $query);
        } else {
            $fileName = $task->file_name . '.xlsx';
            $file     = Excel::store(new TrackTracksExport($query), '/export/' . $fileName, 'data');
        }

        return ['file' => $file, 'file_name' => $fileName];
    }

    /**
     * @param $task
     * @param $log
     * @return array|false[]
     * 退款信息导出
     */
    public function refundsInfo($task, $log): array
    {
        $queryCriteria = $task->query_criteria;

        $queryRefundsInfo = Refund::with('order')->with('paymentRefund');

        foreach ($queryCriteria['inputs'] as $key => $value) {
            switch ($key) {
                case 'merchant_id':
                    $queryRefundsInfo->whereHas('order', function ($query) use ($value) {
                        $query->where('merchant_id', $value);
                    });
                    break;
                case 'order_number':
                    $queryRefundsInfo->whereHas('order', function ($query) use ($value) {
                        $query->where('order_number', $value);
                    });
                    break;
                case 'paymentRefund.payment_refund_id':
                    $queryRefundsInfo->whereHas('paymentRefund', function ($query) use ($value) {
                        $query->where('payment_refund_id', $value);
                    });
                    break;
                case 'order.channelObj.channel_supplier_id':
                    $queryRefundsInfo->whereHas('order.channelObj', function ($query) use ($value) {
                        $query->where('channel_supplier_id', $value);
                    });
                    break;
            }
        }

        $query = $this->addConditions($queryRefundsInfo, $queryCriteria['filter'] ?? []);

        $resultNumber = $query->count();

        if ($resultNumber <= 0) {
            return ['file' => false];
        }

        if ($resultNumber >= 20000) {
            // 创建空文件
            $fileName = $task->file_name . '.csv';
            $file     = Excel::store(new RefundsInfoExportCsv([], $task->titles), '/export/' . $fileName, 'data');

            $this->queryCsv($fileName, $resultNumber, $log, $task, $query);
        } else {
            $fileName = $task->file_name . '.xlsx';
            $file     = Excel::store(new RefundsInfoExport($query, $task->titles), '/export/' . $fileName, 'data');
        }

        return ['file' => $file, 'file_name' => $fileName];
    }

    /**
     * @param $task
     * @param $log
     * @return array|false[]
     * 退款信息导出
     */
    public function abnormalRefunds($task, $log): array
    {
        $queryCriteria = $task->query_criteria;

        $queryRefundsInfo = AbnormalRefund::with(['order', 'abnormalPaymentRefund']);

        foreach ($queryCriteria['inputs'] as $key => $value) {
            switch ($key) {
                case 'merchant_id':
                    $queryRefundsInfo->whereHas('order', function ($query) use ($value) {
                        $query->where('merchant_id', $value);
                    });
                    break;
                case 'order_number':
                    $queryRefundsInfo->whereHas('order', function ($query) use ($value) {
                        $query->where('order_number', $value);
                    });
                    break;
                case 'abnormalPaymentRefund.payment_refund_id':
                    $queryRefundsInfo->whereHas('abnormalPaymentRefund', function ($query) use ($value) {
                        $query->where('payment_refund_id', $value);
                    });
                    break;
                case 'order.channelObj.channel_supplier_id':
                    $queryRefundsInfo->whereHas('order.channelObj', function ($query) use ($value) {
                        $query->where('channel_supplier_id', $value);
                    });
                    break;
            }
        }

        $query = $this->addConditions($queryRefundsInfo, $queryCriteria['filter'] ?? []);

        $resultNumber = $query->count();

        if ($resultNumber <= 0) {
            return ['file' => false];
        }

        if ($resultNumber >= 20000) {
            // 创建空文件
            $fileName = $task->file_name . '.csv';
            $file     = Excel::store(new AbnormalRefundsExportCsv([], $task->titles), '/export/' . $fileName, 'data');

            $this->queryCsv($fileName, $resultNumber, $log, $task, $query);
        } else {
            $fileName = $task->file_name . '.xlsx';
            $file     = Excel::store(new AbnormalRefundsExport($query, $task->titles), '/export/' . $fileName, 'data');
        }

        return ['file' => $file, 'file_name' => $fileName];
    }

    /**
     * @param $task
     * @param $log
     * @return array|false[]
     * 退款差错导出
     */
    public function refundError($task, $log): array
    {
        $queryCriteria = $task->query_criteria;

        $queryRefundError = Refund::whereIn('status', [Refund::STATUS_DECLINED, Refund::STATUS_PENDING])->with('order')->with('paymentRefund');

        foreach ($queryCriteria['inputs'] as $key => $value) {
            switch ($key) {
                case 'paymentRefund.payment_refund_id':
                    $queryRefundError->whereHas('paymentRefund', function ($query) use ($value) {
                        $query->where('payment_refund_id', $value);
                    });
                    break;
            }
        }

        $query = $this->addConditions($queryRefundError, $queryCriteria['filter'] ?? []);

        $resultNumber = $query->count();

        if ($resultNumber <= 0) {
            return ['file' => false];
        }

        if ($resultNumber >= 20000) {
            // 创建空文件
            $fileName = $task->file_name . '.csv';
            $file     = Excel::store(new RefundErrorsExportCsv([]), '/export/' . $fileName, 'data');

            $this->queryCsv($fileName, $resultNumber, $log, $task, $query);
        } else {
            $fileName = $task->file_name . '.xlsx';
            $file     = Excel::store(new RefundErrorsExport($query), '/export/' . $fileName, 'data');
        }

        return ['file' => $file, 'file_name' => $fileName];
    }

    /**
     * @param $task
     * @param $log
     * @return array|false[]
     * 结算明细导出
     */
    public function settlementDetail($task, $log): array
    {
        $query = ExportService::settlementDetailQuery($task->query_criteria);

        $resultNumber = $query->count();

        if ($resultNumber <= 0) {
            return ['file' => false];
        }

        if ($resultNumber >= 20000) {
            // 创建空文件
            $fileName = $task->file_name . '.csv';
            $file     = Excel::store(new SettlementDetailExportCsv([]), '/export/' . $fileName, 'data');
            // 查找所有渠道信息
            $channelSuppliers = ChannelSupplier::all()->pluck('supplier_name', 'id')->toArray();
            $customize        = [
                'channelSuppliers' => $channelSuppliers
            ];

            $this->queryCsv($fileName, $resultNumber, $log, $task, $query, $customize);
        } else {
            $fileName = $task->file_name . '.xlsx';
            $file     = Excel::store(new SettlementDetailExport($query), '/export/' . $fileName, 'data');
        }

        return ['file' => $file, 'file_name' => $fileName];
    }

    /**
     * @param $task
     * @param $log
     * @return array|false[]
     * 提现信息导出
     */
    public function transferTickets($task, $log): array
    {
        $queryCriteria = $task->query_criteria;

        $queryTransferTickets = TransferTicket::with('accounts');

        foreach ($queryCriteria['inputs'] as $key => $value) {
            switch ($key) {
                case 'accounts.cardholder':
                    $queryTransferTickets->whereHas('accounts', function ($query) use ($value) {
                        $query->where('cardholder', $value);
                    });
                    break;
            }
        }

        $query = $this->addConditions($queryTransferTickets, $queryCriteria['filter'] ?? []);

        $resultNumber = $query->count();

        if ($resultNumber <= 0) {
            return ['file' => false];
        }

        if ($resultNumber >= 20000) {
            // 创建空文件
            $fileName = $task->file_name . '.csv';
            $file     = Excel::store(new TransferTicketsExportCsv([]), '/export/' . $fileName, 'data');

            $this->queryCsv($fileName, $resultNumber, $log, $task, $query);
        } else {
            $fileName = $task->file_name . '.xlsx';
            $file     = Excel::store(new TransferTicketsExport($query), '/export/' . $fileName, 'data');
        }

        return ['file' => $file, 'file_name' => $fileName];
    }

    /**
     * @param $task
     * @param $log
     * @return array|false[]
     * MID结算信息导出
     */
    public function settleDetailMerchant($task, $log): array
    {
        $queryCriteria = $task->query_criteria;

        $querySettleDetailMerchant = SettleDetailMerchant::query();

        $query = $this->addConditions($querySettleDetailMerchant, $queryCriteria['filter'] ?? []);

        $resultNumber = $query->count();

        if ($resultNumber <= 0) {
            return ['file' => false];
        }

        if ($resultNumber >= 20000) {
            // 创建空文件
            $fileName = $task->file_name . '.csv';
            $file     = Excel::store(new SettleDetailMerchantExportCsv([]), '/export/' . $fileName, 'data');

            $this->queryCsv($fileName, $resultNumber, $log, $task, $query);
        } else {
            $fileName = $task->file_name . '.xlsx';
            $file     = Excel::store(new SettleDetailMerchantExport($query), '/export/' . $fileName, 'data');
        }

        return ['file' => $file, 'file_name' => $fileName];
    }

    /**
     * @param $task
     * @param $log
     * @return array
     * 拒付工单导出
     */
    public function chargeback($task, $log): array
    {

        $queryCriteria = $task->query_criteria;

        $queryChargeback = Chargeback::select('*', 'chargeback_id as chargeback_id_temp', DB::raw('IF(status=1, parent_chargeback_id, chargeback_id) chargeback_id'))
        ->with([
            'orderRelation:order_id,is_refund',
            'order.card',
            'chargebackHistory:chargeback_id,chargeback_code,chargeback_at,type,arn'
        ])->where('use_flag', 0)->orderBy('chargeback_at', 'desc');

        foreach ($queryCriteria['inputs'] as $key => $value) {
            switch ($key) {
                case 'is_refund':
                    $queryChargeback->whereHas('orderRelation', function ($query) use ($value) {
                        $query->where('order_relations.is_refund', $value);
                    });
                    break;
                case strpos($key, 'chargebackHistory'):
                    $column = trim(strrchr($key, '.'), '.');
                    $queryChargeback->whereHas('chargebackHistory', function ($query) use ($value, $column) {
                        $query->where('chargeback_history.' . $column, $value);
                    });
                    break;
                case 'channel_id':
                    $queryChargeback->whereHas('order', function ($query) use ($value) {
                        $channelList = Channel::where('channel_supplier_id', $value)->get();
                        $tempArr     = array();

                        foreach ($channelList as $channel) {
                            $tempArr[] = $channel->id;
                        }

                        if (sizeof($tempArr) > 0) {
                            $query->whereIn('channel_id', $tempArr);
                        } else {
                            $query->where('channel_id', '-1');
                        }
                    });
                    break;
                case 'merchant_id':
                    $queryChargeback->whereHas('order', function ($query) use ($value, $key) {
                        $query->where('orders.' . $key, $value);
                    });
                    break;
                case 'order_number':
                    $queryChargeback->whereHas('order', function ($query) use ($value) {
                        $query->where('order_number', 'like', "%{$value}%");
                    });
                    break;
				case 'cc_type':
					$queryChargeback->whereHas('order.card', function ($query) use ($value) {
						$query->where('cc_type', $value);
					});
					break;
            }
        }

        $query        = $this->addConditions($queryChargeback, $queryCriteria['filter'] ?? []);
        $resultNumber = $query->count();

        if ($resultNumber <= 0) {
            return ['file' => false];
        }

        if ($resultNumber >= 20000) {
            // 创建空文件
            $fileName = $task->file_name . '.csv';
            $file     = Excel::store(new ChargebackExportCsv([], $task->titles), '/export/' . $fileName, 'data');

            $this->queryCsv($fileName, $resultNumber, $log, $task, $query);
        } else {
            $fileName = $task->file_name . '.xlsx';
            $file     = Excel::store(new ChargebackExport($query, $task->titles), '/export/' . $fileName, 'data');
        }

        return ['file' => $file, 'file_name' => $fileName];
    }


    /**
     * @param $task
     * @param $log Logger
     * @return array
     * 拒付预警导出
     */
    public function chargebackCase($task, $log): array
    {

        $queryCriteria = $task->query_criteria;

		$queryChargebackCase = ChargebackCase::with([
			'orderRelation:order_id,is_refund,is_chargeback',
			'refund:order_id,created_at',
			'order:order_id,card_bill,created_at,card_id,currency,amount',
			'order.card:id,cc_type',
		])->where(['is_normal' => ChargebackCase::IS_NORMAL_TRUE]);

		foreach ($queryCriteria['inputs'] as $key => $value) {
			switch ($key) {
				case 'channelObj.channel_supplier_id':
					$queryChargebackCase->whereHas('channelObj', function ($query) use ($value) {
						$query->where('channel_supplier_id', $value);
					});
					break;
				case 'order_card_cc_type':
					$queryChargebackCase->whereHas('order.card', function (Builder $query) use ($value) {
						$query->where('cc_type', $value);
					});
					break;
			}
		}

        $query        = $this->addConditions($queryChargebackCase, $queryCriteria['filter'] ?? []);
        $resultNumber = $query->count();

        if ($resultNumber <= 0) {
            return ['file' => false];
        }

        if ($resultNumber >= 20000) {
            // 创建空文件
            $fileName = $task->file_name . '.csv';
            $file     = Excel::store(new ChargebackCaseExportCsv([]), '/export/' . $fileName, 'data');
            $this->queryCsv($fileName, $resultNumber, $log, $task, $query);
        } else {
            $fileName = $task->file_name . '.xlsx';
            $file     = Excel::store(new ChargebackCaseExport($query), '/export/' . $fileName, 'data');
        }

        return ['file' => $file, 'file_name' => $fileName];
    }

	/**
	 * @param $task
	 * @param $log Logger
	 * @return array
	 * 异常拒付预警导出
	 */
	public function chargebackCaseAbnormal($task, $log): array
	{

		$queryCriteria = $task->query_criteria;

		$queryChargebackCase = ChargebackCase::with([
			'orderRelation:order_id,is_refund,is_chargeback',
			'refund:order_id,created_at',
			'order:order_id,card_bill,created_at,card_id',
			'order.card:id,cc_type',
		])->where(['is_normal' => ChargebackCase::IS_NORMAL_FALSE]);

		foreach ($queryCriteria['inputs'] as $key => $value) {
			switch ($key) {
				case 'channelObj.channel_supplier_id':
					$queryChargebackCase->whereHas('channelObj', function ($query) use ($value) {
						$query->where('channel_supplier_id', $value);
					});
					break;
				case 'order_card_cc_type':
					$queryChargebackCase->whereHas('order.card', function (Builder $query) use ($value) {
						$query->where('cc_type', $value);
					});
					break;
			}
		}

		$query        = $this->addConditions($queryChargebackCase, $queryCriteria['filter'] ?? []);
		$resultNumber = $query->count();

		if ($resultNumber <= 0) {
			return ['file' => false];
		}

		if ($resultNumber >= 20000) {
			// 创建空文件
			$fileName = $task->file_name . '.csv';
			$file     = Excel::store(new ChargebackCaseAbnormalExportCsv([]), '/export/' . $fileName, 'data');
			$this->queryCsv($fileName, $resultNumber, $log, $task, $query);
		} else {
			$fileName = $task->file_name . '.xlsx';
			$file     = Excel::store(new ChargebackCaseAbnormalExport($query), '/export/' . $fileName, 'data');
		}

		return ['file' => $file, 'file_name' => $fileName];
	}

    /**
     * @param $task
     * @param $log Logger
     * @return array
     * 拒付罚金导出
     */
    public function chargebackPenalty($task, $log): array
    {
        $queryCriteria          = $task->query_criteria;
        $queryChargebackPenalty = ChargebackPenalty::query();
        $query                  = $this->addConditions($queryChargebackPenalty, $queryCriteria['filter'] ?? []);
        $resultNumber           = $query->count();

        if ($resultNumber <= 0) {
            return ['file' => false];
        }

        if ($resultNumber >= 20000) {
            // 创建空文件
            $fileName = $task->file_name . '.csv';
            $file     = Excel::store(new ChargebackPenaltyExportCsv([]), '/export/' . $fileName, 'data');
            $this->queryCsv($fileName, $resultNumber, $log, $task, $query);
        } else {
            $fileName = $task->file_name . '.xlsx';
            $file     = Excel::store(new ChargebackPenaltyExport($query), '/export/' . $fileName, 'data');
        }

        return ['file' => $file, 'file_name' => $fileName];
    }

    /**
     * @param $task
     * @param $log
     * @return array
     * 本地支付订单导出
     */
    public function localOrderExport($task, $log): array
    {
        $queryCriteria = $task->query_criteria;

        $queryOrder = LocalOrder::with([
            // 'track:order_id,tracking_type,tracking_number',
            'address:id,bill_name,bill_email,bill_country,bill_address,ship_name,ship_email,ship_country,ship_address,ip,bill_city,bill_state,bill_postcode,bill_phone,ship_city,ship_state,ship_postcode,ship_phone',
            'relation:order_id,is_refund,is_chargeback',
            'paymentOrder:order_id,order_number,payment_order_id,amount,currency,code,result,remark',
            'products:order_id,name',
            // 'settlements:order_id,amount_usd,payment_amount_usd,is_settle,settle_at',
        ]);

        foreach ($queryCriteria['inputs'] as $key => $value) {
            switch ($key) {
                case 'channelObj.channel_supplier_id':
                    $queryOrder->whereHas('channelObj', function ($query) use ($value) {
                        $query->where('channel_supplier_id', $value);
                    });
                    break;
                case strpos($key, 'address'):
                    $column = trim(strrchr($key, '.'), '.');
                    $queryOrder->whereHas('address', function ($query) use ($value, $column) {
                        $query->where('order_addresses.' . $column, $value);
                    });
                    break;
                case strpos($key, 'relation'):
                    $column = trim(strrchr($key, '.'), '.');
                    $queryOrder->whereHas('relation', function ($query) use ($value, $column) {
                        $query->where('order_relations.' . $column, $value);
                    });
                    break;
                case strpos($key, 'paymentOrder'):
                    $column = trim(strrchr($key, '.'), '.');
                    $queryOrder->whereHas('paymentOrder', function ($query) use ($value, $column) {
                        $query->where($column, $value);
                    });
                    break;
            }
        }

        $query        = $this->addConditions($queryOrder, $queryCriteria['filter'] ?? []);
        $resultNumber = $query->count();

        if ($resultNumber <= 0) {
            return ['file' => false];
        }

        if ($resultNumber >= 20000) {
            // 创建空文件
            $fileName = $task->file_name . '.csv';
            $file     = Excel::store(new LocalOrderExportCsv([]), '/export/' . $fileName, 'data');

            $channelSuppliers = Channel::query()->with('channelSupplier:id,supplier_name')->select('id', 'channel_supplier_id')->get()->pluck('channelSupplier.supplier_name', 'id');
            $customize        = [
                'channelSuppliers' => $channelSuppliers
            ];

            $this->queryCsv($fileName, $resultNumber, $log, $task, $query, $customize);
        } else {
            $fileName = $task->file_name . '.xlsx';
            $file     = Excel::store(new LocalOrderExport($query), '/export/' . $fileName, 'data');
        }

        return ['file' => $file, 'file_name' => $fileName];
    }

    /**
     * @param $task
     * @param $log
     * @return array|false[]
     * 本地支付退款信息导出
     */
    public function localRefundsInfo($task, $log): array
    {
        $queryCriteria = $task->query_criteria;

        $queryRefundsInfo = LocalRefund::with('order')->with('paymentRefund');

        foreach ($queryCriteria['inputs'] as $key => $value) {
            switch ($key) {
                case 'merchant_name':
                    $queryRefundsInfo->whereHas('order', function ($query) use ($value) {
                        $query->where('merchant_name', 'like', "%{$value}%");
                    });
                    break;
                case 'order_number':
                    $queryRefundsInfo->whereHas('order', function ($query) use ($value) {
                        $query->where('order_number', $value);
                    });
                    break;
                case 'paymentRefund.payment_refund_id':
                    $queryRefundsInfo->whereHas('paymentRefund', function ($query) use ($value) {
                        $query->where('payment_refund_id', $value);
                    });
                    break;
                case 'order.channelObj.channel_supplier_id':
                    $queryRefundsInfo->whereHas('order.channelObj', function ($query) use ($value) {
                        $query->where('channel_supplier_id', $value);
                    });
                    break;
            }
        }

        $query = $this->addConditions($queryRefundsInfo, $queryCriteria['filter'] ?? []);

        $resultNumber = $query->count();

        if ($resultNumber <= 0) {
            return ['file' => false];
        }

        if ($resultNumber >= 20000) {
            // 创建空文件
            $fileName = $task->file_name . '.csv';
            $file     = Excel::store(new LocalRefundsInfoExportCsv([]), '/export/' . $fileName, 'data');

            $this->queryCsv($fileName, $resultNumber, $log, $task, $query);
        } else {
            $fileName = $task->file_name . '.xlsx';
            $file     = Excel::store(new LocalRefundsInfoExport($query), '/export/' . $fileName, 'data');
        }

        return ['file' => $file, 'file_name' => $fileName];
    }

    /**
     * @param $task
     * @param $log
     * @return array|false[]
     * 本地支付退款差错导出
     */
    public function localRefundError($task, $log): array
    {
        $queryCriteria = $task->query_criteria;

        $queryRefundError = LocalRefund::whereIn('status', [LocalRefund::STATUS_DECLINED, LocalRefund::STATUS_PENDING])->with('order')->with('paymentRefund');

        foreach ($queryCriteria['inputs'] as $key => $value) {
            switch ($key) {
                case 'paymentRefund.payment_refund_id':
                    $queryRefundError->whereHas('paymentRefund', function ($query) use ($value) {
                        $query->where('payment_refund_id', $value);
                    });
                    break;
            }
        }

        $query = $this->addConditions($queryRefundError, $queryCriteria['filter'] ?? []);

        $resultNumber = $query->count();

        if ($resultNumber <= 0) {
            return ['file' => false];
        }

        if ($resultNumber >= 20000) {
            // 创建空文件
            $fileName = $task->file_name . '.csv';
            $file     = Excel::store(new LocalRefundErrorsExportCsv([]), '/export/' . $fileName, 'data');

            $this->queryCsv($fileName, $resultNumber, $log, $task, $query);
        } else {
            $fileName = $task->file_name . '.xlsx';
            $file     = Excel::store(new LocalRefundErrorsExport($query), '/export/' . $fileName, 'data');
        }

        return ['file' => $file, 'file_name' => $fileName];
    }

    /**
     * @param $task
     * @param $log
     * @return array|false[]
     * 风险交易数据导出
     */
    public function riskTransaction($task, $log): array
    {
        $queryCriteria    = $task->query_criteria;
        $queryRefundError = OrderRiskTransaction::with('relation');
        $query            = $this->addConditions($queryRefundError, $queryCriteria['filter'] ?? []);

        $query->selectRaw('order_risk_transactions.*,
        order_relations.is_refund,
        order_relations.is_chargeback,
        if(order_relations.is_refund <> ' . OrderRelation::IS_REFUND_FULL . ' and order_relations.is_chargeback = ' . OrderRelation::IS_CHARGEBACK_NOT . ' and order_risk_transactions.is_accept = ' . OrderRiskTransaction::IS_ACCEPT_NOT . ', ' . OrderRiskTransaction::PROCESSING_STATUS_NOT . ', ' . OrderRiskTransaction::PROCESSING_STATUS . ') as processing_status')
        ->leftJoin('order_relations', 'order_risk_transactions.order_id', '=', 'order_relations.order_id')
        ->orderByRaw('processing_status asc, created_at desc');

        foreach ($queryCriteria['inputs'] as $key => $value) {
            switch ($key) {
                case 'order_id':
                    $query->where('order_risk_transactions.order_id', $value);
                    break;
                case 'created_at.end':
                    $query->where('order_risk_transactions.created_at', '<=', $value);
                    break;
                case 'created_at.start':
                    $query->where('order_risk_transactions.created_at', '>=',$value);
                    break;
                case 'processing_status':
                    if ($value == OrderRiskTransaction::PROCESSING_STATUS_NOT) {
                        $query->where([
                            ['order_relations.is_refund', '<>', OrderRelation::IS_REFUND_FULL],
                            ['order_relations.is_chargeback', '=',  OrderRelation::IS_CHARGEBACK_NOT],
                            ['order_risk_transactions.is_accept', '=', OrderRiskTransaction::IS_ACCEPT_NOT],
                        ]);
                    } else {
                        $query->orWhere('order_relations.is_refund', '=', OrderRelation::IS_REFUND_FULL)->orWhere('order_relations.is_chargeback', '<>',  OrderRelation::IS_CHARGEBACK_NOT)->orWhere('order_risk_transactions.is_accept', '<>', OrderRiskTransaction::IS_ACCEPT_NOT);
                    }
                    break;
            }
        }

        $resultNumber = $query->count();

        if ($resultNumber <= 0) {
            $log->info('请检查查询语句 id:' . $task->id);
            return ['file' => false];
        }

        if ($resultNumber >= 20000) {
            // 创建空文件
            $fileName = $task->file_name . '.csv';
            $file     = Excel::store(new RiskTransactionExportCsv([]), '/export/' . $fileName, 'data');

            $this->queryCsv($fileName, $resultNumber, $log, $task, $query);
        } else {
            $fileName = $task->file_name . '.xlsx';
            $file     = Excel::store(new RiskTransactionExport($query), '/export/' . $fileName, 'data');
        }

        return ['file' => $file, 'file_name' => $fileName];
    }

    public function cardCipherWhitelist($task, $log): array
    {
        $queryCriteria = $task->query_criteria;
        $queryCipherWhitelist = CardCipherWhitelist::query();
        $query         = $this->addConditions($queryCipherWhitelist, $queryCriteria['filter'] ?? []);

        $query->select('card_number','card_mask','merchant_ids','cc_type','created_at')
         ->orderByRaw('created_at desc');

        $midS = false;
        foreach ($queryCriteria['inputs'] as $key => $value) {
            switch ($key) {
                case 'card_number':
                    $query->where('card_number', DES3::encrypt($value));
                    break;
                case preg_match('/^merchant_ids\.\d+$/', $key):
                    $midS = true;
//                    $query->where(function($q) use ($value) {
//                        $q->orWhere('merchant_ids', 'LIKE', "%&{$value}&%")
//                          ->orWhere('merchant_ids', 'LIKE', "{$value}&%")
//                          ->orWhere('merchant_ids', 'LIKE', "%&{$value}")
//                          ->orWhere('merchant_ids', $value);
//                    });
                    break;
                case 'created_at.end':
                    $query->where('created_at', '<=', $value);
                    break;
                case 'created_at.start':
                    $query->where('created_at', '>=',$value);
                    break;
            }
        }

        if ($midS){
            // 处理商户ID多选查询
            $merchantIds = collect($queryCriteria['inputs'])
                ->filter(function($v, $k){
                    return str_starts_with($k, 'merchant_ids.');
                })
                ->values()
                ->toArray();

            $query->where(function($q) use ($merchantIds) {
                foreach ($merchantIds as $id) {
                    $q->orWhere('merchant_ids', 'LIKE', "%&{$id}&%")
                      ->orWhere('merchant_ids', 'LIKE', "{$id}&%")
                      ->orWhere('merchant_ids', 'LIKE', "%&{$id}")
                      ->orWhere('merchant_ids', $id);
                }
            });
        }

        $resultNumber = $query->count();

        if ($resultNumber <= 0) {
            return ['file' => false];
        }

        if ($resultNumber >= 1000) {
            // 创建空文件
            $fileName = $task->file_name . '.csv';
            $file     = Excel::store(new CardCipherWhitelistExportCsv([]), '/export/' . $fileName, 'data');

            $this->queryCsv($fileName, $resultNumber, $log, $task, $query);
        } else {
            $fileName = $task->file_name . '.xlsx';
            $file     = Excel::store(new CardCipherWhitelistExport($query), '/export/' . $fileName, 'data');
        }

        return ['file' => $file, 'file_name' => $fileName];
    }

    public function defFunction($task, $log)
    {
        $log->info('导出任务执行失败,无效的类型 id:' . $task->id);
        dispatch(new SendSlsLog(
            ['message' => '导出任务执行失败,无效的类型 id:' . $task->id],
            [],
            'info',
            'task'
        ));
    }

    /**
     * Add conditions to grid model.
     *
     * @param Builder $query
     * @param array $conditions
     *
     * @return Builder
     */
    public function addConditions(Builder $query, array $conditions): Builder
    {
        sort($conditions);

        foreach ($conditions as $condition) {
            call_user_func_array([$query, key($condition)], current($condition));
        }
        return $query;
    }

    /**
     * @param $fileName
     * @param $resultNumber
     * @param $log
     * @param $task
     * @param $query
     * @return void
     * csv导出
     */
    public function queryCsv($fileName, $resultNumber, $log, $task, $query, $customize = [])
    {
        $loadCount = 3000;
        $totalPage = ceil($resultNumber / $loadCount);


        // 打开csv文件
        $filePath = public_path('/data/export/') . $fileName;
        $fp       = fopen($filePath, 'a');

        if (!$fp) {
            $log->error($task->id . '打开csv文件失败 path:' . $filePath);
            dispatch(new SendSlsLog(
                ['message' => ($task->id . '打开csv文件失败 path:' . $filePath)],
                [],
                'error',
                'task'
            ));

            return;
        }

        $content = '';
        $total   = 0;

        for ($f = 1; $f <= $totalPage; $f++) {
            $result = $query->forPage($f, $loadCount)->get();

            foreach ($result as $row) {
                $functionName = $task->export_identity . 'EndData';

                $endData = $this->$functionName($row, $customize);
                $endData = $this->substitution($endData);

                $content .= implode(',', $endData) . PHP_EOL;
                $total++;
            }

            // 超过10W行先写入
            if ($total > 100000) {
                fwrite($fp, $content);
                $content = '';
                $total   = 0;
            }
        }

        // 拆分写入，避免数据量太大影响写入速度
        if (!empty($content)) {
            fwrite($fp, $content);
        }

        fclose($fp);
    }

    /**
     * @param $fileName
     * @param $resultNumber
     * @param $log
     * @param $task
     * @param $query
     * @return void
     * 白名单csv导出
     */
    public function cardWhiteListQueryCsv($fileName, $resultNumber, $log, $task, $query, array $cardType)
    {
        $loadCount = 3000;
        $totalPage = ceil($resultNumber / $loadCount);

        // 打开csv文件
        $filePath = public_path('/data/export/') . $fileName;
        $fp       = fopen($filePath, 'a');

        if (!$fp) {
            $log->error($task->id . '打开csv文件失败 path:' . $filePath);
            dispatch(new SendSlsLog(
                ['message' => ($task->id . '打开csv文件失败 path:' . $filePath)],
                [],
                'error',
                'task'
            ));

            return;
        }

        $content = '';
        $total   = 0;

        for ($f = 1; $f <= $totalPage; $f++) {
            $result = $query->forPage($f, $loadCount)->get();

            foreach ($result as $row) {
                $endData = $this->cardWhiteListExportEndData($row, $cardType);
                $endData = $this->substitution($endData);

                $content .= implode(',', $endData) . PHP_EOL;
                $total++;
            }

            // 超过10W行先写入
            if ($total > 100000) {
                fwrite($fp, $content);
                $content = '';
                $total   = 0;
            }
        }

        // 拆分写入，避免数据量太大影响写入速度
        if (!empty($content)) {
            fwrite($fp, $content);
        }

        fclose($fp);
    }

	/**
	 * @description: 丢失订单导出
	 * @author: zqc
	 * @date: 2023/8/8
	 **/
	public function orderLosesEndData($row): array
	{
		return OrderLosesExport::getMap($row);
	}

    /**
     * @param $row
     * @return array
     * 交易勾兑
     */
    public function settlementOrderEndData($row): array
    {
        return SettlementOrderExport::getMap($row);
    }

    public function trackTracksEndData($row): array
    {
        return TrackTracksExport::getMap($row);
    }

    public function trackOrdersEndData($row): array
    {
        return TrackOrdersExport::getMap($row);
    }

    public function refundsInfoEndData($row): array
    {
        return RefundsInfoExport::getMap($row);
    }

    public function AbnormalRefundsEndData($row): array
    {
        return AbnormalRefundsExport::getMap($row);
    }

    public function refundErrorEndData($row): array
    {
        return RefundErrorsExport::getMap($row);
    }

    public function cardVirtualEndData($row): array
    {
        return CardVirtualExport::getMap($row);
    }

    public function settlementDetailEndData($row, $customize = []): array
    {
        return SettlementDetailExport::getMap($row, $customize['channelSuppliers'] ?? []);
    }

    public function transferTicketsEndData($row): array
    {
        return TransferTicketsExport::getMap($row);
    }

    public function settleDetailMerchantEndData($row): array
    {
        return SettleDetailMerchantExport::getMap($row);
    }


    public function orderExportEndData($row, $customize = []): array
    {
        return OrderExport::getMap($row, $customize['channelSuppliers'] ?? []);
    }

    public function chargebackEndData($row): array
    {
        return ChargebackExport::getMap($row, $row['chargebackHistory']['type'], $row['chargebackHistory']['chargeback_code'], $row['chargebackHistory']['arn']);
    }

    /**
     * 拒付预警导出数据
     * @param [type] $row
     * @return array
     */
    public function chargebackCaseEndData($row): array
    {
        return ChargebackCaseExport::getMap($row);
    }

	/**
	 * 异常预警导出数据
	 * @param [type] $row
	 * @return array
	 */
	public function chargebackCaseAbnormalEndData($row): array
	{
		return ChargebackCaseAbnormalExport::getMap($row);
	}

    /**
     * 拒付罚金导出数据
     * @param [type] $row
     * @return array
     */
    public function chargebackPenaltyEndData($row): array
    {
        return ChargebackPenaltyExport::getMap($row);
    }

    /**
     * 白名单列表导出数据
     * @return array
     */
    public function cardWhiteListExportEndData($row, array $cardType): array
    {
        return CardWhiteListExport::getMap($row, $cardType);
    }

    public function channelWhiteListExportEndData($row): array
    {
        return ChannelWhiteListExport::getMap($row);
    }

    public function substitution($endData)
    {
        foreach ($endData as $key => $value) {
            $endData[$key] = str_replace(',', ' ', $value);

            // 避免科学计数法
            if (is_numeric($endData[$key]) && strlen($endData[$key]) > 10) {
                $endData[$key] = "'".$endData[$key];
            }
        }

        return $endData;
    }

    public function localOrderExportEndData($row, $customize = []): array
    {
        return LocalOrderExport::getMap($row, $customize['channelSuppliers'] ?? []);
    }

    public function localRefundsInfoEndData($row): array
    {
        return LocalRefundsInfoExport::getMap($row);
    }

    public function localRefundErrorEndData($row): array
    {
        return localRefundErrorsExport::getMap($row);
    }

    public function riskTransactionEndData($row): array
    {
        return RiskTransactionExport::getMap($row);
    }

    public function shellProduct($task, $log): array
    {
        $queryCriteria     = $task->query_criteria;
        $queryShellProduct = ShellProduct::query();
        $query             = $this->addConditions($queryShellProduct, $queryCriteria['filter'] ?? []);
        $resultNumber      = $query->count();

        if ($resultNumber <= 0) {
            return ['file' => false];
        }

        if ($resultNumber >= 20000) {
            // 创建空文件
            $fileName = $task->file_name . '.csv';
            $file     = Excel::store(new ShellProductExportCsv([]), '/export/' . $fileName, 'data');
            $this->queryCsv($fileName, $resultNumber, $log, $task, $query);
        } else {
            $fileName = $task->file_name . '.xlsx';
            $file     = Excel::store(new ShellProductExport($query), '/export/' . $fileName, 'data');
        }

        return ['file' => $file, 'file_name' => $fileName];
    }

    public function channelExternalCode($task, $log): array
    {
        $queryCriteria     = $task->query_criteria;
        $queryChannelExternalCode = ChannelExternalCode::query();
        $query             = $this->addConditions($queryChannelExternalCode, $queryCriteria['filter'] ?? []);
        $resultNumber      = $query->count();

        if ($resultNumber <= 0) {
            return ['file' => false];
        }

        if ($resultNumber >= 20000) {
            // 创建空文件
            $fileName = $task->file_name . '.csv';
            $file     = Excel::store(new ChannelExternalCodeExportCsv([]), '/export/' . $fileName, 'data');
            $this->queryCsv($fileName, $resultNumber, $log, $task, $query);
        } else {
            $fileName = $task->file_name . '.xlsx';
            $file     = Excel::store(new ChannelExternalCodeExport($query), '/export/' . $fileName, 'data');
        }

        return ['file' => $file, 'file_name' => $fileName];
    }
}
