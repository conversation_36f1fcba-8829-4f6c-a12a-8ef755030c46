<?php

namespace App\Console\Commands;

use App\Classes\Pay\Pay;
use Illuminate\Console\Command;
use App\Classes\Supports\Logger;
use App\Jobs\SendSlsLog;
use App\Models\Channel;
use App\Models\ChargebackCase;
use App\Models\Order;
use App\Models\OrderRelation;
use App\Services\ChargebackCaseService;
use App\Services\Shield\EmbracyShieldService;
use App\Services\TransactionService;

class ChargebackCaseQuery extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'task:chargebackCaseQuery';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Chargeback Case Query';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    // 原因码映射
    protected $reasonCodes = [
        'gee' => [
            'V' => [
                'FRAUD'                     => '10.4',
                'INCORRECT_AMOUNT'          => '12.5',
                'DUPLICATE_PROCESSING'      => '12.6',
                'SERVICES_NOT_RECEIVED'     => '13.1',
                'CANCELLED_RECURRING'       => '13.2',
                'DEFECTIVE_DESCRIBED_ERROR' => '13.3',
                'COUNTERFEIT_MERCHANDISE'   => '13.4',
                'REFUND_NOT_RECEIVED'       => '13.6',
                'CANCELLED_TRANSACTION'     => '13.7',
                'DISPUTE'                   => 'AA',
            ],
            'M' => [
                'INCORRECT_AMOUNT'          => '4831',
                'DUPLICATE_PROCESSING'      => '4834',
                'FRAUD'                     => '4837',
                'CANCELLED_RECURRING'       => '4841',
                'DEFECTIVE_DESCRIBED_ERROR' => '4853',
                'SERVICES_NOT_RECEIVED'     => '4855',
                'REFUND_NOT_RECEIVED'       => '4860',
                'CANCELLED_TRANSACTION'     => '4863',
                'DISPUTE'                   => 'AB',
            ],
            'A' => [
                'FRAUD'                     => '4540',
                'SERVICES_NOT_RECEIVED'     => '4554',
                'DEFECTIVE_DESCRIBED_ERROR' => '4553',
                'REFUND_NOT_RECEIVED '      => '4513',
            ],
            'D' => [
                'DUPLICATE_PROCESSING'  => 'B25',
                'SERVICES_NOT_RECEIVED' => 'D62',
                'REFUND_NOT_RECEIVED'   => 'D66',
                'FRAUD'                 => 'D70',
                'INCORRECT_AMOUNT'      => 'D61',
            ],
        ],
    ];

    // 欺诈原因码
    protected $fraud = [
        '10.4', '4841', '4540', 'D70'
    ];

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // 在命令行打印一行信息
	    $this->info('渠道拒付交易预警查询开始');

	    // 初始化日志
	    $log    = new Logger();
	    $config = ['file' => storage_path('logs/consoleTask.log')];
	    $log->setConfig($config);

        $channelSuppliers = ['gee'];

        // 获取需要查询的渠道账单
        $channels = Channel::query()->with(['channelSupplier:id,file_name'])->where('status', Channel::STATUS_ENABLE)->whereHas('channelSupplier', function ($query) use ($channelSuppliers) {
            $query->whereIn('file_name', $channelSuppliers);
        })->get();

        // 日志记录
        $outPut = [];

        $chargebackCaseService = new ChargebackCaseService();

        // 遍历渠道账单
        foreach ($channels as $channel) {
            if (empty($channel->config) || !is_array($channel->config)) {
                continue;
            }

            $data = [
                'order_id' => Order::findAvailableNo(),
            ];
            // 请求网关
            $supplierName = strtolower($channel->channelSupplier->file_name);
            $config       = get_config($channel->config, $supplierName);

            // 关闭预警查询
            if (isset($config['cloture_case_query'])) {
                continue;
            }

            $channelName  = Pay::$supplierName($config);
            $resultCase   = $channelName->retrieveChargebackCase($data);
            $outStr       = "[拒付交易预警查询请求情况] : " . json_encode($resultCase, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
            $this->info($outStr);

            if (!empty($resultCase['error'])) {
                // 当前有错误，记录日志
                $log->info("拒付交易预警查询请求异常输出", [$resultCase['error']]);
                dispatch(new SendSlsLog(
                    ['message' => '拒付交易预警查询请求异常输出'],
                    [$resultCase['error']],
                    'info',
                    'task'
                ));
            }

            if (empty($resultCase['data'])) {
                // 没有数据处理，跳过循环
                $msgStr = sprintf('%s账单没有数据', $channel->channel);
				$this->info($msgStr);
				$outPut[] = $msgStr;
                continue;
            }

            $normal  = 0;
            $anomaly = 0;
            foreach ($resultCase['data'] as $val) {
                $order = $resultCase['orderList'][$val['order_id']] ?? [];

                //预警数据初始化
                $addData = [
                    'case_id'             => $val['case_id'],
                    'order_id'            => '000000000000000000',
                    'order_number'        => '000000000000000000',
                    'merchant_id'         => 0,
                    'merchant_name'       => '无',
                    'business_id'         => 0,
                    'channel_id'          => 0,
                    'channel'             => '无',
                    'is_meddle'           => 0,
                    'reply_status'        => ChargebackCase::REPLY_STATUS_NOT_NOTICE,
                    'business_history_id' => 0,
                    'result'              => 0,
                    'dishonour_warn_info' => $val['dishonour_warn_info'],
                    'remarks'             => '',
                    'by_alert_id'         => '1',
                    'chargeback_from'     => $val['chargeback_from'],
                    'alert_from'          => $val['alert_from'],
                    'by_alert'            => 'admin',
                    'date_complete'       => '1970-01-01',
                    'date_settle'         => '1970-01-01',
                ];
                if (empty($order) || $order->relation->is_chargeback_warn == OrderRelation::IS_CHARGEBACK_WARN_YES) {
                    // 匹配不到订单或订单重复预警
                    $addDataUp = [
                        'is_normal' => ChargebackCase::IS_NORMAL_FALSE,
                        'remarks'   => empty($order) ? '匹配不到订单' : '订单重复预警',
                    ];
                    // 发送异常拒付预警告警
                    $addData  = array_merge($addData, $addDataUp);
                    $warnData = [
                        'alert_from'      => $addData['alert_from'],
                        'chargeback_from' => $addData['chargeback_from'],
                        'case_id'         => $addData['case_id'],
                    ];

                    $embracyShieldService = new EmbracyShieldService();
                    $embracyShieldService->sendWarning($warnData, ChargebackCase::WARNING_TYPE_MATCHING_FAILED);

                    $anomaly++;
                } else {
                    $channelName = strtolower($val['channel']);
                    $reasonCode  = $this->reasonCodes[$channelName][$order->card->cc_type][$val['reason_code']] ?? 'Alert';
                    $addDataUp   = [
                        'business_history_id' => $order->settlements->business_history_id,
                        'order_id'            => $order->order_id,
                        'order_number'        => $order->order_number,
                        'merchant_id'         => $order->merchant_id,
                        'merchant_name'       => $order->merchant_name,
                        'business_id'         => $order->business_id,
                        'channel_id'          => $order->channel_id,
                        'channel'             => $order->channel,
                        'alert_type'          => in_array($reasonCode, $this->fraud) ? ChargebackCase::CONFIRMED_FRAUD : ChargebackCase::CUSTOMER_DISPUTE,
                        'reason_code'         => $reasonCode,
                        'warn_currency'       => $order->paymentOrder->currency,
                        'warn_amount'         => $order->paymentOrder->amount,
                        'date_complete'       => now(),
                        'date_settle'         => get_settle_date(),
                        'mod_times'           => 0,
                        'abnormal_type'       => ChargebackCase::ABNORMAL_TYPE_NORMAL,
                    ];

                    $flag            = 0;  // 0:无操作 1:创建退款成功数据 2:请求退款
                    $availableAmount = 0.00;

                    switch ($val['chargeback_from']) {
                        case ChargebackCase::FROM_RDR:
                            // RDR
                            if ($order->relation->is_refund == OrderRelation::IS_REFUND_FULL) {
                                $addData['result']  = 1;
                                $addData['remarks'] = '需要人工确认是否有退款中';
                            } else { // 创建退款数据
                                $addData['result'] = 1;

                                // 获取可退款最高金额
                                $availableAmount = TransactionService::getAvailableRefundAmount($order);

                                // 创建退款数据
                                if ($availableAmount > 0.00) {
                                    $flag = 1;
                                } else {
                                    $addData['remarks'] = '可退款金额为0, 请核对！';
                                }
                            }
                            break;
                        default:
                            // CDRN Ethoca 判断原始交易是否退款、拒付
                            if ($order->relation->is_refund == OrderRelation::IS_REFUND_FULL || $order->relation->is_chargeback) {
                                $addData['result']  = 1;
                                $addData['remarks'] = '已退款或已拒付';
                            } elseif ($order->is_3d && !$addDataUp['alert_type'] && $val['chargeback_from'] == ChargebackCase::FROM_CDRN) {
                                $addData['result']  = 0;
                                $addData['remarks'] = '该订单属于3d交易属于欺诈类预警需要跟CDRN申诉';
                            } else {
                                $addData['result'] = 1;

                                // 获取可退款最高金额
                                $availableAmount = TransactionService::getAvailableRefundAmount($order);

                                // 添加退款任务
                                if ($availableAmount > 0.00) {
                                    $flag = 2;
                                } else {
                                    $addData['remarks'] = '可退款金额为0, 请核对！';
                                }
                            }
                            break;
                    }

                    if ($order->type == Order::TYPES_AUTH) {
                        $flag               = 0;
                        $addData['remarks'] = 'Auth交易预警';
                    }

                    $result = '';

                    // 保存拒付预警数据
                    $addData        = array_merge($addData, $addDataUp);
                    $chargebackCase = ChargebackCase::create($addData);
                    if ($chargebackCase) {
                        $normal++;

                        // 更新关联表拒付预警字段
                        $order->relation->update(['is_chargeback_warn' => 1]);

                        if ($flag == 1) { // 创建退款成功数据
                            $result = $chargebackCaseService->CerateRefundSuccessData($order, $availableAmount);
                        } elseif ($flag == 2) { // 请求退款
                            $result = $chargebackCaseService->RequestRefund($order, $availableAmount);
                        }

                        // 需要自动处理退款并且失败
                        if ($flag == 2 && $result !== '退款成功'){
                            $chargebackCaseService->AutoFail($chargebackCase);
                        }

                        if ($result) {
                            ChargebackCase::where('case_id', $val['case_id'])->update(['remarks' => $result]);
                        }
                    }
                }

            }

            $msgStr = sprintf('%s账单统计,拒付预警总计:%s条,添加常规预警:%s条,异常预警:%s条', $channel->channel, $normal + $anomaly, $normal, $anomaly);
			$this->info($msgStr);
			$outPut[] = $msgStr;
        }

        $log->info('渠道拒付交易预警查询输出', [$outPut]);
		dispatch(new SendSlsLog(
			['message' => '渠道拒付交易预警查询输出'],
			[$outPut],
			'info',
			'task'
		));

        $this->info('渠道拒付交易预警查询结束');
    }
}
