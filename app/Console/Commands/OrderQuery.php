<?php

namespace App\Console\Commands;

use App\Classes\Pay\Pay;
use App\Classes\Supports\Logger;
use App\Events\Notice;
use App\Events\RiskWarn;
use App\Http\Controllers\Traits\PaymentController;
use App\Jobs\SendSlsLog;
use App\Models\Channel;
use App\Models\Order;
use App\Models\OrderHistory;
use Illuminate\Console\Command;

class OrderQuery extends Command
{
	use PaymentController;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'task:orderQuery';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Order Query';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
	    // 在命令行打印一行信息
	    $this->info("订单检索开始");

	    // 初始化日志
	    $log    = new Logger();
	    $config = ['file' => storage_path('logs/consoleTask.log')];
	    $log->setConfig($config);

	    // 获取检索的订单数据
	    $orderModel    = new Order();
	    $tempOrderList = Order::with('paymentOrder')->whereIn('code', [get_system_code('095'), get_system_code('200'), get_system_code('210'), get_system_code('139')])->where('created_at', '<=', date('Y-m-d H:i:s', strtotime('-30 minutes')))->get();

	    $tempRetrieveList = [];
	    $orderHistoryData = [];
	    $orderIds         = [];

	    // 超时、待检索订单划分
	    if (!empty($tempOrderList)) {
	    	foreach ($tempOrderList as $value) {
				if ($value->expired_at >= now()) {
					$tempRetrieveList[] = $value;
				} else {
					// 获取超时交易订单数据
					$orderHistoryData[] = [
						'order_id'   => $value->order_id,
						'channel_id' => $value->channel_id,
						'channel'    => $value->channel,
						'type'       => $value->type,
						'status'     => $value->status,
						'code'       => $value->code,
						'result'     => $value->result,
						'remark'     => $value->remark,
					];

					$orderIds[] = $value->order_id;
				}
		    }
	    }

	    // 计数
	    $res    = array(
		    'total'         => count($tempOrderList),
		    'temp_retrieve' => count($tempRetrieveList),
		    'order_history' => count($orderHistoryData),
		    'success'       => 0
	    );
	    $outPut = [];

	    // 待检索
		if (!empty($tempRetrieveList)) {
			// 获取支付渠道名称
			$channelArr  = array_column($tempRetrieveList, 'channel_id');
			$channelData = Channel::with('channelSupplier')->whereIn('id', $channelArr)->get();

			$channelSupplierData = [];
			$tempChannelData     = [];
			$retrieveList        = [];

			foreach ($channelData as $value) {
				$channelSupplierData[$value->id] = strtolower($value->channelSupplier->file_name);
				$tempChannelData[$value->id]     = $value->config;
			}

			// 组装检索数据
			foreach ($tempRetrieveList as $tempRetrieve) {
				$retrieveList[$channelSupplierData[$tempRetrieve->channel_id]][] = $tempRetrieve;
			}

			// 加载渠道数据
			foreach ($retrieveList as $supplierName => $orderData) {
				foreach ($orderData as $order) {
					$config      = $this->_getConfig($tempChannelData[$order->channel_id], $supplierName);
					$channelName = Pay::$supplierName($config);
					$result = $channelName->retrieve($order->toArray());

					$outStr = "[订单检索API请求情况] : " . json_encode($result);
					$this->info($outStr);

					if (!empty($result)) {
						if ($order->code != $result['order']['code']) {
							$returnData = $this->updateOrder($order, $result);
							$res['success']++;

							//异步通知
							event(new Notice($order, $returnData));
						}
						// 执行告警事件
						event(new RiskWarn($order));
					}
				}

				$msgStr = sprintf('%s渠道统计,检索总计:%s条,检索成功:%s条,检索失败:%s条', $supplierName, count($orderData), $res['success'], count($orderData) - $res['success']);
				$this->info($msgStr);
				$outPut[] = $msgStr;
			}
		}

	    // 超时处理
	    // 添加 order_history 并更新消费或预授权交易状态
	    if (!empty($orderHistoryData) && !empty($orderIds)) {
		    // 添加交易历史
		    $this->info('处理过期交易开始');
		    $orderHistoryModel = new OrderHistory();
		    $orderHistoryModel->adds($orderHistoryData);
		    unset($orderHistoryData);

		    // 修改订单交易状态
		    $changeArr = array(
			    'code'    => get_system_code('302'),
			    'status'  => Order::STATUS_EXPIRED,
			    'remark' => '持卡人放弃交易',
			    'result' => 'Abandoned by Cardholder',
		    );

		    // 3020 订单更新
		    if (!empty($orderIds)) {
			    $orderModel->whereIn('order_id', $orderIds)->update($changeArr);
		    }

		    $this->info('处理过期交易结束');
	    }

	    $outPut[] = sprintf("本次操作总计:%s条,向渠道检索订单总计:%s条,订单检索成功:%s条,订单检索失败:%s条,处理过期交易:%s条", $res['total'], $res['temp_retrieve'], $res['success'], $res['temp_retrieve'] - $res['success'], $res['order_history']);
	    $log->info("异步通知执行输出", [$outPut]);
		dispatch(new SendSlsLog(
			['message' => '异步通知执行输出'],
			[$outPut],
			'info',
			'task'
		));
	    $this->info("订单检索结束");
    }

	protected function _getConfig($configData, $channelSupplierName)
	{
		$config = [];

		// 循环组装数据
		foreach ($configData as $value) {
			$config[$value['key']] = $value['value'];
		}

		// 增加日志file
		if (!empty($config)) {
			$config['log'] = ['file' => storage_path('logs/' . $channelSupplierName . '.log')];
		}

		return $config;
	}
}
