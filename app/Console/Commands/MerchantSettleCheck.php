<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Classes\Supports\Logger;
use App\Models\OrderSettlement;
use App\Models\SettleDetail;
use App\Models\Settlement;
use App\Models\SettleAdjustment;
use App\Models\ChargebackCase;
use App\Models\MerchantBusinessHistory;
use App\Models\DirectoryCurrency;
use App\Jobs\SendNotice;
use App\Jobs\SendSlsLog;
use App\Models\ChargebackPenalty;
use App\Models\SettleDetailMerchant;
use App\Models\SettleMerchant;
use Illuminate\Support\Facades\Cache;

class MerchantSettleCheck extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'settle:merchantSettleCheck';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Verification of merchant settlement';

    /**
     * @var
     */
    protected $log;

    /**
     * @var array
     */
    protected $businessHistoriesList = [];

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        // 初始化日志
        $this->log = new Logger();
        $config    = ['file' => storage_path('logs/consoleTask.log')];
        $this->log->setConfig($config);
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        set_time_limit(0);

        // 结算日期
        $date = date('Y-m-d');

        $this->_checkSettleData($date);
    }


    /**
     * 核对结算金额数据
     *
     * @param [type] $date
     * @return void
     */
    private function _checkSettleData($date)
    {
        $this->log->info("商家结算核对开始");
        dispatch(new SendSlsLog(
            ['message' => '商家结算核对开始'],
            [],
            'info',
            'task'
        ));

        // 币种汇率
        $currencyList = Cache::get('MerchantSettle_Currency_List' . date('Ymd')) ?? DirectoryCurrency::all()->pluck('rate', 'code')->toArray();

        //统计结算表金额
        $businessSettlementData = $errorData = [];
        $content                = '[商家结算告警]' . PHP_EOL;
        $orderDetailMap         = [
            OrderSettlement::TYPE_SALE                  => SettleDetail::AMOUNT_TYPE_00,
            OrderSettlement::TYPE_CAPTURE               => SettleDetail::AMOUNT_TYPE_20,
            OrderSettlement::TYPE_REFUND                => SettleDetail::AMOUNT_TYPE_30,
            OrderSettlement::TYPE_CHARGEBACK            => SettleDetail::AMOUNT_TYPE_40,
            OrderSettlement::TYPE_CHARGEBACK_REVERSAL   => SettleDetail::AMOUNT_TYPE_42,
        ];
        //结算表
        $orderSettlements = OrderSettlement::query()->selectRaw('business_id, type, settle_currency, sum(settle_amount) settle_amount')
            ->where(['settle_at' => $date, 'is_settle' => 1, 'status' => 1])
            ->where('settle_amount', '<>', 0)
            ->where('type', '<>', OrderSettlement::TYPE_AUTH)
            ->groupBy('business_id', 'type', 'settle_currency')->havingRaw('settle_amount <> 0')->get()->toArray();

        foreach ($orderSettlements as  $settlement) {
            if (in_array($settlement['type'], [OrderSettlement::TYPE_REFUND, OrderSettlement::TYPE_CHARGEBACK])) {
                $settlement['settle_amount'] = amount_format((-1) * $settlement['settle_amount']);
            }
            $businessSettlementData[$settlement['business_id']][$orderDetailMap[$settlement['type']]][$settlement['settle_currency']] = $settlement['settle_amount'];
        }
        //结算调整
        $settleAdjustments = SettleAdjustment::query()->selectRaw('business_id, settle_currency, sum(settle_amount) settle_amount')
            ->where(['settle_at' => $date, 'type' => 0])->where('settle_amount', '!=', 0)->groupBy('business_id', 'settle_currency')->get()->toArray();

        foreach ($settleAdjustments as $adjustment) {
            $businessSettlementData[$adjustment['business_id']][SettleDetail::AMOUNT_TYPE_90][$adjustment['settle_currency']] = $adjustment['settle_amount'];
        }
        //拒付预警
        ChargebackCase::query()->select('case_id', 'business_id', 'business_history_id')
            ->where(['date_settle' => $date, 'is_normal' => ChargebackCase::IS_NORMAL_TRUE])->chunkById(1000, function ($chargebackCases) use (&$businessSettlementData, $currencyList) {
                $this->fillBusinessHistoriesList($chargebackCases->pluck('business_history_id', 'business_history_id')->toArray());

                foreach ($chargebackCases as $case) {
                    $business       = $this->businessHistoriesList[$case['business_history_id']];
                    $chargeCurrency = 'USD';

                    if (!in_array($chargeCurrency, explode(',', $business['settle_currencies']))) {
                        $chargeCurrency = $business['settle_default_currency'];
                    }

                    $chargeRate      = amount_format($currencyList[$chargeCurrency], 4);
                    $preDishonourFee = amount_format((-1) * $business['charge_cc']['pre_dishonour_fee'] * $chargeRate);
                    if ($preDishonourFee != 0) {
                        if (isset($businessSettlementData[$case->business_id][SettleDetail::AMOUNT_TYPE_43])) {
                            $businessSettlementData[$case->business_id][SettleDetail::AMOUNT_TYPE_43][$chargeCurrency] += $preDishonourFee;
                        } else {
                            $businessSettlementData[$case->business_id][SettleDetail::AMOUNT_TYPE_43][$chargeCurrency] = $preDishonourFee;
                        }
                    }
                }
            });

        //拒付罚金
        ChargebackPenalty::query()->select('id', 'business_id', 'business_history_id', 'penalty_amount', 'type')
            ->where('settle_at', $date)->chunkById(1000, function ($chargebackPenalty) use (&$businessSettlementData, $currencyList) {
                $this->fillBusinessHistoriesList($chargebackPenalty->pluck('business_history_id', 'business_history_id')->toArray());

                foreach ($chargebackPenalty as $penalty) {
                    $business       = $this->businessHistoriesList[$penalty['business_history_id']];
                    $chargeCurrency = 'USD';

                    if (!in_array($chargeCurrency, explode(',', $business['settle_currencies']))) {
                        $chargeCurrency = $business['settle_default_currency'];
                    }

                    $chargeRate      = amount_format($currencyList[$chargeCurrency], 4);
                    $preDishonourFee = amount_format((-1) * $penalty['penalty_amount'] * $chargeRate);
                    switch ($penalty['type']) {
                        case ChargebackPenalty::TYPE_FIXED_PENALTY:
                        case ChargebackPenalty::TYPE_PROPORTIONAL_PENALTY:
                            $amountType = SettleDetail::AMOUNT_TYPE_44;
                            break;

                        default:
                            $amountType = SettleDetail::AMOUNT_TYPE_45;
                            break;
                    }

                    if ($preDishonourFee != 0) {
                        if (isset($businessSettlementData[$penalty->business_id][$amountType])) {
                            $businessSettlementData[$penalty->business_id][$amountType][$chargeCurrency] += $preDishonourFee;
                        } else {
                            $businessSettlementData[$penalty->business_id][$amountType][$chargeCurrency] = $preDishonourFee;
                        }
                    }
                }
            });

        if (count($businessSettlementData) === 0) {
            $this->log->info("商家结算核对结束,暂无需要结算的数据");
            dispatch(new SendSlsLog(
                ['message' => '商家结算核对结束,暂无需要结算的数据'],
                [],
                'info',
                'task'
            ));
            return;
        }

        //详情表
        $settleDetails = SettleDetail::query()->selectRaw('business_id, amount_type, settle_currency, sum(settle_amount) settle_amount')
            ->where('settle_at', $date)
            ->where('settle_amount', '<>', 0)
            ->groupBy('business_id', 'amount_type', 'settle_currency')->havingRaw('settle_amount <> 0')->get()->toArray();

        $partCheck = [
            SettleDetail::AMOUNT_TYPE_00, SettleDetail::AMOUNT_TYPE_20, SettleDetail::AMOUNT_TYPE_30, SettleDetail::AMOUNT_TYPE_40,
            SettleDetail::AMOUNT_TYPE_42, SettleDetail::AMOUNT_TYPE_43, SettleDetail::AMOUNT_TYPE_44, SettleDetail::AMOUNT_TYPE_45,
            SettleDetail::AMOUNT_TYPE_90
        ];
        $settleBusinessMap = $settleMerchantDetailMap = [];
        //校验生成详情准确性
        foreach ($settleDetails as $detail) {
            if (!in_array(
                $detail['amount_type'],
                [SettleDetail::AMOUNT_TYPE_50, SettleDetail::AMOUNT_TYPE_51, SettleDetail::AMOUNT_TYPE_52]
            )) {
                //BID汇总数据
                $settleBusinessMap[$detail['business_id']][$detail['settle_currency']][$detail['amount_type']] = $detail['settle_amount'];
            } else {
                $merchantId = substr($detail['business_id'], 0, -3);
                //汇总到MID详情的数据
                if (!isset($settleMerchantDetailMap[$merchantId][$detail['settle_currency']][$detail['amount_type']])) {
                    $settleMerchantDetailMap[$merchantId][$detail['settle_currency']][$detail['amount_type']] = '0.00';
                }
                $settleMerchantDetailMap[$merchantId][$detail['settle_currency']][$detail['amount_type']] += $detail['settle_amount'];
            }

            if (!in_array($detail['amount_type'], $partCheck)) {
                continue;
            }

            if (isset($businessSettlementData[$detail['business_id']][$detail['amount_type']][$detail['settle_currency']])) {
                if ($businessSettlementData[$detail['business_id']][$detail['amount_type']][$detail['settle_currency']] != $detail['settle_amount']) {
                    $errorData[$detail['business_id']] = $detail['business_id'] . ':' . SettleDetail::$amountTypeMap[$detail['amount_type']] .
                        '币种:' . $detail['settle_currency'] . '结算金额不正确，应当结算：' . $businessSettlementData[$detail['business_id']][$detail['amount_type']][$detail['settle_currency']] .
                        ';实际结算：' . $detail['settle_amount'];
                    $this->log->info($errorData[$detail['business_id']]);
                    dispatch(new SendSlsLog(
                        ['message' => $errorData[$detail['business_id']]],
                        [],
                        'info',
                        'task'
                    ));
                }
            } else {
                $errorData[$detail['business_id']] = $detail['business_id']  . ':' . SettleDetail::$amountTypeMap[$detail['amount_type']] .
                    '结算金额不正确，应当结算：0;实际结算：' . $detail['settle_amount'];
                $this->log->info($errorData[$detail['business_id']]);
                dispatch(new SendSlsLog(
                    ['message' => $errorData[$detail['business_id']]],
                    [],
                    'info',
                    'task'
                ));
            }
        }

        if (count($errorData)) {
            $content .= '结算明细错误！' . PHP_EOL;
        }

        //BID汇总准确性
        $settlements               = Settlement::where('settle_at', $date)->get();
        $fields                    = Settlement::$settleAmountFieldMap;
        $settleBusinessContrastMap = [];

        foreach ($settlements as $settlement) {
            foreach ($fields as $type => $field) {
                if ($settlement->$field != 0) {
                    $settleBusinessContrastMap[$settlement['business_id']][$settlement['settle_currency']][$type] = $settlement->$field;
                }
            }
        }
        //格式化数据
        $this->formatContrastData($settleBusinessMap);
        $this->formatContrastData($settleBusinessContrastMap);
        foreach ($settleBusinessMap as $bid => $contrast) {
            if (!isset($settleBusinessContrastMap[$bid])) {
                $content .= 'BID:' . $bid . '汇总数据出错！' . PHP_EOL;
                continue;
            }

            if (serialize($contrast) != serialize($settleBusinessContrastMap[$bid])) {
                $content .= 'BID:' . $bid . '汇总数据出错！' . PHP_EOL;
            }
        }
        //BID汇总校验
        if (serialize($settleBusinessMap) != serialize($settleBusinessContrastMap)) {
            $this->log->info('应该汇总BID数据：', $settleBusinessMap);
            dispatch(new SendSlsLog(
                ['message' => '应该汇总BID数据：'],
                $settleBusinessMap,
                'info',
                'task'
            ));

            $this->log->info('实际汇总BID数据：', $settleBusinessContrastMap);
            dispatch(new SendSlsLog(
                ['message' => '实际汇总BID数据：'],
                $settleBusinessContrastMap,
                'info',
                'task'
            ));
        }

        //商家MID结算详情
        $settleDetailMerchants = SettleDetailMerchant::query()->selectRaw(
            'merchant_id, settle_currency, amount_type, sum(settle_amount) settle_amount, sum(in_deposit_amount) in_deposit_amount, sum(out_deposit_amount) out_deposit_amount'
        )->where('settle_at', $date)->where(function ($query) {
            $query->orWhere('settle_amount', '<>', 0);
            $query->orWhere('in_deposit_amount', '<>', 0);
            $query->orWhere('out_deposit_amount', '<>', 0);
        })->groupBy('merchant_id', 'settle_currency', 'amount_type')->get();

        $settleMerchantContrastDetailMap = $settleMerchantContrasMap = [];
        $merchantFields = [
            SettleDetail::AMOUNT_TYPE_50 => 'settle_amount',
            SettleDetail::AMOUNT_TYPE_51 => 'in_deposit_amount',
            SettleDetail::AMOUNT_TYPE_52 => 'out_deposit_amount',
        ];

        foreach ($settleDetailMerchants as $settlementMerchant) {
            foreach ($merchantFields as $type => $merchantField) {
                if ($settlementMerchant->$merchantField != 0) {
                    if ($settlementMerchant['amount_type'] == SettleDetailMerchant::AMOUNT_TYPE_0) {
                        //BID汇总详情
                        $settleMerchantContrastDetailMap[$settlementMerchant['merchant_id']][$settlementMerchant['settle_currency']][$type] = -1 * $settlementMerchant->$merchantField;
                    }

                    if (!isset($settleMerchantContrasMap[$settlementMerchant['merchant_id']][$settlementMerchant['settle_currency']][$type])) {
                        $settleMerchantContrasMap[$settlementMerchant['merchant_id']][$settlementMerchant['settle_currency']][$type] = 0;
                    }
                    //MID所有详情汇总
                    $settleMerchantContrasMap[$settlementMerchant['merchant_id']][$settlementMerchant['settle_currency']][$type] += $settlementMerchant->$merchantField;
                }
            }
        }
        //格式化数据
        $this->formatContrastData($settleMerchantDetailMap);
        $this->formatContrastData($settleMerchantContrastDetailMap);
        foreach ($settleMerchantDetailMap as $bid => $contrast) {
            if (!isset($settleMerchantContrastDetailMap[$bid])) {
                $content .= 'BID:' . $bid . '汇总商户数据出错！' . PHP_EOL;
                continue;
            }

            if (serialize($contrast) != serialize($settleMerchantContrastDetailMap[$bid])) {
                $content .= 'BID:' . $bid . '汇总商户数据出错！' . PHP_EOL;
            }
        }
        //BID汇总MID详情校验
        if (serialize($settleMerchantDetailMap) != serialize($settleMerchantContrastDetailMap)) {
            $this->log->info('BID应该汇总商户详情数据：', $settleMerchantDetailMap);
            dispatch(new SendSlsLog(
                ['message' => 'BID应该汇总商户详情数据：'],
                $settleMerchantDetailMap,
                'info',
                'task'
            ));

            $this->log->info('实际BID汇总商户详情数据：', $settleMerchantContrastDetailMap);
            dispatch(new SendSlsLog(
                ['message' => '实际BID汇总商户详情数据：'],
                $settleMerchantContrastDetailMap,
                'info',
                'task'
            ));
        }

        //商家MID结算汇总
        $settleMerchantMap = [];
        $settleMerchants   = SettleMerchant::query()->selectRaw(
            'merchant_id, settle_currency, sum(settle_amount) settle_amount, sum(in_deposit_amount) in_deposit_amount, sum(out_deposit_amount) out_deposit_amount'
        )->where('settle_at', $date)->where(function ($query) {
            $query->orWhere('settle_amount', '<>', 0);
            $query->orWhere('in_deposit_amount', '<>', 0);
            $query->orWhere('out_deposit_amount', '<>', 0);
        })->groupBy('merchant_id', 'settle_currency')->get();

        foreach ($settleMerchants as $settleMerchant) {
            foreach ($merchantFields as $type => $merchantField) {
                if ($settleMerchant->$merchantField != 0) {
                    if (!isset($settleMerchantMap[$settleMerchant['merchant_id']][$settleMerchant['settle_currency']][$type])) {
                        $settleMerchantMap[$settleMerchant['merchant_id']][$settleMerchant['settle_currency']][$type] = 0;
                    }
                    //MID所有详情汇总
                    $settleMerchantMap[$settleMerchant['merchant_id']][$settleMerchant['settle_currency']][$type] += $settleMerchant->$merchantField;
                }
            }
        }
        //格式化数据
        $this->formatContrastData($settleMerchantMap);
        $this->formatContrastData($settleMerchantContrasMap);
        foreach ($settleMerchantMap as $mid => $contrast) {
            if (!isset($settleMerchantContrasMap[$mid])) {
                $content .= 'MID:' . $mid . '汇总数据出错！' . PHP_EOL;
                continue;
            }

            if (serialize($contrast) != serialize($settleMerchantContrasMap[$mid])) {
                $content .= 'MID:' . $mid . '汇总数据出错！' . PHP_EOL;
            }
        }
        //MID详情汇总MID校验
        if (serialize($settleMerchantContrasMap) != serialize($settleMerchantMap)) {
            $this->log->info('商户详情应该汇总商户数据：', $settleMerchantMap);
            dispatch(new SendSlsLog(
                ['message' => '商户详情应该汇总商户数据：'],
                $settleMerchantMap,
                'info',
                'task'
            ));

            $this->log->info('实际商户详情汇总商户数据：', $settleMerchantContrasMap);
            dispatch(new SendSlsLog(
                ['message' => '实际商户详情汇总商户数据：'],
                $settleMerchantContrasMap,
                'info',
                'task'
            ));
        }

        if (mb_strlen($content) > 10) {
            $data = [
                'level'             => 1,
                'contents'          => $content . PHP_EOL . '来源:' . config('app.url'),
                'notice_user_roles' => 'Administrator,Settlement Supervisor',
                'type'              => 3,
                'status'            => 2,
            ];
            dispatch(new SendNotice($data, 5));
        }

        $this->log->info("商家结算核对结束");
        dispatch(new SendSlsLog(
            ['message' => '商家结算核对结束'],
            [],
            'info',
            'task'
        ));
    }

    /**
     * 格式化数据
     *
     * @param array $dataMaps
     * @return void
     */
    private function formatContrastData(array &$dataMaps): void
    {
        ksort($dataMaps, SORT_STRING);
        foreach ($dataMaps as &$dataMap) {
            ksort($dataMap, SORT_STRING);
            foreach ($dataMap as &$data) {
                ksort($data, SORT_STRING);
                foreach ($data as &$amount) {
                    $amount = amount_format($amount);
                }
            }
        }
    }

    /**
     * 填充历史条款和手续费数据
     * @param array $HistoriesIds
     * @return void
     */
    public function fillBusinessHistoriesList(array $historiesIds = []): void
    {
        $selectIds = array_diff_key($historiesIds, $this->businessHistoriesList);
        if (count($selectIds)) {
            MerchantBusinessHistory::whereIn('id', array_keys($selectIds))->get()->each(function ($item) {
                $item = $item->toArray();
                $this->businessHistoriesList[$item['id']] = $item;
            });
        }
    }
}
