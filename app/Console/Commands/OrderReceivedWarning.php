<?php

namespace App\Console\Commands;

use App\Classes\Supports\Logger;
use App\Jobs\SendNotice;
use App\Jobs\SendSlsLog;
use App\Models\Order;
use App\Models\Refund;
use Illuminate\Console\Command;

class OrderReceivedWarning extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'task:orderReceivedWarning';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Order Received Warning';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
	    // 初始化日志
	    $log    = new Logger();
	    $config = ['file' => storage_path('logs/consoleTask.log')];
	    $log->setConfig($config);


        try {
            $startTime = date('Y-m-d H:i:s', strtotime('-1 day'));
            $endTime   = date('Y-m-d H:i:s', strtotime('-1 hour'));

            // 获取告警的交易订单总数
            $orderCount = Order::where('status', Order::STATUS_RECEIVED)->whereBetween('completed_at',  [$startTime, $endTime])->count();

            if (!empty($orderCount)) {
                // 组装通知消息
                $content = sprintf(
                    "[交易Received告警]\n获取交易时间：%s-%s\n订单状态: %s\n订单笔数: %s\n来源: %s",
                    $startTime,
                    $endTime,
                    Order::$statusMap[Order::STATUS_RECEIVED],
                    $orderCount,
                    config('app.url')
                );

                // 组装通知数据
                $data = [
                    'level'             => 1,
                    'contents'          => $content,
                    'notice_user_roles' => 'Administrator',
                    'type'              => 3,
                    'status'            => 2,
                ];

                dispatch(new SendNotice($data, 5));
            }
            
            // 获取告警的退款订单总数
            $refundCount = Refund::where('status', Refund::STATUS_RECEIVED)->whereBetween('completed_at',  [$startTime, $endTime])->count();

            if (!empty($refundCount)) {
                // 组装通知消息
                $content = sprintf(
                    "[退款Received告警]\n获取退款时间：%s-%s\n订单状态: %s\n订单笔数: %s\n来源: %s",
                    $startTime,
                    $endTime,
                    Refund::$statusMap[Refund::STATUS_RECEIVED],
                    $refundCount,
                    config('app.url')
                );

                // 组装通知数据
                $data = [
                    'level'             => 1,
                    'contents'          => $content,
                    'notice_user_roles' => 'Administrator',
                    'type'              => 3,
                    'status'            => 2,
                ];

                dispatch(new SendNotice($data, 5));
            }

        } catch (\Throwable $th) {
            $log->warning('交易和退款订单 received 状态告警查询失败，失败原因：' . $th->getMessage());
            dispatch(new SendSlsLog(
                ['message' => '交易和退款订单 received 状态告警查询失败，失败原因：' . $th->getMessage()],
                [],
                'warning',
                'task'
            ));
        }
    }
}
