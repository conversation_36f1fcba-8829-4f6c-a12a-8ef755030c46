<?php

namespace App\Console\Commands;

use App\Classes\Supports\Logger;
use App\Jobs\SendSlsLog;
use App\Models\OrderSettlement;
use Illuminate\Console\Command;

class FailedOrderAutoBlend extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'settle:failedOrderAutoBlend';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        set_time_limit(0);

        // 初始化日志
        $log    = new Logger();
        $config = ['file' => storage_path('logs/consoleTask.log')];
        $log->setConfig($config);

        $log->info("失败交易自动勾兑执行开始");
        dispatch(new SendSlsLog(
            ['message' => '失败交易自动勾兑执行开始'],
            [],
            'info',
            'task'
        ));

        $date = date('Y-m-d', strtotime('-1 days')); // 完成时间

        // 交易类型
        $orderTypeList = [
            OrderSettlement::TYPE_SALE,
            OrderSettlement::TYPE_AUTH
        ];

        // 获取失败交易
        $successCnt = OrderSettlement::whereIn('type', $orderTypeList)
            ->where('status', OrderSettlement::STATUS_DECLINED)
            ->where('blend_status', '<>', OrderSettlement::BLEND_STATUS_SUCCESS)
            ->whereBetween('completed_at', [$date . ' 00:00:00', $date . ' 23:59:59'])
            ->update([
                'blend_status' => OrderSettlement::BLEND_STATUS_SUCCESS,
                'blend_at'     => date('Y-m-d'),
                'blend_by'     => 'system'
            ]);

        $log->info(sprintf('失败交易自动勾兑成功%s条', $successCnt));
        dispatch(new SendSlsLog(
            ['message' => sprintf('失败交易自动勾兑成功%s条', $successCnt)],
            [],
            'info',
            'task'
        ));
    }
}
