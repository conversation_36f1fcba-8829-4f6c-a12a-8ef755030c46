<?php

namespace App\Console\Commands;

use App\Classes\Supports\Logger;
use App\Jobs\SendSlsLog;
use App\Models\Merchant;
use App\Models\MerchantBusiness;
use App\Models\MerchantReportForm as ModelsMerchantReportForm;
use App\Models\Settlement;
use App\Services\MerchantService;
use Illuminate\Console\Command;

class MerchantReportForm extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'settle:merchantReportFormTask';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * @var
     */
    protected $log;

	/**
	 * 特殊商户语言处理
	 *
	 * @var array
	 */
	protected $specialMerchantLang = [];

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        //初始化日志
        $this->log = new Logger();
        $config    = ['file' => storage_path('logs/consoleTask.log')];
        $this->log->setConfig($config);
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        ini_set('memory_limit', '3072M');
        set_time_limit(3600);

        $this->log->info('商户结算报表任务开始');
        dispatch(new SendSlsLog(
            ['message' => '商户结算报表任务开始'],
            [],
            'info',
            'task'
        ));

        $date = date('Y-m-d'); // 结算日期
        try {
            //生成结算报表数据
            $this->_merchantReportFormAdd($date);

            sleep(1);
            //导出结算报表数据
            $this->_merchantReportFormExport($date);
        } catch (\Throwable $th) {
            $this->log->error('商户结算报表任务异常信息：' . $th->getMessage());
            dispatch(new SendSlsLog(
                ['message' => '商户结算报表任务异常信息：' . $th->getMessage()],
                [],
                'error',
                'task'
            ));
        }

        $this->log->info('商户结算报表任务结束');
        dispatch(new SendSlsLog(
            ['message' => '商户结算报表任务结束'],
            [],
            'info',
            'task'
        ));
    }

    public function _merchantReportFormAdd($date)
    {
        //重复性判断
        if (ModelsMerchantReportForm::where('settle_at', $date)->count()) {
            $this->log->info('生成商户结算报表任务已执行过，请勿重复执行');
            dispatch(new SendSlsLog(
                ['message' => '生成商户结算报表任务已执行过，请勿重复执行'],
                [],
                'info',
                'task'
            ));

            return;
        }

        //查找今天交易结算的BID
        $week         = date('w', strtotime($date));
        $tempMerchant = [];
        $businessData = MerchantBusiness::query()->with('merchant:merchant_id,transfer_account_type')->selectRaw('business_id, merchant_id, merchant_name, settle_day')->where('internal_status', MerchantBusiness::INTERNAL_STATUS_ENABLE)->where('settle_day', 'like', '%"' .$week . '"%')->get()->keyBy('business_id')->toArray();

        if (empty($businessData)) {
            $this->log->info('没有需要结算的BID');
            dispatch(new SendSlsLog(
                ['message' => '没有需要结算的BID'],
                [],
                'info',
                'task'
            ));

            return;
        }

        foreach ($businessData as $business) {
            //获取上次生成结算报表的日期
            $lastSettleAt = ModelsMerchantReportForm::where('query_criteria', 'like', '%"business_id": "' . $business['business_id'] . '"%')
                ->where('merchant_id', $business['merchant_id'])
                ->orderBy('settle_at', 'desc')->value('settle_at');

            if (empty($lastSettleAt)) {
                //反推BID上次结算时间
                $lastSettleAt = $this->_getPreviousSettleAt($date, $business);
            }

            //开始时间
            $lastSettleAt = date('Y-m-d', strtotime("{$lastSettleAt} +1 day"));

            //根据settlements表判断结算周期BID是否有结算数据
            $count = Settlement::where('business_id', $business['business_id'])
                ->where('settle_amount', '<>', 0.00)->whereBetween('settle_at', [$lastSettleAt, $date])->count();
            if ($count) {
                //组装结算报表查询条件
                $key                  = $business['merchant_id'] . '!!' . $business['merchant_name'] . '!!' .$business['merchant']['transfer_account_type'];
                $tempMerchant[$key][] = [
                    'merchant_id' => $business['merchant_id'],
                    'business_id' => $business['business_id'],
                    'str_at'      => $lastSettleAt,
                    'end_at'      => $date
                ];
            }
        }

        if (empty($tempMerchant)) {
            $this->log->info('没有需要生成结算报表的BID');
            dispatch(new SendSlsLog(
                ['message' => '没有需要生成结算报表的BID'],
                [],
                'info',
                'task'
            ));

            return;
        }

        $insertData = [];
        foreach ($tempMerchant as $merchantKey => $value) {
            $merchant = explode('!!', $merchantKey);
            $insertData[] = [
                'merchant_id'    => $merchant[0],
                'merchant_name'  => $merchant[1],
                'query_criteria' => json_encode($value),
                'settle_at'      => $date,
                'status'         => $merchant[2] == Merchant::TRANSFER_ACCOUNT_TYPE_TRADER ? ModelsMerchantReportForm::STATUS_PASS : ModelsMerchantReportForm::STATUS_AUDITS,
                'created_at'     => now(),
                'updated_at'     => now(),
            ];
        }

        if(ModelsMerchantReportForm::query()->insert($insertData)) {
            $count = count($insertData);
            $this->log->info("生成{$count}条结算报表数据");
            dispatch(new SendSlsLog(
                ['message' => "生成{$count}条结算报表数据"],
                [],
                'info',
                'task'
            ));
        }
    }

    public function _merchantReportFormExport($date)
    {
        $merchantReportForms = ModelsMerchantReportForm::query()
            ->where('settle_at', $date)->whereNull('url')
            ->get()->toArray();

        if (!empty($merchantReportForms)) {
            foreach ($merchantReportForms as $value) {
                MerchantService::reportFormExport($value);
            }
        }
    }

    public function _getPreviousSettleAt($date, $business)
    {
        $date = date('Y-m-d', strtotime("{$date} -1 day"));
        while (!in_array(date('w', strtotime($date)), json_decode($business['settle_day']), true)) {
            $date = date('Y-m-d', strtotime("{$date} -1 day"));
        }

        return $date;
    }
}
