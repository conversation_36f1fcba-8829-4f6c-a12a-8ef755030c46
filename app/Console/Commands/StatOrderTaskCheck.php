<?php

namespace App\Console\Commands;

use App\Classes\Supports\Logger;
use App\Jobs\SendSlsLog;
use App\Models\Order;
use App\Models\StatOrder;
use Illuminate\Console\Command;

class StatOrderTaskCheck extends Command
{
    protected $signature = 'task:statOrderTaskCheck';

    protected $description = 'Check Transaction statistics';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        set_time_limit(0);

        // 初始化日志
        $log    = new Logger();
        $config = ['file' => storage_path('logs/consoleTask.log')];
        $log->setConfig($config);

        $log->info('核对成功率及失败构成交易信息统计开始');
        dispatch(new SendSlsLog(
            ['message' => '核对成功率及失败构成交易信息统计开始'],
            [],
            'info',
            'task'
        ));

        $time = strtotime(date('Y-m-d 23:59:59', strtotime('-1 days')));
        $date = date('Y-m-d', $time);
        $hour = date('H', $time);

        $orderType = [Order::TYPES_SALE, Order::TYPES_CAPTURE];
        $orders    = Order::with('settlements:order_id,amount_usd')
            ->where('status', Order::STATUS_APPROVED)
            ->whereIn('type', $orderType)
            ->whereBetween('completed_at', [$date . ' 00:00:00', $date . ' ' . $hour . ':59:59'])
            ->get();

        $total = $orders->count();
        $sum   = $orders->sum('settlements.amount_usd');

        $where = [
            ['date_stat', '=', $date],
            ['date_stat_hour', '=', $hour]
        ];

        $yesterdayStatOrders = StatOrder::where($where)->get();
        $yesterdayTotal      = $yesterdayStatOrders->sum('qty');
        $yesterdaySum        = $yesterdayStatOrders->sum('amount_usd');

        if ($total != $yesterdayTotal || amount_format($sum) != amount_format($yesterdaySum)) {
            $log->error('核对数据异常 正在重新统计！');
            dispatch(new SendSlsLog(
                ['message' => '核对数据异常 正在重新统计！'],
                [],
                'error',
                'task'
            ));

            $statOrderTask = new StatOrderTask();

            $statOrderTask->startOrderTask($time, $log);
        }

        $log->info('核对成功率及失败构成交易信息数据结束！');
        dispatch(new SendSlsLog(
            ['message' => '核对成功率及失败构成交易信息数据结束！'],
            [],
            'info',
            'task'
        ));
    }

}