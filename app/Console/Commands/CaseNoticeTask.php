<?php

namespace App\Console\Commands;

use App\Models\OrderRelation;
use Illuminate\Console\Command;
use App\Classes\Supports\Logger;
use App\Jobs\SendSlsLog;
use App\Models\ChargebackCase;
use App\Models\Refund;
use App\Services\AlertService;
use App\Services\Shield\EmbracyShieldService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class CaseNoticeTask extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'task:caseNotice';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send Case Completion Notice';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // 在命令行打印一行信息
	    $this->info("拒付预警完成通知检索开始");

	    // 初始化日志
	    $log    = new Logger();
	    $config = ['file' => storage_path('logs/consoleTask.log')];
	    $log->setConfig($config);

        // 获取要发送通知的拒付预警数据
        $startTime = date('Y-m-d H:i:s', strtotime('-24 hour'));
        $endTime   = date('Y-m-d H:i:s');

		// 最大通知次数
		$maxTimes = Cache::get('case_notice_auto_reply_max_times');
		if ($maxTimes == null) {
			$maxTimes = 3;
			Cache::put('case_notice_auto_reply_max_times', $maxTimes);
		}

        $chargebackCase = ChargebackCase::with('order:order_id,amount,currency', 'order.paymentOrder:order_id,currency,amount', 'refund:order_id,refund_id,status,completed_at')
            ->whereHas('orderRelation', function (Builder $query) {
                $query->where('is_refund', OrderRelation::IS_REFUND_FULL);
            })->where('reply_status', ChargebackCase::REPLY_STATUS_PROCESSING)
            ->whereBetween('updated_at', [$startTime, $endTime])
			->where('auto_reply_times', '<', $maxTimes)->get();

        $successCount   = 0;
        $caseIdData     = [];
		$failCaseIdData = [];

        if (!empty($chargebackCase)) {
            foreach ($chargebackCase as $vo) {
                //剔除status非STATUS_APPROVED的记录且按完成时间倒序
                $refundArr = $vo->refund->sortByDesc('completed_at')->whereIn('status', [Refund::STATUS_APPROVED, Refund::STATUS_REVIEW])->first()->toArray();
                $vo        = $vo->toArray();
                switch ($vo['alert_from']) {
                    case ChargebackCase::ALERT_FROM_VERIFI :
                        $noticeData = [
                            'merchantId' => $vo['dishonour_warn_info']['merchantId'],
                            'type'       => $vo['dishonour_warn_info']['type'],
                            'amount'     => $vo['order']['amount'],
                            'currency'   => $vo['order']['currency'],
                            'caseid'     => $vo['case_id'],
                        ];

                        $result = AlertService::getService($vo['alert_from'])->sendNotice($noticeData);
                        break;

                    case ChargebackCase::ALERT_FROM_WINTRANX :
                        // 510 欺诈类型成功处理并退款； 516 争议类型成功处理并退款
                        $outCome = $vo['alert_type'] == ChargebackCase::CONFIRMED_FRAUD ? '510' : '516';
                        $data = [
                            'winshieldId' => $vo['case_id'],
                            'data' => [
                                'winshieldId' => $vo['case_id'],
                                'outCome'     => $outCome
                            ]
                        ];
                        if ($vo['alert_type'] == ChargebackCase::CONFIRMED_FRAUD) {
                            $data['data']['outCome'] = $outCome;

                            if (empty($refundArr)) {
                                return ;
                            }

                            $data['data']['creditAmount'] = $vo['order']['payment_order']['amount'];
                            $data['data']['creditDate']   = date('Y-m-d', strtotime($refundArr['completed_at']));
                        } else {
                            $data['data']['outCome'] = $outCome;
                        }

                        $result = AlertService::getService($vo['alert_from'])->sendNotice($data);
                        break;

                    case ChargebackCase::ALERT_FROM_EMBRACY_SHIELD :
                        if (empty($refundArr)) {
                            return ;
                        }

                        $dataTemp = [
                            'caseId' => $vo['dishonour_warn_info']['caseId'],
                            'data' => [
                                'merchantId' => $vo['dishonour_warn_info']['merchantId'],
                                'shieldId'   => $vo['dishonour_warn_info']['shieldId'],
                                'caseId'     => $vo['dishonour_warn_info']['caseId']
                            ]
                        ];

                        if ($vo['alert_type'] == ChargebackCase::CONFIRMED_FRAUD) {
                            $dataTemp['data']['statusCode'] = 'a010';
                        } else {
                            $dataTemp['data']['statusCode'] = 'a016';
                        }

                        $dataTemp['data']['refundNo']       = $refundArr['refund_id'];
                        $dataTemp['data']['refundDate']     = $refundArr['completed_at'];
                        $dataTemp['data']['refundAmount']   = $vo['order']['payment_order']['amount'];
                        $dataTemp['data']['refundCurrency'] = $vo['order']['payment_order']['currency'];

                        $embracyService = new EmbracyShieldService();
                        $result = $embracyService->sendNotice($dataTemp, 1);
                }

                $code = $result['code'] ?? $result['status'] ?? '';

                if (in_array($code, ['202', 'success', '0', '1'])) {
                    $caseIdData[] = $vo['case_id'];
                    $successCount++;
                } else {
					$failCaseIdData [] = $vo['case_id'];
				}
            }
        }

        // 更新通知成功案例
        if (!empty($caseIdData)) {
            ChargebackCase::whereIn('case_id', $caseIdData)->update([
                'is_normal'        => ChargebackCase::IS_NORMAL_TRUE,
                'reply_status'     => 2,
                'auto_reply_times' => DB::raw('auto_reply_times + 1'),
                'date_settle'      => get_settle_date()
            ]);
        }

		// 更新通知失败案例
		if (!empty($failCaseIdData)) {
			ChargebackCase::whereIn('case_id', $failCaseIdData)->update(['auto_reply_times' => DB::raw('auto_reply_times + 1')]);
		}

        $msgStr = sprintf('拒付预警完成通知检索统计,检索总计:%s条,检索成功:%s条,检索失败:%s条', count($chargebackCase), $successCount, count($chargebackCase) - $successCount);
		$log->info($msgStr);
        dispatch(new SendSlsLog(
            ['message' => $msgStr],
            [],
            'info',
            'task'
        ));

		$this->info($msgStr);
        $this->info("拒付预警完成通知检索结束");
    }
}
