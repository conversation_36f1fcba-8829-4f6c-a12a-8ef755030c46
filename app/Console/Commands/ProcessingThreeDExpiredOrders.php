<?php

namespace App\Console\Commands;

use App\Models\Order;
use Illuminate\Console\Command;
use App\Classes\Supports\Collection;
use App\Events\Notice;
use App\Models\Channel;
use Illuminate\Support\Facades\Cache;
use App\Classes\Pay\Pay;
use App\Classes\Supports\Logger;
use App\Events\RiskWarn;
use App\Http\Controllers\Traits\PaymentController;
use App\Jobs\SendSlsLog;

class ProcessingThreeDExpiredOrders extends Command
{
    use PaymentController;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'task:processingThreeDExpiredOrders';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Processing expired 3D orders';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $key     = 'Expired_Order_Data';
        $orderId = Cache::get($key);
        if (empty($orderId)) {
            return;
        }

        $orders = Order::with('paymentOrder')->whereIn('order_id', $orderId)->where('code',  get_system_code('200'))->where('is_3d', Order::IS_3D_YES)->get();
        if (empty($orders)) {
            return;
        }

        // 初始化日志
	    $log    = new Logger();
	    $config = ['file' => storage_path('logs/consoleTask.log')];
	    $log->setConfig($config);

        $res = [
            'total'   => count($orders),
            'expire'  => 0,
            'success' => 0
        ];

        // 获取支付渠道名称
        $channelArr  = array_column($orders->toArray(), 'channel_id');
		$channelData = Channel::with('channelSupplier')->whereIn('id', $channelArr)->get();

        // 处理渠道数据
        $tempChannelData = [];
        foreach ($channelData as $value) {
            $supplierName                = strtolower($value->channelSupplier->file_name);
            $tempChannelData[$value->id] = [
                'config'        => $this->_getConfig($value->config, $supplierName),
                'supplier_name' => strtolower($supplierName),
            ];
        }

        foreach ($orders as $order) {
            $supplierName = $tempChannelData[$order->channel_id]['supplier_name'];
            $expired      = true; // 处理为过期标识
            // 过期3D订单只有austpay渠道需要重新查询
            if (in_array($supplierName, ['austpay'])) {
                // 调用渠道查询接口
                $config       = $tempChannelData[$order->channel_id]['config'];
                $channelName  = Pay::$supplierName($config);
                $result       = $channelName->retrieve($order->toArray());
                if (!empty($result) && $result['order']['code'] != get_system_code('200')) {
                    // 渠道返回终态，按渠道返回处理
                    $returnData = $this->updateOrder($order, $result);
                    $res['success']++;
                    $expired = false;
                }
            }

            if ($expired) {
                $code   = get_system_code('152');
                $remark = '3D验证失败';
                $result = '3DS Verification Failed';
                $status = Order::STATUS_DECLINED;

                $orderCollection = [
                    'code'   => $code,
                    'result' => '(*) ' . $result,
                    'remark' => $remark,
                    'status' => $status,
                ];

                $paymentOrderCollection = [
                    'code'             => $code,
                    'result'           => '(*) ' . $result,
                    'remark'           => '',
                    'status'           => $status,
                ];

                $collection = new Collection();
                $collection->set('order', $orderCollection);
                $collection->set('payment_order', $paymentOrderCollection);

                $returnData = $this->updateOrder($order, $collection, false);
                $res['expire']++;
            }

            //异步通知
            event(new Notice($order, $returnData));
            // 执行告警事件
            event(new RiskWarn($order));
        }

        // 更新缓存
        $tempOrderId = Cache::get($key);
        $caseData    = array_diff($tempOrderId, $orderId);
        Cache::put($key, $caseData, 1800);

        $info = sprintf("本次操作总计:%s条,向渠道检索返回终态处理:%s条,处理订单为过期:%s条", $res['total'], $res['success'], $res['expire']);
	    $log->info("处理3D订单过期执行输出", ['info' => $info, 'order' => $orderId]);
		dispatch(new SendSlsLog(
			['message' => '处理3D订单过期执行输出'],
			['info' => $info, 'order' => $orderId],
			'info',
			'task'
		));
    }
}


