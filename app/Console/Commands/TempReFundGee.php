<?php

namespace app\Console\Commands;

use App\Classes\Pay\Gateways\Gee\Support;
use App\Classes\Pay\Parser\Gee;
use App\Classes\Supports\Traits\HasHttpRequest;
use App\Http\Controllers\Traits\PaymentController;
use App\Models\Order;
use Illuminate\Console\Command;

class TempReFundGee extends Command
{
    use PaymentController;
    use HasHttpRequest;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'task:tempReFundGee';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        set_time_limit(0);
        // 开始时打印详细日志
        $this->info("账单标识和PID交易计数更新任务开始");

        $data      = [
            'reqTime'      => time(),
            'version'      => '1.0',
            'mchNo'        => 'M1744687625',
            'appId'        => '665edaf3e4b06b6e5e03b4b1',
            'mchOrderNo'   => '2025051303113906614',
            'mchRefundNo'  => Order::findAvailableNo(),
            'refundAmount' => Support::amount_format(584.94 * 100, 0),
            'refundReason' => '用户退货',
            'signType'     => 'RSA',
        ];

        // 配置服务商号
        $data['isvNo'] = 'V1717492466';

        $transitIp = 'http://************:9999/Channel.php' . '?' . Support::urlPayment([]);

        // 生成签名
        $data['sign'] = Support::sign($data, 'MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQDHZ5V+v3I/pRUIw1HptH6x/MJySXt3ByTup4F5w2sr5ZXLkNMMtzT+VR0qxGi9ofo34Bw/CLkLnlLm8+8B5TjppcwRwHdvQ2oH+NTGVXOBVTQhLKW+T278Msg+QE2W4z176wb+3OWCJZQ+CkuNSxg+iR8Ucd4fHTyz3DWi6/3h82ElFy3wVScbpXjfGbKr5JG7F8LbYKNOr2vgnXyeXvB7eZKpMtcq9hY8LVhke8Pc/nYKx9+qPwtS6Af/57J7l9LEb7qbf+rnVQKohqDCf5T0mMHAi73YmuFwOqgPcjkbM9Upuph3gmHvw+SCjxZu9xWdBnUy74aQrB+/j2+7T4zbAgMBAAECggEAB19OCKSdXbsFvxAxnBlfQcddjQoodWsfM2+f+td9w962HhOZqfOwEuMks+sVEEXH0X8IrE/vP324BO/p20EVTJYjqFhcUhHE/sx1WWvZXGPBDKTw78tSCCL8gkiwVa7LHCTBWnTiS2CGTZDl4DBxkgZRztiy5KWZE/FFTmO5PDxJlXnLhtWZucLVvHKdqWVBaXUbC0VAOJd2kmFM68bLOeKTXK0aCyVNhC7UH1GJ3zVg2rHPlOZASD6X2xJBJ9gq/yrW/hyyWOHAOtr68jNuBIP1OeguzjXWjS7iEeO6VMJXhOw6t1KdCdFq35dSDn9/0+X4+VX7+VsUdMPJExxWcQKBgQDt/c/twwakCsfDt79Q6/EC59Gzz+r2u0nAXsm47EEbcTpq9ZRx/byD3IiOc7I+BDyny65HMSIUlUVdGqBIxdEl44G+qALW95Nwkb/3r+z8lC5Eb6yBJyUveIitjspe6kupEpxhP272y5rjKSTllXkstRBJt6W1uEemSygteR4oQwKBgQDWfkwcSzGA6iMs74fNMulvefD0RkciZZOcu2wV/gGfS8XxlIHl8WNXQvmYy70JGIu0P3PLiN+PQMPwInDpOWMPayR8HVxGzPxNdJMfR8hZn7SUkvfih1dlyeasV67oZyRdnTg+qYN6u4OwatPGyr9GeUHU6xMo7Mcs3zG1ichriQKBgBvdIJAMymVAYd0XUU3vIdueOp4b1L2odd0Q+/srpf/JhPZq5ENoWr7xfgd3okHCizt2DGSHax8Pzvw0ltmOV4thFJq23wY+HEQMvHYKYeu8iaI3lPHWHHNvYEk5s5XjRpJ3/FkibSJ1+h9ofMpocrR6SnrnBn69IZ5lpEGWsaIzAoGAaD2U66M3eblQQwRd8UGu5PhUO8LzyymmZWNIkh+R1kLS/ib4QjHf34azGqjZQ9RkAMNdmSr5yvrkvo1eZln3ZITjhFYYhGgWreXbojYWjkfpUNNkGPlBZjyLGHK8lEGcEMrZjMqBFWKQC2q/ZTsfXjaL4dgkxZzc51fJJrrBZlECgYAMOaL+dpngf2dINBNYDAwj7mNwDnH387KD39TrUDatdpKpY+NRC3PEZ1AgzOrtrqzWaoY6nzBUBU1RfVWYyfAw/JY2Rk6kLdQa7mpJpxbXSt5FET3sqtTpYElvtINhIlIOsV0GA5dff3XQKxrtb1TtQq1Vq2BstD5LRKoxik57eA==');

        $header =[
            'Content-Type' => 'application/json',
        ];

        $post = [
            'transitIp' => $transitIp,
            'header'    => $header,
            'data'      => $data
        ];

        // 转发到中转
        $methodArr = [
            'get'  => 'curlGet',
            'post' => 'curlPost',
        ];

        $post['embMethod'] = $methodArr['post'];
        $post['doUrl']     = 'https://gateway.uneepay.com/api/refund/refundOrder';

        if (!empty($post['header'])) {
            foreach ($post['header'] as $key => $vo) {
                $header[] = $key . ':' . $vo;
            }

            $post['header'] = $header;
        }

        $result = $this->post($transitIp, $post);

        if (gettype($result) === 'string') {
            $result = json_decode($result, true) ?? [];
        }

        // 结束时打印详细日志 记录更新渠道以及数量
        $this->info("账单标识和PID交易计数更新任务结束");
    }

}
