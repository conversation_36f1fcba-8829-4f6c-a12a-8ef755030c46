<?php

namespace app\Console\Commands;

use App\Models\Channel;
use App\Models\ChannelSupplier;
use Illuminate\Console\Command;
use App\Services\ChannelPidLimitService;
use Illuminate\Support\Carbon;

class TempUpdateCardWhiteListed extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'task:tempUpdateCardWhiteListed';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        set_time_limit(0);
        $this->info("卡白名单 cc type更新任务开始");


        $this->info("卡白名单 cc type更新任务结束");
    }

}
