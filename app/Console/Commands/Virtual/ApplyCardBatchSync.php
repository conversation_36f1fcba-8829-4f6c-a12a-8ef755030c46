<?php

namespace App\Console\Commands\Virtual;

use App\Classes\Supports\Log;
use App\Classes\Supports\Logger;
use App\Models\CardBatch;
use App\Models\CardVirtual;
use App\Models\CardHolder;
use App\Models\MerchantApiNoticeTask;
use App\Services\VirtualControllerService;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use App\Services\Virtual\TongLianService;
use App\Jobs\SendSlsLog;
use App\Services\Virtual\VirtualServiceFacade;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class ApplyCardBatchSync extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'card:applyCardBatchSync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '卡批次申请-同步申请使用';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        // 初始化日志
        $log    = new Logger();
        $config = ['file' => storage_path('logs/consoleCardTask.log')];
        $log->setConfig($config);
        Log::setInstance($log);
    }

    /**
     * @throws \Exception
     */
    public function handle()
    {
        $this->info('卡批次同步申请开始');
        set_time_limit(0);
        ini_set('memory_limit', '512M');
        // 会有中途处理失败的情况需要适配
        $cardBatches = CardBatch::where('status', CardBatch::PROCESS)
            ->whereHas('CardBin.CardBinSupplier', function (Builder $query) {
                $query->where('file_name', TongLianService::CHANNEL_SERVICE_CODE);
            })
            ->with(['CardBin.CardBinSupplier', 'merchant'])
            ->orderBy('quantity')
            ->get();

        if (count($cardBatches) <= 0) {
            $this->info('暂无批次要同步处理');
            return;
        }

        foreach ($cardBatches as $cardBatch) {
            $successCount = 0;
            // 处理中的查询是否全都申请成功
            if ($cardBatch->status == CardBatch::PROCESS) {
                $virtuals     = CardVirtual::where('batch_id', $cardBatch->batch_id)->get();
                $successCount = $virtuals->where('status', CardVirtual::ACTIVATION)->count();
                if ($successCount == $cardBatch->quantity) {
                    $cardBatch->status         = CardBatch::PROCESS_SUCCESS;
                    $cardBatch->success_number = $successCount;
                    $cardBatch->save();
                    // 添加回调任务
                    VirtualControllerService::createNoticeTask(
                        $cardBatch['merchant_id'],
                        MerchantApiNoticeTask::WEBHOOK_MESSAGE_TYPE_VIRTUAL_BATCH,
                        [
                            'code'    => '0000',
                            'message' => '成功',
                            'data'    => ['batch_id' => $cardBatch['batch_id'], 'batch_status' => CardBatch::$statusApiMap[CardBatch::PROCESS_SUCCESS]]
                        ]
                    );
                    continue;
                }
                $actualCount = $virtuals->count();
                // 没能完全开卡需要继续开
                if ($actualCount != $cardBatch->quantity) {
                    $successCount = $this->execApply($cardBatch, $actualCount);
                }
            } else {
                $successCount = $this->execApply($cardBatch);
            }

            Log::info("batchId: {$cardBatch->batch_id};预计申请数量：{$cardBatch->quantity};本次成功申请数量:{$successCount}");
            dispatch(new SendSlsLog(
                ['message' => "batchId: {$cardBatch->batch_id};预计申请数量：{$cardBatch->quantity};本次成功申请数量:{$successCount}"],
                [],
                'info',
                'task'
            ));
        }
        $this->info('卡批次同步申请处理结束');
    }

    public static function execApply(CardBatch $cardBatch, int $actualCount = 0): int
    {
        // 获取锁，任务可能会执行很长时间
        $lock = Cache::lock('ApplyCardBatchSync_' . $cardBatch->batch_id . '_Lock', 600);

        if (!$lock->get()) {
            return 0;
        }
        $successCount            = 0;
        $service                 = VirtualServiceFacade::getService($cardBatch->cardBin->CardBinSupplier->file_name)->setGatewayConfig($cardBatch->cardBin->config);
        $params['remarks']       = $cardBatch->cardBin->remarks;
        $params['card_type']     = $cardBatch->cardBin->card_type;
        $params['bin']           = $cardBatch->cardBin->toArray();
        $params['merchant_id']   = $cardBatch->merchant_id;
        $params['merchant_name'] = $cardBatch->merchant->merchant_name;
        $params['cards_id']      = $cardBatch->cards_id;
        $params['batch_id']      = $cardBatch->batch_id;
        // 请求开卡,如果使用默认持卡人则持卡人都是同一个信息
        if ($cardBatch->is_use_default_cardholder) {
            // 默认持卡人信息
            $cardHolder    = CardHolder::select('*', DB::raw('id as cardholder_id'))->where('id', $cardBatch->cardholder_ids)->with('reports')->first()->toArray();
            $params        = array_merge($cardHolder, $params);
            $result        = $service->batchApplyCard($params, $cardBatch->quantity - $actualCount);
            $successCount += $result['success_number'];
        } else {
            // 查询所有持卡人信息，循环开卡
            $cardHolders = CardHolder::select('*', DB::raw('id as cardholder_id'))->whereIn('id', explode(',', $cardBatch->cardholder_ids))->with('reports')->orderBy('id')->get()->toArray();
            for ($i = $actualCount; $i < $cardBatch->quantity; $i++) {
                $applyParams = array_merge($cardHolders[$i], $params);
                $result      = $service->batchApplyCard($applyParams, 1);
                // 有请求错误，不再继续处理
                if ($result['success_number'] == 0) {
                    Log::info('卡批次同步处理申请开卡失败, batchId:' . $cardBatch->batch_id);
                    dispatch(new SendSlsLog(
                        ['message' => '卡批次同步处理申请开卡失败, batchId:' . $cardBatch->batch_id, 'params' => array_diff_key($applyParams, ['bin' => ''])],
                        [],
                        'warning',
                        'task'
                    ));
                    break;
                }
                $successCount += 1;
            }
        }
        $lock->release();

        return $successCount;
    }
}
