<?php

namespace App\Console\Commands\Virtual;

use App\Classes\Supports\Log;
use App\Classes\Supports\Logger;
use App\Jobs\SendNotice;
use App\Jobs\SendSlsLog;
use App\Models\CardTransaction;
use App\Models\CardVirtualBlack;
use DateTime;
use Illuminate\Console\Command;

class ScanCardBlack extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'card:scanCardBlack';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '虚拟卡黑名单交易扫描';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        // 初始化日志
        $log    = new Logger();
        $config = ['file' => storage_path('logs/consoleCardTask.log')];
        $log->setConfig($config);
        Log::setInstance($log);
    }

    // 每分钟执行  每次获取前5分钟 00秒到 当前时间00秒的交易 进行扫描
    // 扫描规则：交易卡账单或者mcc否存在于黑名单列表，存在则标记交易为黑名单交易，匹配时完全匹配 忽略大小写
    // 扫描到黑名单交易需要预警通知vcc风控
    public function handle()
    {
        $this->info("虚拟卡黑名单交易扫描开始");
        $now          = new DateTime();
        $oneMinuteAgo = clone $now;
        $oneMinuteAgo->modify('-5 minute');
        $oneMinuteAgo->setTime($oneMinuteAgo->format('H'), $oneMinuteAgo->format('i'));
        $currentMinuteStart = clone $now;
        $currentMinuteStart->setTime($now->format('H'), $now->format('i'));
        $startTime        = $oneMinuteAgo->format('Y-m-d H:i:s');
        $endTime          = $currentMinuteStart->format('Y-m-d H:i:s');
        $cardVirtualBlack = CardVirtualBlack::select('name', 'type')->where('status', CardVirtualBlack::CARD_BLACK_STATUS_ENABLE)->get();
        if (empty($cardVirtualBlack)) {
            $this->info("虚拟卡黑名单交易扫描结束,无黑名单");
            return;
        }

        $whereMcc         = $cardVirtualBlack->where('type', CardVirtualBlack::CARD_BLACK_TYPE_MCC)->pluck('name');
        $whereDescription = $cardVirtualBlack->where('type', CardVirtualBlack::CARD_BLACK_TYPE_MERCHANT_NAME)->pluck('name');
        $cardTransaction  = CardTransaction::select('id', 'unique_id', 'merchant_id', 'merchant_name', 'transaction_mcc', 'transaction_description')->where('risk_type', '<>', CardTransaction::TRANSACTION_RISK_TYPE_BLACK_TRADE)->whereBetween('date_comtplete', [$startTime, $endTime])->where(function ($query) use ($whereMcc, $whereDescription) {
            if (count($whereMcc)) {
                $query->orWhereIn('transaction_mcc', $whereMcc);
            }
            if (count($whereDescription)) {
                $query->orWhereIn('transaction_description', $whereDescription);
            }
        })->get();

        if ($cardTransaction->count() <= 0) {
            $this->info("虚拟卡黑名单交易扫描结束,无交易数据");
            return;
        }

        $updateIds = $cardTransaction->pluck('id');
        $updateRet = CardTransaction::whereIn('id', $updateIds)->update(['risk_type' => CardTransaction::TRANSACTION_RISK_TYPE_BLACK_TRADE]);

        if (!$updateRet) {
            Log::warning('虚拟卡黑名单交易扫描 更新失败,id为：.' . implode(',', $updateIds));
            dispatch(new SendSlsLog(
                ['message' => '虚拟卡黑名单交易扫描 更新失败,id为：.' . implode(',', $updateIds)],
                [],
                'warning',
                'task'
            ));
            return;
        }

        foreach ($cardTransaction as $transaction) {
            $data = [
                'level'             => 1,
                'contents'          => '[黑名单交易扫描]' . PHP_EOL .
                    'UID:' . $transaction->unique_id . PHP_EOL .
                    'MID:' . $transaction->merchant_id . PHP_EOL .
                    '商户名称:' . $transaction->merchant_name,
                'notice_user_roles' => 'VCC Risk Control',
                'type'              => 3,
                'status'            => 2,
            ];
            dispatch(new SendNotice($data, 5));
        }

        $this->info("虚拟卡黑名单交易扫描结束");
    }
}
