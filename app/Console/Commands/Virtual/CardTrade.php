<?php

namespace App\Console\Commands\Virtual;

use App\Classes\Supports\Log;
use App\Classes\Supports\Logger;
use App\Jobs\SendSlsLog;
use App\Models\CardBin;
use App\Services\Virtual\VirtualServiceFacade;
use Illuminate\Console\Command;

class CardTrade extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'card:inquiryTrade';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Card transaction inquiry';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        // 初始化日志
        $log    = new Logger();
        $config = ['file' => storage_path('logs/consoleCardTask.log')];
        $log->setConfig($config);
        Log::setInstance($log);
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('开始虚拟卡交易查询');

        $binList = CardBin::where('status', 1)->get();
        Log::info('Card transaction query task begins', $binList->toArray() ?? []);
        dispatch(new SendSlsLog(
            ['message' => 'Card transaction query task begins'],
            $binList->toArray() ?? [],
            'info',
            'task'
        ));

        foreach ($binList as $v) {
            $this->info('BIN交易查询' . $v->bin_name);
			VirtualServiceFacade::getService($v->CardBinSupplier->file_name)->setGatewayConfig($v->config)->inquiryTrade();
        }

        $this->info('虚拟卡交易查询结束');
    }
}
