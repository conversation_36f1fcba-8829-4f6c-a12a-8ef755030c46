<?php

namespace App\Console\Commands\Virtual;

use App\Classes\Supports\Log;
use App\Classes\Supports\Logger;
use App\Jobs\CardOperation;
use App\Models\CardTicket;
use Illuminate\Console\Command;

class DealCardTicket extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'card:dealCardTicket';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check card ticket';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        // 初始化日志
        $log    = new Logger();
        $config = ['file' => storage_path('logs/consoleCardTask.log')];
        $log->setConfig($config);
        Log::setInstance($log);
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('卡工单处理开始');
        
        // 获取待处理数据
        CardTicket::where('status', 3)->where('created_at', '<=', date('Y-m-d H:i:s', strtotime('-15 minute')))
            ->chunk(100, function ($cardTicketList) {
                if (count($cardTicketList) <= 0) {
                    $this->info('暂时没有要处理卡工单');
                    return;
                }

                foreach ($cardTicketList as $cardTicket) {
                    $cardTicketArr = $cardTicket->toArray();
                    if (count($cardTicketArr) > 1) {
                        // 添加查卡操作任务
                        dispatch(new CardOperation($cardTicketArr, 0));
                    }
                }
            });

        $this->info('卡工单处理结束');
    }
}
