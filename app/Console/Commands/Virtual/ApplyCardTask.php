<?php


namespace App\Console\Commands\Virtual;

use App\Classes\Supports\Logger;
use App\Models\CardBin;
use App\Models\CardBinClass;
use App\Models\CardVirtual;
use App\Classes\Supports\Log;
use App\Services\Virtual\VirtualServiceFacade;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Builder;
use App\Services\Virtual\TongLianService;

class ApplyCardTask extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'card:applyCardTask';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '创建虚拟卡';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        // 初始化日志
        $log    = new Logger();
        $config = ['file' => storage_path('logs/consoleCardTask.log')];
        $log->setConfig($config);
        Log::setInstance($log);
    }

    public function handle()
    {
        // 在命令行打印一行信息
        $this->info("囤卡处理开始");

        // 获取数据，排除通联渠道囤卡
        $cardBins = CardBin::where('status', CardBin::CARD_BIN_STATUS_ENABLE)->where('anticipated_count', '>', 0)->whereHas('CardBinClass', function (Builder $query) {
            $query->where('status', CardBinClass::STATUS_START);
        })->whereHas('CardBinSupplier', function (Builder $query) {
            $query->where('file_name', '<>', TongLianService::CHANNEL_SERVICE_CODE);
        })->with('CardBinSupplier')->get();

        $cardBinIds = $cardBins->pluck('id');

        if (count($cardBinIds) == 0) {
            $this->info("暂无囤卡要处理");
            return;
        }

        // 分组查询囤卡数量
        $cardVirtuals = CardVirtual::select(DB::raw('count(*) as count, card_bin_id'))->where('cards_id', '')->whereIn('card_bin_id', $cardBinIds)->groupBy('card_bin_id')->get()->keyBy('card_bin_id')->toArray();

        $handelCount = 0;
        //处理开卡
        foreach ($cardBins as $bin) {
            $anticipatedCount = $bin->anticipated_count;
            if (isset($cardVirtuals[$bin->id])) {
                $anticipatedCount = $anticipatedCount - $cardVirtuals[$bin->id]['count'];
            }

            if ($anticipatedCount > 0) {
                $bin->anticipated_count = $anticipatedCount;
                $successNumber          = $this->execApply($bin);
                $this->info(sprintf("本次处理囤卡bin(%s)成功数量:%s", $bin->bin_name, $successNumber));
                $handelCount++;
            }
        }
        $this->info(sprintf("本次处理囤卡bin数量:%s", $handelCount));
        $this->info("囤卡bin处理结束");
    }

    private function execApply(CardBin $cardBin): int
    {
        $params['remarks']   = $cardBin->remarks;
        $params['card_type'] = $cardBin->card_type;
        $params['suppler']   = $cardBin->CardBinSupplier->toArray();
        $params['bin']       = $cardBin->toArray();

        // 申请卡片
        $result        = VirtualServiceFacade::getService($cardBin->CardBinSupplier->file_name)->setGatewayConfig($cardBin->config)->batchApplyCard($params, $cardBin->anticipated_count);
        $successNumber = $result['success_number'] ?? 0;

        return $successNumber;
    }
}
