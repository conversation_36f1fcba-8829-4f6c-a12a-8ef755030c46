<?php

namespace App\Console\Commands\Virtual;

use App\Classes\Supports\Log;
use App\Classes\Supports\Logger;
use App\Jobs\SendSlsLog;
use App\Models\CardBin;
use App\Models\CardVirtualBlack;
use App\Services\Virtual\VirtualServiceFacade;
use Illuminate\Console\Command;

class SetCardFraud extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'card:setCardFraud';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '设置虚拟卡黑名单';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        // 初始化日志
        $log    = new Logger();
        $config = ['file' => storage_path('logs/consoleCardTask.log')];
        $log->setConfig($config);
        Log::setInstance($log);
    }

    public function handle()
    {
        $this->info("设置虚拟卡黑名单开始");
        $isSync = CardVirtualBlack::where('is_sync', CardVirtualBlack::CARD_BLACK_IS_SYNC_NO)->where('status', CardVirtualBlack::CARD_BLACK_STATUS_ENABLE)->first();
        // 没有需要同步的黑名单，跳出
        if (empty($isSync)) {
            $this->info("设置虚拟卡黑名单结束,更新0条");
            return;
        }

        $cardVirtualBlackInfo = CardVirtualBlack::select('id', 'name', 'type')->where('status', CardVirtualBlack::CARD_BLACK_STATUS_ENABLE)->get();
        if (empty($cardVirtualBlackInfo)) {
            return;
        }

        $blackMcc   = [];
        $blackNames = [];
        foreach ($cardVirtualBlackInfo as $black) {
            if ($black->type == CardVirtualBlack::CARD_BLACK_TYPE_MCC) {
                $blackMcc[] = $black->name;
            } else {
                $blackNames[] = $black->name;
            }
        }

        $cardBinInfo = CardBin::selectRaw('bin_supplier_id, MAX(id) as id, ANY_VALUE(config) as config')
            ->where('status', '=', CardBin::CARD_BIN_STATUS_ENABLE)  // 确保查询条件匹配
            ->with('CardBinSupplier')
            ->groupBy('bin_supplier_id')
            ->get();

        $updateCount = 0;
        foreach ($cardBinInfo as $cardBin) {
            $this->info("设置虚拟卡黑名单 bin_supplier_id: " . $cardBin->bin_supplier_id);
            try {
                $updateCount = VirtualServiceFacade::getService($cardBin->CardBinSupplier->file_name)->setGatewayConfig($cardBin->config)->setFraud(['mcc' => $blackMcc, 'merchant_name' => $blackNames, 'is_all' => true]);
            } catch (\Exception $e) {
                Log::warning($e->getMessage());
                dispatch(new SendSlsLog(
                    ['message' => "设置虚拟卡黑名单失败 bin_supplier_id: " . $cardBin->bin_supplier_id . $e->getMessage()],
                    [],
                    'warning',
                ));
                continue;
            }
        }

        $this->info("设置虚拟卡黑名单结束,更新{$updateCount}条");
    }
}
