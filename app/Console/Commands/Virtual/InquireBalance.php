<?php

namespace App\Console\Commands\Virtual;

use App\Classes\Supports\Log;
use App\Classes\Supports\Logger;
use App\Jobs\SendSlsLog;
use App\Models\CardVirtual;
use App\Services\Virtual\VirtualServiceFacade;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;

class InquireBalance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'card:inquireBalance';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check card balance';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        // 初始化日志
        $log    = new Logger();
        $config = ['file' => storage_path('logs/consoleCardTask.log')];
        $log->setConfig($config);
        Log::setInstance($log);
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('卡余额查询开始');

        CardVirtual::whereHas('CardBin', function (Builder $query) {
            $query->where('status', '=', 1);
        })->where([
            ['status', '=', CardVirtual::ACTIVATION],
            ['card_type', '=', 0],
        ])->where('date_api_balance', '<=', date('Y-m-d H:i:s', strtotime('-1 hour')))
            ->select('virtual_id', 'card_number', 'date_api_balance', 'card_bin_id', 'trans_id')
            ->with('cardBin.CardBinClass')
            ->orderBy('date_api_balance', 'asc')
            ->chunkById(1000, function ($flights) {
                foreach ($flights as $v) {
                    $this->info('卡余额查询' . $v->virtual_id);
					VirtualServiceFacade::getService($v->CardBin->CardBinSupplier->file_name)->setGatewayConfig($v->CardBin->config)->inquireBalance($v->toArray());
                }
            });

        $this->info('卡余额查询结束');
    }
}
