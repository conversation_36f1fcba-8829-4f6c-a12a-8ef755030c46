<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\OrderChargebackStatisticsService;

class StatOrderChargebacks extends Command
{
    protected $signature = 'task:sataOrderChargebacks';

    protected $description = 'Refusal statistics';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle(OrderChargebackStatisticsService $service)
    {
        $date  = date('Y-m-d', strtotime('-2 hour'));
        $service->timingStatistics($date);
    }
}
