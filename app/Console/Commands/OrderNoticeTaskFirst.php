<?php

namespace App\Console\Commands;

use App\Classes\Supports\Logger;
use App\Classes\Supports\Traits\HasHttpRequest;
use App\Jobs\SendSlsLog;
use Illuminate\Console\Command;
use App\Models\OrderNoticeTask as OrderNotice;

class OrderNoticeTaskFirst extends Command
{
	use HasHttpRequest;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notice:orderNoticeTaskFirst';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
	    // 在命令行打印一行信息
	    $this->info("首次异步通知开始");

        $startTime = microtime(true);

	    // 初始化日志
	    $log    = new Logger();
	    $config = ['file' => storage_path('logs/consoleTask.log')];
	    $log->setConfig($config);

	    $orderNoticeTaskModel = new OrderNotice();

	    // 获取待通知数据
	    $orderNoticeTask = OrderNotice::where('status', '0')->where('cnt', '=', '0')->get()->toArray();
	    $success         = 0;
	    $total           = 0;

	    if (!empty($orderNoticeTask)) {
	    	foreach ($orderNoticeTask as $orderNotice) {
                $useTime = amount_format(microtime(true) - $startTime, 2);
                // 通知时间不能大于55秒
                if ($useTime > 55) {
                    break;
                }

			    $noticeData = json_decode($orderNotice['content'], true);

                try {
                    $result = trim($this->post($orderNotice['notify_url'], $noticeData));
                } catch (\Exception $e) {
                    $result = $e->getMessage();
                }

			    $data       = [
				    'result' => (strlen($result) > 255) ? mb_strcut($result, 0, 252) : $result,
				    'cnt'    => $orderNotice['cnt'] + 1,
			    ];

			    if ($result == 'ok') {
				    $data['status'] = '1'; // status=1 表示通知成功
				    $success++;
			    }

			    $orderNoticeTaskModel->where('id', $orderNotice['id'])->update($data);
			    $total++;
		    }

		    $outPut = "异步通知总数:" . $total . ', 成功通知数:' . $success;

		    $this->info($outPut);
		    $log->info("异步通知执行输出", [$outPut]);
            dispatch(new SendSlsLog(
                ['message' => '异步通知执行输出'],
                [$outPut],
                'info',
                'task'
            ));
	    }

	    $this->info("首次异步通知结束");
    }
}
