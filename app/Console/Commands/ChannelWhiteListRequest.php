<?php

namespace App\Console\Commands;

use App\Classes\Supports\Logger;
use App\Classes\Supports\Traits\HasHttpRequest;
use App\Jobs\SendSlsLog;
use App\Models\ChannelWhiteListed;
use App\Models\DirectoryDictionary;
use App\Models\Order;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Services\ChannelWhiteListService;
use Illuminate\Database\Eloquent\Builder;

class ChannelWhiteListRequest extends Command
{
    use HasHttpRequest;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'task:channelWhiteListRequest';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    const URL           = 'http://api.3dpaymentverification.com/tapi.php';
    const API_ID        = '0897c061';
    const API_KEY       = 'ae8c323b2a4b';
    const IS_OPEN       = '1';
    const SUPPLIER_NAME = 'Austpay';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // 初始化日志
        $log    = new Logger();
        $config = ['file' => storage_path('logs/channelWhiteList.log')];
        $log->setConfig($config);

        $shellStandConfig = DirectoryDictionary::where('type', '=', '渠道白名单配置')->pluck('remarks', 'name');
        $isOpen           = $shellStandConfig['is_open'] ?? self::IS_OPEN;

        if ($isOpen) {
            set_time_limit(0);
            ini_set('memory_limit', '512M');

            $startTime = now()->startOfDay()->subDay(1);
            $endTime   = now()->startOfDay();

            $orderData = Order::select('status', 'card_id', 'completed_at')
            ->with('card:id,cc_type,card_mask')
            ->whereHas('card', function (Builder $query) {
                $query->whereIn('cc_type', ['V', 'M']);
            })
            ->where('status', Order::STATUS_APPROVED)
            ->whereBetween('completed_at', [$startTime, $endTime])->get();

            $cardMasks = $orderData->pluck('card.id', 'card.card_mask')->toArray();

            // 根据渠道白名单表过滤
            $channelWhiteListeds = ChannelWhiteListed::whereIn('card_mask', array_keys($cardMasks))->pluck('id', 'card_mask')->toArray();
            $cardMasks           = array_diff_key($cardMasks, $channelWhiteListeds);

            $data = [];

            if (count($cardMasks)) {
                $nowTimeDay              = now();
                $marchTimeDay            = now()->subMonths(3);
                $sixTimeDay              = now()->subMonths(6);
                $channelWhiteListService = new ChannelWhiteListService();
                //半年到三个月前
                $channelWhiteListService->handelDate($data, $cardMasks, $sixTimeDay, $marchTimeDay);
                //三个月到现在
                $channelWhiteListService->handelDate($data, $cardMasks, $marchTimeDay, $nowTimeDay);

                if (count($data)) {
                    $addChannelWhiteListeds = [];
                    $url                    = $shellStandConfig['url'] ?? self::URL;
                    $appId                  = $shellStandConfig['app_id'] ?? self::API_ID;
                    $appKey                 = $shellStandConfig['app_key'] ?? self::API_KEY;
                    $supplierName           = $shellStandConfig['supplier_name'] ?? self::SUPPLIER_NAME;
    
                    $post = [
                        'api_id'  => $appId,
                        'api_key' => $appKey,
                        'card_no' => '',
                        'data'    => [],
                    ];
    
                    foreach ($data as $cardMask => $vo) {
                        if (count($vo) == 3) {
                            $post['card_no'] = $cardMask;
                            $post['data']    = $vo;
        
                            // 记录日志
                            $log->info('Api Request', ['url' => $url, 'data' => $post]);
                            dispatch(new SendSlsLog(
                                ['message' => 'Api Request'],
                                ['url' => $url, 'data' => $post],
                                'info',
                                'task',
                                $cardMask
                            ));
                            
                            try {
                                $result = $this->post($url, [], ['json' => $post]);
                                $result = is_array($result) ? $result : json_decode($result, true);
                                
                                // 记录日志
                                $log->info('Api Return', ['result' => $result]);
                                dispatch(new SendSlsLog(
                                    ['message' => 'Api Return'],
                                    ['result' => $result],
                                    'info',
                                    'task',
                                    $cardMask
                                ));
                                
                                if (isset($result['code']) && $result['code'] == '000') {
                                    // 验证通过加入缓存数组
                                    $cacheCard[$cardMask] = 1;
        
                                    //生成渠道白名单待生效数据
                                    $addChannelWhiteListeds[] = [
                                        'supplier_name' => $supplierName,
                                        'card_mask'     => $cardMask,
                                        'status'        => ChannelWhiteListed::STATUS_NOT_EFFECTIVE,
                                        'created_at'    => now(),
                                        'updated_at'    => now(),
                                    ];
                                }
    
                            } catch (\Exception $e) {
                                $log->warning('Error', ['error' => $e->getMessage()]);
                                dispatch(new SendSlsLog(
                                    ['message' => 'Error'],
                                    ['error' => $e->getMessage()],
                                    'warning',
                                    'task'
                                ));
                            }
                        }
                    }

                    if (!empty($addChannelWhiteListeds)) {
                        $tempChannelWhiteListeds = array_chunk($addChannelWhiteListeds, 800);

                        foreach ($tempChannelWhiteListeds as $vo) {
                            DB::table('channel_white_listeds')->insertOrIgnore($vo);
                        }
                    }
                }
            }
        }
    }
}
