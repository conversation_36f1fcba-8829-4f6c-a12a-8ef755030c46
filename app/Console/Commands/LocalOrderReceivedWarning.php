<?php

namespace App\Console\Commands;

use App\Classes\Supports\Logger;
use App\Jobs\SendNotice;
use App\Jobs\SendSlsLog;
use App\Models\LocalOrder;
use App\Models\LocalRefund;
use Illuminate\Console\Command;

class LocalOrderReceivedWarning extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'task:LocalOrderReceivedWarning';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Local Order Received Warning';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
	    // 初始化日志
	    $log    = new Logger();
	    $config = ['file' => storage_path('logs/consoleTask.log')];
	    $log->setConfig($config);

        try {
            $startTime = date('Y-m-d H:i:s', strtotime('-1 day'));
            $endTime   = date('Y-m-d H:i:s', strtotime('-1 hour'));

            // 获取告警的本地支付交易订单总数
            $orderCount = LocalOrder::where('status', LocalOrder::STATUS_RECEIVED)->whereBetween('completed_at',  [$startTime, $endTime])->count();

            if (!empty($orderCount)) {
                // 组装通知消息
                $content = sprintf(
                    "[本地支付交易Received告警]\n获取交易时间：%s-%s\n订单状态: %s\n订单笔数: %s\n来源: %s",
                    $startTime,
                    $endTime,
                    LocalOrder::$statusMap[LocalOrder::STATUS_RECEIVED],
                    $orderCount,
                    config('app.url')
                );

                // 组装通知数据
                $data = [
                    'level'             => 1,
                    'contents'          => $content,
                    'notice_user_roles' => 'Administrator',
                    'type'              => 3,
                    'status'            => 2,
                ];

                dispatch(new SendNotice($data, 5));
            }
            
            // 获取告警的本地支付退款订单总数
            $refundCount = LocalRefund::where('status', LocalRefund::STATUS_RECEIVED)->whereBetween('completed_at',  [$startTime, $endTime])->count();
            if (!empty($refundCount)) {
                // 组装通知消息
                $content = sprintf(
                    "[本地支付退款Received告警]\n获取退款时间：%s-%s\n订单状态: %s\n订单笔数: %s\n来源: %s",
                    $startTime,
                    $endTime,
                    LocalRefund::$statusMap[LocalRefund::STATUS_RECEIVED],
                    $refundCount,
                    config('app.url')
                );

                // 组装通知数据
                $data = [
                    'level'             => 1,
                    'contents'          => $content,
                    'notice_user_roles' => 'Administrator',
                    'type'              => 3,
                    'status'            => 2,
                ];

                dispatch(new SendNotice($data, 5));
            }

        } catch (\Throwable $th) {
            $log->warning('本地支付交易和退款订单 received 状态告警查询失败，失败原因：' . $th->getMessage());
            dispatch(new SendSlsLog(
                ['message' => '本地支付交易和退款订单 received 状态告警查询失败，失败原因：' . $th->getMessage()],
                [],
                'warning',
                'task'
            ));
        }
    }
}
