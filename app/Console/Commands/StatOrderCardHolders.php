<?php

namespace App\Console\Commands;

use App\Classes\Supports\Logger;
use App\Jobs\SendSlsLog;
use App\Models\Order;
use App\Models\StatOrderCardholder;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class StatOrderCardHolders extends Command
{
    protected $signature = 'task:sataOrderCardHolders';

    protected $description = 'Cardholder statistics';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle(){
        // 初始化日志
        $log    = new Logger();
        $config = ['file' => storage_path('logs/consoleTask.log')];
        $log->setConfig($config);

        $log->info("持卡人统计开始");
        dispatch(new SendSlsLog(
            ['message' => '持卡人统计开始'],
            [],
            'info',
            'task'
        ));

        $date      = date('Y-m-d', strtotime('-1 day'));
        $dateMonth = date('Ym', strtotime($date));
        $addCnt    = 0;

        $orderList = [];

        $orderType = [Order::TYPES_SALE, Order::TYPES_CAPTURE];

        //获取成功的订单
        $approved = DB::table('orders as o')
            ->leftJoin('order_cards as c', 'o.card_id', '=', 'c.id')
            ->select('o.order_id', 'o.status', 'o.type', 'o.completed_at', 'o.merchant_id', 'o.business_id', 'o.channel_id',
                'o.url_id', 'o.d_mcc_id', 'o.currency', 'c.cc_type', 'c.card_country', 'o.card_bill')
            ->where('o.status', '=', Order::STATUS_APPROVED)
            ->whereIn('o.type', $orderType)
            ->whereBetween('o.completed_at', [$date . ' 00:00:00', $date . ' 23:59:59'])
            ->get();

        foreach ($approved as $value) {
            $value = (array)$value;

            $mid       = $value['merchant_id'];
            $bid       = $value['business_id'];
            $channelId = $value['channel_id'];
            $cardBill  = $value['card_bill'];
            $urlId     = $value['url_id'];
            $dMccId    = $value['d_mcc_id'];
            $currency  = $value['currency'];
            $ccType    = $value['cc_type'];
            $country   = $value['card_country'];

            $orderList[$mid][$bid][$channelId][$cardBill][$urlId][$dMccId][$currency][$ccType][$country][] = $value;
        }

        if (!count($orderList)) {
            $log->info("暂无持卡人数据统计");
            dispatch(new SendSlsLog(
                ['message' => '暂无持卡人数据统计'],
                [],
                'info',
                'task'
            ));
            
            return;
        }

        $data = [];
        //统计
        foreach ($orderList as $mid => $bList) {
            foreach ($bList as $bid => $channel) {
                foreach ($channel as $channelId => $res) {
                    foreach ($res as $cardBill => $value) {
                        foreach ($value as $url => $merchant) {
                            foreach ($merchant as $dMccId => $payment) {
                                foreach ($payment as $currency => $card) {
                                    foreach ($card as $ccType => $end) {
                                        foreach ($end as $country => $item) {
                                            $addCnt++;
                                            $data[] = [
                                                'merchant_id'     => $mid,
                                                'business_id'     => $bid,
                                                'channel_id'      => $channelId,
                                                'card_bill'       => $cardBill,
                                                'url_id'          => $url,
                                                'd_mcc_id'        => $dMccId,
                                                'currency'        => $currency,
                                                'cc_type'         => $ccType,
                                                'card_country'    => $country,
                                                'qty'             => count($item),
                                                'date_stat'       => $date,
                                                'date_stat_month' => $dateMonth,
                                                'created_at'      => date('Y-m-d H:i:s'),
                                            ];
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        if (count($data)) {
            StatOrderCardholder::insert($data);
        }

        $log->info(sprintf("日期：%s 添加总数：%s条", $date, $addCnt));
        dispatch(new SendSlsLog(
            ['message' => sprintf("日期：%s 添加总数：%s条", $date, $addCnt)],
            [],
            'info',
            'task'
        ));
    }

}