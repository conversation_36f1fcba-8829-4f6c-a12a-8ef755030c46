<?php

namespace App\Console\Commands;

use App\Classes\Pay\Exceptions\Exception;
use App\Classes\Supports\Logger;
use App\Jobs\SendSlsLog;
use App\Models\DirectoryCurrency;
use App\Models\MerchantBlacklist;
use App\Models\OrderSettlement;
use App\Models\UploadCenter;
use App\Services\BlacklistService;
use App\Services\SettlementService;
use Dcat\Admin\Models\Administrator;
use Dcat\EasyExcel\Contracts\Sheet as SheetInterface;
use Dcat\EasyExcel\Support\SheetCollection;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Dcat\EasyExcel\Excel;

class ImportTask extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'task:importTask';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'importTask';

    /**
     * @var
     */
    protected $log;

    /**
     * @var
     */
    protected $goFileName = 'paymentLinux';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        // 初始化日志
        $this->log = new Logger();
        $config    = ['file' => storage_path('logs/consoleTask.log')];
        $this->log->setConfig($config);
        // 根据当前系统类型获取不同名称的 go 程序
        switch (PHP_OS) {
            case 'WINNT':
            case 'WIN32':
            case 'Windows':
                $this->goFileName = 'paymentWindows';
                break;
            case 'Darwin':
                $this->goFileName = 'paymentMac';
                break;
            default:
                $this->goFileName = 'paymentLinux';
                break;
        }
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        ini_set('memory_limit', '1024M');
        set_time_limit(3600);

        $import = UploadCenter::where('status', UploadCenter::IMPORT_STATUS_NOT)->first();

        if (!$import) {
            return;
        }

        $import->status = UploadCenter::IMPORT_STATUS_ON;
        $import->save();

		$function    = $import->import_identity ?? 'defFunction';
		$res         = $this->$function($import);
		$errorRecord = $res['error_record'] ?? [];

		// 更新导入任务的错误记录
		if (isset($errorRecord['error']) && !empty($errorRecord['message'])) {
			$import->error_record = $errorRecord['message'];
		}

        if (isset($res['msg'])) {
            $import->error_record = $res['msg'];
        }

        if (isset($res['result'])) {
            $import->status = UploadCenter::IMPORT_STATUS_SUCCESS;
        } else {
            $import->status = UploadCenter::IMPORT_STATUS_ERROR;
            $error = $res['error'] ?? '';
            $this->log->warning("导入任务执行失败 id: {$import->id} {$error}");
            dispatch(new SendSlsLog(
                ['message' => "导入任务执行失败 id: {$import->id} {$error}"],
                [],
                'warning',
                'task'
            ));
        }

        $import->save();
    }

	/**
	 * @description: 交易勾兑导入
	 * @author: zqc
	 * @date: 2023/10/30
	 **/
	public function orderSettlement($import)
	{
		$result  = ['error' => false, 'message' => ''];
        $blendBy = Administrator::query()->where('id',$import->admin_user_id ?? 0)->value('name');
		foreach ($import->url as $vo) {
			$file = public_path('/data/orderSettlement') . '/' . $vo;

			Excel::import($file)->each(function (SheetInterface $sheet) use (&$result, $blendBy) {
				// 分块处理表格数据
				$sheet->chunk(100, function (SheetCollection $collection) use (&$result, $blendBy) {
					$tempOrderList = $collection->toArray();
					$orderList     = [];

					foreach ($tempOrderList as $key => $tempOrder) {
						foreach (OrderSettlement::$fieldList as $field => $value) {
							$orderList[$key][$field] = isset($tempOrder[$value]) ? $tempOrder[$value] : '';
						}
					}

					// 货币信息
					$currencyList = DirectoryCurrency::all()->pluck('code', 'code')->toArray();

					// 获取订单结算数据信息
					$orderIds            = array_column($orderList, 'order_id');
					$paymentOrderIds     = array_column($orderList, 'payment_order_id');
					$orderSettlementList = OrderSettlement::whereIn('order_id', !empty($orderIds) ? $orderIds : [0])
						->orWhereIn('payment_order_id', !empty($paymentOrderIds) ? $paymentOrderIds : [0])->get();

					foreach ($orderList as $row => $order) {
						if (empty($order['order_id']) && empty($order['payment_order_id'])) {
							$result['error']   = true;
							$result['message'] .= sprintf("第%s行:订单信息缺失<br>", $row);
							continue;
						}

						// 获取订单信息
						if (!empty($order['order_id'])) {
							$tempOrderSettlement = $orderSettlementList->where('order_id', $order['order_id']);
						} else {
							$tempOrderSettlement = $orderSettlementList->where('payment_order_id', $order['payment_order_id']);
						}

						if (count($tempOrderSettlement) < 1) {
							$result['error']   = true;
							$result['message'] .= sprintf("第%s行:交易信息不存在<br>", $row);
							continue;
						}

						if (count($tempOrderSettlement) > 1) {
							$result['error']   = true;
							$result['message'] .= sprintf("第%s行:匹配到多笔交易信息<br>", $row);
							continue;
						}

						$orderSettlement = $tempOrderSettlement->first();

						if ($orderSettlement->status != OrderSettlement::STATUS_APPROVED) {
							$result['error']   = true;
							$result['message'] .= sprintf("第%s行:订单号:%s交易信息状态错误<br>", $row, $orderSettlement->order_id);
							continue;
						}

						if ($orderSettlement->arrival_status == OrderSettlement::ARRIVAL_STATUS_SUCCESS) {
							$result['error']   = true;
							$result['message'] .= sprintf("第%s行:订单号:%s交易信息已到账<br>", $row, $orderSettlement->order_id);
							continue;
						}

						if ($order['arrival_amount'] < 0) {
							$result['error']   = true;
							$result['message'] .= sprintf("第%s行:订单号:%s到账金额不能小于0<br>", $row, $orderSettlement->order_id);
							continue;
						}

						if (!isset($currencyList[$order['arrival_currency']])) {
							$result['error']   = true;
							$result['message'] .= sprintf("第%s行:订单号:%s到账货币选择错误<br>", $row, $orderSettlement->order_id);
							continue;
						}

						try {
							SettlementService::blend($orderSettlement, $order, $blendBy);
						} catch (Exception $exception) {
							$result['error']   = true;
							$result['message'] = '上传文件处理出错';
							return ['result' => false, 'error' => $exception, 'error_record' => $result];
						}
					}
				});
			});
		}

		return ['result' => true, 'error_record' => $result];
	}

    /**
     * 卡号白名单导入
     *
     * @param $import
     * @return array
     */
    public function cardWhiteListImport($import): array
    {
        $row         = 0;
        $addCount    = 0;
        $updateCount = 0;
        $repeatCount = 0;
        $errorCount  = 0;
        $errorCards = [];
        $errorRecord = ['error' => false, 'message' => ''];
        foreach ($import->url as $vo) {
            try {
                $safePath = escapeshellarg(public_path('/data/cardWhiteList') . '/' . $vo);
                $output =  exec(public_path('go/' . $this->goFileName) . ' CWL ' . $safePath );
                if (!$output) {
                    $this->log->warning("go 执行执行失败");
                    $errorRecord['error'] = true;
                    $errorRecord['message'] = 'go 执行执行失败';
                    return $errorRecord;
                }

                $this->log->info("Go执行原始输出: " . $output);

                $res = json_decode($output, true);
                if (isset($res['result']) && !$res['result']) {
                    $errorRecord['message'] = isset($res['msg']) ?? '';
                    $this->log->warning("go 执行错误:{$res['msg']}");
                    continue;
                }

                if (isset($res['result'])) {
                    $row         += $res['row'];
                    $addCount    += $res['addCount'];
                    $updateCount += $res['updateCount'];
                    $repeatCount += $res['repeatCount'];
                    $errorCount  += $res['errorCount'];
                    if (count($res['errorCards'] ?? [])){
                        $errorCards   = array_merge($errorCards,['失败文件：'.$vo],$res['errorCards']);
                    }
                }
            } catch (\Throwable $exception) {
                $this->log->warning("执行错误:{$exception->getMessage()}");
                $errorRecord['message'] = $exception->getMessage();
                $errorRecord['error'] = true;
                return $errorRecord;
            }
        }


        $msg = sprintf('本次导入总数：%s，成功上传条数：%s，重复条数：%s, 失败条数：%s', $row, $addCount + $updateCount,$repeatCount, $errorCount);
        if (count($errorCards)){
            $msg .= sprintf('<br />失败数据：<br />%s', implode(',<br />',$errorCards));
        }


        return ['result' => true, 'msg' => $msg, 'error_record' => $errorRecord];
    }

    /**
     * 黑名单导入
     *
     * @param $import
     * @param $log
     * @return array
     */
    public function blackListImport($import)
    {
        $handleBlack       = [];
        $tempBlack         = [];
        $row               = 0;
        $typesMap          = array_flip(MerchantBlacklist::$typesMap);
        $merchantBlacklist = MerchantBlacklist::where('source', MerchantBlacklist::SOURCE_ARTIFICIAL)->get()->pluck('id', 'limit_value');
		$errorRecord       = ['error' => false, 'message' => ''];
        foreach ($import->url as $vo) {
            $file = public_path('/data/blackList') . '/' . $vo;

			if (($handle = fopen($file, "r")) === FALSE) {
				$errorRecord['error']   = true;
				$errorRecord['message'] = '读取文件失败';
				continue;
			}

            while (($blackList = fgetcsv($handle, 1000, ",")) !== FALSE) {
                $row++;
                // 处理中文
                $blackList[0] = mb_convert_encoding($blackList[0], "UTF-8", "GBK");
                $blackList[1] = trim($blackList[1]);

                $type  = $typesMap[$blackList[0]] ?? '';
                $value = $blackList[1] ?? '';

                if ((empty($type) && $type !== 0) && empty($value)) {
                    continue;
                }

                if (!isset($handleBlack[$value]) && !isset($merchantBlacklist[$value])) {
                    $handleBlack[$value] = [
                        'limit_type'  => $type,
                        'limit_value' => $value,
                        'source'      => MerchantBlacklist::SOURCE_ARTIFICIAL,
                        'created_at'  => now(),
                        'updated_at'  => now(),
                    ];
                    $tempBlack[$type][$value] = $value;
                }
            }
            fclose($handle);
        }

        foreach ($tempBlack as $type => $blackData) {
            // 新增风险交易数据
            $blacklistService = new BlacklistService();
            $blacklistService->queryOrder($blackData, $type);
        }

        return $this->dataAdd($handleBlack, ['table' => 'merchant_blacklist', 'row' => $row, 'repeat' => 'limit_value', 'error_record' => $errorRecord]);
    }

    /**
     * 导入数据添加
     *
     * @param array $data
     * @param array $customize
     * @return array
	 */
    private function dataAdd($data, $customize)
    {
        if (empty($data)) {
			return ['result' => false, 'error_record' => ['error' => true, 'message' => '导入数据为空！']];
        }

        $dataTemp    = array_chunk($data, 800);
        $success     = 0;
		$errorRecord = $customize['error_record'];
        foreach ($dataTemp as $vo) {
            if (isset($customize['repeat'])) {
                // 数据去重
                $repeat   = array_column($vo, $customize['repeat']);
                $tempData = DB::table($customize['table'])->whereIn($customize['repeat'], $repeat)
                    ->get()->pluck($customize['repeat'], $customize['repeat'])->toArray();

                if (!empty($tempData)) {
                    $vo = array_diff_key($vo, $tempData);
                }
            }

			if ($customize['table'] == 'merchant_blacklist') {
				$count = DB::table($customize['table'])->insertOrIgnore($vo);
				$success += $count;
			}
        }

        $this->log->info('本次导入总数：' . $customize['row'] . '，成功条数：' . $success . '，失败条数：' . ($customize['row'] - $success));
        dispatch(new SendSlsLog(
            ['message' => '本次导入总数：' . $customize['row'] . '，成功条数：' . $success . '，失败条数：' . ($customize['row'] - $success)],
            [],
            'info',
            'task'
        ));

		return ['result' => true, 'error_record' => $errorRecord];
    }

    public function defFunction($import)
    {
        $this->log->info('导入任务执行失败,无效的类型 id:' . $import->id);
        dispatch(new SendSlsLog(
            ['message' => '导入任务执行失败,无效的类型 id:' . $import->id],
            [],
            'info',
            'task'
        ));
    }

	// 如果新增成功，维护商户卡白名单文件CSV，append
	// protected function appendCardWhiteListedMidCSV($cardWhiteListed)
	// {
	// 	DealCardWhiteListedMidCSV::dispatch([
	// 		'card_number'  => DES3::decrypt($cardWhiteListed['card_number']),
	// 		'cc_type' 	   => $cardWhiteListed['cc_type'],
	// 		'created_at'   => $cardWhiteListed['created_at'],
	// 		'merchant_ids' => $cardWhiteListed['merchant_ids'],
	// 	], [], 'append');
	// }
}
