<?php

namespace App\Console\Commands;

use App\Classes\Supports\Logger;
use App\Jobs\SendSlsLog;
use App\Models\Order;
use App\Models\StatOrder;
use App\Models\StatOrderFail;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class StatOrderTask extends Command
{
    protected $signature = 'task:statOrderTask';

    protected $description = 'Transaction statistics';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        set_time_limit(0);

        // 初始化日志
        $log    = new Logger();
        $config = ['file' => storage_path('logs/consoleTask.log')];
        $log->setConfig($config);

        $log->info("成功率及失败构成交易信息统计开始");
        dispatch(new SendSlsLog(
            ['message' => '成功率及失败构成交易信息统计开始'],
            [],
            'info',
            'task'
        ));


        $time  = time();
        $hourM = date('H:i', $time);
        if ($hourM >= '00:00' && $hourM <= '00:05') {
            $time = strtotime(date('Y-m-d 23:59:59', strtotime('-1 days', $time)));
        }

        $this->startOrderTask($time,$log);

    }

    public function startOrderTask($time, $log)
    {
        $date      = date('Y-m-d', $time);
        $hour      = date('H', $time);
        $orderList = [];

        $approvedNumber = [
            'up'  => 0,
            'ins' => 0,
        ];

        $failNumber = [
            'up'  => 0,
            'ins' => 0,
        ];

        $orderType = [Order::TYPES_SALE, Order::TYPES_CAPTURE];

        //获取成功的订单
        $approved = DB::table('orders as o')
            ->leftJoin('order_cards as c', 'o.card_id', '=', 'c.id')
            ->leftJoin('order_addresses as a', 'o.address_id', '=', 'a.id')
            ->leftJoin('order_settlements as s', 'o.order_id', '=', 's.order_id')
            ->select('o.order_id', 'o.status', 'o.type', 'o.completed_at', 'o.merchant_id', 'o.business_id', 'o.channel_id',
                'o.card_bill', 'o.url_id', 'o.d_mcc_id', 'o.currency', 'o.amount', 'o.url_name', 'o.is_3d', 'a.ship_email', 'c.cc_type', 'c.card_country', 's.amount_usd', 's.payment_amount_usd')
            ->where('o.status', '=', Order::STATUS_APPROVED)
            ->whereIn('o.type', $orderType)
            ->whereBetween('o.completed_at', [$date . ' 00:00:00', $date . ' ' . $hour . ':59:59'])
            ->get();

        $excludeApprove = [];
        foreach ($approved as $value) {
            $value = (array)$value;

            $mid       = $value['merchant_id'];
            $bid       = $value['business_id'];
            $channelId = $value['channel_id'];
            $cardBill  = $value['card_bill'];
            $urlId     = $value['url_id'];
            $dMccId    = $value['d_mcc_id'];
            $currency  = $value['currency'];
            $ccType    = $value['cc_type'];
            $country   = $value['card_country'];
            $amount    = $value['amount'];
            $urlName   = $value['url_name'];
            $shipEmail = $value['ship_email'];

            $excludeApprove[$bid][$amount][$urlName][$shipEmail] = $value['completed_at'];

            $orderList[$mid][$bid][$channelId][$cardBill][$urlId][$dMccId][$currency][$ccType][$country]['approved'][] = $value;
        }

        //获取失败的订单
        $failList = DB::table('orders as o')
            ->leftJoin('order_cards as c', 'o.card_id', '=', 'c.id')
            ->leftJoin('order_addresses as a', 'o.address_id', '=', 'a.id')
            ->select('o.order_id', 'o.status', 'o.type', 'o.completed_at', 'o.merchant_id', 'o.business_id', 'o.channel_id',
                'o.card_bill', 'o.url_id', 'o.d_mcc_id', 'o.currency', 'o.amount', 'o.url_name', 'o.is_3d', 'o.code', 'o.result',
                'a.ship_email', 'c.cc_type', 'c.card_country', DB::raw("CONCAT(amount, url_name, a.ship_email, '{$date}') fail"))
            ->whereIn('o.status', [Order::STATUS_DECLINED, Order::STATUS_EXPIRED])
            ->where('o.risk_type', '=', Order::RISK_TYPE_PASS)
            ->whereBetween('o.completed_at', [$date . ' 00:00:00', $date . ' ' . $hour . ':59:59'])
            ->groupBy('o.order_id', 'o.business_id', 'fail')
            ->get();

        foreach ($failList as $value) {
            $value = (array)$value;

            $bid       = $value['business_id'];
            $amount    = $value['amount'];
            $urlName   = $value['url_name'];
            $shipEmail = $value['ship_email'];

            //去除成功前60分钟的失败
            $approvedTime = $excludeApprove[$bid][$amount][$urlName][$shipEmail] ?? '';
            if ($approvedTime) {
                $approvedTime = strtotime($approvedTime);
                $failTime     = strtotime($value['completed_at']);

                if ($approvedTime - 3600 <= $failTime && $failTime < $approvedTime) {
                    continue;
                }
            }

            $mid       = $value['merchant_id'];
            $channelId = $value['channel_id'];
            $cardBill  = $value['card_bill'];
            $urlId     = $value['url_id'];
            $dMccId    = $value['d_mcc_id'];
            $currency  = $value['currency'];
            $ccType    = $value['cc_type'];
            $country   = $value['card_country'];

            $orderList[$mid][$bid][$channelId][$cardBill][$urlId][$dMccId][$currency][$ccType][$country]['fail'][] = $value;
        }

        //已统计数据
        $where      = [
            ['date_stat', '=', $date],
            ['date_stat_hour', '=', $hour]
        ];
        $tmpApprove = StatOrder::where($where)->get();

        $StatOrderData = [];
        foreach ($tmpApprove as $value) {
            $bid       = $value['business_id'];
            $channelId = $value['channel_id'];
            $cardBill  = $value['card_bill'];
            $urlId     = $value['url_id'];
            $dMccId    = $value['d_mcc_id'];
            $currency  = $value['currency'];
            $ccType    = $value['cc_type'];
            $country   = $value['card_country'];

            $StatOrderData[$bid][$channelId][$cardBill][$urlId][$dMccId][$currency][$ccType][$country] = $value;
        }

        $tmpFail = StatOrderFail::where($where)->get();

        $StatOrderFailData = [];
        foreach ($tmpFail as $value) {
            $bid       = $value['business_id'];
            $channelId = $value['channel_id'];
            $cardBill  = $value['card_bill'];
            $urlId     = $value['url_id'];
            $dMccId    = $value['d_mcc_id'];
            $currency  = $value['currency'];
            $ccType    = $value['cc_type'];
            $country   = $value['card_country'];
            $code      = $value['fail_code'];

            $StatOrderFailData[$bid][$channelId][$cardBill][$urlId][$dMccId][$currency][$ccType][$country][$code] = $value;
        }

        if (!count($orderList)) {
            $log->info("暂无成功率及失败构成交易信息数据统计");
            dispatch(new SendSlsLog(
                ['message' => '暂无成功率及失败构成交易信息数据统计'],
                [],
                'info',
                'task'
            ));

            return;
        }

        //统计
        foreach ($orderList as $mid => $bList) {
            foreach ($bList as $bid => $channel) {
                foreach ($channel as $channelId => $res) {
                    foreach ($res as $cardBill => $value) {
                        foreach ($value as $url => $merchant) {
                            foreach ($merchant as $dMccId => $payment) {
                                foreach ($payment as $currency => $card) {
                                    foreach ($card as $ccType => $end) {
                                        foreach ($end as $country => $item) {
                                            $data = [
                                                'merchant_id'    => $mid,
                                                'business_id'    => $bid,
                                                'channel_id'     => $channelId,
                                                'card_bill'      => $cardBill,
                                                'url_id'         => $url,
                                                'd_mcc_id'       => $dMccId,
                                                'currency'       => $currency,
                                                'cc_type'        => $ccType,
                                                'card_country'   => $country,
                                                'date_stat'      => $date,
                                                'date_stat_hour' => $hour,
                                            ];

                                            $this->addStatOrder($data, $item, $StatOrderData, $StatOrderFailData, $approvedNumber, $failNumber);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        $log->info(sprintf('成功率统计 日期:%s 更新:%s 插入:%s 条', $date, $approvedNumber['up'], $approvedNumber['ins']));
        dispatch(new SendSlsLog(
            ['message' => sprintf('成功率统计 日期:%s 更新:%s 插入:%s 条', $date, $approvedNumber['up'], $approvedNumber['ins'])],
            [],
            'info',
            'task'
        ));

        $log->info(sprintf('失败构成统计 日期:%s 更新:%s 插入:%s 条', $date, $failNumber['up'], $failNumber['ins']));
        dispatch(new SendSlsLog(
            ['message' => sprintf('失败构成统计 日期:%s 更新:%s 插入:%s 条', $date, $failNumber['up'], $failNumber['ins'])],
            [],
            'info',
            'task'
        ));

        $log->info("成功率及失败构成交易信息统计结束");
        dispatch(new SendSlsLog(
            ['message' => '成功率及失败构成交易信息统计结束'],
            [],
            'info',
            'task'
        ));
    }

    public function addStatOrder($data, $item, $StatOrderData, $StatOrderFailData, &$approvedNumber, &$failNumber)
    {
        $approved = $item['approved'] ?? [];
        $fail     = $item['fail'] ?? [];

        //成功统计
        $qty         = 0;
        $qty3d       = 0;
		$amountUsd   = 0.00;
		$amountUsd3d = 0.00;
		$paymentAmountUsd   = 0.00;
		$paymentAmountUsd3d = 0.00;

        foreach ($approved as $value) {

            //3d统计
            if ($value['is_3d']) {
                $qty3d++;
				$amountUsd3d += $value['amount_usd'];
				$paymentAmountUsd3d += $value['payment_amount_usd'];
            }

            $qty++;
			$amountUsd += $value['amount_usd'];
			$paymentAmountUsd += $value['payment_amount_usd'];
        }

        //失败统计
        $failQty      = 0;
        $expriedQty   = 0;
        $failCodeData = [];


        foreach ($fail as $value) {
            $failQty++;

            if ($value['status'] == Order::STATUS_EXPIRED) {
                $expriedQty++;
            }

            $failCodeData[$value['code']][] = $value;
        }

        $statOrder = [
            'qty'                	=> $qty,
            'qty_3d'             	=> $qty3d,
			'amount_usd'         	=> $amountUsd,
			'amount_usd_3d'      	=> $amountUsd3d,
			'payment_amount_usd'    => $paymentAmountUsd,
			'payment_amount_usd_3d' => $paymentAmountUsd3d,
            'unique_fail_qty'       => $failQty,
            'unique_expried_qty'    => $expriedQty,
        ];
        $statOrder = Arr::collapse([$statOrder, $data]);

        $statOrderExist = $StatOrderData[$data['business_id']][$data['channel_id']][$data['card_bill']][$data['url_id']][$data['d_mcc_id']][$data['currency']][$data['cc_type']][$data['card_country']] ?? '';
        if ($statOrderExist) {
            $approvedNumber['up']++;
            StatOrder::where('id', $statOrderExist['id'])->update($statOrder);
        } else {
            $approvedNumber['ins']++;
            StatOrder::create($statOrder);
        }

        $failCodeDataList = [];
        foreach ($failCodeData as $code => $value) {

            $statOrderFails = [
                'fail_code'    => $code,
                'fail_results' => $value[0]['result'] ?? '',
                'qty'          => count($value),
                'created_at'   => date('Y-m-d H:i:s'),
            ];

            $statOrderFails = Arr::collapse([$statOrderFails, $data]);

            $statOrderExist = $StatOrderFailData[$data['business_id']][$data['channel_id']][$data['card_bill']][$data['url_id']][$data['d_mcc_id']][$data['currency']][$data['cc_type']][$data['card_country']][$code] ?? '';
            if ($statOrderExist) {
                unset($statOrderFails['created_at']);
                $failNumber['up']++;
                StatOrderFail::where('id', $statOrderExist['id'])->update($statOrderFails);
            } else {
                $failNumber['ins']++;
                $failCodeDataList[] = $statOrderFails;
            }
        }

        if (count($failCodeDataList)) {
            StatOrderFail::insert($failCodeDataList);
        }

    }

}