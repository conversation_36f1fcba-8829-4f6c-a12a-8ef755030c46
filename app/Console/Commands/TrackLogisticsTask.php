<?php

namespace App\Console\Commands;

use App\Classes\Supports\Logger;
use App\Jobs\SendSlsLog;
use App\Models\OrderTrack;
use App\Services\TrackService;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Collection;

class TrackLogisticsTask extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'task:trackLogisticsTask';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        set_time_limit(0);

        // 初始化日志
        $log    = new Logger();
        $config = ['file' => storage_path('logs/consoleTask.log')];
        $log->setConfig($config);

        // 需要更新的状态
        $apiStatusList = array(
            OrderTrack::API_RESULT_STATUS_ERROR,
            OrderTrack::API_RESULT_STATUS_LAUNCHED,
            OrderTrack::API_RESULT_STATUS_WAITING
        );

        // 每6小时更新一次
        $orderTrackList = OrderTrack::where('is_delivered', '<>', '1')
            ->where('tracking_type', '<>', 'other')
            ->whereIn('api_result_status', $apiStatusList)
            ->where('api_result_date', '<=', date('Y-m-d H:i:s', strtotime('-6 hour')))
            ->get()
            ->forPage(1, 3000);

        if (empty($orderTrackList)) {
            $log->info("没有需要更新物流的运单信息");
            dispatch(new SendSlsLog(
                ['message' => '没有需要更新物流的运单信息'],
                [],
                'info',
                'task'
            ));
            return false;
        }

        $trackTypeList = [];
        $maxRow        = 20;
        $failure       = 0;
        $success       = 0;
        $startTime     = microtime(true);

        foreach ($orderTrackList as $value) {
            $useTime = number_format(microtime(true) - $startTime, 2, '.', '');

            if ($useTime > 4 * 60) {
                $log->info("处理时长超过4分钟,已中断");
                dispatch(new SendSlsLog(
                    ['message' => '处理时长超过4分钟,已中断'],
                    [],
                    'info',
                    'task'
                ));
                break;
            }

            $trackTypeList[$value['api_type']][] = $value;  // 重组数据

            if (count($trackTypeList[$value['api_type']]) >= $maxRow) { // 接口一次最多允许40条，复合运单号的原因，设置为20条
                $requestData = Collection::make($trackTypeList[$value['api_type']]);
                $resultData  = TrackService::track($requestData);

                if ($resultData['error']) {
                    $failure += $maxRow;
                    $log->info('运单物流追踪任务执行输出', [$resultData['msg']]);
                    dispatch(new SendSlsLog(
                        ['message' => '运单物流追踪任务执行输出'],
                        [$resultData['msg']],
                        'info',
                        'task'
                    ));
                } else {
                    $success += $maxRow;
                }

                // 防止频繁请求
                sleep(1);

                // 清空数组
                unset($trackTypeList[$value['api_type']]);
            }
        }

        // 未完成部分
        foreach ($trackTypeList as $apiType => $list) {
            $useTime = number_format(microtime(true) - $startTime, 2, '.', '');

            if ($useTime > 4 * 60) {
                $log->info("处理时长超过4分钟,已中断");
                dispatch(new SendSlsLog(
                    ['message' => '处理时长超过4分钟,已中断'],
                    [$resultData['msg']],
                    'info',
                    'task'
                ));
                break;
            }

            $requestData = Collection::make($list);
            $resultData  = TrackService::track($requestData);

            if ($resultData['error']) {
                $failure += count($list);
                $log->info('运单物流追踪任务执行输出', [$resultData['msg']]);
                dispatch(new SendSlsLog(
                    ['message' => '运单物流追踪任务执行输出'],
                    [$resultData['msg']],
                    'info',
                    'task'
                ));
            } else {
                $success += count($list);
            }
        }

        // 打印日志
        $log->info("运单物流追踪任务执行输出", [sprintf('运单物流追踪成功[%s]条,失败[%s]条', $success, $failure)]);
        dispatch(new SendSlsLog(
            ['message' => '运单物流追踪任务执行输出'],
            [sprintf('运单物流追踪成功[%s]条,失败[%s]条', $success, $failure)],
            'info',
            'task'
        ));
    }
}
