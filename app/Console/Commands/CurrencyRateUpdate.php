<?php

namespace App\Console\Commands;

use App\Classes\Pay\Contracts\Support;
use App\Classes\Supports\Logger;
use App\Classes\Supports\Traits\HasHttpRequest;
use App\Jobs\SendSlsLog;
use App\Models\DirectoryCurrency;
use Illuminate\Console\Command;

class CurrencyRateUpdate extends Command
{
	use HasHttpRequest;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'task:currencyRateUpdate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Currency Rate Update';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
	    // 在命令行打印一行信息
	    $this->info("汇率更新开始");

	    // 初始化日志
	    $log    = new Logger();
	    $config = ['file' => storage_path('logs/consoleTask.log')];
	    $log->setConfig($config);

	    // 聚合url
	    $url = 'http://op.juhe.cn/onebox/exchange/currency?from=USD&to=%s&key=********************************';

	    // 获取汇率数据
	    $directoryCurrencyList = DirectoryCurrency::all();

	    // 计数
	    $success = 0;
	    $total   = 0;

	    if (!empty($directoryCurrencyList)) {
		    foreach ($directoryCurrencyList as $directoryCurrency) {
				// 跳过特殊币种
				if (in_array($directoryCurrency->code, ['T'])) {
					continue;
				}

			    $rate   = '0.00';
			    $result = $this->get(sprintf($url, $directoryCurrency->code));
			    $outPut = "[更新汇率API请求情况] : " . json_encode($result);
			    $this->info($outPut);
			    $log->info("聚合接口返回原始数据", [$outPut]);
				dispatch(new SendSlsLog(
					['message' => '聚合接口返回原始数据'],
					[$outPut],
					'info',
					'task'
				));
				
			    $total++;

			    if (isset($result['result'][0]['result'])) {
				    $rate = $result['result'][0]['result'];
			    }

			    if ($rate == '0.00') {
				    continue;
			    }

			    // 判断汇率阈值是否超过5%
			    $maxCurrencyRate = Support::amount_format($directoryCurrency->rate * (1 + 0.05), 4);
			    $minCurrencyRate = Support::amount_format($directoryCurrency->rate * (1 - 0.05), 4);

			    if ($rate > $maxCurrencyRate || $rate < $minCurrencyRate) {
				    // 添加告警通知 todo...
				    $msg = sprintf('%s:币种汇率超过阈值, %s : %s', $directoryCurrency->code, $minCurrencyRate, $maxCurrencyRate);
				    $this->info($msg);
				    $log->info("币种汇率超过阈值", [$msg]);
					dispatch(new SendSlsLog(
						['message' => '币种汇率超过阈值'],
						[$msg],
						'info',
						'task'
					));
					
				    continue;
			    }

			    $directoryCurrency->rate = $rate;
			    $directoryCurrency->save();
			    $success++;
		    }

		    $outPut = "汇率更新总数:" . $total . ', 成功汇率更新总数:' . $success;
		    $this->info($outPut);
		    $log->info("汇率更新执行输出", [$outPut]);
			dispatch(new SendSlsLog(
				['message' => '汇率更新执行输出'],
				[$outPut],
				'info',
				'task'
			));
	    }

	    $this->info("汇率更新结束");
    }
}
