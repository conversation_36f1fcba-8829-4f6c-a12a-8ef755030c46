<?php

namespace App\Console\Commands;

use Illuminate\Database\Eloquent\Builder;
use App\Models\ChannelPid;
use App\Models\Order;
use App\Models\OrderSettlement;
use App\Services\SettlementService;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;

class TempUpdateOrderSettlements extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'task:tempUpdateOrderSettlements';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        set_time_limit(0);
        // 开始时打印详细日志
        $this->info("交易勾兑数据任务开始");

        $order = Order::where('order_id','2024120507180186540')->where('merchant_id','168612884744097')->first();

        $this->transactionSettle($order);

        // 结束时打印详细日志 记录更新渠道以及数量
        $this->info("交易勾兑数据任务结束");
    }

    protected function transactionSettle($order)
    {
        if (empty($order->order_id)) {
            return;
        }

        $settleOrder       = OrderSettlement::firstWhere('order_id', '=', $order->order_id);
        $settleDate        = SettlementService::getSettleDate($order->business_id);
        $depositDate       = SettlementService::getDepositSettleDates($order->business_id, $order->completed_at, $settleDate, $order->card->cc_type);
        $expectDepositDate = $depositDate;
        //保证金返还日期小于当前结算时间
        if (!empty($depositDate) && Carbon::parse($depositDate)->lt(Carbon::parse($settleDate))) {
            $expectDepositDate = date('Y-m-d', strtotime("{$settleDate} +1 days"));
        }

        $statusList = [Order::STATUS_DECLINED, Order::STATUS_APPROVED, Order::STATUS_EXPIRED];

        //抛投更新
        if (!empty($settleOrder) && $settleOrder['channel'] != $order['channel'] && in_array($order['status'], $statusList)) {

            // 补充数据计算结算金额
            $order->payment_amount_usd = $order->paymentOrder->amount;
            $order->payment_currency   = $order->paymentOrder->currency;

            $settleAmount = SettlementService::getSettleAmount($order, $order->card->card_number);

            //去掉补充数据
            unset($order->payment_amount_usd);
            unset($order->payment_currency);

            $settleOrder->channel_id              = $order->channel_id;
            $settleOrder->channel                 = $order->channel;
            $settleOrder->order_number            = $order->order_number;
            $settleOrder->type                    = $order->type;
            $settleOrder->status                  = $order->status == Order::STATUS_APPROVED ? '1' : '0';
            $settleOrder->currency                = $order->currency;
            $settleOrder->amount                  = $order->amount;
            $settleOrder->settle_currency         = $settleAmount['settle_currency'];
            $settleOrder->settle_amount           = $settleAmount['settle_amount'];
            $settleOrder->payment_currency        = $order->paymentOrder->currency;
            $settleOrder->payment_amount          = $order->paymentOrder->amount;
            $settleOrder->payment_settle_currency = $order->paymentOrder->currency;
            $settleOrder->payment_settle_amount   = $order->paymentOrder->amount;
            $settleOrder->rate                    = $settleAmount['rate'];
            $settleOrder->payment_at              = $order->created_at;
            $settleOrder->completed_at            = $order->completed_at;
            $settleOrder->amount_usd              = $settleAmount['amount_usd'];
            $settleOrder->payment_amount_usd      = $settleAmount['payment_amount_usd'];


            $settleOrder->save();
        }

        // 交易差错时,需要更新交易结算数据
        if (!empty($settleOrder)) {
            if (
                $settleOrder->status == OrderSettlement::ARRIVAL_STATUS_WAITING
                && $order->status == Order::STATUS_APPROVED
            ) {
                $settleOrder->status       = $order->status;
                $settleOrder->completed_at = $order->completed_at;

                if (
                    $settleOrder->blend_status == OrderSettlement::BLEND_STATUS_SUCCESS
                    || $settleOrder->settle_at != $settleDate
                ) {
                    $settleOrder->settle_at          = $settleDate;
                    $settleOrder->original_settle_at = $settleDate;
                    $settleOrder->blend_status       = OrderSettlement::BLEND_STATUS_WAITING;
                    $settleOrder->blend_by           = 'system';
                    $settleOrder->is_settle          = OrderSettlement::IS_SETTLE_WAITING;
                }

                $settleOrder->save(); // 更新结算数据
            }

            return;
        }

        if (!in_array($order->status, $statusList)) {
            return;
        }

        // 补充数据计算结算金额
        $order->payment_amount_usd = $order->paymentOrder->amount;
        $order->payment_currency   = $order->paymentOrder->currency;

        // 添加结算信息
        $settleAmount      = SettlementService::getSettleAmount($order, $order->card->card_number);
        //获取条款历史id
        $businessHistoryId = SettlementService::getBusinessHistoryId($order->business_id);

        //去掉补充数据
        unset($order->payment_amount_usd);
        unset($order->payment_currency);

        $data = [
            'order_id'                => $order->order_id,
            'payment_order_id'        => $order->paymentOrder->payment_order_id,
            'parent_order_id'         => $order->parent_order_id ?? '0',
            'business_history_id'     => $businessHistoryId,
            'merchant_id'             => $order->merchant_id,
            'cc_type'                 => $order->card->cc_type,
            'business_id'             => $order->business_id,
            'merchant_name'           => $order->merchant_name,
            'channel_id'              => $order->channel_id,
            'channel'                 => $order->channel,
            'order_number'            => $order->order_number,
            'type'                    => $order->type,
            'status'                  => $order->status == Order::STATUS_APPROVED ? '1' : '0',
            'currency'                => $order->currency,
            'amount'                  => $order->amount,
            'settle_currency'         => $settleAmount['settle_currency'],
            'settle_amount'           => $settleAmount['settle_amount'],
            'payment_currency'        => $order->paymentOrder->currency,
            'payment_amount'          => $order->paymentOrder->amount,
            'payment_settle_currency' => $order->paymentOrder->currency,
            'payment_settle_amount'   => $order->paymentOrder->amount,
            'rate'                    => $settleAmount['rate'],
            'original_settle_at'      => $settleDate,
            'settle_at'               => $settleDate,
            'deposit_return_at'       => $expectDepositDate,
            'deposit_expect_return_at'=> $depositDate,
            'payment_at'              => $order->created_at,
            'completed_at'            => $order->completed_at,
            'amount_usd'              => $settleAmount['amount_usd'],
            'payment_amount_usd'      => $settleAmount['payment_amount_usd']
        ];

        // 更新到缓存
        if ($order->status == Order::STATUS_APPROVED && in_array($order->type, [Order::TYPES_SALE, Order::TYPES_CAPTURE])) {
            $pidData = ChannelPid::whereHas('channel', function (Builder $query) use ($order) {
                $query->where('channel', $order->channel);
            })->with('channelSupplier:id,timezone,effective_date')->first();

            $timezone = config('app.timezone');
            if ($pidData->channelSupplier->timezone){
                // 使用timezone前需要判断当前app配置里的时区时间是否大于effective_date时间
                $effectiveDate = Carbon::parse($pidData->channelSupplier->effective_date);
                if (!$effectiveDate->gt(now())) {
                    $timezone = $pidData->channelSupplier->timezone;
                }
            }
            $time = Carbon::now($timezone);

            $channelKey     = 'Transaction_' . MD5($order->channel) . '_Qty_And_Amount_' . $time->format('Ym');
            $channelLockKey = $channelKey . '_Lock';
            Cache::lock($channelLockKey, 10)->block(5, function () use ($channelKey, $settleAmount, $order) {
                $transactionChannel = Cache::get($channelKey) ?? [];
                $this->handelTransactionCacheData($transactionChannel, $settleAmount['payment_amount_usd'], 'day_amount', $order->card->cc_type);
                $this->handelTransactionCacheData($transactionChannel, 1, 'day_qty', $order->card->cc_type);
                $this->handelTransactionCacheData($transactionChannel, $settleAmount['payment_amount_usd'], 'month_amount', $order->card->cc_type);
                $this->handelTransactionCacheData($transactionChannel, 1, 'month_qty', $order->card->cc_type);

                foreach ($transactionChannel as $limitType => &$channelAmounts) {
                    if ($limitType == 'day_amount' || $limitType == 'month_amount') {
                        foreach ($channelAmounts as $ccType => $amount) {
                            $channelAmounts[$ccType] = amount_format($amount);
                        }
                    }
                }

                Cache::put($channelKey, $transactionChannel, 31 * 24 * 60 * 60);
            });

            // BID限额缓存
            $bidKey     = 'Transaction_' . $order->business_id . '_Amount_' . date('Ymd');
            $bidLockKey = $bidKey . '_Lock';
            Cache::lock($bidLockKey, 10)->block(5, function () use ($bidKey, $settleAmount, $order) {
                $transactionBusiness = Cache::get($bidKey) ?? [];
                $this->handelTransactionCacheData($transactionBusiness, $settleAmount['amount_usd'], 'day_amount', $order->card->cc_type);
                $this->handelTransactionCacheData($transactionBusiness, $settleAmount['amount_usd'], 'month_amount', $order->card->cc_type);

                foreach ($transactionBusiness as &$amounts) {
                    foreach ($amounts as $ccType => $amount) {
                        $amounts[$ccType] = amount_format($amount);
                    }
                }

                Cache::put($bidKey, $transactionBusiness, 24 * 60 * 60);
            });

            //PID限额缓存
            $pidKey     = 'Transaction_' . $pidData->channel_pid . '_Amount_' . $time->format('Ym');
            $pidLockKey = $pidKey . '_Lock';
            Cache::lock($pidLockKey, 10)->block(5, function () use ($pidKey, $settleAmount, $order) {
                $transactionPid = Cache::get($pidKey) ?? [];
                $this->handelTransactionCacheData($transactionPid, $settleAmount['payment_amount_usd'], 'day_amount', $order->card->cc_type);
                $this->handelTransactionCacheData($transactionPid, $settleAmount['payment_amount_usd'], 'month_amount', $order->card->cc_type);

                foreach ($transactionPid as &$pidAmounts) {
                    foreach ($pidAmounts as $ccType => $amount) {
                        $pidAmounts[$ccType] = amount_format($amount);
                    }
                }

                Cache::put($pidKey, $transactionPid, 31 * 24 * 60 * 60);
            });

            // 同卡同金额同网站120s成功交易（163953459255967 J卡为15秒）
            $key      = 'Order_Card_Mask_Amount_Url_Status_' . $order->card->card_mask . $order->amount . $order->url_name . Order::STATUS_APPROVED;
            $interval = ($order->merchant_id == '163953459255967' && $order->card->cc_type == 'J') ? '15' : '120';
            Cache::add($key, 1, $interval);
        }

        OrderSettlement::firstOrCreate($data);
    }

    private function handelTransactionCacheData(&$cacheData, $number, string $handelType, string $ccType): void
    {
        if (!isset($cacheData[$handelType]['*'])) {
            $cacheData[$handelType]['*'] = 0;
        }

        if (!isset($cacheData[$handelType][$ccType])) {
            $cacheData[$handelType][$ccType] = 0;
        }

        $cacheData[$handelType]['*']     += $number;
        $cacheData[$handelType][$ccType] += $number;
    }
}
