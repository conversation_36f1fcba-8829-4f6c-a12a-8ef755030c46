<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Illuminate\Support\Facades\Artisan;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        //
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {

		// 获取当前的队列服务器名称
		$queueServerName = config('horizon.queue_server_name');

		// 获取当前任务服务器对应的horizon队列配置
		$horizonQueue = config('horizon_queue');

		// 默认队列
		$defaultQueue = '';
		if ($queueServerName == 'default' || !($queueServerName && isset($horizonQueue[$queueServerName]))) {
			$defaultQueue = 'default';
		}

		// 时区设置
		$timezone = 'PRC';

		// 每日重启队列
		$schedule->command('horizon:terminate')->timezone($timezone)->daily();

		/*
		|----------------------------------------------
		| 结算类
		|----------------------------------------------
		| settle
		| 支持的队列参考配置文件 config/horizon_queue.php
		| 目前额外添加了一些统计类、资金类的任务
		*/
		if ($defaultQueue || $queueServerName == 'settle') {
			// ------ 结算模块 ------
			// 商户结算
			$schedule->call(function () use ($defaultQueue) {
				Artisan::queue('settle:merchantSettle')->onQueue($defaultQueue ?: 'settle');
			})->name('settle:merchantSettle')->withoutOverlapping()->dailyAt('11:30');

			// 商户结算核对任务
			$schedule->call(function () use ($defaultQueue) {
				Artisan::queue('settle:merchantSettleCheck')->onQueue($defaultQueue ?: 'settle');
			})->name('settle:merchantSettleCheck')->withoutOverlapping()->dailyAt('13:30');

			// 失败交易自动勾兑
			$schedule->call(function () use ($defaultQueue) {
				Artisan::queue('settle:failedOrderAutoBlend')->onQueue($defaultQueue ?: 'settle');
			})->name('settle:failedOrderAutoBlend')->withoutOverlapping()->dailyAt('00:30');

			// 拒付工单生成罚金结算任务
			$schedule->call(function () use ($defaultQueue) {
				Artisan::queue('settle:chargebackSettle')->onQueue($defaultQueue ?: 'settle');
			})->name('settle:chargebackSettle')->withoutOverlapping()->monthlyOn(1, '08:30');

			// 释放冻结资金通知
			$schedule->call(function () use ($defaultQueue) {
				Artisan::queue('task:releaseFrozenAmount')->onQueue($defaultQueue ?: 'work');
			})->name('task:releaseFrozenAmount')->withoutOverlapping()->dailyAt('09:30');

			// 账单标识和PID交易计数更新
			$schedule->call(function () use ($defaultQueue) {
				Artisan::queue('task:channelLimitUpdate')->onQueue($defaultQueue ?: 'work');
			})->name('task:channelLimitUpdate')->withoutOverlapping()->hourly();

			// Business交易金额更新
			$schedule->call(function () use ($defaultQueue) {
				Artisan::queue('task:businessMoneyAstrict')->onQueue($defaultQueue ?: 'work');
			})->name('task:businessMoneyAstrict')->withoutOverlapping()->hourly();

			// 汇率币种更新
			$schedule->call(function () use ($defaultQueue) {
				Artisan::queue('task:currencyRateUpdate')->onQueue($defaultQueue ?: 'work');
			})->name('task:currencyRateUpdate')->hourly();

			// ------ 统计类 ------
			// 持卡人交易信息统计
			$schedule->call(function () use ($defaultQueue) {
				Artisan::queue('task:sataOrderCardHolders')->onQueue($defaultQueue ?: 'work');
			})->name('task:sataOrderCardHolders')->withoutOverlapping()->dailyAt('01:00');

			// 拒付统计
			$schedule->call(function () use ($defaultQueue) {
				Artisan::queue('task:sataOrderChargebacks')->onQueue($defaultQueue ?: 'work');
			})->name('task:sataOrderChargebacks')->withoutOverlapping()->dailyAt('01:20');
			$schedule->call(function () use ($defaultQueue) {
				Artisan::queue('task:sataOrderChargebacks')->onQueue($defaultQueue ?: 'work');
			})->name('task:sataOrderChargebacks')->withoutOverlapping()->twiceDaily(9, 15);

			// 交易统计核对校验
			$schedule->call(function () use ($defaultQueue) {
				Artisan::queue('task:statOrderTaskCheck')->onQueue($defaultQueue ?: 'work');
			})->name('task:statOrderTaskCheck')->withoutOverlapping()->dailyAt('03:00');

			// 每日清空卡库计数
			$schedule->call(function () use ($defaultQueue) {
				Artisan::queue('task:cardLibraryClearCount')->onQueue($defaultQueue ?: 'work');
			})->name('task:cardLibraryClearCount')->daily();

            // ------ 对账单 ------
            // coshine 日对账单导出
            $schedule->call(function () use ($defaultQueue) {
                Artisan::queue('task:coshineDailyBillExport')->onQueue($defaultQueue ?: 'work');
            })->name('task:coshineDailyBillExport')->daily();

			// ------ 导入、导出和商户结算报表 ------
			// 将导入、导出和商户结算报表单独移出队列，目前因时间太长，超出队列执行时间和内存限制，导致报错
			// 导出任务
			$schedule->command('task:exportTask')->name('task:exportTask')->withoutOverlapping()->onOneServer()->everyFiveMinutes();

			// 生成商户结算报表
			$schedule->command('settle:merchantReportFormTask')->timezone($timezone)->dailyAt('12:30')->withoutOverlapping();
		}

		/*
		|----------------------------------------------
		| 业务类
		|----------------------------------------------
		| business
		| 支持的队列参考配置文件 config/horizon_queue.php
		*/
		if ($defaultQueue || $queueServerName == 'business') {
			// ------ 通知类 ------
			// 订单异步通知
			$schedule->call(function () use ($defaultQueue) {
				Artisan::queue('notice:orderNoticeTask')->onQueue($defaultQueue ?: 'default');
			})->name('notice:orderNoticeTask')->withoutOverlapping()->everyFiveMinutes();

			// 订单 received 状态告警查询
			$schedule->call(function () use ($defaultQueue) {
				Artisan::queue('task:orderReceivedWarning')->onQueue($defaultQueue ?: 'default');
			})->name('task:orderReceivedWarning')->withoutOverlapping()->hourly();

			// 本地支付订单 received 状态告警查询
			$schedule->call(function () use ($defaultQueue) {
				Artisan::queue('task:LocalOrderReceivedWarning')->onQueue($defaultQueue ?: 'default');
			})->name('task:LocalOrderReceivedWarning')->withoutOverlapping()->hourly();

			// CDRN完成通知
			$schedule->call(function () use ($defaultQueue) {
				Artisan::queue('task:caseNotice')->onQueue($defaultQueue ?: 'default');
			})->name('task:caseNotice')->withoutOverlapping()->everyFiveMinutes();

			// ------ 交易模块 ------
			// 拒付调控任务
			$schedule->call(function () use ($defaultQueue) {
				Artisan::queue('task:dishonourControl')->onQueue($defaultQueue ?: 'default');
			})->name('task:dishonourControl')->everyMinute();

			// 风控缓存校准任务
			$schedule->call(function () use ($defaultQueue) {
				Artisan::queue('task:riskCacheSet')->onQueue($defaultQueue ?: 'default');
			})->name('task:riskCacheSet')->withoutOverlapping()->hourly();

			// 渠道风控缓存校准任务
			$schedule->call(function () use ($defaultQueue) {
				Artisan::queue('task:riskCacheSetChannel')->onQueue($defaultQueue ?: 'default');
			})->name('task:riskCacheSetChannel')->withoutOverlapping()->hourly();

			// 订单检索
			$schedule->call(function () use ($defaultQueue) {
				Artisan::queue('task:orderQuery')->onQueue($defaultQueue ?: 'default');
			})->name('task:orderQuery')->withoutOverlapping()->everyFifteenMinutes();

			// 退款自动查询 每5分钟一次、但是查询第二次开始是10分钟的N次方、N是查询次数。一天最多三次
			$schedule->call(function () use ($defaultQueue) {
				Artisan::queue('task:refundQuery')->onQueue($defaultQueue ?: 'default');
			})->name('task:refundQuery')->withoutOverlapping()->everyFiveMinutes();

			// 本地支付订单检索
			$schedule->call(function () use ($defaultQueue) {
				Artisan::queue('task:localOrderQuery')->onQueue($defaultQueue ?: 'default');
			})->name('task:localOrderQuery')->withoutOverlapping()->everyFifteenMinutes();

			// 渠道提现订单检索
			$schedule->call(function () use ($defaultQueue) {
				Artisan::queue('task:withdrawalQuery')->onQueue($defaultQueue ?: 'default');
			})->name('task:withdrawalQuery')->withoutOverlapping()->everyThirtyMinutes();

			// 渠道拒付交易预警检索
			$schedule->call(function () use ($defaultQueue) {
				Artisan::queue('task:chargebackCaseQuery')->onQueue($defaultQueue ?: 'default');
			})->name('task:chargebackCaseQuery')->withoutOverlapping()->everyThirtyMinutes();

			// ------ 卡模块 ------
			// 创建虚拟卡
			$schedule->call(function () use ($defaultQueue) {
				Artisan::queue('card:applyCardTask')->onQueue($defaultQueue ?: 'card');
			})->name('card:applyCardTask')->everyFiveMinutes();

			// 卡工单查询
			$schedule->call(function () use ($defaultQueue) {
				Artisan::queue('card:dealCardTicket')->onQueue($defaultQueue ?: 'card');
			})->name('card:dealCardTicket')->everyFiveMinutes();

			// 卡结算查询
			$schedule->call(function () use ($defaultQueue) {
				Artisan::queue('card:inquirySettlement')->onQueue($defaultQueue ?: 'card');
			})->name('card:inquirySettlement')->withoutOverlapping()->dailyAt('10:00');

			// 卡交易查询
			$schedule->call(function () use ($defaultQueue) {
				Artisan::queue('card:inquiryTrade')->onQueue($defaultQueue ?: 'card');
			})->name('card:inquiryTrade')->withoutOverlapping()->dailyAt('09:00');

			// 卡余额查询任务
			$schedule->call(function () use ($defaultQueue) {
				Artisan::queue('card:inquireBalance')->onQueue($defaultQueue ?: 'card');
			})->name('card:inquireBalance')->withoutOverlapping()->twiceDaily(9, 15);

			// 渠道白名单自动上传
			$schedule->call(function () use ($defaultQueue) {
				Artisan::queue('task:channelWhiteListRequest')->onQueue($defaultQueue ?: 'card');
			})->name('task:channelWhiteListRequest')->withoutOverlapping()->dailyAt('01:00');

			// 表格任务生成
			$schedule->call(function () use ($defaultQueue) {
				Artisan::queue('task:TabularTask')->onQueue($defaultQueue ?: 'card');
			})->name('task:TabularTask')->withoutOverlapping()->dailyAt('08:30');

			// 囤卡卡批次处理
			$schedule->call(function () use ($defaultQueue) {
				Artisan::queue('card:applyCardBatch')->onQueue($defaultQueue ?: 'card');
			})->name('card:applyCardBatch')->everyFiveMinutes();

			// 同步卡批次处理
			$schedule->call(function () use ($defaultQueue) {
				Artisan::queue('card:applyCardBatchSync')->onQueue($defaultQueue ?: 'card');
			})->name('card:applyCardBatchSync')->everyFiveMinutes();

			// 卡查询任务
			$schedule->call(function () use ($defaultQueue) {
				Artisan::queue('card:inquireCardTask')->onQueue($defaultQueue ?: 'card');
			})->name('card:inquireCardTask')->everyTwoMinutes();


            // 虚拟卡黑名单设置
            $schedule->call(function () use ($defaultQueue) {
                Artisan::queue('card:setCardFraud')->onQueue($defaultQueue ?: 'card');
            })->name('card:setCardFraud')->everyMinute();

            // 虚拟卡黑名单交易扫描
            $schedule->call(function () use ($defaultQueue) {
                Artisan::queue('card:scanCardBlack')->onQueue($defaultQueue ?: 'card');
            })->name('card:scanCardBlack')->everyMinute();

            // 虚拟卡欺诈交易扫描
            $schedule->call(function () use ($defaultQueue) {
                Artisan::queue('card:scanFraudTrade')->onQueue($defaultQueue ?: 'card');
            })->name('card:scanFraudTrade')->everyFiveMinutes();

			// ------ 运单模块 ------
			// 接口运单添加、更新
			$schedule->call(function () use ($defaultQueue) {
				Artisan::queue('task:trackTask')->onQueue($defaultQueue ?: 'default');
			})->name('task:trackTask')->withoutOverlapping()->everyFiveMinutes();

			// 运单追踪
			$schedule->call(function () use ($defaultQueue) {
				Artisan::queue('task:trackLogisticsTask')->onQueue($defaultQueue ?: 'default');
			})->name('task:trackLogisticsTask')->withoutOverlapping()->everyFiveMinutes();

			// ------ 统计类 ------
			// 成功率及失败构成交易信息统计
			$schedule->call(function () use ($defaultQueue) {
				Artisan::queue('task:statOrderTask')->onQueue($defaultQueue ?: 'default');
			})->name('task:statOrderTask')->withoutOverlapping()->everyFiveMinutes();

			// ------ 导入导出 ------
			// 将导入导出单独移出队列，目前因时间太长，超出队列执行时间和内存限制，导致报错
			// 商户导出任务
			$schedule->command('task:merchantExportTask')->name('task:merchantExportTask')->withoutOverlapping()->onOneServer()->everyFiveMinutes();

			// 导入任务
			$schedule->command('task:importTask')->name('task:importTask')->withoutOverlapping()->onOneServer()->everyFiveMinutes();

			// 过期3D订单处理
			$schedule->command('task:processingThreeDExpiredOrders')->everyFiveMinutes()->withoutOverlapping();
		}

		// 统一处理
		foreach ($schedule->events() as $event) {
			// 设置时区
			$event->timezone($timezone);
		}
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
