<?php

namespace App\Merchant\Extensions;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithHeadings;

class MerchantChargebackCaseExportCsv implements WithHeadings
{
    use Exportable;

    protected $order;

    public function __construct(array $order = [])
    {
        $this->order   = $order;
    }

    public function array(): array
    {
        return $this->order;
    }

    public function headings(): array
    {
        return [
            '订单号',
            '商户号',
            'Case Id',
            'MID',
            '商户名',
            '是否拒付',
            '是否退款',
            '创建时间',
        ];
    }
}
