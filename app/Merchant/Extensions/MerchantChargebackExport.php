<?php

namespace App\Merchant\Extensions;

use App\Models\Chargeback;
use App\Models\ChargebackHistory;
use App\Models\OrderRelation;
use App\Services\ExportService;
use Illuminate\Database\Eloquent\Builder;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithCustomChunkSize;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\DefaultValueBinder;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;

class MerchantChargebackExport extends DefaultValueBinder implements WithCustomValueBinder, FromQuery, WithHeadings, WithCustomChunkSize, WithMapping, ShouldAutoSize
{
    use Exportable;

    protected $filter;
    protected $query;
    protected $exportTitle;
    protected $lang;

    public function __construct(array $filter = [], $query)
    {
        $this->filter      = $filter['filter'] ?? [];
        $this->query       = $query;
        $this->exportTitle = $filter['export_title'] ?? [];
        $this->lang        = $filter['lang'] ?? 'zh_CN';
    }

    public function chunkSize(): int
    {
        return 500;
    }

    public function bindValue(Cell $cell, $value)
    {
        $cell->setValueExplicit($value, DataType::TYPE_STRING);

        return true;
    }

    public function query()
    {
        return $this->query;
    }

    /**
     * Add conditions to grid model.
     *
     * @param Builder $query
     * @param array $conditions
     *
     * @return Builder
     */
    public function addConditions(Builder $query, array $conditions)
    {
        sort($conditions);

        foreach ($conditions as $condition) {
            call_user_func_array([$query, key($condition)], current($condition));
        }
        return $query;
    }

    public function map($row): array
    {
        return self::getMap($row, $this->lang);
    }

    public static function getMap($row, $lang): array
    {
        return [
            $row->order['order_id'] ?? '',
            $row->order['order_number'] ?? '',
            $row->chargeback_id ?? '',
            $row->order['merchant_id'] ?? '',
            $row->order['merchant_name'] ?? '',
            $row->chargebackHistory['chargeback_code'] ?? '',
            ChargebackHistory::$typesMap[$row->chargebackHistory['type'] ?? 0] ?? '未知',
            $row->order['currency'] ?? '',
            $row->order['amount'] ?? '',
            ExportService::getFieldTranslation(OrderRelation::$isRefundMap[$row->orderRelation['is_refund']] ?? '未知', $lang),
            ExportService::getFieldTranslation(Chargeback::$statusMap[$row->status] ?? '未知', $lang),
            $row->order['url_name'] ?? '',
            $row->order->address['bill_email'] ?? '-',
            $row->chargebackHistory['arn'] ?? '',
            $row->order['created_at'] ?? '',
            $row->chargebackHistory['chargeback_at'] ?? '',
            $row->chargeback_deadline ?? ''
        ];
    }

    public function headings(): array
    {
        return $this->exportTitle;
    }
}
