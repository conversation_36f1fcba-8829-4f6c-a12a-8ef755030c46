<?php

namespace App\Merchant\Extensions;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;

class MerchantCardVirtualExportCsv implements WithHeadings,FromArray
{
    use Exportable;

    protected $order;

    public function __construct(array $order = [])
    {
        $this->order = $order;
    }

    public function array(): array
    {
        return $this->order;
    }

    public function headings(): array
    {
        return ['卡唯一标识', '卡号', 'cvv', '有效年', '有效月'];
    }
}
