<?php

namespace App\Merchant\Extensions;

use App\Models\ChargebackCase;
use App\Models\OrderRelation;
use Illuminate\Database\Eloquent\Builder;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithCustomChunkSize;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\DefaultValueBinder;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;

class MerchantChargebackCaseExport extends DefaultValueBinder implements WithCustomValueBinder, FromQuery, WithHeadings, WithCustomChunkSize, WithMapping, ShouldAutoSize
{
    use Exportable;

    protected $filter;
    protected $query;

    public function __construct(array $filter = [], $query)
    {
        $this->filter = $filter['filter'] ?? [];
        $this->query   = $query;
    }

    public function chunkSize(): int
    {
        return 500;
    }

    public function bindValue(Cell $cell, $value)
    {
        $cell->setValueExplicit($value, DataType::TYPE_STRING);

        return true;
    }

    public function query()
    {
        return $this->query;
    }

    /**
     * Add conditions to grid model.
     *
     * @param Builder $query
     * @param array $conditions
     *
     * @return Builder
     */
    public function addConditions(Builder $query, array $conditions)
    {
        sort($conditions);

        foreach ($conditions as $condition) {
            call_user_func_array([$query, key($condition)], current($condition));
        }
        return $query;
    }

    public function map($row): array
    {
        return self::getMap($row);
    }

    public static function getMap($row): array
    {
        return [
            $row['order_id'] ?? '',
            $row['order_number'] ?? '',
            $row['case_id'] ?? '',
            $row['merchant_id'] ?? '',
            $row['merchant_name'] ?? '',
            OrderRelation::$isChargebackMap[$row['orderRelation']['is_chargeback']] ?? '',
            OrderRelation::$isRefundMap[$row['orderRelation']['is_refund']] ?? '',
            $row['created_at'] ?? '',
        ];
    }

    public function headings(): array
    {
        return [
            '订单号',
            '商户号',
            'Case Id',
            'MID',
            '商户名',
            '是否拒付',
            '是否退款',
            '创建时间',
        ];
    }
}
