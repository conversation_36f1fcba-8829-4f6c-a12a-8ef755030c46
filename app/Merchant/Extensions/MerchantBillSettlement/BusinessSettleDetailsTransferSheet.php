<?php

namespace App\Merchant\Extensions\MerchantBillSettlement;

use App\Models\SettleDetail;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithCustomChunkSize;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\DefaultValueBinder;
use Maatwebsite\Excel\Concerns\WithStyles;
use App\Exports\Traits\BusinessSettleDetails;

class BusinessSettleDetailsTransferSheet extends DefaultValueBinder implements WithTitle, WithMapping, WithColumnWidths, WithCustomValueBinder, WithCustomChunkSize, WithHeadings, FromQuery, WithStyles
{
    use Exportable;
    use BusinessSettleDetails;

    protected $filter;
    protected $lang;

    public function __construct(array $filter = [], string $lang = 'zh_CN')
    {
        $this->setFilter($filter);
        $this->lang = $lang;
    }

    public function title(): string
    {
        return 'BID结转明细';
    }

    public function headings(): array
    {
        if ($this->lang == 'en') {
            return [
                'BID', 
                'MID',
                'Merchant Name',
                'Settlement Amount',
                'Settlement Currency',
                'Transaction Amount',
                'Transaction Currency',
                'Settlement ratio',
                'Type',
                'Note',
                'Transfer Time',
                'Settlement Date'
            ];  
        }

        return [
            'BID', 
            'MID',
            '商户名称' . PHP_EOL . '(Merchant Name)',
            '结算金额' . PHP_EOL . '(Settlement Amount)',
            '结算币种' . PHP_EOL . '(Settlement Currency)',
            '订单金额' . PHP_EOL . '(Transaction Amount)',
            '订单币种' . PHP_EOL . '(Transaction Currency)',
            '结算比例' . PHP_EOL . '(Settlement ratio)',
            '金额类型' . PHP_EOL . '(Type)',
            '备注' . PHP_EOL . '(Note)',
            '结转完成时间' . PHP_EOL . '(Transfer Time)',
            '结算日期' . PHP_EOL . '(Settlement Date)'
        ];
    }

    public function map($row): array
    {
        return [
            $row['business_id'] ?? '',
            $row['merchant_id'] ?? '',
            $row['merchant_name'] ?? '',
            $row['settle_amount'] ?? '',
            $row['settle_currency'] ?? '',
            $row['amount'] ?? '',
            $row['currency'] ?? '',
            $row['rate'] ?? '',
            isset($row['amount_type']) ? (SettleDetail::$amountTypeMap[$row['amount_type']] ?? '-') : '',
            $row['remarks'] ?? '',
            $row['order_complete_at'] ?? '',
            $row['settle_at'] ?? ''
        ];
    }

    /**
     * @param array $filter
     */
    public function setFilter(array $filter = []): void
    {
        $filter[]['where'] = ['settle_amount', '<>', '0'];
        // 结转
        $filter[]['whereIn'] = ['amount_type', [SettleDetail::AMOUNT_TYPE_50, SettleDetail::AMOUNT_TYPE_51, SettleDetail::AMOUNT_TYPE_52]];

        sort($filter);

        $this->filter = $filter;
    }
}
