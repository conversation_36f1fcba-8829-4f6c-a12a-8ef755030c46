<?php

namespace App\Merchant\Extensions\MerchantBillSettlement;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithCustomChunkSize;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\DefaultValueBinder;
use App\Exports\Traits\SettleTotal;

class SettleTotalSheet extends DefaultValueBinder implements WithTitle, WithCustomValueBinder, WithCustomChunkSize, FromCollection, WithStyles
{
    use Exportable;
    use SettleTotal;
}
