<?php

namespace App\Merchant\Extensions\MerchantBillSettlement;

use App\Models\SettleDetail;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithCustomChunkSize;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\DefaultValueBinder;
use Maatwebsite\Excel\Concerns\WithStyles;
use App\Exports\Traits\BusinessSettleDetails;
use App\Models\OrderSettlement;

class BusinessSettleDetailsSheet extends DefaultValueBinder implements WithTitle, WithMapping, WithColumnWidths, WithCustomValueBinder, WithCustomChunkSize, WithHeadings, FromQuery, WithStyles
{
    use Exportable;
    use BusinessSettleDetails;

    protected $filter;
    protected $lang;
    protected $orderSettlement = [];

    public function __construct(array $filter = [], string $lang = 'zh_CN')
    {
        $this->setFilter($filter);
        $this->lang = $lang;
    }

    public function title(): string
    {
        return 'BID结算明细';
    }

    public function headings(): array
    {
        if ($this->lang == 'en') {
            return [
                'BID', 
                'MID',
                'Merchant Name',
                'Transaction ID',
                'Merchant Transaction ID',
                'Card type',
                'Settlement Amount',
                'Settlement Currency',
                'Transaction Amount',
                'Transaction Currency',
                'Settlement ratio',
                'Type',
                'Note',
                'Transaction Time',
                'Settlement Date'
            ]; 
        }

        return [
            'BID', 
            'MID',
            '商户名称' . PHP_EOL . '(Merchant Name)',
            '关联ID' . PHP_EOL . '(Transaction ID)',
            '商户订单号' . PHP_EOL . '(Merchant Transaction ID)',
            '卡种' . PHP_EOL . '(Card type)',
            '结算金额' . PHP_EOL . '(Settlement Amount)',
            '结算币种' . PHP_EOL . '(Settlement Currency)',
            '订单金额' . PHP_EOL . '(Transaction Amount)',
            '订单币种' . PHP_EOL . '(Transaction Currency)',
            '结算比例' . PHP_EOL . '(Settlement ratio)',
            '金额类型' . PHP_EOL . '(Type)',
            '备注' . PHP_EOL . '(Note)',
            '交易完成时间' . PHP_EOL . '(Transaction Time)',
            '结算日期' . PHP_EOL . '(Settlement Date)'
        ];
    }

    public function prepareRows($rows)
    {
        $tempRows              = $rows->toArray();
        $relatedId             = array_unique(array_column($tempRows, 'related_id'));
        $this->orderSettlement = OrderSettlement::query()->whereIn('order_id', $relatedId)->pluck('cc_type', 'order_id')->toArray();

        return $rows;
    }

    public function map($row): array
    {
        if (isset($row['amount_type'])) {
            if (in_array($row['amount_type'], [SettleDetail::AMOUNT_TYPE_03, SettleDetail::AMOUNT_TYPE_22])) {
                $amountTypeNote = $row['settle_amount'] > 0 ? '返还' . SettleDetail::$amountTypeMap[$row['amount_type']] :  '收取' . SettleDetail::$amountTypeMap[$row['amount_type']];
            } else {
                $amountTypeNote = SettleDetail::$amountTypeMap[$row['amount_type']] ?? '-';
            }
        } else {
            $amountTypeNote = '';
        }

        return [
            $row['business_id'] ?? '',
            $row['merchant_id'] ?? '',
            $row['merchant_name'] ?? '',
            $row['related_id'] ?? '',
            $row['order_number'] ?? '',
            $this->orderSettlement[$row['related_id']] ?? '-',
            $row['settle_amount'] ?? '',
            $row['settle_currency'] ?? '',
            $row['amount'] ?? '',
            $row['currency'] ?? '',
            $row['rate'] ?? '',
            $amountTypeNote,
            $row['remarks'] ?? '',
            $row['order_complete_at'] ?? '',
            $row['settle_at'] ?? ''
        ];
    }

    /**
     * @param array $filter
     */
    public function setFilter(array $filter = []): void
    {
        $filter[]['where'] = ['settle_amount', '<>', '0'];
        $filter[]['where'] = ['settle_at', '<=', date('Y-m-d')];
        //结算明细
        $filter[]['whereNotIn'] = ['amount_type', [SettleDetail::AMOUNT_TYPE_50, SettleDetail::AMOUNT_TYPE_51, SettleDetail::AMOUNT_TYPE_52]];

        sort($filter);

        $this->filter = $filter;
    }
}
