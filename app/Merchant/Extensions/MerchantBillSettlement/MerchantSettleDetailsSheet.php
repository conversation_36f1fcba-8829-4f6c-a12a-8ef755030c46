<?php

namespace App\Merchant\Extensions\MerchantBillSettlement;

use App\Models\MerchantBusiness;
use App\Models\SettleDetailMerchant;
use Illuminate\Database\Eloquent\Builder;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithCustomChunkSize;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\DefaultValueBinder;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;


class MerchantSettleDetailsSheet extends DefaultValueBinder implements WithTitle, WithMapping, WithColumnWidths, WithCustomValueBinder, WithCustomChunkSize, FromQuery, WithHeadings, WithStyles
{
    use Exportable;

    protected $filter;
    protected $lang;

    public function __construct(array $filter = [], string $lang = 'zh_CN')
    {
        $this->setFilter($filter);
        $this->lang = $lang;
    }

    public function title(): string
    {
        return 'MID结算明细';
    }

    public function columnWidths(): array
    {
        return ['A' => 17, 'B' => 18, 'C' => 26, 'D' => 20, 'E' => 24, 'F' => 25, 'G' => 35, 'H' => 20];
    }

    public function chunkSize(): int
    {
        return 500;
    }

    public function bindValue(Cell $cell, $value)
    {
        $cell->setValueExplicit($value, DataType::TYPE_STRING);

        return true;
    }

    public function headings(): array
    {
        if ($this->lang == 'en') {
            return [
                'MID',
                'Merchant Name',
                'Type',
                'Settlement Amount',
                'Settlement Currency',
                'Current Reserve Amount',
                'Release of Current Reserve Amount',
                'Settlement Date'
            ];  
        }

        return [
            'MID',
            '商户名称' . PHP_EOL . '（Merchant Name)',
            '金额类型' . PHP_EOL . '(Type)',
            '结算金额' . PHP_EOL . '(Settlement Amount)',
            '结算币种' . PHP_EOL . '(Settlement Currency)',
            '当期保证金入账' . PHP_EOL . '(Current Reserve Amount)',
            '当期保证金返还' . PHP_EOL . '(Release of Current Reserve Amount)',
            '结算日期' . PHP_EOL . '(Settlement Date)'
        ];
    }

    public function map($row): array
    {
        return [
            $row->merchant_id,
            $row->merchant_name,
            (SettleDetailMerchant::$amountTypeMap[$row->amount_type] ?? '-') . 
            (SettleDetailMerchant::$amountTypeNoteMap[$row->amount_type] ? '(' . SettleDetailMerchant::$amountTypeNoteMap[$row->amount_type] . ')' : ''),
            $row->settle_amount,
            $row->settle_currency,
            $row->in_deposit_amount,
            $row->out_deposit_amount,
            $row->settle_at,
        ];
    }

    public function query()
    {
        return $this->addConditions(SettleDetailMerchant::query(), $this->filter);
    }

    /**
     * Add conditions to grid model.
     *
     * @param Builder $query
     * @param array $conditions
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function addConditions(Builder $query, array $conditions)
    {
        foreach ($conditions as $condition) {
            call_user_func_array([$query, key($condition)], current($condition));
        }

        return $query;
    }

    /**
     * @param array $filter
     */
    public function setFilter(array $filter): void
    {
        // 过滤
        $bMerchantId = 0;
        $merchantId  = 0;

        foreach ($filter as $key => $value) {
            if (isset($value['where'][0]) && $value['where'][0] == 'business_id') {
                $business    = MerchantBusiness::find($value['where'][1]);
                $bMerchantId = empty($business) ? -1 : $business->merchant_id;

                // 删除bid条件
                unset($filter[$key]);
            }

            if (isset($value['where'][0]) && $value['where'][0] == 'merchant_id') {
                $merchantId = $value['where'][1];
            }
        }

        if (!empty($merchantId) && !empty($bMerchantId) && $merchantId != $bMerchantId) {
            $filter[]['where'] = $merchantId != 0 ? ['merchant_id', 0] : ['merchant_id', $bMerchantId];
        }

        sort($filter);

        $this->filter = $filter;
    }

    public function styles(Worksheet $sheet)
    {
        $maxEnd   = $sheet->getHighestColumn();
        $sheet->getStyle('A1:' . $maxEnd . '1')->getAlignment()->setWrapText(true); // 字体设置
        $sheet->getRowDimension(1)->setRowHeight(40); // 设置行高
    }
}
