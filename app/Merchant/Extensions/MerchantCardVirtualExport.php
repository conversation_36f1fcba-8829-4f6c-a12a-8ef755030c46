<?php

namespace App\Merchant\Extensions;

use App\Models\CardVirtual as CardVirtualModel;
use Illuminate\Database\Eloquent\Builder;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithCustomChunkSize;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\DefaultValueBinder;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use DES3;

class MerchantCardVirtualExport extends DefaultValueBinder implements WithCustomValueBinder, FromQuery, WithHeadings, WithCustomChunkSize, WithMapping, ShouldAutoSize
{
    use Exportable;

    protected $filter;
    protected $headings;
    protected $query;

    public function __construct(array $filter = [], $query)
    {
        $this->filter = $filter['filter'] ?? [];
        $this->query  = $query;
    }

    public function chunkSize(): int
    {
        return 500;
    }

    public function query()
    {
        return $this->query;
    }

    public function bindValue(Cell $cell, $value)
    {
        $cell->setValueExplicit($value, DataType::TYPE_STRING);

        return true;
    }

    /**
     * Add conditions to grid model.
     *
     * @param Builder $query
     * @param array $conditions
     *
     * @return Builder
     */
    public function addConditions(Builder $query, array $conditions)
    {
        sort($conditions);

        foreach ($conditions as $condition) {
            call_user_func_array([$query, key($condition)], current($condition));
        }
        return $query;
    }

    public function map($row): array
    {
        return self::getMap($row);
    }

    public static function getMap($row): array
    {
        return [
            $row->trans_id ?? '',
			DES3::decrypt($row->card_number,env('DES3_CARD_VIRTUAL')) ?? '',
			DES3::decrypt($row->cvv,env('DES3_CARD_VIRTUAL')) ?? '',
            DES3::decrypt($row->expiration_year,env('DES3_CARD_VIRTUAL')) ?? '',
            DES3::decrypt($row->expiration_month,env('DES3_CARD_VIRTUAL')) ?? ''
        ];
    }

    public function headings(): array
    {
        return ['卡唯一标识', '卡号', 'cvv', '有效年', '有效月'];
    }
}
