<?php

namespace App\Merchant\Extensions;

use App\Models\Refund;
use Illuminate\Database\Eloquent\Builder;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithCustomChunkSize;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\DefaultValueBinder;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;

class MerchantRefundExport extends DefaultValueBinder implements WithCustomValueBinder, FromQuery, WithHeadings, WithCustomChunkSize, WithMapping, ShouldAutoSize
{
    use Exportable;

    protected $filter;
    protected $query;
    protected $exportTitle;

    public function __construct(array $filter = [], $query)
    {
        $this->filter      = $filter['filter'] ?? [];
        $this->query        = $query;
        $this->exportTitle = $filter['export_title'] ?? [];
    }

    public function chunkSize(): int
    {
        return 500;
    }

    public function query()
    {
        return $this->query;
    }

    public function bindValue(Cell $cell, $value)
    {
        $cell->setValueExplicit($value, DataType::TYPE_STRING);

        return true;
    }

    /**
     * Add conditions to grid model.
     *
     * @param Builder $query
     * @param array $conditions
     *
     * @return Builder
     */
    public function addConditions(Builder $query, array $conditions)
    {
        sort($conditions);

        foreach ($conditions as $condition) {
            call_user_func_array([$query, key($condition)], current($condition));
        }
        return $query;
    }

    public function map($row): array
    {
        return self::getMap($row);
    }

    public static function getMap($row): array
    {
        return [
            $row->refund_id ?? '',
            $row->order_id ?? '',
            $row->order->order_number ?? '',
            $row->order->currency ?? '',
            $row->order->amount ?? '',
            $row->currency ?? '',
            $row->amount ?? '',
            Refund::$statusMap[$row->status] ?? '未知',
            $row->code ?? '',
            $row->result ?? '',
            $row->remark ?? '',
            $row->created_at ?? '',
            $row->updated_at ?? '',
        ];
    }

    public function headings(): array
    {
        return $this->exportTitle;
    }
}
