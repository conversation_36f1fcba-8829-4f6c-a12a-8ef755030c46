<?php

namespace App\Merchant\Extensions;

use App\Models\Order;
use App\Models\Order as OrderModel;
use App\Models\OrderRelation;
use Illuminate\Database\Eloquent\Builder;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithCustomChunkSize;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\DefaultValueBinder;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;

class MerchantTrackOrderExport extends DefaultValueBinder implements WithCustomValueBinder, FromQuery, WithHeadings, WithCustomChunkSize, WithMapping, ShouldAutoSize
{
    use Exportable;

    protected $filter;
    protected $headings;
    protected $query;

    public function __construct(array $filter = [], $query)
    {
        $this->filter = $filter['filter'] ?? [];
        $this->query  = $query;
    }

    public function chunkSize(): int
    {
        return 500;
    }

    public function query()
    {
        return $this->query;
    }

    public function bindValue(Cell $cell, $value)
    {
        $cell->setValueExplicit($value, DataType::TYPE_STRING);

        return true;
    }

    /**
     * Add conditions to grid model.
     *
     * @param Builder $query
     * @param array $conditions
     *
     * @return Builder
     */
    public function addConditions(Builder $query, array $conditions)
    {
        sort($conditions);

        foreach ($conditions as $condition) {
            call_user_func_array([$query, key($condition)], current($condition));
        }
        return $query;
    }

    public function map($row): array
    {
        return self::getMap($row);
    }

    public static function getMap($row): array
    {
        return [
            $row->order_id ?? '',
            $row->order_number ?? '',
            $row->url_name ?? '',
            $row->currency ?? '',
            $row->amount ?? '',
            Order::$typesMap[$row->type] ?? '',
            $row->created_at ?? ''
        ];
    }

    public function headings(): array
    {
        return ['交易订单号', '商户订单号', '交易网址', '支付币种', '支付金额', '交易类型', '支付时间'];
    }
}
