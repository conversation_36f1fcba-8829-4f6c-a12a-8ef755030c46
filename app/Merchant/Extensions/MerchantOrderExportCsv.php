<?php

namespace App\Merchant\Extensions;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;

class MerchantOrderExportCsv implements FromArray, WithHeadings
{
    use Exportable;

    protected $order;
    protected $exportTitle;

    public function __construct(array $order = [], array $filter = [])
    {
        $this->order       = $order;
        $this->exportTitle = $filter['export_title'] ?? [];
    }

    public function array(): array
    {
        return $this->order;
    }

    public function headings(): array
    {
        return $this->exportTitle;
    }
}
