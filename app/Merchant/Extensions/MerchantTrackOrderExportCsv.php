<?php

namespace App\Merchant\Extensions;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;

class MerchantTrackOrderExportCsv implements WithHeadings,FromArray
{
    use Exportable;

    protected $order;

    public function __construct(array $order = [])
    {
        $this->order = $order;
    }

    public function array(): array
    {
        return $this->order;
    }

    public function headings(): array
    {
        return ['交易订单号', '商户订单号', '交易网址', '支付币种', '支付金额', '交易类型', '支付时间'];
    }
}
