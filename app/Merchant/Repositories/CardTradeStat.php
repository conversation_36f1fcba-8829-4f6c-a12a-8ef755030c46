<?php

namespace App\Merchant\Repositories;

use App\Models\CardTransaction;
use App\Services\CardTransactionService;
use Dcat\Admin\Repositories\EloquentRepository;
use Dcat\Admin\Grid;

class CardTradeStat extends EloquentRepository
{
    /**
     * Model.
     *
     * @var string
     */
    protected $eloquentClass = CardTransaction::class;

    public function get(Grid\Model $model)
    {
        // 获取搜索条件
        $searchData       = $this->relations;
        $statisticsData[] = CardTransactionService::getCardTradeStatAmountData($searchData, "table");
        return $statisticsData;
    }
}