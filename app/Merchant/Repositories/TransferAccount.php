<?php

namespace App\Merchant\Repositories;

use App\Models\TransferAccount as Model;
use Dcat\Admin\Repositories\EloquentRepository;
use Dcat\Admin\Form;

class TransferAccount extends EloquentRepository
{
    /**
     * Model.
     *
     * @var string
     */
    protected $eloquentClass = Model::class;

    public function store(Form $form)
    {
        // 获取待新增的数据
        $row  = $form->updates();

        $type = $row['type'];
        $arr  = ['cardholder', 'bank_name', 'bank_address', 'bank_account', 'convert_currency'];

        foreach ($arr as $vo) {
            if (isset($row[$vo . $type])) {
                $row[$vo] = $row[$vo . $type];
            } else {
                $row[$vo] = '';
            }
        }

        //重新组装新增数据
        $arr = [
            'type',
            'cardholder_id',
            'cardholder',
            'bank_name',
            'bank_address',
            'bank_account',
            'nickname',
            'convert_currency',
        ];

        if ($type == Model::TYPE_DOMESTIC) {
            $arr[] = 'inter_bank_number';
            $arr[] = 'bank_mobile';
            $arr[] = 'cardholder_id_card';
        } else {
            $arr[] = 'company_address';
            $arr[] = 'swift_iban';
        }

        // 返回新增记录id或bool值
        $insert = [];

        foreach ($arr as $vo) {
            $insert[$vo] = $row[$vo];
        }

        $insert['created_at'] = now();
        $insert['updated_at'] = now();

        try {
            Model::insert($insert);
        } catch (\Exception $e) {
            return false;
        }
        return true;
    }

    public function update(Form $form)
    {
        // 获取待编辑的数据
        $row  = $form->updates();

        $type = $row['type'];
        $arr  = ['cardholder', 'bank_name', 'bank_address', 'bank_account', 'convert_currency'];

        foreach ($arr as $vo) {
            if (isset($row[$vo . $type])) {
                $row[$vo] = $row[$vo . $type];
            } else {
                $row[$vo] = '';  
            }
        }

        //重新组装编辑数据
        $arr = [
            'type',
            'cardholder_id',
            'cardholder',
            'bank_name',
            'bank_address',
            'bank_account',
            'nickname',
            'convert_currency',
        ];

        if ($type == Model::TYPE_DOMESTIC) {
            $arr[] = 'inter_bank_number';
            $arr[] = 'bank_mobile';
            $arr[] = 'cardholder_id_card';
        } else {
            $arr[] = 'company_address';
            $arr[] = 'swift_iban';
        }

        $update = [];

        foreach ($arr as $vo) {
            $update[$vo] = $row[$vo];
        }

        $update['updated_at'] = now();
        $update['status']     = Model::STATUS_EXAMINE;

        try {
            Model::where('id', $form->getKey())->update($update);
        } catch (\Exception $e) {
            return false;
        }

        // 返回成功
        return true;
    }

    public function edit(Form $form): array
    {
        // 获取数据主键值
        $id = $form->getKey();

        $account = Model::where('id', $id)->first()->toArray();

        //组装编辑页面数据
        $arr = ['cardholder', 'bank_name', 'bank_address', 'bank_account', 'convert_currency'];
        foreach ($arr as $vo) {
            $account[$vo . $account['type']] = $account[$vo];
        }

        return $account;
    }
}
