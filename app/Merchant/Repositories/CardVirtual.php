<?php

namespace App\Merchant\Repositories;

use App\Models\CardVirtual as Model;
use Dcat\Admin\Repositories\EloquentRepository;

class CardVirtual extends EloquentRepository
{
    /**
     * Model.
     *
     * @var string
     */
    protected $eloquentClass = Model::class;

    public static $internalMerchantStatusMap = ['停用', '激活', '待处理', '处理中', '已销卡', '待处理', '处理中', '开卡进行中', '冻结', '冻结'];
    public static $internalTypetMap = ['常规卡', '共享卡'];

}
