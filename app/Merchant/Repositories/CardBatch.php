<?php

namespace App\Merchant\Repositories;

use App\Models\CardBatch as Model;
use Dcat\Admin\Repositories\EloquentRepository;

class CardBatch extends EloquentRepository
{
    /**
     * Model.
     *
     * @var string
     */
    protected $eloquentClass = Model::class;

    public static $internalStatusMap = ['待处理', '处理中', '处理失败', '完成'];

    public static $internalTypetMap = ['常规卡', '共享卡'];

    public static function findAvailableNo()
    {
        $no   = date('YmdHis');
        $time = explode(' ', microtime());
        $no   = $no . str_pad(intval($time[0] * 100000), 5, '0', STR_PAD_LEFT);

        return $no;
    }
}
