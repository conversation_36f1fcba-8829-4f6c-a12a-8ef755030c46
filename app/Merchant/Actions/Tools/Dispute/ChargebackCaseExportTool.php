<?php

namespace App\Merchant\Actions\Tools\Dispute;

use App\Classes\Supports\Str;
use App\Models\MerchantDownloadCenter;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Admin;
use Dcat\Admin\Grid\Tools\AbstractTool;
use Dcat\Admin\Traits\HasPermissions;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;

class ChargebackCaseExportTool extends AbstractTool
{
    /**
     * @return string
     */
    public function title()
    {
        return '<i class="feather icon-download"></i> ' . admin_trans_field('导出');
    }

    protected $style = 'btn btn-primary pull-right';

    /**
     * Handle the action request.
     *
     * @param Request $request
     *
     * @return Response
     */
    public function handle(Request $request)
    {
        $user          = Admin::user();
        $queryCriteria = $request->toArray();

        $fileName = 'merchantChargebackCase-' . Str::random(10);
        $center   = MerchantDownloadCenter::create([
            'user_id'         => $user['id'],
            'merchant_id'     => $user['merchant_id'],
            'file_name'       => $fileName,
            'export_identity' => 'merchantChargebackCase',
            'query_criteria'  => $queryCriteria,
        ]);

        if ($center) {
            return $this->response()->success(admin_trans_label('添加导出任务成功'));
        }

        return $this->response()->error(admin_trans_label('添加导出任务失败'));
    }

    /**
     * @return string|array|void
     */
    public function confirm()
    {
        return [admin_trans_label('您确定要导出') . admin_trans_label('拒付预警') . '?', admin_trans_label('数量超过2万会通过csv格式导出') . '！'];
    }

    /**
     * @param Model|Authenticatable|HasPermissions|null $user
     *
     * @return bool
     */
    protected function authorize($user): bool
    {
        return true;
    }

    /**
     * @return array
     */
    protected function parameters()
    {
        return [
            'filter' => $this->parent->filter()->getConditions(),
            'inputs' => $this->parent->filter()->inputs(),
        ];
    }
}
