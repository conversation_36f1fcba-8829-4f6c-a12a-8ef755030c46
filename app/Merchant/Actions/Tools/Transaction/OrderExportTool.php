<?php

namespace App\Merchant\Actions\Tools\Transaction;

use App\Models\MerchantDownloadCenter;
use App\Services\ExportService;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Admin;
use Dcat\Admin\Grid\Tools\AbstractTool;
use Illuminate\Http\Request;

class OrderExportTool extends AbstractTool
{
    protected $exportTitle = [];
    public function __construct($exportTitle = [])
    {
        $this->exportTitle = $exportTitle;

        parent::__construct();
    }

    /**
     * @return string
     */
    public function title()
    {
        return '<i class="feather icon-download"></i> ' . admin_trans('order.fields.export');
    }

    protected $style = 'btn btn-primary grid-refresh btn-mini btn-outline pull-right';

    public function handle(Request $request): Response
    {
        $user          = Admin::user();
        $queryCriteria = $request->toArray();

        if (!$user->isMainAccount()) {
            $queryCriteria['filter']   = $queryCriteria['filter'] ?? [];
            $bid                       = $user->bid->pluck('business_id')->toarray();
            $queryCriteria['filter'][] = ['whereIn' => ['business_id', $bid]];
        }
        // 语言处理
        $queryCriteria['export_title'] = ExportService::getExportTitle($queryCriteria['export_title'], $queryCriteria['lang']);

        $fileName = 'merchantOrder' . date('YmdHms') . random_int(1000, 9999);
        $center   = MerchantDownloadCenter::create([
            'user_id'         => $user['id'],
            'merchant_id'     => $user['merchant_id'],
            'file_name'       => $fileName,
            'export_identity' => 'merchantOrder',
            'query_criteria'  => $queryCriteria,
        ]);

        if ($center) {
            return $this->response()->success(admin_trans('global.labels.添加导出任务成功'));
        }

        return $this->response()->error(admin_trans('global.labels.添加导出任务失败'));
    }

    public function confirm(): array
    {
        return [admin_trans('order.labels.ConfirmExport'), admin_trans('global.labels.数量超过2万会通过csv格式导出')];
    }

    public function parameters(): array
    {
        return [
            'filter'       => $this->parent->filter()->getConditions(),
            'inputs'       => $this->parent->filter()->inputs(),
            'export_title' => $this->exportTitle,
            'lang'         => ExportService::getLang()
        ];
    }
}
