<?php

namespace App\Merchant\Actions\Grid\User;

use App\Models\User as UserModel;
use Dcat\Admin\Grid\RowAction;

class ResetAccount extends RowAction
{
    /**
     * @return string
     */
    public function title()
    {
        return '<span>' . admin_trans_label('ResetAccount') . '</span>';
    }

    public function handle()
    {
        $userRow = UserModel::find($this->getKey());

        if (!$userRow) {
            return $this->response()->error(admin_trans('user.labels.找不到该用户信息，请核对'))->refresh();
        }

        $updateRet = UserModel::where('id', $userRow->id)->update(['google2fa_secret' => Null]);

        if (empty($updateRet)) {
            return $this->response()->error(admin_trans('user.labels.重置绑定失败'))->refresh();
        }

        return $this->response()->success(admin_trans('user.labels.重置绑定成功'))->refresh();
    }

    /**
     * @return string|array|void
     */
    public function confirm()
    {
        return [admin_trans('user.labels.您确认要重置该账号的谷歌认证绑定吗')];
    }
}
