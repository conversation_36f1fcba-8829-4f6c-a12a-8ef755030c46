<?php

namespace App\Merchant\Actions\Grid;

use App\Models\MerchantUrl;
use App\Models\RiskCase;
use Dcat\Admin\Grid\RowAction;
use Illuminate\Support\Facades\Auth;

class RevokeUrl extends RowAction
{
    protected $id;

    public function __construct($id = 0)
    {
        $this->id = $id;
    }

    public function title()
    {
        return '<button class="btn btn-primary btn-shadow btn-change">' . admin_trans('url.labels.revoke') . '</button>';
    }

    public function handle()
    {
        $id = request()->input('id');

        $urlInfo = MerchantUrl::with('case')->find($id);

        if (!$urlInfo || !$urlInfo->case) {
            return $this->response()->error(admin_trans('url.fields.url_info.not_exist'))->refresh();
        }

        if ($urlInfo->merchant_id != Auth::user()->merchant_id) {
            return $this->response()->error(admin_trans('url.fields.url_info.illegal'))->refresh();
        }

        if (!in_array($urlInfo->url_status, [MerchantUrl::STATUS_CHECK, MerchantUrl::STATUS_REJECT])) {
            return $this->response()->error(admin_trans('url.fields.url_info.status_immutable'))->refresh();
        }

        $urlInfo->url_status          = MerchantUrl::STATUS_DELETE;
        $urlInfo->case->audit_result  = RiskCase::CASE_AUDIT_REVOKE;
        $urlInfo->case->case_status   = RiskCase::CASE_STATUS_COMPLETE;
        $urlInfo->case->completion_at = now();
        $urlInfo->case->save();
        $del = $urlInfo->save();

        if (!$del) {
            return $this->response()->error(trans('admin.failed'))->refresh();
        }

        return $this->response()->success(trans('admin.succeeded'))->refresh();
    }

    public function confirm()
    {
        return [admin_trans('url.labels.Confirm') . "？"];
    }

    public function parameters()
    {
        return ['id' => $this->id];
    }
}
