<?php

namespace App\Merchant\Actions\Grid\Cards;

use App\Models\DirectoryCurrency;
use App\Services\MerchantService;
use App\Services\VirtualControllerService;
use Dcat\Admin\Admin;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;
use Illuminate\Support\Facades\Auth;
use App\Models\MerchantCard;

class TransferIn extends Form implements LazyRenderable
{
    use LazyWidget;

    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return \Dcat\Admin\Http\JsonResponse
     */
    public function handle(array $input)
    {
        $merchantId   = Auth::user()->merchant_id;
        $input['mid'] = $merchantId;
        $resultData   = VirtualControllerService::cidRecharge($input);
        if (!$resultData['isSuccess']) {
            return $this->response()->error($resultData['message'])->refresh();
        }

        return $this->response()->success($resultData['message'])->refresh();
    }

    /**
     * Build a form here.
     */
    public function form()
    {
        Admin::script(
            <<<JS
$(function() {
    $(".field_currency,.field_amount").bind('input propertychange', function() {
        var currency = $(".field_currency").val();
        var amount = $(".field_amount").val();
        var amountData = {$this->getAmountList()};
        var rateList = {$this->getCurrencyRateList()};
        var transferAmount = currency.length > 0 ? (amount / rateList[currency]).toFixed(2) : '0.00';
        var availableAmount = currency.length > 0 ? amountData[currency]['available_amount'] : '0.00';

        $(".field_available_amount").val(availableAmount);
        $(".field_transfer_amount").val(transferAmount);
    });
})
JS
        );

        $merchantId = Auth::user()->merchant_id;

        // 获取CID列表
        $cardsIdList = MerchantCard::where('merchant_id', $merchantId)->where('status', '1')->get()->pluck('cards_id', 'cards_id')->toArray();

        // 获取mid结算币种
        $currencyList = MerchantService::getMerchantCurrencyList($merchantId);

        $this->select('cards_id', 'CID')->options($cardsIdList)->required();
        $this->select('currency')->options($currencyList)->required();
        $this->text('available_amount', admin_trans('ticket.labels.可提现货币'))->default('0.00')->readOnly();
        $this->text('amount', admin_trans('ticket.labels.充值') . admin_trans('ticket.fields.金额'))->default('0.00')->required();
        $this->text('transfer_amount', admin_trans('ticket.labels.预计转入金额'))->default('0.00')->readOnly();
    }

    public function getAmountList()
    {
        return json_encode(MerchantService::getAvailableAmountData(Auth::user()->merchant_id));
    }

    public function getCurrencyRateList()
    {
        return json_encode(DirectoryCurrency::all()->pluck('rate', 'code')->toArray());
    }
}
