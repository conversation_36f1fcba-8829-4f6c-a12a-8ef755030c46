<?php

namespace App\Merchant\Actions\Grid\Business;

use App\Models\Merchant;
use App\Models\MerchantBusiness;
use App\Services\MerchantService;
use Dcat\Admin\Actions\Action;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Traits\HasPermissions;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class Bid extends Action
{
    /**
     * @return string
     */
    public function title()
    {
        return '<button class="btn btn-primary grid-refresh">' . admin_trans('business.labels.add_to_bid') . '</button>';
    }

    /**
     * Handle the action request.
     *
     * @param Request $request
     *
     * @return Response
     */
    public function handle(Request $request)
    {
        $merchantId = Auth::user()->merchant_id;

        // 获取mid信息
        $merchantData = Merchant::find($merchantId);

        if (empty($merchantData)) {
            return $this->response()->error('MID信息不存在')->refresh();
        }

        // 获取MID下最后一个BID
        $businessData = MerchantBusiness::select('business_id')->orderBy('business_id', 'desc')->firstWhere('merchant_id', $merchantId);
        $total        = !empty($businessData) && !empty($businessData->business_id) ? (int)substr($businessData->business_id, -3) : '0';

        //添加新BID
        $tempMerchant = [
            'merchant_id'   => $merchantId,
            'merchant_name' => $merchantData->merchant_name,
            'business_id'   => $merchantId . str_pad($total + 1, 3, '0', STR_PAD_LEFT)
        ];
        $business = MerchantService::getAddBusinessInfo($tempMerchant);
        if (!empty($business) && MerchantBusiness::create($business)) {
            // 默认生成国际卡收单计费 默认生成保底规则0-*-*
            MerchantService::createChargeRateCc($business['merchant_id'], $business['business_id']);

            return $this->response()->success(admin_trans('business.labels.add_to_bid') . trans('admin.succeeded'))->refresh();
        }

        return $this->response()->error(admin_trans('business.labels.add_to_bid') . trans('admin.failed'))->refresh();
    }

    /**
     * @return string|array|void
     */
    public function confirm()
    {
        return [admin_trans('business.labels.are_add_to_bid') . '?'];
    }

    /**
     * @param Model|Authenticatable|HasPermissions|null $user
     *
     * @return bool
     */
    protected function authorize($user): bool
    {
        return true;
    }

    /**
     * @return array
     */
    protected function parameters()
    {
        return [];
    }
}
