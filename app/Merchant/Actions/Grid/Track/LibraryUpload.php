<?php

namespace App\Merchant\Actions\Grid\Track;

use App\Classes\Pay\Exceptions\Exception;
use App\Models\OrderTrack;
use App\Models\TrackLibrary;
use App\Traits\TrackingTypeRules;
use Dcat\Admin\Admin;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;
use Dcat\EasyExcel\Contracts\Sheet as SheetInterface;
use Dcat\EasyExcel\Excel;
use Dcat\EasyExcel\Support\SheetCollection;

class LibraryUpload extends Form implements LazyRenderable
{
    use LazyWidget;

    use TrackingTypeRules;

    /**
     * 文件字段映射
     *
     * @var string[]
     */
    protected $fieldList = array(
        'tracking_number'    => '服务商单号',
        'ship_country_isoa2' => '收件人国家简码',
        'ship_name'          => '收件人姓名',
        'ship_address'       => '收件人地址',
        'ship_address1'     => '收件人地址1',
        'ship_city'          => '收件人城市',
        'ship_state'         => '收件人州省',
        'ship_mobile'        => '收件人手机',
        'ship_phone'         => '收件人电话',
        'ship_postcode'      => '收件人邮编',
    );

    // 处理请求
    public function handle(array $input)
    {
        $user = Admin::user();
        $mid  = $user->merchant_id;
        // 获取外部传递参数
        $file   = $input['file'];
        $result = ['error' => false, 'message' => ''];

        // 导入xlsx
        Excel::import(public_path('/data/merchant/' . $file))->each(function (SheetInterface $sheet) use (&$result, $mid) {

            // 分块处理表格数据
            $sheet->chunk(100, function (SheetCollection $collection) use (&$result, $mid) {
                $orderList = $collection->toArray();

                if ($result['error']) return false;

                foreach ($orderList as $row => $tempOrder) {
                    // 字段替换
                    $data = ['merchant_id' => $mid];

                    // 模板验证
                    if ($row == 2) {
                        if (array_values($this->fieldList) != array_keys($tempOrder)) {
                            $result['error']   = true;
                            $result['message'] = '上传模板已更新,请重新下载模板上传';
                            return false;
                        }
                    }

                    $tempData = [];

                    foreach ($this->fieldList as $field => $value) {
                        if (in_array($field, ['ship_name', 'ship_address1', 'ship_mobile'])) {
                            $tempData[$field] = isset($tempOrder[$value]) ? trim($tempOrder[$value]) : '';
                        } else {
                            $data[$field] = isset($tempOrder[$value]) ? trim($tempOrder[$value]) : '';
                        }
                    }

                    if (empty($data['tracking_number']) || strpos($data['tracking_number'], '=VLOOKUP') !== false) {
                        $result['message'] .= sprintf("第%s行,运单号为空<br>", $row);
                        continue;
                    }

                    // 判断是否重复
                    if (TrackLibrary::where('tracking_number', $data['tracking_number'])->exists()
                        || OrderTrack::where('tracking_number', $data['tracking_number'])->exists()) {
                        $result['message'] .= sprintf("第%s行,服务商单号已存在<br>", $row);
                        continue;
                    }

                    // 判断收件人姓名是否规范
                    if (empty($tempData['ship_name'])) {
                        $result['message'] .= sprintf("第%s行,收件人姓名为空<br>", $row);
                        continue;
                    }

                    $shipNameArr = explode(' ', $tempData['ship_name']);
                    $data['ship_first_name'] = $shipNameArr[0] ?? '';
                    $data['ship_last_name']  = $shipNameArr[1] ?? '';

                    if (empty($data['ship_first_name']) || empty($data['ship_last_name'])) {
                        $result['message'] .= sprintf("第%s行,收件人姓名格式错误<br>", $row);
                        continue;
                    }

                    // 判断地址
                    if (empty($data['ship_address']) && empty($tempData['ship_address1'])) {
                        $result['message'] .= sprintf("第%s行,收件人地址和收件人地址1为空<br>", $row);
                        continue;
                    }

                    // 优先获取ship_address
                    $data['ship_address'] = empty($data['ship_address']) ? $tempData['ship_address1'] : $data['ship_address'];

                    // 判断电话
                    if (empty($data['ship_phone']) && empty($tempData['ship_mobile'])) {
                        $result['message'] .= sprintf("第%s行,收件人电话和收件人手机为空<br>", $row);
                        continue;
                    }

                    // ship_phone
                    $data['ship_phone'] = empty($data['ship_phone']) ? $tempData['ship_mobile'] : $data['ship_phone'];

                    // 判断国家
                    if (empty($data['ship_country_isoa2']) || $data['ship_country_isoa2'] != 'US') {
                        $result['message'] .= sprintf("第%s行,收件人国家简码错误<br>", $row);
                        continue;
                    }

                    // 判断省、城市、收件人邮编
                    if (empty($data['ship_state']) || empty($data['ship_city']) || empty($data['ship_postcode'])) {
                        $result['message'] .= sprintf("第%s行,收件人省或者收件人城市或者收件人邮编为空<br>", $row);
                        continue;
                    }

                    try {
                        TrackLibrary::create($data);
                    } catch (Exception $exception) {
                        return $this->response()->error('上传文件处理出错', trans('admin.save_failed'));
                    }
                }
            });
        });

        if ($result['error']) {
            return $this->response()->error($result['message'], trans('admin.save_failed'));
        }

        return $this->response()->success("处理完成<br>" . $result['message'])->refresh();
    }

    public function form()
    {
        $url = config('app.url') . '/download/trackLibraries.xlsx';
        $this->file('file')->accept('xlsx')->autoUpload()->required();
        $this->html('<a href="' . $url . '" download="delivery" target = "_blank">Download</a>', admin_trans_label('示例文件下载'));
    }
}
