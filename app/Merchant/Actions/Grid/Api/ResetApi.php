<?php

namespace App\Merchant\Actions\Grid\Api;

use App\Models\MerchantApi;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Traits\HasPermissions;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use App\Traits\OpenApiCommon;

class ResetApi extends RowAction
{
    use OpenApiCommon;

    public function title()
    {
        return "<button class='btn btn-primary btn-shadow btn-change sys-custom-purple'>" . admin_trans_field('重置') . "</button>";
    }
    /**
     * Handle the action request.
     *
     * @param Request $request
     *
     * @return Response
     */
    public function handle(Request $request)
    {
        $clientId       = MerchantApi::getId();
        $code           = MerchantApi::getCode();
        $api            = MerchantApi::where('merchant_id', Auth::user()->merchant_id)->first();
        $api->client_id = $clientId;
        $api->secret    = $code;
        $api->save();
        // 清除缓存
        if (!empty($api->api_token) && !empty($api->token_time) && $api->token_time > date('Y-m-d H:i:s')) {
            Cache::forget($this->setRedisToken($api->api_token));
        }
        return $this->response()->success(admin_trans_label('重置API成功'))->refresh();
    }

    /**
     * @return string|array|void
     */
    public function confirm()
    {
        return [admin_trans_label('你确定要重置api吗') . '?'];
    }

    /**
     * @param Model|Authenticatable|HasPermissions|null $user
     *
     * @return bool
     */
    protected function authorize($user): bool
    {
        return true;
    }

    /**
     * @return array
     */
    protected function parameters()
    {
        return [];
    }
}
