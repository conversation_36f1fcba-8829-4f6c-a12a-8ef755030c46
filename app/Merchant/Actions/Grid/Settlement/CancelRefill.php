<?php

namespace App\Merchant\Actions\Grid\Settlement;

use App\Models\MerchantTicket;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Traits\HasPermissions;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class CancelRefill extends RowAction
{
    public function title()
    {
        return "<span class='btn-success btn btn-primary scheme btn-shadow btn-change'>" . admin_trans_label('Cancel') . '</span>&emsp;';
    }

    /**
     * Handle the action request.
     *
     * @param Request $request
     *
     * @return Response
     */
    public function handle(Request $request)
    {
        $merchantTicket = MerchantTicket::where('id', $this->getKey())->first();

        if (!$merchantTicket) {
            return $this->response()->error(admin_trans('refill.labels.取消充值失败,无法找到对应数据'))->refresh();
        }

        if ($merchantTicket->status != MerchantTicket::MERCHANT_TICKET_STATUS_CHECK) {
            return $this->response()->error(admin_trans('refill.labels.取消充值失败,当前状态无法取消充值'))->refresh();
        }

        $merchantTicket->status = MerchantTicket::MERCHANT_TICKET_STATUS_CANCEL;

        DB::beginTransaction();
        try {
            if (!$merchantTicket->save()) {
                throw new \Exception(admin_trans('refill.labels.取消充值失败'));
            }
            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();

            return $this->response()->error($exception->getMessage())->refresh();
        }

        return $this->response()->success(admin_trans('refill.labels.取消充值成功'))->refresh();
    }

    /**
     * @return string|array|void
     */
    public function confirm()
    {
        return [admin_trans('refill.labels.您确认要取消充值吗')];
    }

    /**
     * @param Model|Authenticatable|HasPermissions|null $user
     *
     * @return bool
     */
    protected function authorize($user): bool
    {
        return true;
    }

    /**
     * @return array
     */
    protected function parameters()
    {
        return [];
    }
}
