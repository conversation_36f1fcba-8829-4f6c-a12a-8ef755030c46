<?php

namespace App\Merchant\Actions\Grid\Settlement;

use Dcat\Admin\Grid\LazyRenderable;
use App\Admin\Repositories\SettleDetail;
use App\Models\SettleDetail as SettleDetailModel;
use Dcat\Admin\Admin;
use Dcat\Admin\Grid;

class SettleDetailTable extends LazyRenderable
{
    public function grid(): Grid
    {
        Admin::style(
            <<<css
                body:not(.dark-mode) .simple-grid .table-collapse .custom-data-table {
                    color: #414750;
                }
            css
        );
        return Grid::make(new SettleDetail(), function (Grid $grid) {
            $grid->model()
                ->where('business_id', $this->payload['business_id'])
                ->where('settle_at', $this->payload['settle_at'])
                ->where('settle_currency', $this->payload['settle_currency'])
                ->where('settle_amount', '<>', 0)
                ->orderBy('settle_at', 'desc');

            $grid->column('related_id', admin_trans('settlement.fields.related_id'));
            $grid->column('order_number');
            $grid->column('settle_amount', admin_trans('settlement.fields.settle_amount'))->display(function ($value) {
                return $value . '&nbsp;&nbsp;' . $this->settle_currency;
            });
            $grid->column('amount', admin_trans('settlement.fields.amount'))->display(function ($value) {
                return $value . '&nbsp;&nbsp;' . $this->currency;
            });
            $grid->column('rate', admin_trans('settlement.fields.rate'));

            $grid->column('amount_type', admin_trans('settlement.fields.amount_type'))->display(function ($value) {
                return admin_trans('settlement.options.amount_type_map.' . SettleDetailModel::$amountTypeMap[$value]);
            });

            $grid->column('remarks', admin_trans('settlement.fields.remarks'));
            $grid->column('order_complete_at', admin_trans('settlement.fields.order_complete_at'));
            $grid->column('settle_at', admin_trans('settlement.fields.settle_at'));

            $grid->paginate(50);
            $grid->disableActions();
            $grid->disableCreateButton();
            $grid->disableRefreshButton();
            $grid->disableFilterButton();
        });
    }
}
