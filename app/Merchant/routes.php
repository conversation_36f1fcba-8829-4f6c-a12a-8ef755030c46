<?php

use Dcat\Admin\Admin;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;
use Mosiboom\DcatIframeTab\Controllers\IframeController;

Admin::routes();

Route::group([
    'prefix'     => config('admin.route.prefix'),
    'namespace'  => config('admin.route.namespace'),
    'middleware' => ['web', 'admin'],
], function (Router $router) {
    $router->get('/2fa/{mark}/verificationPage/{mode?}', 'MerchantSecondaryValidationController@verificationPage')->name('2fa.merchant.verificationPage');
    $router->post('/2fa/validateToken', 'MerchantSecondaryValidationController@validateToken')->name('2fa.merchant.validateToken');
    $router->post('/2fa/changePassword', 'MerchantSecondaryValidationController@changePassword')->name('2fa.merchant.changePassword');
    $router->get('/2fa/get_random_password', 'MerchantSecondaryValidationController@getRandomPassWord')->name('2fa.merchant.getRandomPassword');
});

Route::group([
    'namespace' => config('admin.route.namespace')
], function (Router $router) {
    $router->get('/customs/{token}', 'CustomCareController@index')->name('customs.index');
    $router->post('/customs/{token}', 'CustomCareController@service')->name('customs.service');
});

Route::group([
    'prefix'     => config('merchant.route.prefix'),
    'namespace'  => config('merchant.route.namespace'),
    'middleware' => ['web'],
], function (Router $router) {
    //设置语言
    $router->post('/setLang/{lang}', function ($lang) {
        request()->session()->put('lang', $lang);
        return response()->json(['code' => 0], 200);
    });
    $router->get('/open_im/get_login_address', 'OpenIm\OpenImController@getLoginAddress');
    $router->any('/settlement/deposit_info/{type}', 'Settlement\MerchantRegularDepositController@getDepositInfo');
    $router->any('/kyc/register', 'KycController@create')->middleware('change-locale');
    $router->any('/kyc/index', 'KycController@index')->middleware('change-locale')->name('kycIndex');
    $router->resource('/kyc', 'KycController')->middleware('change-locale');
});

Route::group([
    'prefix'     => config('merchant.route.prefix'),
    'namespace'  => config('merchant.route.namespace'),
    'middleware' => config('merchant.route.middleware'),
], function (Router $router) {
    $router->get('/dashboard', 'HomeController@index');

    // 系统设定
    $router->resource('/roles', 'System\RoleController');
    $router->resource('/users', 'System\UserController');
    $router->resource('/apis', 'System\ApiController');
    $router->resource('/api/{merchant_id}/webhooks', 'System\WebhooksController');

    // 导出
    $router->get('/download/{file_name}', function ($fileName) {
        [$downloadFileName, $fileName] = explode('/', base64_decode($fileName));
        $file = config('excel.temporary_files.local_path') . '/' . $fileName;

        return response()->download($file, $downloadFileName);
    });

    // 网址管理
    $router->get('/urls/get_saas_url', 'UrlController@getSaasUrl');
    $router->resource('/urls', 'UrlController')->names('m.urls');

    // 争议管理
    $router->resource('/complaints', 'Dispute\ComplaintController')->names('m.complaints');
    $router->get('/complaints/item/{id}', 'Dispute\ComplaintController@item')->name('m.complaints.item')->middleware('complaint');
    $router->post('/complaints/item/{id}', 'Dispute\ComplaintController@save')->name('m.complaints.save');
    $router->resource('/chargeback', 'Dispute\ChargebackController');
    $router->resource('/alert_chargeback', 'Dispute\ChargebackCasesController');


    // 交易管理
    $router->resource('/orders', 'Transaction\OrderController');
    $router->resource('/refunds', 'Transaction\RefundApplyController');
    $router->resource('/info_refunds', 'Transaction\RefundController');
    $router->resource('/order_loses', 'Transaction\OrderLosesController');
    $router->resource('/order_risk_transaction', 'Transaction\OrderRiskTransactionController');// 风险交易

    // 运单管理
    $router->resource('/track/tracks', 'Track\TrackController')->names('m.tracks');
    $router->resource('/track/orders', 'Track\OrderController');
    $router->resource('/track/libraries', 'Track\LibraryController');

    // 结算管理
    $router->resource('/settlement/settlement', 'Settlement\SettlementController');
    $router->resource('/settlement/adjustment', 'Settlement\AdjustmentController');
    $router->resource('/settlement/refill', 'Settlement\RefillController');
    $router->resource('/settlement/regular_deposit', 'Settlement\MerchantRegularDepositController');
    $router->resource('/settlement/merchant_report_forms', 'Settlement\MerchantReportFormController');

    // 提现管理
    $router->resource('/transfer/Accounts', 'Transfer\AccountController');
    $router->resource('/transfer/Tickets', 'Transfer\TicketController');
    $router->get('/transfer/Tickets/accounts/{cardholder_id}', 'Transfer\TicketController@accounts');

    // BID管理
    $router->resource('/business/businesses', 'Business\BusinessController')->names('m.business');

    //CID管理
    $router->resource('/cards/cards', 'Cards\CardsController')->names('m.cards');
    $router->resource('/cards/tickets', 'Cards\TicketController');
    $router->resource('/cards/settle_details', 'Cards\SettleDetailController');
    $router->resource('/cards/trade_stat', 'Cards\CardTradeStatController');

    //虚拟卡管理
    $router->resource('/card_batch_virtual', 'CardVirtual\CardBatchController');
    $router->get('/card_type/{cards_id}', 'CardVirtual\CardBatchController@card_type');
    $router->resource('/card_virtual', 'CardVirtual\CardVirtualController');
    $router->resource('/card/holder', 'CardVirtual\CardHolderController');

    // 交易管理
    $router->resource('/card/orders', 'CardTransaction\CardOrderController');
    // 下载管理
    $router->resource('/download_control/download_center', 'Download\DownloadCenterController');

});

// iframe-tab 解决多应用后台的路由问题
Route::group([
	'prefix'     => config('merchant.route.prefix'),
	'middleware' => config('merchant.route.middleware'),
], function (Router $router) {
	$controller = IframeController::class;
	$router->get('/', $controller . '@index');
});
