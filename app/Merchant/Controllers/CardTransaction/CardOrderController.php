<?php

namespace App\Merchant\Controllers\CardTransaction;

use App\Admin\Controllers\CardVirtual\CardBaseController;
use App\Admin\Exceptions\CardTransAuthCode;
use App\Merchant\Controllers\CardVirtual\MerchantCardBaseController;
use App\Merchant\Controllers\MerchantSecondaryValidationController;
use Dcat\Admin\Grid;
use Dcat\Admin\Widgets\Modal;
use DES3;
use App\Models\CardTransaction;
use Illuminate\Support\Facades\Auth;

class CardOrderController extends MerchantCardBaseController
{
    public function title()
    {
        return admin_trans_label('虚拟卡订单管理');
    }

    protected function grid()
    {
        $is2faCheck = $this->is2faCheck(1);
        return Grid::make(new CardTransaction(), function (Grid $grid) use ($is2faCheck) {
            $grid->model()->where('merchant_id', Auth::user()->merchant_id)->orderBy('id', 'desc');
            $grid->column('unique_id', 'UID');
            $grid->column('transaction_order_id');
            $grid->column('cards_id', 'CID');
            $grid->column('card_number')->display(function ($value) {
                return empty($value) ? '' : get_markcard(DES3::decrypt($value, env('DES3_CARD_VIRTUAL')));
            });
            $grid->column('amount');
            $grid->column('currency');
            $grid->column('settle_amount');
            $grid->column('settle_currency');
            $grid->column('transaction_settle_status')->using(admin_trans_label('transaction_settle_status'), admin_trans_label('Not settled'));
            $grid->column('transaction_type')->using(admin_trans_label('transaction_type'));
            $grid->column('transaction_status')->using(admin_trans_label('transaction_status'));
            $grid->column('fail_reason');
            $grid->column('transaction_description');
            $grid->column('transaction_mcc');
            $grid->column('created_at');
            $grid->column('updated_at');
            $grid->fixColumns(0, 0);
            $grid->disableCreateButton();
            $grid->disableRowSelector();
            $grid->disableDeleteButton();
            $grid->disableEditButton();
            $grid->disableViewButton();

            $grid->actions(function (Grid\Displayers\Actions $actions) use ($is2faCheck) {
                if (!empty($actions->row->auth_code)) {
                    if ($is2faCheck) {
                        $faView = admin_route('2fa.merchant.verificationPage', ['mark' => 'google2fa', 'mode' => MerchantSecondaryValidationController::MODE_CARD_AUTH_CODE]);
                        $modal  = "<a href={$faView} class='btn-outline-primary btn btn-primary btn-shadow sys-custom-sky-blue'>" . admin_trans_field('auth_code') . "</a>&emsp;";
                    } else {
                        $modal = Modal::make()->lg()->body(CardTransAuthCode::make()->payload(['id' => $actions->getKey()]))->button('<button class="btn btn-primary">授权码</button>');
                    }
                    $actions->append($modal);
                }
            });

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('unique_id', 'UID');
                $filter->equal('transaction_order_id');
                $filter->equal('cards_id', 'CID');
                $filter->where('card_number', function ($query) {
                    $query->where('card_number', DES3::encrypt($this->input, env('DES3_CARD_VIRTUAL')));
                });
                $filter->equal('transaction_type')->select(admin_trans_label('transaction_type', CardTransaction::$transactionTypeMap));
                $filter->equal('transaction_status')->select(admin_trans_label('transaction_status', CardTransaction::$transactionStatusMap));
                $filter->equal('transaction_settle_status')->select(admin_trans_label('transaction_settle_status', CardTransaction::$transactionChannelStatusMap));
                $filter->between('created_at')->datetime(['sideBySide'=>true]);
            });
        });
    }
}
