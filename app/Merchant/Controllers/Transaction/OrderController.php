<?php

namespace App\Merchant\Controllers\Transaction;

use App\Admin\Repositories\Order;
use App\Merchant\Actions\Tools\Transaction\OrderExportTool;
use App\Models\MerchantBusiness;
use App\Models\MerchantUrl;
use App\Models\OrderAddress;
use App\Models\OrderCard;
use App\Models\Order as OrderModel;
use App\Models\OrderProduct;
use App\Models\OrderRelation;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Support\Facades\Auth;
use App\Models\DirectoryCc;

class OrderController extends AdminController
{
    public function title()
    {
        return admin_trans_label('订单管理');
    }

    protected $exportTitle = [
        'order_id',
        'type',
        'status',
		'is_refund',
		'is_chargeback',
        'merchant_id',
        'business_id',
        'merchant_name',
        'cc_type',
        'card_mask',
        'card_country',
        'url_name',
        'bill_name',
        'bill_email',
        'bill_country',
        'bill_state',
        'bill_city',
        'bill_address',
        'bill_postcode',
        'bill_phone',
        'ship_name',
        'ship_email',
        'ship_country' ,
        'ship_state',
        'ship_city',
        'ship_address',
        'ship_postcode',
        'ship_phone',
        'order_number',
        'currency',
        'amount',
        'code',
        'result',
        'remark',
        'parent_order_id',
        'is_3d',
        'tracking_type',
        'tracking_number',
        'expired_at',
        'is_settle',
        'settle_at',
        'created_at',
        'updated_at'
    ];

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(OrderModel::with(['card', 'address', 'track', 'relation', 'settlements:order_id,is_settle,settle_at'])->where('merchant_id', Auth::user()->merchant_id), function (Grid $grid) {
            if (count(request()->toArray()) <= 1) {
                request()->offsetSet('completed_at', [
                    'start' => date('Y-m-d 00:00:00', strtotime('-1 week')),
                    'end'   => date('Y-m-d H:i:s')
                ]);
            }

            $grid->model()->join('order_cards', 'orders.card_id', '=', 'order_cards.id')->orderBy('orders.updated_at', 'desc')->orderBy('order_id', 'desc');
            $grid->column('order_id/order_number/parent_order_id', admin_trans_field('collection.order_id'))->display(function () {
                return $this->order_id . '<br/>' . $this->order_number . '<br/>' . $this->parent_order_id;
            });
            $grid->column('type')->display(function ($value) {
                return OrderModel::$typesMap[$value] ?? '未知';
            });
            $grid->column('status')->display(function ($value) {
                return OrderModel::$statusMap[$value] ?? '未知';
            })->dot(['0' => 'danger', '1' => 'success', '2' => 'primary', '3' => 'primary', '4' => 'danger']);

			$grid->column('relation.is_refund')->display(function ($value) {
				return OrderRelation::$isRefundMap[$value] ?? '未知';
			});

			$grid->column('relation.is_chargeback')->display(function ($value) {
				return OrderRelation::$isChargebackMap[$value] ?? '未知';
			});

            $grid->column('merchant_name/merchant_id/business_id', admin_trans_field('collection.merchant_name'))->display(function () {
                return $this->merchant_name . '<br/>' . $this->merchant_id . '<br/>' . $this->business_id;
            });
            $grid->column('cc_type/card_mask/card_country/url_name', admin_trans_field('collection.cc_type'))->display(function () {
                return $this->card['cc_type'] . '<br/>' . $this->card['card_mask'] . '<br/>' . $this->card['card_country'] . '<br/>' . $this->url_name;
            });
            $grid->column('bill_name/bill_city/bill_state/bill_postcode/bill_country/bill_phone/bill_email/bill_address', admin_trans_field('collection.bill_name'))->display(function () {
                return $this->address['bill_name'] . '<br/>' . $this->address['bill_email'] . '<br/>' . $this->address['bill_country'] . '  ' . $this->address['ship_state'] . '  ' .  $this->address['bill_city'] . '<br/>' . $this->address['bill_address'] . '<br/>' . $this->address['ship_postcode'] . '  ' . $this->address['bill_phone'];
            });
            $grid->column('ship_name/ship_city/ship_state/ship_postcode/ship_country/ship_phone/ship_email/ship_address', admin_trans_field('collection.ship_name'))->display(function () {
                return $this->address['ship_name'] . '<br/>' . $this->address['ship_email'] . '<br/>' . $this->address['ship_country'] . '  ' . $this->address['ship_state'] . '  ' .  $this->address['ship_city'] . '<br/>' . $this->address['ship_address'] . '<br/>' . $this->address['ship_postcode'] . '  ' . $this->address['ship_phone'];
            });
            $grid->column('amount/currency', admin_trans_field('collection.amount'))->display(function () {
                return $this->amount . '<br/>' . $this->currency;
            });
            //英文状态下不显示备注
            $lang = session('lang') ?? 'zh_CN';
            if ($lang === 'zh_CN') {
                $grid->column('code/result/remark', admin_trans_field('collection.code'))->display(function () {
                    return $this->code . '<br/>' . $this->result . '<br/>' . $this->remark;
                });
            } else {
                $grid->column('code/result', admin_trans_field('collection.code_en'))->display(function () {
                    return $this->code . '<br/>' . $this->result;
                });
            }
            $grid->column('is_3d')->display(function ($val) {
                return admin_trans_option($val ? '是' : '否', 'is_3d');
            });
            $grid->column('tracking_type/tracking_number', admin_trans_field('collection.tracking_type'))->display(function () {
                return $this->track['tracking_type'] . '<br/>' . $this->track['tracking_number'];
            });
            $grid->column('expired_at');
            $grid->column('is_settle/settle_at', admin_trans_field('collection.is_settle'))->display(function () {
                return admin_trans_option($this->settlements['is_settle'] ? '是' : '否', 'is_settle');
            });
            $grid->column('settle_at')->display(function () {
                return $this->settlements['settle_at'];
            });
            $grid->column('created_at/updated_at', admin_trans_field('collection.created_at'))->display(function () {
                return $this->created_at . '<br/>' . $this->updated_at;
            });

            $grid->showColumnSelector();
            $grid->hideColumns(['bill_name/bill_city/bill_state/bill_postcode/bill_country/bill_phone/bill_email/bill_address', 'ship_name/ship_city/ship_state/ship_postcode/ship_country/ship_phone/ship_email/ship_address']);

            $grid->fixColumns(1, -1);

            $grid->disableDeleteButton();
            $grid->disableCreateButton();
            $grid->disableEditButton();
            $grid->disableBatchDelete();

            $grid->tools(new OrderExportTool($this->exportTitle));

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('order_id');
                $filter->equal('order_number');
                $filter->equal('type')->select(OrderModel::$typesMap);
                $filter->equal('status')->select(OrderModel::$statusMap);
                $filter->equal('url_id')->select(MerchantUrl::where('merchant_id', Auth::user()->merchant_id)->get()->pluck('url_name', 'id')->toArray());
                $filter->equal('business_id')->select(MerchantBusiness::where('merchant_id', Auth::user()->merchant_id)->get()->pluck('business_id', 'business_id')->toArray());
                $filter->equal('is_3d')->select(admin_trans_label('is_3d', ['否', '是']));
                $filter->where('card_mask', function ($query) {
                    $cardMask = $this->input;

                    if (strlen($cardMask) == 6) {
                        $query->where('order_cards.card_mask', 'like', $cardMask .'%');
                    } else {
                        $query->where('order_cards.card_mask', $cardMask);
                    }
                }, '卡号');
                $filter->where('cc_type', function ($query) {
                    $ccType = $this->input;
                    $query->where([['order_cards.cc_type', '=', $ccType], ['completed_at', '<=', date('Y-m-d H:i:s', time())]]);
                },'卡种')->select(DirectoryCc::isRiskControl()->get()->pluck('cc_type', 'cc_type')->toArray());
                $filter->equal('address.bill_email');
                $filter->equal('address.bill_phone');
                $filter->equal('address.bill_name');
                $filter->equal('relation.is_refund')->select(admin_trans_label('is_refund_map',OrderRelation::$isRefundMap));
                $filter->equal('relation.is_chargeback')->select(admin_trans_label('is_chargeback_map',OrderRelation::$isChargebackMap));
                $filter->equal('code');
                $filter->whereBetween('created_at', function ($query) {
                    $start = $this->input['start'] ?? '';
                    $end   = $this->input['end'] ?? '';

                    if (!empty($end)) {
                        $query->where('orders.created_at', '<=', $end);
                    }

                    if (!empty($start)) {
                        $query->where('orders.created_at', '>=', $start);
                    }
                })->datetime(['sideBySide'=>true]);
                $filter->between('completed_at')->datetime(['sideBySide'=>true]);
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Order(['card', 'address', 'products']), function (Show $show) {

            $show->html();
            $show->disableEditButton();
            $show->disableDeleteButton();

            $show->row(function (Show\Row $show) {
                $show->width(4)->order_id;
                $show->width(4)->parent_order_id;
                $show->width(4)->order_number;
                $show->width(4)->merchant_id;
                $show->width(4)->business_id;
                $show->width(4)->merchant_name;
                $show->width(4)->url_name;
                $show->width(4)->currency;
                $show->width(4)->amount;
                $show->width(4)->type->using(\App\Models\Order::$typesMap);
                $show->width(4)->status->using(\App\Models\Order::$statusMap);
                $show->width(4)->code;
                $show->width(4)->result;
                $show->width(4)->is_3d()->using(admin_trans_label('is_3d', ['否', '是']));
                $show->width(4)->created_at;
                $show->width(4)->updated_at;
            });

            // 卡信息
            $show->card_info(function ($model) {
                return Show::make($model->card['id'], new OrderCard(), function (Show $show) {
                    $show->row(function (Show\Row $show) {
                        $show->width(4)->card_mask;
                        $show->width(4)->cc_type;
                        $show->width(4)->card_country;
                    });

                    $show->disableDeleteButton();
                    $show->disableEditButton();
                    $show->disableListButton();
                });
            });

            // 地址信息
            $show->address_info(function ($model) {
                return Show::make($model->address['id'], new OrderAddress(), function (Show $show) {
                    $show->row(function (Show\Row $show) {
                        $show->width(4)->bill_name;
                        $show->width(4)->bill_email;
                        $show->width(4)->bill_address;
                        $show->width(4)->bill_city;
                        $show->width(4)->bill_state;
                        $show->width(4)->bill_postcode;
                        $show->width(4)->bill_country;
                        $show->width(4)->bill_country_isoa2;
                        $show->width(4)->bill_phone;
                    });

                    $show->row(function (Show\Row $show) {
                        $show->width(4)->ship_name;
                        $show->width(4)->ship_email;
                        $show->width(4)->ship_address;
                        $show->width(4)->ship_city;
                        $show->width(4)->ship_state;
                        $show->width(4)->ship_postcode;
                        $show->width(4)->ship_country;
                        $show->width(4)->ship_country_isoa2;
                        $show->width(4)->ship_phone;
                    });

                    $show->row(function (Show\Row $show) {
                        $show->width(4)->ip;
                        $show->width(4)->ip_country;
                        $show->width(4)->ip_country_isoa2;
                        $show->width(4)->ip_city;
                        $show->width(4)->ip_isp;
                        $show->width(4)->ip_postal_code;
                    });

                    $show->disableDeleteButton();
                    $show->disableEditButton();
                    $show->disableListButton();
                });
            });

            // 产品信息
            $show->product_info_merchant_list(function ($model) {
                $grid = new Grid(new OrderProduct());
                $grid->model()->where(['order_id' => $model->order_id, 'type' => OrderProduct::TYPE_MERCHANT]);

                $grid->name();
                $grid->qty();
                $grid->price();
                $grid->url();
                $grid->attribute();
                $grid->sku();
                $grid->is_gift();
                $grid->is_virtual();

                $grid->disableActions();
                $grid->disableCreateButton();
                $grid->disableRowSelector();
                $grid->disableRefreshButton();
                $grid->disablePagination();

                return $grid;
            });
        });
    }
}
