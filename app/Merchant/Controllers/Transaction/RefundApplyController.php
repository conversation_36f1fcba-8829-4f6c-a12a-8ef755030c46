<?php

namespace App\Merchant\Controllers\Transaction;

use App\Admin\Actions\Grid\Order\Refund;
use App\Admin\Repositories\Order;
use App\Models\OrderAddress;
use App\Models\OrderCard;
use App\Models\Order as OrderModel;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Widgets\Modal;
use Illuminate\Support\Facades\Auth;

class RefundApplyController extends AdminController
{
	public function title()
    {
        return admin_trans_label('退款申请');    
    }
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(OrderModel::with(['card'])->where('merchant_id', Auth::user()->merchant_id), function (Grid $grid) {
	        $grid->model()->whereIn('type', [OrderModel::TYPES_SALE, OrderModel::TYPES_CAPTURE])
		        ->where('status', OrderModel::STATUS_APPROVED)->orderBy('updated_at', 'desc')->orderBy('order_id', 'desc');
            $grid->column('order_id')->sortable();
	        $grid->column('type')->display(function ($value) {
		        return OrderModel::$typesMap[$value] ?? admin_trans_field('未知');
	        });
	        $grid->column('status')->display(function ($value) {
		        return OrderModel::$statusMap[$value] ?? admin_trans_field('未知');
	        })->dot(['0' => 'danger', '1' => 'success', '2' => 'primary', '3' => 'primary', '4' => 'danger']);
            $grid->column('merchant_id');
            $grid->column('business_id');
            $grid->column('merchant_name');
            $grid->column('url_name');
	        $grid->column('card.card_mask');
            $grid->column('order_number');
            $grid->column('currency');
            $grid->column('amount');
            $grid->column('code');
            $grid->column('result');
			//英文状态下不显示备注
			$lang = session('lang') ?? 'zh_CN';
			if($lang === 'zh_CN') {
				$grid->column('remark');
			}
	        $grid->column('parent_order_id');
            $grid->column('is_3d')->display(function($val) {
	            return admin_trans_option($val ? '是' : '否', 'is_3d');
            });
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();

	        $grid->disableDeleteButton();
	        $grid->disableCreateButton();
	        $grid->disableEditButton();
            $grid->disableRowSelector();

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $modale = Modal::make()
                    ->xl()
					->title(admin_trans_field('退款'))
					->body(Refund::make()->payload(['order_id' => $this->order_id]))
					->button('<button type="button" class="btn-outline-primary btn btn-primary btn-shadow btn-change sys-custom-red">' . admin_trans_field('退款') . '</button>');
                // prepend一个操作
                $actions->prepend($modale . '&nbsp;');
            });

            $grid->fixColumns(1, -1);

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('order_id');
                $filter->equal('order_number');
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Order(['card', 'address']), function (Show $show) {

	        $show->html();
	        $show->disableEditButton();
	        $show->disableDeleteButton();

	        $show->row(function (Show\Row $show) {
		        $show->width(4)->order_id;
		        $show->width(4)->parent_order_id;
		        $show->width(4)->order_number;
		        $show->width(4)->merchant_id;
		        $show->width(4)->business_id;
		        $show->width(4)->merchant_name;
		        $show->width(4)->url_name;
		        $show->width(4)->currency;
		        $show->width(4)->amount;
		        $show->width(4)->type->using(\App\Models\Order::$typesMap);
		        $show->width(4)->status->using(\App\Models\Order::$statusMap);
		        $show->width(4)->code;
		        $show->width(4)->result;
		        $show->width(4)->is_3d()->using(admin_trans_label('is_3d', ['否', '是']));
		        $show->width(4)->created_at;
		        $show->width(4)->updated_at;
	        });

	        // 卡信息
	        $show->card_info(function ($model) {
		        return Show::make($model->card['id'], new OrderCard(), function (Show $show) {
			        $show->row(function (Show\Row $show) {
				        $show->width(4)->card_mask;
				        $show->width(4)->cc_type;
				        $show->width(4)->card_country;
			        });

			        $show->disableDeleteButton();
			        $show->disableEditButton();
			        $show->disableListButton();
		        });
	        });

	        // 地址信息
	        $show->address_info(function ($model) {
		        return Show::make($model->address['id'], new OrderAddress(), function (Show $show) {
			        $show->row(function (Show\Row $show) {
				        $show->width(4)->bill_name;
				        $show->width(4)->bill_email;
				        $show->width(4)->bill_address;
				        $show->width(4)->bill_city;
				        $show->width(4)->bill_state;
				        $show->width(4)->bill_postcode;
				        $show->width(4)->bill_country;
				        $show->width(4)->bill_country_isoa2;
				        $show->width(4)->bill_phone;
			        });

			        $show->row(function (Show\Row $show) {
				        $show->width(4)->ship_name;
				        $show->width(4)->ship_email;
				        $show->width(4)->ship_address;
				        $show->width(4)->ship_city;
				        $show->width(4)->ship_state;
				        $show->width(4)->ship_postcode;
				        $show->width(4)->ship_country;
				        $show->width(4)->ship_country_isoa2;
				        $show->width(4)->ship_phone;
			        });

			        $show->row(function (Show\Row $show) {
				        $show->width(4)->ip;
				        $show->width(4)->ip_country;
				        $show->width(4)->ip_country_isoa2;
				        $show->width(4)->ip_city;
				        $show->width(4)->ip_isp;
				        $show->width(4)->ip_postal_code;
			        });

			        $show->disableDeleteButton();
			        $show->disableEditButton();
			        $show->disableListButton();
		        });
	        });
        });
    }
}
