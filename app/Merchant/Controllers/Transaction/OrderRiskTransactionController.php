<?php

namespace App\Merchant\Controllers\Transaction;

use App\Admin\Actions\Grid\Order\Refund;
use App\Merchant\Actions\Grid\Transaction\Accept;
use App\Admin\Repositories\Order;
use App\Admin\Repositories\OrderRiskTransaction;
use App\Models\MerchantBusiness;
use App\Models\MerchantUrl;
use App\Models\OrderAddress;
use App\Models\OrderCard;
use App\Models\OrderProduct;
use App\Models\OrderRelation;
use App\Models\OrderRiskTransaction as ModelsOrderRiskTransaction;
use Dcat\Admin\Grid;
use Dcat\Admin\Admin;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Show;
use Dcat\Admin\Widgets\Modal;
use Illuminate\Support\Facades\Auth;

class OrderRiskTransactionController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        Admin::style(
            <<<css
	.modal-xxl {max-width: 100%;}
css
        );

        return Grid::make(new OrderRiskTransaction(), function (Grid $grid) {
            $grid->model()->selectRaw('order_risk_transactions.*,order_relations.is_refund,order_relations.is_chargeback,if(order_relations.is_refund <> ' . OrderRelation::IS_REFUND_FULL . ' and order_relations.is_chargeback = ' . OrderRelation::IS_CHARGEBACK_NOT . ' and order_risk_transactions.is_accept = ' . ModelsOrderRiskTransaction::IS_ACCEPT_NOT . ', ' . ModelsOrderRiskTransaction::PROCESSING_STATUS_NOT . ', ' . ModelsOrderRiskTransaction::PROCESSING_STATUS . ') as processing_status')
            ->leftJoin('order_relations', 'order_risk_transactions.order_id', '=', 'order_relations.order_id')
            ->where('merchant_id', Auth::user()->merchant_id)
            ->orderByRaw('processing_status asc, created_at desc');

            $grid->column('order_id');
            $grid->column('merchant_id');
            $grid->column('business_id');
            $grid->column('merchant_name');
            $grid->column('url_name');
            $grid->column('card_mask');
            $grid->column('order_number');
            $grid->column('currency');
            $grid->column('amount');
            $grid->column('is_3d')->display(function ($value) {
                return admin_trans_option($value ? '是' : '否', 'is_3d');
            });
            $grid->column('is_refund')->display(function ($value) {
                return admin_trans_option(OrderRelation::$isRefundMap[$value], 'is_refund_map') ?? '-';
            });
            $grid->column('is_chargeback')->display(function ($value) {
                return admin_trans_option(OrderRelation::$isChargebackMap[$value], 'is_chargeback_map') ?? '-';
            });
            $grid->column('processing_status')->display(function ($value) {
                return admin_trans_option(ModelsOrderRiskTransaction::$processingStatusMap[$value], 'processing_status_map') ?? '-';
            });
            $grid->column('created_at');
            $grid->column('updated_at');

            $grid->column('other')->display(function () {
                $btn = '';
                
                if (
                    (isset(optional($this->relation)->is_refund) && isset(optional($this->relation)->is_chargeback))
                    && (optional($this->relation)->is_refund <> OrderRelation::IS_REFUND_FULL && optional($this->relation)->is_chargeback == OrderRelation::IS_CHARGEBACK_WAITING)
                ) {
                    // 添加接受按钮
                    if ($this->is_accept == ModelsOrderRiskTransaction::IS_ACCEPT_NOT) {
                        $btn .= new Accept($this->order_id);
                    }
                    
                    $btn .= Modal::make()
                        ->xl()
                        ->title(admin_trans_label('Refund'))
                        ->body(Refund::make()->payload(['order_id' => $this->order_id]))
                        ->button('<span class="btn btn-info chargebackEdit">' . admin_trans_label('Refund') . '</span>');
                } else {
                    $btn .= "<span class='btn btn-white disabled'>". admin_trans_label('Refund') . "</span>";
                }

                return $btn;
            });

            $grid->filter(function (Grid\Filter $filter) {
                $filter->where('order_id', function ($query) {
                    $query->where('order_risk_transactions.order_id', $this->input);
                });
                $filter->equal('order_number');
                $filter->equal('url_name')->select(MerchantUrl::get()->pluck('url_name', 'url_name')->toArray());
                $filter->equal('business_id')->select(
                    MerchantBusiness::where('internal_status', MerchantBusiness::INTERNAL_STATUS_ENABLE)->pluck('business_id', 'business_id')->toArray()
                );
                $filter->equal('card_mask');
                $filter->equal('is_refund')->select(admin_trans_label('is_refund_map',OrderRelation::$isRefundMap));
                $filter->equal('is_chargeback')->select(admin_trans_label('is_chargeback_map',OrderRelation::$isChargebackMap));
                $filter->where('processing_status', function($query) {
                    if ($this->input == ModelsOrderRiskTransaction::PROCESSING_STATUS_NOT) {
                        $query->where([
                            ['order_relations.is_refund', '<>', OrderRelation::IS_REFUND_FULL],
                            ['order_relations.is_chargeback', '=',  OrderRelation::IS_CHARGEBACK_NOT],
                            ['order_risk_transactions.is_accept', '=', ModelsOrderRiskTransaction::IS_ACCEPT_NOT],
                        ]);
                    } else {
                        $query->orWhere('order_relations.is_refund', '=', OrderRelation::IS_REFUND_FULL)->orWhere('order_relations.is_chargeback', '<>',  OrderRelation::IS_CHARGEBACK_NOT)->orWhere('order_risk_transactions.is_accept', '<>', ModelsOrderRiskTransaction::IS_ACCEPT_NOT); 
                    }
                })->select(admin_trans_label('processing_status_map',ModelsOrderRiskTransaction::$processingStatusMap));
            });

            // 禁用
            $grid->disableDeleteButton();
            $grid->disableCreateButton();
            $grid->disableEditButton();
            $grid->disableBatchDelete();

            // 禁用行选择器
            $grid->disableRowSelector();

            $grid->fixColumns(1, -2);
        });
    }
    
    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Order(['card', 'address', 'products']), function (Show $show) {

            $show->html();
            $show->disableEditButton();
            $show->disableDeleteButton();

            $show->row(function (Show\Row $show) {
                $show->width(4)->order_id;
                $show->width(4)->parent_order_id;
                $show->width(4)->order_number;
                $show->width(4)->merchant_id;
                $show->width(4)->business_id;
                $show->width(4)->merchant_name;
                $show->width(4)->url_name;
                $show->width(4)->currency;
                $show->width(4)->amount;
                $show->width(4)->type->using(\App\Models\Order::$typesMap);
                $show->width(4)->status->using(\App\Models\Order::$statusMap);
                $show->width(4)->code;
                $show->width(4)->result;
                $show->width(4)->is_3d()->using(admin_trans_label('is_3d', ['否', '是']));
                $show->width(4)->created_at;
                $show->width(4)->updated_at;
            });

            // 卡信息
            $show->card_info(function ($model) {
                return Show::make($model->card['id'], new OrderCard(), function (Show $show) {
                    $show->row(function (Show\Row $show) {
                        $show->width(4)->card_mask;
                        $show->width(4)->cc_type;
                        $show->width(4)->card_country;
                    });

                    $show->disableDeleteButton();
                    $show->disableEditButton();
                    $show->disableListButton();
                });
            });

            // 地址信息
            $show->address_info(function ($model) {
                return Show::make($model->address['id'], new OrderAddress(), function (Show $show) {
                    $show->row(function (Show\Row $show) {
                        $show->width(4)->bill_name;
                        $show->width(4)->bill_email;
                        $show->width(4)->bill_address;
                        $show->width(4)->bill_city;
                        $show->width(4)->bill_state;
                        $show->width(4)->bill_postcode;
                        $show->width(4)->bill_country;
                        $show->width(4)->bill_country_isoa2;
                        $show->width(4)->bill_phone;
                    });

                    $show->row(function (Show\Row $show) {
                        $show->width(4)->ship_name;
                        $show->width(4)->ship_email;
                        $show->width(4)->ship_address;
                        $show->width(4)->ship_city;
                        $show->width(4)->ship_state;
                        $show->width(4)->ship_postcode;
                        $show->width(4)->ship_country;
                        $show->width(4)->ship_country_isoa2;
                        $show->width(4)->ship_phone;
                    });

                    $show->row(function (Show\Row $show) {
                        $show->width(4)->ip;
                        $show->width(4)->ip_country;
                        $show->width(4)->ip_country_isoa2;
                        $show->width(4)->ip_city;
                        $show->width(4)->ip_isp;
                        $show->width(4)->ip_postal_code;
                    });

                    $show->disableDeleteButton();
                    $show->disableEditButton();
                    $show->disableListButton();
                });
            });

            // 产品信息
            $show->product_info_merchant_list(function ($model) {
                $grid = new Grid(new OrderProduct());
                $grid->model()->where(['order_id' => $model->order_id, 'type' => OrderProduct::TYPE_MERCHANT]);

                $grid->name();
                $grid->qty();
                $grid->price();
                $grid->url();
                $grid->attribute();
                $grid->sku();
                $grid->is_gift();
                $grid->is_virtual();

                $grid->disableActions();
                $grid->disableCreateButton();
                $grid->disableRowSelector();
                $grid->disableRefreshButton();
                $grid->disablePagination();

                return $grid;
            });
        });
    }
}
