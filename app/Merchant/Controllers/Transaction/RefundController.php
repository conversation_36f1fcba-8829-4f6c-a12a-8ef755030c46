<?php

namespace App\Merchant\Controllers\Transaction;

use App\Admin\Repositories\Refund;
use App\Merchant\Actions\Tools\Transaction\RefundExportTool;
use App\Models\Refund as RefundModel;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Support\Facades\Auth;

class RefundController extends AdminController
{
    public function title()
    {
        return admin_trans_label('退款信息');    
    }

    protected $exportTitle = [
        'refund_id',
        'order_id',
        'order_number',
        'original_order_currency',
        'original_order_amount',
        'refund_currency',
        'refund_amount',
        'refund_status',
        'code',
        'result',
        'remark',
        'created_at',
        'updated_at',
    ];

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Refund(['order']), function (Grid $grid) {
            $grid->model()->whereHas('order', function ($query) {
                $query->where('merchant_id', Auth::user()->merchant_id);
            })->orderBy('updated_at', 'desc')->orderBy('order_id', 'desc');

            $grid->refund_id->sortable();
            $grid->order_id;
            $grid->column('order.order_number');
            $grid->column('order.merchant_name');
            $grid->column('order.currency');
            $grid->column('order.amount');
            $grid->column('currency');
            $grid->column('amount');
            $grid->column('status')->display(function ($value) {
                return RefundModel::$statusMap[$value] ?? '未知';
            })->dot(['0' => 'danger', '1' => 'success', '2' => 'primary', '3' => 'primary', '4' => 'success']);
            //英文状态下不显示备注
			$lang = session('lang') ?? 'zh_CN';
			if($lang === 'zh_CN') {
                $grid->column('code/result/remark')->display(function () {
                    return $this->code . '<br/>' . $this->result . '<br/>' . $this->remark;
                });
			} else {
                $grid->column('code/result')->display(function () {
                    return $this->code . '<br/>' . $this->result;
                });
            }

            $grid->created_at;
            $grid->updated_at->sortable();

            $grid->tools(new RefundExportTool($this->exportTitle));

            $grid->disableDeleteButton();
            $grid->disableCreateButton();
            $grid->disableEditButton();
            $grid->disableBatchDelete();

            $grid->fixColumns(1, -1);

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('refund_id');
                $filter->equal('order_id');
                $filter->where('merchant_name', function ($query) {
                    $query->whereHas('order', function ($query) {
                        $query->where('merchant_name', 'like', "%{$this->input}%");
                    });
                }, admin_trans_field('merchant_name'));
                $filter->equal('status')->select(RefundModel::$statusMap);
                $filter->where('order_number', function ($query) {
                    $query->whereHas('order', function ($query) {
                        $query->where('order_number', "{$this->input}");
                    });
                }, admin_trans_field('order_number'));
                $filter->between('created_at')->datetime(['sideBySide'=>true]);
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Refund(), function (Show $show) {
            $show->refund_id;
            $show->order_id;
            $show->currency;
            $show->amount;
            $show->status;
            $show->code;
            $show->result;
            //英文状态下不显示备注
			$lang = session('lang') ?? 'zh_CN';
			if($lang === 'zh_CN') {
                $show->remark;
			}
            $show->created_at;
            $show->updated_at;

            $show->disableEditButton();
            $show->disableDeleteButton();
        });
    }
}
