<?php


namespace App\Merchant\Controllers;

use App\Merchant\Controllers\CardVirtual\MerchantCardBaseController;
use Illuminate\Http\Request;
use Dcat\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use App\Models\User;
use chillerlan\QRCode\QRCode;
use Dcat\Admin\Traits\HasFormResponse;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Hash;

class MerchantSecondaryValidationController extends Controller
{
    use HasFormResponse;

    const MODE_CARD_DETAIL    = 0;
    const MODE_CARD_AUTH_CODE = 1;

    public function validateToken(Request $request)
    {
        $user       = auth()->user()->load('merchant');
        $google2fa  = app('pragmarx.google2fa');
        $verifyCode = $request->verify_code;
        $secret     = $request->secret ?? $user->google2fa_secret;
        $mode       = $request->mode;

        if ($user->google2fa_code == $verifyCode) {
            return $this->validationErrorsResponse([
                'verify_code' => admin_trans('admin.验证码已使用'),
            ]);
        }

        if ($google2fa->verifyKey($secret, $verifyCode)) {
            if (!empty($request->secret)) {
                User::query()->where('id', $user->id)->update(['google2fa_secret' => $request->secret]);
            }

            $user->google2fa_code = $request->verify_code;
            $user->save();

            $key         = md5('merchantGoogle2fa' . $user->id);
            $redisKey    = 'merchantGoogle2fa' . md5(time() . mt_rand(10000, 99999));
            $redirectUrl = url(session('redirectUrl'));
            session()->forget('redirectUrl');
            switch ($mode) {
                case self::MODE_CARD_DETAIL:
                    session([MerchantCardBaseController::MERCHANT_FA_CHECK_CONSUMED_KEY => true]);
                    break;
                case self::MODE_CARD_AUTH_CODE:
                    session([MerchantCardBaseController::FA_CHECK_CONSUMED_AUTH_CODE_KEY => true]);
                    break;
            }

            //存储缓存
            Cache::put($redisKey, 1, 24 * 60 * 60);
            return response()->json(['status' => true, 'message' => admin_trans('admin.验证通过'), 'redirect' => $redirectUrl])->cookie($key, $redisKey, 24 * 60 * 60);
        }

        return response()->json(['status' => false, 'message' => admin_trans('admin.验证失败')]);
    }

    public function changePassword(Request $request)
    {
        if (!check_password_strength($request->password)) {
            return $this->validationErrorsResponse([
                'confirm_password' => "新密码必须包含字母/数字/标点符号至少两种，长度8-14位",
            ]);
        }

        if (!$request->password || !$request->confirm_password) {
            return $this->validationErrorsResponse([
                'confirm_password' => "新密码或确认密码为空",
            ]);
        }

        if ($request->password !== $request->confirm_password) {
            return $this->validationErrorsResponse([
                'confirm_password' => "新密码和确认密码不一致",
            ]);
        }

        $user = auth()->user()->load('merchant');

        //验证修改密码是否与最近5次修改重复
        $arr = ['password', 'password1', 'password2', 'password3', 'password4', 'password'];
        foreach ($arr as $val) {
            if (Hash::check($request->password, $user->{$val})) {
                return $this->validationErrorsResponse([
                    'confirm_password' => "新密码不能与最近5次修改重复",
                ]);
            }
        }

        $password                = bcrypt($request->password);
        $user->password4         = $user->password3;
        $user->password3         = $user->password2;
        $user->password2         = $user->password1;
        $user->password1         = $user->password;
        $user->password          = $password;
        $user->password_valid_at = now()->addMonths(3);

        if ($user->save()) {
            return response()->json(['status' => true, 'message' => '修改密码成功', 'redirect' => url('/merchant/auth/logout')]);
        }

        return response()->json(['status' => false, 'message' => '修改密码失败']);
    }

    public function verificationPage(Content $content, $mark, $mode = self::MODE_CARD_DETAIL)
    {
        $data = [
            'company' => config('app.name'),
            'type'    => $mark,
        ];
        $user = auth()->user()->load('merchant');

        switch ($mark) {
            case 'password':
                $data['url'] = admin_route('2fa.merchant.changePassword');
                break;

            default:
                $data['secret']  = '';
                $data['image']   = '';
                $data['display'] = 'display:none;';
                $data['url']     = admin_route('2fa.merchant.validateToken');
                $data['mode']    = $mode;

                if (empty($user->google2fa_secret)) {
                    $data['display'] = 'display:line;';
                    $google2fa       = app('pragmarx.google2fa');
                    $data['secret']  = $google2fa->generateSecretKey();
                    $google2fa_url   = $google2fa->getQRCodeUrl(config('google2fa.mfa_title') . '-' . $user->name, '', $data['secret']);
                    $data['image']   = (new QRCode)->render($google2fa_url);
                }
                break;
        }

        return $content->full()->body(view('admin.2fa.embracyValidate', $data));
    }
}
