<?php

namespace App\Merchant\Controllers\Cards;

use Admin;
use App\Merchant\Metrics\Dashboard\CardTradeCountPie;
use App\Merchant\Metrics\Dashboard\CardTradeFailRatePie;
use App\Merchant\Metrics\Dashboard\CardTradeStatAmountPie;
use App\Merchant\Repositories\CardTradeStat;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Layout\Row;
use Dcat\Admin\Widgets\Card;

class CardTradeStatController extends AdminController
{
    public function title()
    {
        return admin_trans('cards.labels.' . 'CID交易统计');
    }

    public function index(Content $content)
    {
        return $content
            ->title($this->title())
            ->body($this->tradeStat())
            ->body(function (Row $row) {
                $row->column(5, Card::make(admin_trans('cards.labels.' . 'CID交易金额统计'), CardTradeStatAmountPie::make()));
                $row->column(5, Card::make(admin_trans('cards.labels.' . 'CID交易量统计'), CardTradeCountPie::make()));
                $row->column(5, Card::make(admin_trans('cards.labels.' . 'CID交易失败率统计'), CardTradeFailRatePie::make()));
            });
    }

    protected function tradeStat()
    {
        ini_set('memory_limit', '1024M');

        return Grid::make(new CardTradeStat(request()->all()), function (Grid $grid) {
            if (count(request()->toArray()) <= 1) {
                request()->offsetSet('date_stat', [
                    'start' => date('Y-m-d', strtotime('-7 day')),
                    'end'   => date('Y-m-d')
                ]);
            }

            $grid->disableActions();
            $grid->disableRowSelector();
            $grid->disableBatchActions();
            $grid->disableCreateButton();
            $grid->disablePagination();

            $grid->filter(function (Grid\Filter $filter) {
                $timeRange = [
                    '7_days_ago'  => 'last 7',
                    '10_days_ago' => 'last 10',
                    '28_days_ago' => 'last 28',
                    '30_days_ago' => 'last 30',
                ];
                $filter->equal('date')->select($timeRange);
            });

            Admin::script(
                <<<JS
                    // 隐藏分页
                    var page = document.getElementsByClassName("box-footer d-block clearfix text-80");
                    page[0].style.visibility="hidden";
JS
            );
        });
    }
}