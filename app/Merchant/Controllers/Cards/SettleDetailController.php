<?php

namespace App\Merchant\Controllers\Cards;

use App\Models\MerchantCard;
use App\Models\SettleDetailCard;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Support\Facades\Auth;
use DES3;

class SettleDetailController extends AdminController
{
    public function title()
    {
        return admin_trans_label('CID结算明细');
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new SettleDetailCard(), function (Grid $grid) {
            $grid->model()->where('merchant_id', Auth::user()->merchant_id)->where('amount', '!=', '0')->orderByDesc('created_at');

            $grid->column('cards_id', 'CID');
            $grid->column('merchant_id', 'MID');
            $grid->column('merchant_name');
            $grid->column('card_number')->display(function ($value) {
                return $value != '-' ? get_markcard(DES3::decrypt($value, env('DES3_CARD_VIRTUAL'))) : '-';
            });
            $grid->column('amount');
            $grid->column('type')->display(function ($value) {
                return admin_trans_option(SettleDetailCard::$settleDetailCardsTypeMap[$value] ?? admin_trans_field('未知'), 'cards_type_map');
            });
            $grid->column('remarks');
            $grid->column('balance');
            $grid->column('settle_at')->sortable();
            $grid->column('created_at')->sortable();

            $grid->disableCreateButton();
            $grid->disableActions();
            $grid->disableRowSelector();

            $grid->filter(function (Grid\Filter $filter) {
                $cardsIdList = MerchantCard::where('merchant_id', Auth::user()->merchant_id)->where('status', '1')->get()->pluck('cards_id', 'cards_id')->toArray();

                $filter->equal('cards_id', 'CID')->select($cardsIdList);
                $filter->where('card_number', function ($query) {
                    $query->where('card_number', DES3::encrypt($this->input, env('DES3_CARD_VIRTUAL')));
                });
                $filter->equal('type')->select(admin_trans_label('cards_type_map', SettleDetailCard::$settleDetailCardsTypeMap));
                $filter->between('settle_at')->date();
                $filter->between('created_at')->date();
            });
        });
    }
}
