<?php

namespace App\Merchant\Controllers\Cards;

use App\Merchant\Actions\Grid\Cards\TicketIn;
use App\Merchant\Actions\Grid\Cards\TicketOut;
use App\Models\MerchantCard;
use App\Models\MerchantCardTicket;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Support\Facades\Auth;

class TicketController extends AdminController
{
    public function title()
    {
        return admin_trans_label('CID充值工单');    
    }
    
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new MerchantCardTicket(), function (Grid $grid) {
            $grid->model()->where('merchant_id', Auth::user()->merchant_id)->orderBy('id', 'desc');
            $grid->column('merchant_id', 'MID');
            $grid->column('merchant_name');
            $grid->column('cards_id', 'CID');
            $grid->column('type', admin_trans('global.fields.type'))->display(function ($value) {
                return admin_trans_option(MerchantCardTicket::$merchantTicketTypeMap[$value] ?? admin_trans_field('未知'), 'ticket_type_map');
            })->dot(['0' => 'success', '1' => 'danger']);
            $grid->column('currency');
            $grid->column('amount');
            $grid->column('arrival_amount');
            $grid->column('fee');
            $grid->column('status', admin_trans('global.fields.status'))->display(function ($value){
                return admin_trans_option(MerchantCardTicket::$merchantTicketStatusMap[$value] ?? admin_trans_field('未知'), 'ticket_status_map');
            })->dot(['0' => 'primary', '1' => 'success', '2' => 'danger']);
            $grid->column('remark');
            $grid->column('created_at')->sortable();
            $grid->column('updated_at');


            $grid->disableCreateButton();
            $grid->disableActions();
            $grid->disableRowSelector();

            $grid->tools(new TicketIn()); //充值
            $grid->tools(new TicketOut()); //退值

            $grid->filter(function (Grid\Filter $filter) {
                $cardsIdList = MerchantCard::where('merchant_id', Auth::user()->merchant_id)->where('status', '1')->get()->pluck('cards_id', 'cards_id')->toArray();

                $filter->equal('cards_id', 'CID')->select($cardsIdList);
                $filter->equal('type', admin_trans('global.fields.type'))->select(admin_trans_label('ticket_type_map', MerchantCardTicket::$merchantTicketTypeMap));
                $filter->equal('status', admin_trans('global.fields.status'))->select(admin_trans_label('ticket_status_map', MerchantCardTicket::$merchantTicketStatusMap));
                $filter->between('created_at')->date();
            });
        });
    }
}
