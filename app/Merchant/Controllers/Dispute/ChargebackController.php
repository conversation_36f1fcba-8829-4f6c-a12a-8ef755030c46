<?php

namespace App\Merchant\Controllers\Dispute;

use App\Admin\Repositories\Chargeback;
use App\Merchant\Actions\Tools\Dispute\ChargebackExportTool;
use App\Services\ExportService;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Support\Facades\Auth;

class ChargebackController extends AdminController
{
    public function title()
    {
        return admin_trans_label('拒付工单');    
    }

    protected $exportTitle = [
        'order_id',
        'order_number',
        'chargeback_id',
        'merchant_id',
        'merchant_name',
        'chargeback_code',
        'chargeback_type',
        'order_currency',
        'order_amount',
        'is_refund',
        'chargeback_status',
        'url_name',
        'email',
        'arn',
        'order_created_at',
        'chargeback_at',
        'chargeback_deadline'                
    ];

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Chargeback(['order', 'orderRelation', 'chargebackHistory']), function (Grid $grid) {

            $grid->model()->whereHas('order', function ($query) {
                $query->where('merchant_id', Auth::user()->merchant_id);
            });

            $grid->column('order.order_id', admin_trans_field('order_id'))->sortable();
            $grid->column('order.order_number', admin_trans_field('order_number'));
            $grid->column('order.merchant_id', 'MID');
            $grid->column('chargebackHistory.chargeback_code', admin_trans_field('chargeback_code'));
            $grid->column('order.url_name', admin_trans_field('url_name'));
            $grid->column('order.created_at', admin_trans_field('created_at'));
            $grid->column('chargebackHistory.chargeback_at', admin_trans_field('chargeback_at'));
            $grid->column('chargeback_deadline');

            $grid->tools(new ChargebackExportTool($this->exportTitle));

            $grid->disableCreateButton();
            $grid->disableEditButton();
            $grid->disableDeleteButton();
            $grid->disableActions();
            $grid->disableRefreshButton();
            $grid->disableRowSelector();

            $grid->quickSearch(['order_id']);
            $grid->filter(function (Grid\Filter $filter) {
                $filter->panel();

                $filter->equal('order_id')->width(3);
                $filter->where('order_number', function ($query) {
                    $query->whereHas('order', function ($query) {
                        $query->where('order_number', 'like', "%{$this->input}%");
                    });
                })->width(3);
                $filter->equal('chargebackHistory.chargeback_code', admin_trans_field('chargeback_code'))->width(3);
                $filter->whereBetween('chargeback_at',function ($query) {
                    $start = $this->input['start'] ?? '';
                    $end   = $this->input['end'] ?? '';

                    $query->whereHas('chargebackHistory', function ($q) use ($start,$end) {
                        if ($start !== null) {
                            $q->where('chargeback_at', '>=', $start);
                        }

                        if ($end !== null) {
                            $q->where('chargeback_at', '<=', $end);
                        }
                    });
                }, admin_trans_field('chargeback_at'))->datetime(['sideBySide'=>true])->width(4);
            });
        });
    }
}
