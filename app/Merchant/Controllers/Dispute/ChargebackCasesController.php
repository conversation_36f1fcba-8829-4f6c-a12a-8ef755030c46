<?php

namespace App\Merchant\Controllers\Dispute;

use App\Models\ChargebackCase;
use App\Models\OrderRelation;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Support\Facades\Auth;
use App\Merchant\Actions\Tools\Dispute\ChargebackCaseExportTool;
class ChargebackCasesController extends AdminController
{

    public function title()
    {
        return admin_trans_label('拒付预警');    
    }

    protected $exportTitle = [
        'order_id'                       => '订单号',
        'order_number'                   => '商户号',
        'case_id'                        => 'Case Id',
        'merchant_id'                    => 'MID',
        'merchant_name'                  => '商户名',
        'orderRelation.is_chargeback'    => '是否拒付',
        'orderRelation.is_refund'        => '是否退款',
        'created_at'                     => '创建时间',
    ];
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make((ChargebackCase::with(['orderRelation']))->where('merchant_id', Auth::user()->merchant_id), function (Grid $grid) {
            $grid->model()->where(['is_normal' => ChargebackCase::IS_NORMAL_TRUE])->orderByDesc('created_at');

            $grid->column('order_id', admin_trans_field('order_id'))->sortable();
            $grid->column('order_number', admin_trans_field('order_number'));
            $grid->column('case_id', 'CASE ID');
            $grid->column('merchant_id', 'MID');
            $grid->column('merchant_name', admin_trans_field('merchant_name'));
            $grid->column('orderRelation.is_chargeback', admin_trans('chargeback-case.fields.is_chargeback'))->display(function ($value) {
                return admin_trans('chargeback-case.options.is_chargeback_map.' . (OrderRelation::$is_chargeback[$value]) ?? admin_trans_field('未知'));
            })->dot(['0' => 'danger', '1' => 'success'], 'danger');
            $grid->column('orderRelation.is_refund', admin_trans('chargeback-case.fields.is_refund'))->display(function ($value) {
                return admin_trans('chargeback-case.options.is_refund_map.' . (OrderRelation::$isRefundMap[$value] ?? admin_trans_field('未知')));
            })->dot(['0' => 'danger', '1' => 'success', '2' => 'primary'], 'danger');
            $grid->column('created_at', admin_trans('chargeback-case.fields.created_at'));

            $grid->disableCreateButton();
            $grid->disableEditButton();
            $grid->disableDeleteButton();
            $grid->disableActions();
            $grid->disableRefreshButton();
            $grid->disableRowSelector();

            $grid->quickSearch(['order_id']);
            $grid->tools(new ChargebackCaseExportTool());
            $grid->filter(function (Grid\Filter $filter) {
                $filter->panel();

                $filter->equal('order_id', admin_trans_field('order_id'))->width(3);
                $filter->equal('order_number', admin_trans_field('order_number'))->width(3);
                $filter->between('created_at', admin_trans('chargeback-case.fields.created_at'))->datetime(['sideBySide'=>true])->width(4);
            });
        });
    }
}
