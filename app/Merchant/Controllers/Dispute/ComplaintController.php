<?php

namespace App\Merchant\Controllers\Dispute;

use App\Admin\Actions\Grid\Order\Refund;
use App\Admin\Repositories\DirectoryDictionary;
use App\Classes\Pay\Exceptions\Exception;
use App\Handlers\ImageUploadHandler;
use App\Jobs\SendEmail;
use App\Models\Channel;
use App\Models\Order;
use App\Models\OrderAddress;
use App\Models\OrderComplaint;
use App\Models\OrderProduct;
use App\Models\OrderRelation;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Grid;
use Dcat\Admin\Widgets\Modal;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Dcat\Admin\Admin;

class ComplaintController extends AdminController
{
    public function title()
    {
        return admin_trans_label('投诉工单');    
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        Admin::script(
            <<<JS
                $(".orderDetail").dblclick(function(){
                    var orderId = $(this).html();
                    orderId = orderId.replace(/(^\s*)|(\s*$)/g, "");
                    var orderIdArr = orderId.split(';');
                    val = '/merchant/orders/' + orderIdArr[1];
                    window.open(val,'_blank').location;
                })
            JS
        );

        return Grid::make(OrderComplaint::with(['dictionary', 'orderRelation']), function (Grid $grid) {
            $grid->model()
                ->where('merchant_id', Auth::user()->merchant_id)
                ->orderByRaw('FIELD(is_reply, 0,1)')
                ->orderBy('updated_at', 'desc')
                ->orderBy('created_at', 'desc');

            $grid->order_id(admin_trans_field('order_id'))->display(function () {
                return '<button class="orderDetail fa fa-align-right"  style="border: none; color: #009ece;">' . '&nbsp;' . $this->order_id . '</button>';
            });

            $grid->column('order_number', admin_trans_field('order_number'));
            $grid->column('business_id');
            $grid->column('bill_name');
            $grid->column('bill_email');
            $grid->column('dictionary.name');
            $grid->column('content')->display(function ($value) {
                $contents = !empty($value) ? json_decode($value, true) : array();
                $content  = current($contents);

                return isset($content['content']['text']) && !empty($content['content']['text']) ? substr_replace($content['content']['text'], '...', 50) : '...'; // 截取部分内容显示
            });
            $grid->column('is_reply')->display(function ($value) {
                return admin_trans_option(OrderComplaint::$isReplyMap[$value], 'is_reply_map') ?? '-';
            })->dot(['0' => 'danger', '1' => 'success']);
            $grid->column('orderRelation.is_refund', admin_trans_field('is_refund'))->display(function ($value) {
                if (!$value){
                    return '-';
                }
                return admin_trans_option(OrderRelation::$isRefundMap[$value], 'is_refund_map') ?? '-';
            })->dot(['0' => 'danger', '1' => 'success', '2' => 'primary']);
            $grid->column('orderRelation.is_chargeback', admin_trans_field('is_chargeback'))->display(function ($value) {
                if (!$value){
                    return '-';
                }
                return admin_trans_option(OrderRelation::$isChargebackMap[$value], 'is_chargeback_map') ?? '-';
            })->dot(['0' => 'danger', '1' => 'success']);
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();

            // grid代码
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $modale = Modal::make()
                    ->xl()
                    ->title(admin_trans_field('退款'))
                    ->body(Refund::make()->payload(['order_id' => $this->order_id]))
                    ->button('<button type="button" class="btn-outline-primary btn btn-primary btn-shadow btn-change sys-custom-red">' . admin_trans_field('退款') . '</button>');
                // prepend一个操作
                $actions->prepend($modale . '&nbsp;');

                $url = admin_route("m.complaints.item", [$this->id]);
                if (config('merchant.name') == 'Embracy') {
                    $actions->append('<a target="_self" class="btn-danger btn btn-primary btn-shadow btn-green" href="' . $url . '">' . admin_trans_field('详情') . '</a>');
                } else {
                    $actions->append('<a target="_blank" class="btn-danger btn btn-primary btn-shadow btn-green" href="' . $url . '">' . admin_trans_field('详情') . '</a>');
                }
                
            });

            $grid->fixColumns(1, -1);

            $grid->disableEditButton();
            $grid->disableViewButton();
            $grid->disableDeleteButton();
            $grid->disableCreateButton();
            $grid->disableRowSelector();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('order_id');
                $filter->equal('order_number');
                $filter->equal('bill_email');
                $filter->equal('bill_name');
                $filter->equal('dictionary.id', admin_trans_field('id'))->select((new DirectoryDictionary())->getTypeList('投诉标题'));
                $filter->equal('is_reply')->select(admin_trans_label('is_reply_map', OrderComplaint::$isReplyMap));
                $filter->equal('orderRelation.is_refund', admin_trans_field('is_refund'))->select(admin_trans_label('is_refund_map', OrderRelation::$isRefundMap));
                $filter->equal('orderRelation.is_chargeback', admin_trans_field('is_chargeback'))->select(admin_trans_label('is_chargeback_map', OrderRelation::$isChargebackMap));
                $filter->between('created_at')->datetime(['sideBySide'=>true]);
                $filter->between('replied_at')->datetime(['sideBySide'=>true]);
            });
        });
    }

    public function item($id)
    {
        // 初始化数组
        $displayData = [
            'order'            => [],
            'orderAddress'     => [],
            'orderProduct'     => [],
            'orderDelivery'    => [],
            'orderComplaint'   => [],
            'trackingList'     => [],
            'complaintTagList' => [],
            'messageList'      => [],
            'id'               => $id
        ];

        // 投诉标题
        $dictionaryModel                 = new DirectoryDictionary();
        $displayData['complaintTagList'] = $dictionaryModel->getTypeList('投诉标题')->toArray();

        // 工单
        $complaint                     = OrderComplaint::with('order')->find($id)->toArray();
        if (!$complaint['order']){
            $complaint['order'] = new Order();
        }

        $displayData['orderComplaint'] = $complaint;
        $displayData['order']          = $complaint['order'];

        // 渠道信息
        $supplierData                          = Channel::with('channelSupplier')->where('id', $complaint['order']['channel_id'])->first();
        if ($supplierData){
            $displayData['order']['supplier_name'] = $supplierData->channelSupplier->supplier_name;
        }

        // 订单信息
        $orderRelation = OrderRelation::firstWhere('order_id', $complaint['order_id']);
        $orderRelation = $orderRelation ? $orderRelation->toArray() : [];

        // 状态显示
        $statusList = [];

        if (isset($orderRelation['is_refund']) && $orderRelation['is_refund']) {
            $statusList[] = 'Refunded';
        }

        if (isset($orderRelation['is_dishonour']) && $orderRelation['is_dishonour']) {
            $statusList[] = 'Chargebacked';
        }

        $displayData['order']['amount']     = isset($complaint['order']['amount']) ? number_format($complaint['order']['amount'], 2) : '0.00';
        $displayData['order']['status_des'] = empty($statusList) ? 'Approved' : implode('+', $statusList);

        // 订单地址
        $address                     = OrderAddress::with('orders')->where('id', $complaint['order']['address_id'])->first();
        $displayData['orderAddress'] = !empty($address) ? $address->toArray() : [];

        // 商品信息
        $product                     = OrderProduct::where('type', '0')->where('order_id', $complaint['order_id'])->get();
        $displayData['orderProduct'] = !empty($product) ? $product->toArray() : [];

        // 争议工单
        $complaint = OrderComplaint::with('order')->where('order_id', $complaint['order_id'])->first();

        if (!empty($complaint)) {
            $displayData['orderComplaint'] = $complaint->toArray();

            // 对话信息
            $displayData['messageList'] = json_decode($displayData['orderComplaint']['content'], true);
        }

        return view('merchant.complaint.index', $displayData);
    }

    public function save()
    {
        $data = request()->toArray();

        // 数据校验
        $compliantId = request('id');
        $content     = isset($data['text']) ? $data['text'] : '';

        if (empty($compliantId)) {
            return ['error' => true, 'msg' => '非法请求!'];
        }

        // 内容过滤
        $content = preg_replace('/<script[\s\S]*?<\/script>/i', '', $content);

        // 争议工单
        $orderComplaint = OrderComplaint::with('order')->where('id', $compliantId)->first();

        if (empty($orderComplaint)) {
            return ['error' => true, 'msg' => '投诉工单信息不存在!'];
        }

        // 事务
        DB::beginTransaction();

        // 文件上传
        $fileList  = request()->file('imgUpload');
        $imageList = [];
        $uploader  = new ImageUploadHandler();

        if (!empty($fileList)) {
            foreach ($fileList as $key => $file) {
                $result = $uploader->save($file, 'merchant_complaints');

                if ($result) {
                    $imageList[$key]['path'] = $result['path'];
                    $imageList[$key]['url']  = $result['url'];
                }
            }
        }

        if (empty($content) && empty($imageList)) {
            return ['error' => true, 'msg' => '回复信息不能为空'];
        }

        $contentList = json_decode($orderComplaint->content, true);

        if (strlen($content) > 0) {
            $tempData = [
                'type'       => 'Merchant',
                'content'    => array('text' => $content, 'pic' => array()),
                'device'     => '0',
                'ip'         => request()->getClientIp(),
                'by_added'   => Auth::user()->name,
                'date_added' => now()
            ];
            array_unshift($contentList, $tempData);
        } elseif (!empty($imageList)) {
            foreach ($imageList as $key => $list) {
                $tempData = [
                    'type'       => 'Merchant',
                    'content'    => array('text' => '', 'pic' => $list),
                    'device'     => '0',
                    'ip'         => request()->getClientIp(),
                    'by_added'   => Auth::user()->name,
                    'date_added' => now()
                ];
                array_unshift($contentList, $tempData);
            }
        }

        // 系统用户
        $roleIds = DB::table('admin_roles')->select(['id'])->get()->pluck('id')->toArray();
        $roleStr = '';

        foreach ($roleIds as $id) {
            $roleStr .= '[' . $id . ']';
        }

        $contentList = clean($contentList, 'default');

        // 更新
        $data = [
            'order_id'    => $orderComplaint->order_id,
            'merchant_id' => $orderComplaint->merchant_id,
            'content'     => json_encode($contentList),
            'read_ids'    => !empty($roleIds) ? implode(',', $roleIds) : '',
            'is_reply'    => 1,
            'replied_at'  => now()
        ];

        try {
            OrderComplaint::updateOrCreate(['id' => $compliantId], $data);
        } catch (Exception $exception) {
            DB::rollBack();
            return ['error' => true, 'msg' => '操作失败,原因是:' . $exception->getMessage()];
        }

        DB::commit();

        // 发送邮件
        $templateData = [
            'address' => $orderComplaint->bill_email,
            'user'    => $orderComplaint->bill_name,
            'view'    => 'ComplaintReply',
            'data'    => [
                'bill_name'   => $orderComplaint->bill_name,
                'service_url' => admin_route('customs.index', ['0' . md5($orderComplaint->order_id)])
            ]
        ];

        dispatch(new SendEmail($templateData, 5));

        return ['error' => false, 'msg' => 'success', 'content' => $content];
    }
}
