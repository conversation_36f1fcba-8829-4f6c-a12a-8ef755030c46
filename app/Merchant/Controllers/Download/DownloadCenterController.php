<?php

namespace App\Merchant\Controllers\Download;

use App\Admin\Repositories\DownloadCenter;
use App\Models\MerchantDownloadCenter;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class DownloadCenterController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new MerchantDownloadCenter(), function (Grid $grid) {
            $user = Admin::user();

            $grid->model()->where('user_id', $user->id)->orderByDesc('created_at');

            $grid->showColumnSelector();
            $grid->hideColumns(['id', 'user_id', 'merchant_id', 'updated_at']);
            $grid->disableCreateButton();
            $grid->disableViewButton();
            $grid->disableEditButton();

            $grid->column('id')->sortable();
            $grid->column('user_id');
            $grid->column('merchant_id');
            $grid->column('file_name');
            $grid->column('export_identity')->display(function ($identity) {
                return admin_trans_option(MerchantDownloadCenter::$exportIdentityMap[$identity] ?? $identity, 'export_identity_map');
            });

            $grid->column('status')->using(admin_trans_label('status', MerchantDownloadCenter::$exportStatusMap))
                ->dot([
                    0 => 'primary',
                    1 => 'info',
                    2 => 'success',
                    3 => 'danger',
                ], 'primary');
            $grid->column('url')->display(function ($url) {

                if ($url != '' && $this->status == MerchantDownloadCenter::EXPORT_STATUS_SUCCESS) {
                    if (env('APP_NAME', 'Laravel') == 'PunctualPay') {
                        return '<a href="' . $url . '" target="_blank">' . admin_trans_field('click_download') . '</a>';
                    } else {
                        return '<a href="' . $url . '" target="_blank"><button class="btn-mini btn-outline btn btn-primary btn-shadow btn-change">' . admin_trans_field('click_download') . '</button></a>'; 
                    }
                }

                return '';
            });
            $grid->column('created_at')->sortable();
            $grid->column('updated_at');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->panel();
                $filter->equal('file_name')->width(3);
                $filter->equal('status')->select(admin_trans_label('status', MerchantDownloadCenter::$exportStatusMap))->width(3);
                $filter->equal('export_identity')->select(admin_trans_label('export_identity_map', MerchantDownloadCenter::$exportIdentityMap))->width(3);

            });
        });
    }

    protected function form()
    {
        return Form::make(new MerchantDownloadCenter(), function (Form $form) {

            $form->deleted(function (Form $form, $result) {
                $data = $form->model()->toArray();
                if ($result) {
                    foreach ($data as $value) {
                        Storage::disk('data')->delete('/merchantExport/' . $value['file_name']);
                    }
                }
            });

        });


    }
}
