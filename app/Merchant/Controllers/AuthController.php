<?php

namespace App\Merchant\Controllers;

use function Couchbase\defaultDecoder;
use Dcat\Admin\Http\Controllers\AuthController as BaseAuthController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\SystemLogin;
use App\Services\SecondaryValidationService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Cookie;
use App\Models\Merchant;

class AuthController extends BaseAuthController
{
    /**
     * @var string
     */
    protected $view = 'merchant::views.pages.login';

    public function __construct()
    {
        switch (config('merchant.name')) {
            case 'Embracy':
                $this->view = 'merchant::views.pages.embracyLogin';
                break;

            case 'PunctualPay':
                $this->view = 'merchant::views.pages.punctualPayLogin';
                break;

            case 'Hpaymerchants':
                $this->view = 'merchant::views.pages.hpaymerchantsLogin';
                break;

            default:
                $this->view = 'merchant::views.pages.login';
                break;
        }
    }
    /**
     * Handle a login request.
     *
     * @param Request $request
     *
     * @return mixed
     */
    public function postLogin(Request $request)
    {
        $credentials = $request->only(['merchant_id', $this->username(), 'password']);
        $remember    = (bool) $request->input('remember', false);

        /** @var \Illuminate\Validation\Validator $validator */
        $validator = Validator::make($credentials, [
            'merchant_id'     => 'required|digits:15',
            $this->username() => 'required',
            'password'        => 'required',
        ]);

        if ($validator->fails()) {
            return $this->validationErrorsResponse($validator);
        }

        $ip = $request->getClientIp(); //登录IP

        //检测是否触发拦截
        $row = SecondaryValidationService::allowLogin($ip, SystemLogin::SYSTEM_TYPE_MERCHANT);

        if ($row['status'] == false) {
            return $this->validationErrorsResponse([
                $this->username() => "连续登录失败{$row['num']}次，请{$row['divide']}分钟后重试",
            ]);
        }

        if ($this->guard()->attempt($credentials, $remember)) {
            //登录成功把失败记录重置和新增成功记录
            SystemLogin::query()->where('ip', $ip)
                ->where('system_type', SystemLogin::SYSTEM_TYPE_MERCHANT)
                ->where('status', SystemLogin::STATUS_FAILED)
                ->update(['status' => SystemLogin::STATUS_RESETTING]);

            $systemLogin              = new SystemLogin();
            $systemLogin->account     = $credentials[$this->username()];
            $systemLogin->ip          = $ip;
            $systemLogin->system_type = SystemLogin::SYSTEM_TYPE_MERCHANT;
            $systemLogin->status      = SystemLogin::STATUS_SUCCESS;
            $systemLogin->save();

            //记录自动退出时间
            session(['merchantLogout' => time() + 20 * 60]);
            return $this->sendLoginResponse($request);
        }

        //账号或密码错误，增加失败记录
        $systemLogin              = new SystemLogin();
        $systemLogin->account     = $credentials[$this->username()];
        $systemLogin->ip          = $ip;
        $systemLogin->system_type = SystemLogin::SYSTEM_TYPE_MERCHANT;
        $systemLogin->status      = SystemLogin::STATUS_FAILED;
        $systemLogin->save();

        return $this->validationErrorsResponse([
            $this->username() => $this->getFailedLoginMessage(),
        ]);
    }

    /**
     * User logout.
     *
     * @return Redirect|string
     */
    public function getLogout(Request $request)
    {
        $data = $request->all();
        $user = Admin::user();
        $this->guard()->logout();

        $request->session()->invalidate();

        $path = admin_url('auth/login');

        if (!isset($data['make']) && $user) {
            //主动退出，清除cookie和缓存
            $key      = md5('merchantGoogle2fa' . $user->id);
            $redisKye = Cookie::get($key);

            if ($redisKye) {
                Cache::forget($redisKye);
                Cookie::queue(Cookie::forget($key));
            }

            $redirect = redirect($path)->withCookie(Cookie::forget($key));
        } else {
            $redirect = redirect($path);
        }

        if ($request->pjax()) {
            return "<script>location.href = '$path';</script>";
        }

        return $redirect;
    }

    /**
     * Get the login username to be used by the controller.
     *
     * @return string
     */
    protected function username()
    {
        return 'name';
    }

    /**
     * Show the login page.
     *
     * @return Content
     */
    public function getLogin(Content $content)
    {
        if ($this->guard()->check()) {
            return redirect($this->getRedirectPath());
        }

        return $content->full()->body(view($this->view));
    }

    /**
     * User setting page.
     *
     * @param Content $content
     *
     * @return Content
     */
    public function getSetting(Content $content)
    {
        Admin::script(
            <<<JS
            $(".form-group.row.form-field:first").remove();
            $(".feather.icon-eye").on('click', (eye) => {
                let password = $(eye.target).parent().parent().next();

                if(password.hasClass("on")) {
                    password.removeClass("on");
                    password.attr("type","password");
                } else {
                    password.addClass("on");
                    password.attr("type","text");
                }
            });
            JS
        );
        $form = $this->settingForm();

        $form->tools(
            function (Form\Tools $tools) {
                $tools->disableList();
            }
        );

        return $content
            ->title(trans('admin.user_setting'))
            ->body($form->edit(Admin::user()->getKey()));
    }
}
