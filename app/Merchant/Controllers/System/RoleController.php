<?php

namespace App\Merchant\Controllers\System;

use App\Merchant\Repositories\MerchantRole;
use App\Models\MerchantRole as RoleModel;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Support\Helper;
use Dcat\Admin\Widgets\Tree;
use Illuminate\Support\Facades\Gate;
use Illuminate\Validation\Rule;

class RoleController extends AdminController
{
    public function title()
    {
        return admin_trans_label('角色');    
    }

    public function show($id, Content $content)
    {
        Gate::authorize('own', RoleModel::find($id));
        return parent::show($id, $content);
    }

    public function edit($id, Content $content)
    {
        Gate::authorize('own', RoleModel::find($id));
        return parent::edit($id, $content);
    }

    public function update($id)
    {
        Gate::authorize('own', RoleModel::find($id));
        return parent::update($id);
    }

    public function destroy($id)
    {
        Gate::authorize('own', RoleModel::find($id));
        return parent::destroy($id);
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new MerchantRole());
        $grid->model()->whereIn('merchant_id', [Admin::user()->merchant_id, '0']);
        $grid->id('ID')->sortable();
        $grid->slug->label('primary');
        $grid->name;
        $grid->created_at;
        $grid->updated_at->sortable();
        $grid->disableBatchDelete();
        $grid->disableEditButton();
        $grid->showQuickEditButton();
        $grid->disableFilterButton();
        $grid->quickSearch(['id', 'name', 'slug']);
        $grid->enableDialogCreate();

        $grid->actions(function (Grid\Displayers\Actions $actions) {
            $roleModel = config('admin.database.roles_model');

            if ($roleModel::isAdministrator($actions->row->slug)) {
                $actions->disableDelete();
                $actions->disableEdit();
                $actions->disableQuickEdit();
                $actions->disableView();
            }
        });

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new MerchantRole('permissions'), function (Show $show) {
            $show->id;
            $show->slug;
            $show->name;

            $show->permissions->unescape()->as(function ($permission) {
                $permissionModel = config('merchant.database.permissions_model');
                $permissionModel = new $permissionModel();
                $nodes = $permissionModel->allNodes();

                foreach ($nodes as &$node) {
                    $node->name = admin_trans_label($node->name);
                }
                
                $tree = Tree::make($nodes);

                $keyName = $permissionModel->getKeyName();
                $tree->check(
                    array_column(Helper::array($permission), $keyName)
                );

                return $tree->render();
            });

            $show->created_at;
            $show->updated_at;

            if ($show->getKey() == RoleModel::ADMINISTRATOR_ID) {
                $show->disableDeleteButton();
            }
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new MerchantRole('permissions'), function (Form $form) {
            $roleTable = config('merchant.database.roles_table');
            $id        = $form->getKey();

            $form->display('id', 'ID');

            $form->text('slug', trans('admin.slug'))
                ->required()
                ->creationRules(['required', Rule::unique($roleTable)->where(function ($query) {
                    return $query->where('merchant_id', Admin::user()->merchant_id);
                })])
                ->updateRules(['required', Rule::unique($roleTable)->ignore($id)->where(function ($query) {
                    return $query->where('merchant_id', Admin::user()->merchant_id);
                })]);

            $form->text('name', trans('admin.name'))->required();

            $form->tree('permissions')
                ->nodes(function () {
                    $permissionModel = config('merchant.database.permissions_model');
                    $permissionModel = new $permissionModel();
                    $nodes           = $permissionModel->allNodes();

                    foreach ($nodes as &$node) {
                        $node->name = admin_trans_label($node->name);
                    }

                    return $nodes;
                })
                ->customFormat(function ($v) {
                    if (! $v) {
                        return [];
                    }

                    return array_column($v, 'id');
                });

            $form->display('created_at', trans('admin.created_at'));
            $form->display('updated_at', trans('admin.updated_at'));

            if ($id == RoleModel::ADMINISTRATOR_ID) {
                $form->disableDeleteButton();
            }
        });
    }
}
