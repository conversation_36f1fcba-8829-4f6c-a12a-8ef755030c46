<?php

namespace App\Merchant\Controllers\System;

use App\Admin\Actions\Grid\Record;
use App\Admin\Actions\Grid\S2s;
use App\Merchant\Actions\Grid\User\ResetAccount;
use App\Merchant\Forms\User\BidList;
use App\Merchant\Repositories\User;
use App\Models\OpenImUser;
use App\Models\User as UserModel;
use App\Services\OpenImService;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Show;
use Dcat\Admin\Widgets\Tree;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;

class UserController extends AdminController
{
    public function title()
    {
        return admin_trans_label('用户');
    }

    public function show($id, Content $content)
    {
        Gate::authorize('own', UserModel::find($id));
        return parent::show($id, $content);
    }

    public function edit($id, Content $content)
    {
        Gate::authorize('own', UserModel::find($id));
        return parent::edit($id, $content);
    }

    public function update($id)
    {
        Gate::authorize('own', UserModel::find($id));
        return parent::update($id);
    }

    public function destroy($id)
    {
        Gate::authorize('own', UserModel::find($id));
        return parent::destroy($id);
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new User(['roles']));

        $grid->model()->whereIn('merchant_id', [Admin::user()->merchant_id, '0']);
        $grid->name;
        $grid->email;

        if (config('admin.permission.enable')) {
            $grid->roles->pluck('name')->label('primary', 3);

            $permissionModel = config('admin.database.permissions_model');
            $roleModel       = config('admin.database.roles_model');
            $nodes           = (new $permissionModel())->allNodes();

            foreach ($nodes as &$node) {
                $node->name = admin_trans_label($node->name);
            }

            $grid->permissions
                ->if(function () {
                return ! empty($this->roles);
            })
                ->showTreeInDialog(function (Grid\Displayers\DialogTree $tree) use (&$nodes, $roleModel) {
                    $tree->nodes($nodes);

                    foreach (array_column($this->roles, 'slug') as $slug) {
                        if ($roleModel::isAdministrator($slug)) {
                            $tree->checkAll();
                        }
                    }
                })
                ->else()
                ->display('');
        }

        if (auth()->user()->merchant->is_credit) {
            $grid->column('bid', trans('admin.bid'))->if(function () {
                if ($this->name == 'admin') {
                    return false;
                }

                return true;
            })->display(admin_trans_field('config'))->modal(function () {
                return BidList::make()->payload(['merchant_id' => $this->merchant_id, 'user_id' => $this->id]);
            })->else()->display('');
        }

        $grid->disableBatchDelete();
        $grid->disableEditButton();
        $grid->showQuickEditButton();
        $grid->disableFilterButton();
        $grid->quickSearch(['name', 'email']);
        $grid->enableDialogCreate();

        $grid->actions(function (Grid\Displayers\Actions $actions) {
            if ($actions->row->name == 'admin') {
                $actions->disableDelete();
            }

            if ($actions->row->name != 'admin') {
                $actions->append(new ResetAccount());
            }
        });

        return $grid;
    }

    protected function detail($id)
    {
        return Show::make($id, new User('roles'), function (Show $show) {
            $show->id;
            $show->name;

            $show->avatar(__('admin.avatar'))->image();

            if (config('admin.permission.enable')) {
                $show->roles->as(function ($roles) {
                    if (! $roles) {
                        return;
                    }

                    return collect($roles)->pluck('name');
                })->label();

                $show->permissions->unescape()->as(function () {
                    $roles = $this->roles->toArray();

                    $permissionModel = config('admin.database.permissions_model');
                    $roleModel = config('admin.database.roles_model');
                    $permissionModel = new $permissionModel();
                    $nodes = $permissionModel->allNodes();

                    foreach ($nodes as &$node) {
                        $node->name = admin_trans_label($node->name);
                    }

                    $tree = Tree::make($nodes);

                    $isAdministrator = false;
                    foreach (array_column($roles, 'slug') as $slug) {
                        if ($roleModel::isAdministrator($slug)) {
                            $tree->checkAll();
                            $isAdministrator = true;
                        }
                    }

                    if (! $isAdministrator) {
                        $keyName = $permissionModel->getKeyName();
                        $tree->check(
                            $roleModel::getPermissionId(array_column($roles, $keyName))->flatten()
                        );
                    }

                    return $tree->render();
                });
            }

            $show->created_at;
            $show->updated_at;

            $show->disableDeleteButton();
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        Admin::script(
            <<<JS
            $('#random_password').on('click', () => {
                $.ajax({
                    url: '/get_random_password',
                    type: 'GET',
                    success: function(res) {
                        $('.help-block').parent().find('.box-body').text(res);
                        $('input[name=password]').val(res);
                        $('input[name=password_confirmation]').val(res);
                    }
                });
            });
            JS
        );
        return Form::make(new User(['roles']), function (Form $form) {
            $id        = $form->getKey();
            $userTable = config('admin.database.users_table');

            if ($id) {
                $form->display('name', trans('admin.username'));
            } else {
                $form->text('name', trans('admin.username'))
                    ->required()
                    ->creationRules(['required', Rule::notIn(['admin']), Rule::unique($userTable)->where(function ($query) {
                        return $query->where('merchant_id', Admin::user()->merchant_id);
                    })]);
            }

            $form->email('email')->rules(['required', Rule::unique('users')->ignore($id)]);
            $form->image('avatar', trans('admin.avatar'));
            $form->display('random_password', trans('admin.random_password'))->help(trans('admin.click_generate'));
            $form->html('<span style="cursor: pointer" class="copy-group"><a href="javascript:;" id="random_password">'.trans('admin.generate_random_password').'</a></span>');

            if ($id) {
                $form->password('password', trans('admin.password'))
                    ->minLength(5)
                    ->maxLength(20)
                    ->customFormat(function () {
                        return '';
                    });
            } else {
                $form->password('password', trans('admin.password'))
                    ->required()
                    ->minLength(5)
                    ->maxLength(20);
            }

            $form->password('password_confirmation', trans('admin.password_confirmation'))->same('password');
            $form->hidden('password1');
            $form->hidden('password2');
            $form->hidden('password3');
            $form->hidden('password4');
            $form->hidden('password_valid_at');
            $form->ignore(['password_confirmation', 'random_password']);

            if (config('admin.permission.enable')) {
                if ($id && UserModel::find($id)->isMainAccount()) {
                    $this->getMultipleSelect($form)->disable();
                } else {
                    $this->getMultipleSelect($form);
                }
            }

            $form->hidden('merchant_id');
            $form->display('created_at', trans('admin.created_at'));
            $form->display('updated_at', trans('admin.updated_at'));
        })->saving(function (Form $form) {
            $password = $form->password;

            if ($password) {
                //密码强度验证
                if (check_password_strength($password)) {
                    $arr = ['password', 'password1', 'password2', 'password3', 'password4'];
                    foreach ($arr as $val) {
                        if (Hash::check($password, $form->model()->{$val})) {
                            return $form->response()->error('新密码不能与最近5次修改重复');
                        }
                    }

                    $form->password4           = $form->model()->password3;
                    $form->password3           = $form->model()->password2;
                    $form->password2           = $form->model()->password1;
                    $form->password1           = $form->model()->password;
                    $form->password            = bcrypt($password);
                    $form->password_valid_at   = now()->addMonths(3);
                } else {
                    return $form->response()->error('密码必须包括：字母/数字/标点符号至少两种并且长度为8-14位');
                }
            } else {
                $form->deleteInput('password');
            }

            $form->merchant_id = Admin::user()->merchant_id;

            if (!isset($form->roles[0]) || empty($form->roles[0])) {
                $form->deleteInput('roles');
            }

            return true;
        })->saved(function (Form $form, $result) {
            if ($form->isCreating()) {
                if (config('open_im.open_im')) {
                    // 查询主账户
                    $mainUser = OpenImUser::where('merchant_id', Admin::user()->merchant_id)->where('status', OpenImUser::STATUS_NORMAL)->where('user_type', OpenImUser::USER_TYPE_MERCHANT_MAIN)->first();
                    if (!$mainUser) {
                        return $form->response()->error('主账户不存在');
                    }
                    // 注册openIm
                    $openImUser = [
                        'userId'          => $result,
                        'userType'        => OpenImUser::USER_TYPE_MERCHANT_SUB,
                        'nickname'        => $form->name,
                        'password'        => $form->password,
                        'addGroup'        => false,
                        'addFriend'       => false,
                        'createGroup'     => false,
                        'defaultAddGroup' => false,
                        'groupRoleLevel'  => OpenImUser::ROLE_LEVEL_MEMBER,
                        'userName'        => 'system',
                    ];
                    $openIm     = new OpenImService;
                    $userId     = $openIm->register($openImUser, $mainUser['merchant_id'], $mainUser['merchant_name']);
                    if ($userId === '') {
                        return $form->response()->error('openIm注册失败');
                    }
                    // 商户主账户和子账户默认为好友
                    if (!$openIm->addFriend($mainUser['open_im_id'], $userId)) {
                        return $form->response()->error('openIm添加好友失败');
                    }
                }
            }

            return true;
        });
    }

    protected function getMultipleSelect(Form $form)
    {
        return $form->multipleSelect('roles', trans('admin.roles'))
            ->options(function () {
                $roleModel = config('admin.database.roles_model');

                return $roleModel::whereIn('merchant_id', [Admin::user()->merchant_id, '0'])->pluck('name', 'id');
            })
            ->customFormat(function ($v) {
                return array_column($v, 'id');
            });
    }
}
