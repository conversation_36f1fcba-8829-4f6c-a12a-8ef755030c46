<?php

namespace App\Merchant\Controllers\Transfer;

use App\Jobs\SendNotice;
use App\Merchant\Repositories\TransferAccount;
use App\Models\Merchant;
use App\Models\TransferAccount as Models;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Support\Facades\Auth;
use Dcat\Admin\Admin;
use Dcat\Admin\Show;

class AccountController extends AdminController
{
    public function title()
    {
        return admin_trans_label('提现账户');
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new TransferAccount(), function (Grid $grid) {
            $grid->model()->where('cardholder_id', Auth::user()->merchant_id)->orderBy('created_at', 'DESC');

            $grid->column('type', admin_trans_field('type'))->display(function ($value) {
                return admin_trans_option(Models::$typeMap[$value] ?? admin_trans_field('未知'), 'currency_type_map');
            });
            $grid->column('bank_name', admin_trans_field('bank_name' . Models::TYPE_DOMESTIC));
            $grid->column('bank_address', admin_trans_field('bank_address' . Models::TYPE_DOMESTIC));
            $grid->column('nickname');
            $grid->column('cardholder', admin_trans_field('cardholder' . Models::TYPE_ABROAD));
            $grid->column('bank_account', admin_trans_field('bank_account'));
            $grid->column('convert_currency', admin_trans_field('convert_currency' . Models::TYPE_DOMESTIC));
            $grid->column('swift_iban');
            $grid->column('status')->display(function ($value) {
                return admin_trans_option(Models::$statusmerchantMap[$value] ?? admin_trans_field('未知'), 'status_merchant_map');
            });
            $grid->column('remarks');
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();

            //不是重新审核状态隐藏按钮 已过审账号不给删除
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                if ($actions->row->status != Models::STATUS_RE_AUDIT) {
                    $actions->disableEdit();
                }
                if ($actions->row->status == Models::STATUS_ADOPT) {
                    $actions->disableDelete();
                }
            });

            $grid->disableRowSelector();
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $merchant = Merchant::where('merchant_id', Auth::user()->merchant_id)->first();
        return Form::make(new TransferAccount(), function (Form $form) use ($merchant) {
            // 可提现币种处理
            $transfer = !isset($merchant->transfer) ? [] : array_column($merchant->transfer, 'transfer_currency', 'transfer_currency');
            $form->radio('type')->options(admin_trans_label('currency_type_map', Models::$typeMap))->default(0)
                ->when(0, function (Form  $form) use ($transfer) {
                    //选择人民币账户，出款货币只能选择CNY
                    if (isset($transfer['CNY'])) {
                        $transfer = ['CNY' => 'CNY'];
                    } else {
                        $transfer = [];
                    }

                    $form->select('convert_currency' . Models::TYPE_DOMESTIC)->options($transfer)
                        ->rules('required_if:type,0', ['required_if' => admin_trans_field('convert_currency') . admin_trans_field('cannot_be_empty')])->setLabelClass(['asterisk'])->help(admin_trans_field('tip.rmb_support'));

                    $form->text('bank_name' . Models::TYPE_DOMESTIC)->rules('required_if:type,0', ['required_if' => admin_trans_field('tip.bank_null_error')])
                        ->setLabelClass(['asterisk'])->help(admin_trans_field('tip.bank_name'));

                    $form->text('bank_address' . Models::TYPE_DOMESTIC)->rules('required_if:type,0', ['required_if' => admin_trans_field('tip.bank_address_null_error')])
                        ->setLabelClass(['asterisk']);

                    $form->text('inter_bank_number')->rules('required_if:type,0', ['required_if' => admin_trans_field('tip.inter_bank_number')])->setLabelClass(['asterisk']);

                    $form->text('bank_account' . Models::TYPE_DOMESTIC)->rules('required_if:type,0', ['required_if' => admin_trans_field('tip.bank_account_null_error')])
                        ->setLabelClass(['asterisk']);

                    $form->text('cardholder' . Models::TYPE_DOMESTIC)->rules('required_if:type,0', ['required_if' => admin_trans_field('tip.cardholder_id_name')])
                        ->setLabelClass(['asterisk']);

                    $form->text('cardholder_id_card')->rules('required_if:type,0', ['required_if' => admin_trans_field('tip.cardholder_id_card')])
                        ->setLabelClass(['asterisk']);

                    $form->mobile('bank_mobile')->defaultAttribute('style', 'width:230px;flex:none')->rules('required_if:type,0', ['required_if' => admin_trans_field('tip.bank_mobile')])
                        ->setLabelClass(['asterisk'])->placeholder(admin_trans_field('tip.bank_mobile_placeholder'));

                    $form->text('nickname')->rules("nullable|unique:transfer_accounts,nickname,{$form->getKey()},id,cardholder_id,{$form->cardholder_id}");
                })
                ->when(1, function (Form  $form) use ($transfer) {
                    //删除CNY
                    unset($transfer['CNY']);

                    $form->select('convert_currency' . Models::TYPE_ABROAD)->options($transfer)
                        ->rules('required_if:type,1', ['required_if' => admin_trans_field('tip.disbursement_currency')])->setLabelClass(['asterisk'])
                        ->help(admin_trans_field('tip.not_rmb_support'));

                    $form->text('bank_name' . Models::TYPE_ABROAD)->rules('required_if:type,1', ['required_if' => admin_trans_field('tip.bank_english_null_error')])
                        ->setLabelClass(['asterisk']);

                    $form->text('bank_address' . Models::TYPE_ABROAD)->rules('required_if:type,1', ['required_if' => admin_trans_field('tip.bank_address_english_null_error')])
                        ->setLabelClass(['asterisk']);

                    $form->text('swift_iban')->rules('required_if:type,1', ['required_if' => 'SWIFT/IBAN ' . admin_trans_field('cannot_be_empty')])->setLabelClass(['asterisk']);

                    $form->text('bank_account' . Models::TYPE_ABROAD)->rules('required_if:type,1', ['required_if' => admin_trans_field('tip.bank_account_null_error')])
                        ->setLabelClass(['asterisk']);

                    $form->text('cardholder' . Models::TYPE_ABROAD)->rules('required_if:type,1', ['required_if' => admin_trans_field('tip.account_name_null_error')])
                        ->setLabelClass(['asterisk']);

                    $form->text('company_address')->rules('required_if:type,1', ['required_if' => admin_trans_field('tip.company_address_null_error')])
                        ->setLabelClass(['asterisk']);

                    $form->text('nickname')->rules("nullable|unique:transfer_accounts,nickname,{$form->getKey()},id,cardholder_id,{$form->cardholder_id}");
                })->when(2, function (Form  $form) use ($transfer) {
                    //选择Crypto账户，出款货币只能选择T
                    if (isset($transfer['T'])) {
                        $transfer = ['T' => 'T'];
                    } else {
                        $transfer = [];
                    }

                    $form->select('convert_currency' . Models::TYPE_CRYPTO)->options($transfer)
                        ->rules('required_if:type,2', ['required_if' => admin_trans_field('convert_currency') . admin_trans_field('cannot_be_empty')])->setLabelClass(['asterisk']);

                    $form->text('bank_account' . Models::TYPE_CRYPTO)->rules('required_if:type,2', ['required_if' => admin_trans_field('tip.crypto_address_null_error')])
                        ->setLabelClass(['asterisk']);
                });

            $form->hidden('cardholder_id')->value(Auth::user()->merchant_id);

            //提示信息改成红色
            Admin::script(
                <<<JS
                    $(".help-block").css('color', 'red');
                JS
            );
        })->saving(function (Form $form) {
            //选择人民币账户，判断身份证号和手机号格式
            if ($form->type == Models::TYPE_DOMESTIC) {
                if (!is_id_card($form->cardholder_id_card)) {
                    return $form->response()->error(admin_trans_field('tip.cardholder_id_card_error'));
                }

                if (!is_mobile($form->bank_mobile)) {
                    return $form->response()->error(admin_trans_field('tip.bank_mobile_error'));
                }
            }
        })->saved(function (Form $form) use ($merchant) {
            if ($form->isCreating()) {
				// 发送企业微信通知到 Withdrawal reminder 角色
				$noticeData = [
					'level'             => 1,
					'contents'          =>
						'[已提交新提现账户]' . PHP_EOL .
						'MID: ' . $form->cardholder_id . PHP_EOL .
						'商户名称: ' . $merchant->merchant_name . PHP_EOL .
						'来源:' . config('app.url'),
					'notice_user_roles' => 'Withdrawal reminder',
					'type'              => 3,
					'status'            => 2,
				];
				dispatch(new SendNotice($noticeData, 5, 'workNoticeStorage'));
            }
        });
    }

    // 数据详情
    protected function detail($id)
    {
        return Show::make($id, new TransferAccount(), function (Show $show) {
            $show->field('type')->as(function ($type) {
                return admin_trans_option(Models::$typeMap[$type], 'currency_type_map');
            });

            $show->field('convert_currency', admin_trans_field('convert_currency' . Models::TYPE_DOMESTIC));

            switch ($show->model()->type) {
                case Models::TYPE_DOMESTIC:
                    $show->field('bank_name', admin_trans_field('bank_name' . Models::TYPE_DOMESTIC));
                    $show->field('bank_address', admin_trans_field('bank_address' . Models::TYPE_DOMESTIC));
                    $show->field('inter_bank_number');
                    $show->field('bank_account', admin_trans_field('bank_account' . Models::TYPE_DOMESTIC));
                    $show->field('cardholder', admin_trans_field('cardholder' . Models::TYPE_DOMESTIC));
                    $show->field('cardholder_id_card');
                    $show->field('bank_mobile');
                    $show->field('nickname');
                    break;

                case Models::TYPE_ABROAD:
                    $show->field('bank_name', admin_trans_field('bank_name' . Models::TYPE_ABROAD));
                    $show->field('bank_address', admin_trans_field('bank_address' . Models::TYPE_ABROAD));
                    $show->field('swift_iban');
                    $show->field('bank_account', admin_trans_field('bank_account' . Models::TYPE_ABROAD));
                    $show->field('cardholder', admin_trans_field('cardholder' . Models::TYPE_ABROAD));
                    $show->field('company_address');
                    $show->field('nickname');
                    break;

                case Models::TYPE_CRYPTO:
                    $show->field('bank_account', admin_trans_field('bank_account' . Models::TYPE_CRYPTO));
                    break;
            }

            $show->field('created_at');
            $show->field('updated_at');

            $show->disableEditButton();
            $show->disableDeleteButton();
        });
    }
}
