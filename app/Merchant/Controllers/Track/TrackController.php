<?php

namespace App\merchant\Controllers\Track;

use App\Admin\Actions\Grid\Track\Details;
use App\Admin\Repositories\OrderTrack;
use App\Models\DirectoryCarrier;
use App\Models\OrderTrack as OrderTrackModel;
use App\Services\TrackService;
use App\Traits\TrackingTypeRules;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Widgets\Modal;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Dcat\Admin\Admin;

class TrackController extends AdminController
{

    use TrackingTypeRules;

    public function title()
    {
        return admin_trans_label('运单信息');    
    }
    
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        Admin::script(
            <<<JS
                $(".orderDetail").dblclick(function(){
                    var orderId = $(this).html();
                    orderId = orderId.replace(/(^\s*)|(\s*$)/g, "");
                    var orderIdArr = orderId.split(';');
                    val = '/merchant/orders/' + orderIdArr[1];
                    window.open(val,'_blank').location;
                })
            JS
        );

        return Grid::make(new OrderTrack(['order', 'order.address', 'order.relation']), function (Grid $grid) {
            $grid->model()->whereHas('order', function (Builder $query) {
                $query->where('orders.merchant_id', Auth::user()->merchant_id);
            });

            $grid->content(admin_trans_field('物流信息'))->display(function ($modal) {
                $modal = Modal::make()
                    ->title(admin_trans_field('物流信息'))
                    ->lg()
                    ->body(Details::make()->payload(['order_id' => $this->order_id]))
                    ->button('<button class="btn btn-primary btn-shadow btn-green">' . admin_trans_field('详情') . '</button>');

                return $modal;
            });

            $grid->order_id(admin_trans_field('order_id'))->display(function () {
                return '<button class="orderDetail fa fa-align-right"  style="border: none; color: #009ece;">' . '&nbsp;' . $this->order->order_id . '</button>';
            });

            $grid->column('order.order_number', admin_trans_field('商户订单号'));
            $grid->column('order.url_name', admin_trans_field('交易网址'));
            $grid->column('order.created_at', admin_trans_field('支付时间'));

            $grid->column('amount')->display(function ($val) {
                return $this->order['amount'] . '&nbsp;&nbsp;' . $this->order['currency'];
            });
            $grid->column('order.address.ship_name', admin_trans_field('收件人'));

            $grid->column('is_checked')->display(function ($val) {
                return $val ? admin_trans_field('是') : admin_trans_field('否');
            });
            $grid->column('is_delivered')->display(function ($val) {
                return $val ? admin_trans_field('是') : admin_trans_field('否');
            });
            $grid->column('is_activated')->display(function ($val) {
                return $val ? admin_trans_field('是') : admin_trans_field('否');
            });
            $grid->column('api_result_status')->display(function ($val) {
                return admin_trans_option(OrderTrackModel::$apiResultStatusMap[$val], 'api_status');
            });

            $grid->column('tracking_type');
            $grid->column('tracking_number');

            $grid->column('remarks');
            $grid->column('created_at');
            $grid->column('updated_at');

            $grid->fixColumns(2, -1);

            $grid->other(admin_trans_field('操作'))->display(function () {
                if ($this->is_delivered != '1') {
                    Form::dialog(admin_trans_field('修改') . admin_trans_label('运单信息'))
                        ->click('.merchantTrackEdit') // 绑定点击按钮
                        ->width('35%')
                        ->height('40%')
                        ->success('Dcat.reload()');

                    return "<span class='btn btn-primary btn-shadow merchantTrackEdit sys-custom-blue' data-url=" . admin_route('m.tracks.edit', [$this->order_id]) . ">" . admin_trans_field('修改') . "</span>";
                } else {
                    return "<span class='btn btn-white disabled'>" . admin_trans_field('修改') . "</span>";
                }
            });

            $grid->disableCreateButton();
            $grid->disableActions();
            $grid->disableRowSelector();
            
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('order.order_id', admin_trans_field('order_id'));
                $filter->equal('order.order_number', admin_trans_field('order_number'));
                $filter->equal('order.url_name', admin_trans_field('交易网址'))->select(\App\Models\MerchantUrl::where('merchant_id', Auth::user()->merchant_id)->get()->pluck('url_name', 'url_name')->toArray());
                $filter->equal('tracking_number');
                $filter->equal('tracking_type')->select(DirectoryCarrier::getTrackTypeList());
                $filter->equal('api_result_status', admin_trans_field('物流状态'))->select(admin_trans_label('logistics', OrderTrackModel::$apiResultStatusMap));
                $filter->equal('is_delivered', admin_trans_field('妥投状态'))->select(admin_trans_label('delivered', ['未妥投', '已妥投']));
                $filter->equal('is_checked', admin_trans_field('审核状态'))->select(admin_trans_label('checked', ['未审核', '已审核']));
                $filter->equal('is_activated', admin_trans_field('生效状态'))->select(admin_trans_label('activated', ['未生效', '已生效']));
                $filter->between('order.created_at', admin_trans_field('支付时间'))->datetime(['sideBySide'=>true]);
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $thisCopy = $this;
        return Form::make(new OrderTrack(), function (Form $form) {
            $form->display('order_id');
            $form->hidden('tracking_type');
            $form->text('tracking_number')->required();
            $form->text('remarks');
        })->saving(function (Form $form) use ($thisCopy) {
            $orderTrack = OrderTrackModel::with(['order'])->find($form->getKey());
            $trackType  = $thisCopy->getTrackingTypeByTrackingNumber($form->tracking_number);
            $update     = [
                'tracking_type'   => empty($trackType) ? 'other' : $trackType,
                'tracking_number' => $form->tracking_number,
                'remarks'         => $form->remarks
            ];
            $taskData   = TrackService::createTrackUpdateTask($orderTrack, $update);

            foreach ($update as $field => $value) {
                $form->{$field} = $value;
            }

            if (!empty($taskData)) {
                $orderTrack->task()->createMany($taskData);
            }
        });
    }
}
