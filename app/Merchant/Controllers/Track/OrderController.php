<?php

namespace App\Merchant\Controllers\Track;

use App\Admin\Actions\Grid\Order\Track;
use App\Admin\Actions\Grid\Track\TrackUpload;
use App\Admin\Repositories\Order;
use App\Merchant\Actions\Tools\Track\TrackOrderExportTool;
use App\Models\Order as OrderModel;
use App\Models\OrderRelation;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Widgets\Modal;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class OrderController extends AdminController
{
    public function title()
    {
        return admin_trans_label('运单申请');    
    }

    protected $exportTitle = [
        'order_id'     => '交易订单号',
        'order_number' => '商户订单号',
        'url_name'     => '交易网址',
        'currency'     => '支付币种',
        'amount'       => '支付金额',
        'type'         => '交易类型',
        'created_at'   => '支付时间'
    ];

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Order(['paymentOrder']), function (Grid $grid) {
            $grid->model()->where('merchant_id', Auth::user()->merchant_id)
                ->whereIn('type', [OrderModel::TYPES_SALE, OrderModel::TYPES_CAPTURE])
                ->where('status', OrderModel::STATUS_APPROVED)
                ->whereHas('relation', function (Builder $query) {
                    $query->where('order_relations.is_refund', OrderRelation::IS_REFUND_NOT)
                        ->where('order_relations.is_delivery', OrderRelation::IS_DELIVERY_NOT)
                        ->where('order_relations.is_chargeback', OrderRelation::IS_CHARGEBACK_NOT);
                });

            // 导入运单
            $grid->tools(
                Modal::make()
                    ->lg()
                    ->title(admin_trans_field('导入'))
                    ->body(TrackUpload::make())
                    ->button('<button class="btn btn-primary grid-refresh">' . admin_trans_field('导入') . '</button>')
            );

            $grid->column('order_id')->sortable();
            $grid->column('order_number');
            $grid->column('url_name');
            $grid->column('currency');
            $grid->column('amount');
            $grid->column('type')->display(function($value){
                return OrderModel::$typesMap[$value];
            });
            $grid->column('created_at')->sortable();
            $grid->column('updated_at')->sortable();

            $grid->tools(new TrackOrderExportTool());

            $grid->disableCreateButton();
            $grid->disableActions();
            $grid->disableBatchDelete();

            $grid->column('other')->display(admin_trans_label('运单申请'))->modal(function ($modal) {
                return Track::make();
            });

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('order_id');
                $filter->equal('order_number');
                $filter->between('created_at')->datetime(['sideBySide'=>true]);
            });
        });
    }
}
