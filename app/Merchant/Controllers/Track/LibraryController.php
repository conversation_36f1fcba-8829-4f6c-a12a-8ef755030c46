<?php

namespace App\Merchant\Controllers\Track;

use App\Merchant\Actions\Grid\Track\LibraryUpload;
use App\Admin\Repositories\TrackLibrary;
use App\Models\OrderTrack;
use Dcat\Admin\Admin;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Form;
use Dcat\Admin\Widgets\Modal;

class LibraryController extends AdminController
{
    public function title()
    {
        return admin_trans_label('运单库管理');    
    }

    /**CountryUpload
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new TrackLibrary(), function (Grid $grid) {
            $grid->model()->where('merchant_id', Admin::user()->merchant_id);
            $grid->column('tracking_number');
            $grid->column('ship_country_isoa2');
            $grid->column('ship_first_name/ship_last_name')->display(function () {
                return $this->ship_first_name . '  ' . $this->ship_last_name;
            });

            $grid->column('ship_address');
            $grid->column('ship_city');
            $grid->column('ship_state');
            $grid->column('ship_phone');
            $grid->column('ship_postcode');
            $grid->column('created_at')->sortable();

            $grid->enableDialogCreate();
            $grid->disableEditButton();
            $grid->showQuickEditButton();
            $grid->disableViewButton();

            // 传入闭包
            $grid->tools(
                Modal::make()
                    ->lg()
                    ->title(admin_trans_field('导入'))
                    ->body(
                        LibraryUpload::make()
                    )
                    ->button('<button class="btn btn-primary grid-refresh">' . admin_trans_field('导入') . '</button>')
            );

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('tracking_number');
                $filter->between('created_at')->datetime(['sideBySide'=>true]);
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new TrackLibrary(), function (Form $form) {
            if ($form->isCreating()) {
                $form->text('tracking_number')->required();
            } else {
                $form->display('tracking_number')->required();
            }

            $form->input('ship_country_isoa2', 'US');
            $form->display('ship_country_isoa2')->default('US');
            $form->text('ship_first_name')->required();
            $form->text('ship_last_name')->required();
            $form->text('ship_address')->required();
            $form->text('ship_city')->required();
            $form->text('ship_state')->required();
            $form->text('ship_phone')->required();
            $form->text('ship_postcode')->required();
            $form->hidden('status')->default(\App\Models\TrackLibrary::ENABLE);
            $form->hidden('merchant_id')->default(Admin::user()->merchant_id);

            $form->saving(function (Form $form) {
                $form->input('merchant_id', Admin::user()->merchant_id);
                if ($form->isCreating()) {
                    $trackLibrary = \App\Models\TrackLibrary::where('tracking_number', $form->input('tracking_number'))->exists();
                    $orderTrack = OrderTrack::where('tracking_number', $form->input('tracking_number'))->exists();

                    if ($trackLibrary || $orderTrack) {
                        return $form->response()->error(admin_trans_label('运单号已存在'));
                    }
                }
            });
        });
    }
}
