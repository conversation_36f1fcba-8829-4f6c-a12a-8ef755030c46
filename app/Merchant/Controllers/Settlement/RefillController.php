<?php

namespace App\Merchant\Controllers\Settlement;

use App\Models\MerchantTicket;
use App\Models\Merchant;
use App\Models\DirectoryCurrency;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Support\Facades\Auth;
use App\Merchant\Actions\Grid\Settlement\CancelRefill;
use Dcat\Admin\Admin;

class RefillController extends AdminController
{

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new MerchantTicket(), function (Grid $grid) {
            $grid->model()->where('merchant_id', Auth::user()->merchant_id)->orderBy('created_at', 'desc');

            $grid->id->sortable();
            $grid->merchant_id;
            $grid->merchant_name;
            $grid->arrival_currency;
            $grid->arrival_amount;
            $grid->refill_currency;
            $grid->refill_amount;
            $grid->current_rate;
            $grid->reality_arrival_amount;
            $grid->reality_refill_amount;
            $grid->column('status')->display(function ($value) {
                return admin_trans_option(MerchantTicket::$merchantTicketStatusMap[$value] ?? admin_trans_field('未知'), 'refill_status_map');
            })->dot(['0' => 'danger', '1' => 'success', '2' => 'primary', '3' => 'primary', '4' => 'danger']);
            $grid->captcha;
            $grid->remark;
            $grid->reject_remark;
            $grid->created_at;
            $grid->updated_at;

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                if ($this->status == MerchantTicket::MERCHANT_TICKET_STATUS_CHECK) {
                    $resetApi = new CancelRefill();
                    $actions->append($resetApi);
                }
            });

            $grid->disableEditButton();
            $grid->disableDeleteButton();
            $grid->disableViewButton();
            $grid->disableRowSelector();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('status')->select(admin_trans_label('refill_status_map', MerchantTicket::$merchantTicketStatusMap));
                $filter->between('created_at')->datetime(['sideBySide'=>true]);
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $this->currencyList = DirectoryCurrency::all()->pluck('rate', 'code')->toArray();
        return Form::make(new MerchantTicket(), function (Form $form) {
            $please_speed           = admin_trans_field('please_speed');
            $remittance_and_remarks = admin_trans_field('remittance_and_remarks');
            $tip_second_details     = admin_trans_field('tip_second_details');
            Admin::script(
                <<<JS
$(function() {
    $(".field_arrival_currency").bind('input propertychange', function() {
        var refillCurrency = $(".field_refill_currency").val();
        var refillAmount = $(".field_refill_amount").val();
        var arrivalCurrency = $(".field_arrival_currency").val();
        var captcha = $(".field_captcha").val();
        var currencyData = {$this->getCurrencyList()};
        $(".field_current_rate").val(currencyData[arrivalCurrency]);
        var arrivalAmount = refillAmount * currencyData[arrivalCurrency] / currencyData[refillCurrency];
        arrivalAmount = arrivalAmount.toFixed(2)
        $(".field_arrival_amount").val(arrivalAmount);
        var hintStr = '{$please_speed}' + arrivalAmount + ' ' + arrivalCurrency + '{$remittance_and_remarks}' + captcha + '{$tip_second_details}';
        $(".field_remark").val(hintStr);
    })

    $(".field_refill_amount").bind('input propertychange', function() {
        var refillCurrency = $(".field_refill_currency").val();
        var refillAmount = $(".field_refill_amount").val();
        var arrivalCurrency = $(".field_arrival_currency").val();
        var captcha = $(".field_captcha").val();
        var currencyData = {$this->getCurrencyList()};
        $(".field_current_rate").val(currencyData[arrivalCurrency]);
        var arrivalAmount = refillAmount * currencyData[arrivalCurrency] / currencyData[refillCurrency];
        arrivalAmount = arrivalAmount.toFixed(2)
        $(".field_arrival_amount").val(arrivalAmount);
        var hintStr = '{$please_speed}' + arrivalAmount + ' ' + arrivalCurrency + '{$remittance_and_remarks}' + captcha + '{$tip_second_details}';
        $(".field_remark").val(hintStr);
    })
})
JS
            );

            $captcha = mt_rand(1000, 9999);
            $form->hidden('merchant_id')->value(Auth::user()->merchant_id);
            $form->hidden('refill_currency')->default('USD');
            $form->text('refill_amount', admin_trans_field('refill_amount') . '(USD)')->default('1.00')->required();
            $form->select('arrival_currency')->default('USD')->options(['USD' => 'USD', 'CNY' => 'CNY'])->required();
            $form->text('arrival_amount')->default('1.00')->readonly();
            $form->text('current_rate')->value('1.0000')->readonly();
            $form->textarea('remark', admin_trans_field('tip'))->value(admin_trans_field('tip_first_details') . $captcha . admin_trans_field('tip_second_details'))->readonly();
            $form->hidden('merchant_name')->value('test');
            $form->hidden('is_settle')->value(MerchantTicket::STATUS_DECLINED);
            $form->hidden('status')->value(MerchantTicket::MERCHANT_TICKET_STATUS_CHECK);
            $form->hidden('captcha')->value($captcha);

            $form->saving(function (Form $form) {
                if ($form->isCreating()) {
                    if ($form->refill_amount <= 0.00) {
                        return $form->response()->error('充值金额不能小于0');
                    }
                    $merchantData        = Merchant::where('merchant_id', $form->merchant_id)->first();
                    $form->merchant_name = $merchantData->merchant_name;
                    $form->created_at    = now();
                    $form->updated_at    = now();
                    $form->remark        = '';
                }
            });
        });
    }

    private function getCurrencyList()
    {
        return json_encode($this->currencyList);
    }
}
