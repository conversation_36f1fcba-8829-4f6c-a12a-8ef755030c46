<?php

namespace App\Merchant\Controllers\CardVirtual;

use App\Merchant\Repositories\CardBatch;
use App\Models\CardBatch as CardBatchModel;
use App\Models\CardHolder as CardHolderModel;
use App\Models\CardVirtual;
use App\Models\MerchantCard;
use App\Models\MerchantApiNoticeTask;
use App\Services\VirtualControllerService;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Support\Facades\Auth;
use App\Models\CardBin;
use Dcat\Admin\Admin;
use App\Services\Virtual\TongLianService;

class CardBatchController extends AdminController
{
    public function title()
    {
        return admin_trans_label('卡批次');
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new CardBatch(), function (Grid $grid) {
            $grid->model()->where('merchant_id', Auth::user()->merchant_id)->orderBy('created_at', 'desc');
            $grid->column('batch_id');
            $grid->column('cards_id');
            $grid->column('bin');
            $grid->column('card_type')->display(function ($val) {
                return admin_trans_option(CardBatch::$internalTypetMap[$val] ?? admin_trans_field('未知'), 'internal_type_map');
            });
            $grid->column('initial_amount');
            $grid->column('quantity');
            $grid->column('day_amount_limit');
            $grid->column('batch_total_amount');
            $grid->column('success_number');
            $grid->column('remark');
            $grid->column('status')->display(function ($val) {
                return admin_trans_option(CardBatch::$internalStatusMap[$val] ?? admin_trans_field('未知'), 'internal_status_map');
            });
            $grid->column('processing_time');
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();

            $grid->disableActions();
            $grid->disableRowSelector();

            $grid->enableDialogCreate();
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('batch_id');
                $filter->equal('cards_id');
                $filter->equal('bin');
                $filter->equal('card_type')->select(admin_trans_label('internal_type_map', CardBatch::$internalTypetMap));
                $filter->equal('status')->select(admin_trans_label('internal_status_map', CardBatch::$internalStatusMap));
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new CardBatch(), function (Show $show) {
            $show->field('batch_id');
            $show->field('merchant_id');
            $show->field('cards_id');
            $show->field('bin');
            $show->field('card_type');
            $show->field('initial_amount');
            $show->field('quantity');
            $show->field('day_amount_limit');
            $show->field('batch_total_amount');
            $show->field('success_number');
            $show->field('remark');
            $show->field('status');
            $show->field('processing_time');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new CardBatch(), function (Form $form) {
            Admin::script(
                <<<JS
            $('input[name="day_amount_limit"]').parent().parent().parent().hide();
            $('input[name="initial_amount"]').parent().parent().parent().hide();
            JS
            );
            $cidArr      = MerchantCard::where('merchant_id', Auth::user()->merchant_id)->where('status', 1)->get()->pluck('cards_id', 'cards_id')->toArray();
            $cardHolders = CardHolderModel::where('merchant_id', Auth::user()->merchant_id)->where('status', CardHolderModel::CARD_HOLDER_STATUS_ENABLE)->where('audit_status', CardHolderModel::CARD_HOLDER_AUDIT_STATUS_PASS)->get()->pluck('full_name', 'id')->toArray();
            $form->hidden('batch_id');
            $form->hidden('cardholder_ids');
            $form->hidden('status');
            $form->hidden('merchant_id')->value(Auth::user()->merchant_id);
            $form->select('cards_id')->required()->options($cidArr);
            $form->hidden('card_bin_id');
            $form->hidden('bin');
            $form->hidden('bin_name');
            $form->hidden('card_type');
            $form->text('initial_amount')->default('0.00', true)->disable();
            $form->text('quantity')->default(0)->required()->rules('gte:1');
            $form->text('day_amount_limit')->default('0.00', true);
            $form->hidden('batch_total_amount')->default(0);
            $form->radio('is_use_default_cardholder')->required()->default(0)->options(admin_trans_label('default_cardholder_map', CardBatchModel::$defaultCardholderApiMap))->when(CardBatchModel::DEFAULT_CARDHOLDER_NO, function (Form $form) use ($cardHolders) {
                $form->checkbox('cardholder_checkbox')->options($cardHolders);
            });
            $form->textarea('remark');

            Admin::script(
                <<<JS
                    $(function () {
                        $('select[name="cards_id"]').change(function () {
                        var cid = $(this).val()
                        $.ajax({
                            url : '/merchant/card_type/' + cid,
                            type : 'GET',
                            success : function (data) {
                                if (data['card_type'] == 0) {
                                    $('input[name="day_amount_limit"]').parent().parent().parent().hide();
                                    $('input[name="initial_amount"]').parent().parent().parent().show();
                                    $('input[name="initial_amount"]').val(data['initial_amount']);
                                } else {
                                    $('input[name="day_amount_limit"]').parent().parent().parent().show();
                                    $('input[name="initial_amount"]').parent().parent().parent().hide();
                                }
                                
                            }
                        })
                    })
                    })
                JS
            );

            $form->saving(function (Form $form) {
                $filteredArr = array_filter($form->cardholder_checkbox, function ($value) {
                    return ($value !== null && $value !== "");
                });
                $form->deleteInput('cardholder_checkbox');
                $form->cardholder_ids = implode(',', $filteredArr);

                if ($form->isCreating()) {
                    $res = VirtualControllerService::HandleCardBatchApplyVerify($form);
                    if (!$res['isSuccess']) {
                        return $form->response()->error($res['message']);
                    }
                    $form->status = CardBatchModel::PENDING;
                    $cardBin      = CardBin::select('id', 'bin_supplier_id')->where('id', $form->card_bin_id)->with('CardBinSupplier')->first();
                    $channelName  = $cardBin->CardBinSupplier->file_name;
                    // 通联渠道直接处理
                    if ($channelName == TongLianService::CHANNEL_SERVICE_CODE) {
                        $form->status = CardBatchModel::PROCESS;
                    }
                }
            });

            // 保存后回调
            $form->saved(function (Form $form, $result) {
                // 判断是否是新增
                if ($form->isCreating()) {
                    $cardBatchData = $form->repository()->model();
                    // 处理中走同步开卡逻辑
                    if ($cardBatchData['status'] == CardBatchModel::PROCESS) {
                        VirtualControllerService::HandleCardBatchSyncApply($cardBatchData->toArray());
                        return;
                    }

                    $cardVirtualCount = CardVirtual::where('card_bin_id', $cardBatchData['card_bin_id'])->where('cards_id', '')->where('status', CardVirtual::ACTIVATION)->count();
                    // 囤卡数量 > 开卡数量 才给进行直接开卡
                    if ($cardVirtualCount >= $form->quantity) {
                        VirtualControllerService::HandleCardBatchAsyncApply($cardBatchData->toArray());
                        // 添加回调任务
                        VirtualControllerService::createNoticeTask(
                            $cardBatchData->merchant_id,
                            MerchantApiNoticeTask::WEBHOOK_MESSAGE_TYPE_VIRTUAL_BATCH,
                            [
                                'code'    => '0000',
                                'message' => '成功',
                                'data'    => ['batch_id' => $cardBatchData->batch_id, 'batch_status' => CardBatchModel::$statusApiMap[CardBatchModel::PROCESS_SUCCESS]]
                            ]
                        );
                    }
                }
            });

            $form->display('created_at');
            $form->display('updated_at');
        });
    }

    public function card_type($cards_id)
    {
        $cidData = MerchantCard::where('cards_id', $cards_id)->first();
        $card    = CardBin::where('id', $cidData->card_bin_id)->first();

        $config = json_decode($card['config'], true);

        $initialAmount = 0.00;
        foreach ($config as $value) {
            if ($value['key'] == 'initial_amount') {
                $initialAmount = $value['value'];
            }
        }

        $card['initial_amount'] = $initialAmount;

        return $card;
    }
}
