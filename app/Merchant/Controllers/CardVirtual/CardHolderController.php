<?php

namespace App\Merchant\Controllers\CardVirtual;

use App\Admin\Exceptions\CardHolderBatchDelete;
use App\Admin\Exceptions\CardHolderDelete;
use App\Admin\Exceptions\CardHolderIsDefault;
use App\Admin\Exceptions\CardHolderStatus;
use App\Admin\Repositories\CardHolder;
use App\Models\CardHolder as CardHolderModel;
use App\Models\DirectoryCountry;
use App\Models\DirectoryDictionary;
use App\Services\ToolService;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Show;
use DES3;
use Illuminate\Support\Facades\Auth;


class CardHolderController extends AdminController
{
    public function title()
    {
        return admin_trans_label('持卡人信息列表');
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new CardHolder(), function (Grid $grid) {
            $grid->model()->where('merchant_id', Auth::user()->merchant_id)->orderBy('created_at', 'desc');
            $grid->column('full_name');
            $grid->column('gender')->display(function ($value) {
                return admin_trans_option(CardHolderModel::$genderMap[$value] ?? admin_trans_field('未知'), 'gender');
            });
            $grid->column('phone')->display(function ($value) {
                return ToolService::desensitizePhone($value);
            });
            $grid->column('email');
            $grid->column('country');
            $grid->column('city');
            $grid->column('province');
            $grid->column('address');
            $grid->column('id_type')->display(function ($value) {
                return admin_trans_option(CardHolderModel::$idTypeMap[$value] ?? admin_trans_field('未知'), 'id_type');
            });
            $grid->column('id_number')->display(function ($value) {
                return ToolService::desensitizeIdNumber(DES3::decrypt($value), $this->id_type);
            });
            $grid->column('birth_date')->display(function ($value) {
                return $value ? date('Y-m-d', strtotime($value)) : '';
            });
            $grid->column('nationality');
            $grid->column('company_position')->display(function ($value) {
                return admin_trans_option(CardHolderModel::$companyPositionMap[$value] ?? admin_trans_field('未知'), 'company_position');
            });
            $grid->column('subject_type')->display(function ($value) {
                return admin_trans_option(CardHolderModel::$subjectTypeMap[$value] ?? admin_trans_field('未知'), 'subject_type');
            });
            $grid->column('holder_type')->display(function ($value) {
                return admin_trans_option(CardHolderModel::$holderTypeMap[$value] ?? admin_trans_field('未知'), 'holder_type');
            });
            $grid->column('is_default_cardholder')->display(function ($value) {
                return admin_trans_option(CardHolderModel::$defaultCardholderMap[$value] ?? admin_trans_field('未知'), 'is_default_cardholder');
            });
            $grid->column('status')->display(function ($value) {
                return admin_trans_option(CardHolderModel::$statusMap[$value] ?? admin_trans_field('未知'), 'status');
            });
            $grid->column('audit_status')->display(function ($value) {
                return admin_trans_option(CardHolderModel::$auditStatusMap[$value] ?? admin_trans_field('未知'), 'audit_status');
            });
            $grid->column('audit_remark');
            $grid->created_at;
            $grid->updated_at->sortable();

            $grid->enableDialogCreate();
            $grid->disableDeleteButton();
            $grid->disableBatchDelete();

            $grid->batchActions(function ($batch) {
                $batch->add(new CardHolderBatchDelete());
            });


            $grid->actions(function (Grid\Displayers\Actions $actions) {
                // 启用/禁用编辑按钮
                $status = $this->status == CardHolderModel::CARD_HOLDER_STATUS_ENABLE ? 'on' : 'off';
                $actions->append(new CardHolderStatus($this->id, $status));
                $actions->append('&emsp;');
                // 默认持卡人编辑按钮
                $isDefault = $this->is_default_cardholder == CardHolderModel::CARD_HOLDER_IS_DEFAULT_CARDHOLDER_YES ? 'on' : 'off';
                $actions->append(new CardHolderIsDefault($isDefault));
                $actions->append('&emsp;');
                // 删除按钮
                $actions->append(new CardHolderDelete());
                $actions->append('&emsp;');
            });

            $grid->fixColumns(1, -1);

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id_type')->select(CardHolderModel::$idTypeMap);
                $filter->where('id_number', function ($query) {
                    $query->where('id_number', DES3::encrypt($this->input));
                });
                $filter->like('full_name');
                $filter->between('created_at')->datetime(['sideBySide'=>true]);
                $filter->between('updated_at')->datetime(['sideBySide'=>true]);
            });
        });
    }

    protected function detail($id)
    {
        return Show::make($id, new CardHolder(), function (Show $show) {
            $securityIssues = DirectoryDictionary::where('type', '持卡人密保问题')->first();
            $issuesSelect   = json_decode($securityIssues->remarks, true);
            $trans          = admin_trans_label('security_index', $issuesSelect);
            $show->field('merchant_id');
            $show->field('first_name');
            $show->field('last_name');
            $show->field('full_name');
            $show->field('gender')->as(function ($value) {
                return admin_trans_option(CardHolderModel::$genderMap[$value] ?? admin_trans_field('未知'), 'gender');
            });
            $show->field('calling_prefix');
            $show->field('phone');
            $show->field('email');
            $show->field('country');
            $show->field('city');
            $show->field('province');
            $show->field('address');
            $show->field('id_type')->as(function ($value) {
                return admin_trans_option(CardHolderModel::$idTypeMap[$value] ?? admin_trans_field('未知'), 'id_type');
            });
            $show->field('id_number')->as(function ($value) {
                return DES3::decrypt($value);
            });
            $show->field('birth_date')->as(function ($value) {
                return $value ? date('Y-m-d', strtotime($value)) : '';
            });
            $show->field('nationality');
            $show->field('photo_front')->image();
            $show->field('photo_back')->image();
            $show->field('company_position')->as(function ($value) {
                return admin_trans_option(CardHolderModel::$companyPositionMap[$value] ?? admin_trans_field('未知'), 'company_position');
            });
            $show->field('subject_type')->as(function ($value) {
                return admin_trans_option(CardHolderModel::$subjectTypeMap[$value] ?? admin_trans_field('未知'), 'subject_type');
            });
            $show->field('holder_type')->as(function ($value) {
                return admin_trans_option(CardHolderModel::$holderTypeMap[$value] ?? admin_trans_field('未知'), 'holder_type');
            });
            $show->field('security_index')->as(function ($value) use ($trans) {
                return $trans[$value] ?? '未知';
            });
            $show->field('security_answer');
            $show->field('is_default_cardholder')->as(function ($value) {
                return admin_trans_option(CardHolderModel::$defaultCardholderMap[$value] ?? admin_trans_field('未知'), 'is_default_cardholder');
            });
            $show->field('status')->as(function ($value) {
                return admin_trans_option(CardHolderModel::$statusMap[$value] ?? admin_trans_field('未知'), 'status');
            });
            $show->field('audit_status')->as(function ($value) {
                return admin_trans_option(CardHolderModel::$auditStatusMap[$value] ?? admin_trans_field('未知'), 'audit_status');
            });
            $show->field('audit_remark');
            $show->field('created_at');
            $show->field('updated_at');

            $show->panel()
                ->tools(function ($tools) {
                    $tools->disableEdit();
                    $tools->disableDelete();
                });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new CardHolder(), function (Form $form) {
            $form->disableViewButton();
            $form->disableDeleteButton();
            $form->disableResetButton();
            $securityIssues = DirectoryDictionary::where('type', '持卡人密保问题')->first();
            $issuesSelect   = json_decode($securityIssues->remarks, true);
            $form->text('first_name')->required();
            $form->text('last_name')->required();
            $form->select('gender')->options(admin_trans_label('gender', CardHolderModel::$genderMap))->required();
            $form->select('calling_prefix')->options(
                DirectoryCountry::pluck('name', 'phone_prefix')
                    ->map(static function ($item, $key) {
                        return $item . ':' . $key;
                    })
                    ->toArray()
            )->required();
            $form->text('phone')->required();
            $form->email('email')->required();
            $form->text('postal_code')->required()->maxLength(10);
            $form->select('country')->options(
                DirectoryCountry::pluck('name', 'isoa3')
                    ->map(static function ($item, $key) {
                        return $item . ':' . $key;
                    })
                    ->toArray()
            )->required();
            $form->text('city')->required();
            $form->text('province')->required();
            $form->text('address')->required();
            $form->select('id_type')->options(admin_trans_label('id_type', CardHolderModel::$idTypeMap))->required();
            $form->text('id_number')->customFormat(function ($value) {
                return DES3::decrypt($value);
            })->required();
            $form->date('birth_date')->required();
            $form->select('nationality')->options(
                DirectoryCountry::pluck('name', 'isoa3')
                    ->map(static function ($item, $key) {
                        return $item . ':' . $key;
                    })
                    ->toArray()
            )->required();
            $form->image('photo_front')->uniqueName()->autoUpload()->required();
            $form->image('photo_back')->uniqueName()->autoUpload()->required();
            $form->select('company_position')->options(admin_trans_label('company_position', CardHolderModel::$companyPositionMap))->required();
            $form->select('subject_type')->options(admin_trans_label('subject_type', CardHolderModel::$subjectTypeMap))->default(CardHolderModel::CARD_HOLDER_SUBJECT_TYPE_PERSON)->required();
            $form->select('holder_type')->options(admin_trans_label('holder_type', CardHolderModel::$holderTypeMap))->required();
            $form->select('security_index')->options(admin_trans_label('security_index', $issuesSelect))->required();
            $form->text('security_answer')->required()->maxLength(50);
            $form->switch('is_default_cardholder');
            $form->switch('status')->default(CardHolderModel::CARD_HOLDER_STATUS_ENABLE);
            $form->hidden('full_name')->default('');
            $form->hidden('audit_status')->default('');
            $form->hidden('merchant_id')->value(Auth::user()->merchant_id);
            $form->display('created_at');
            $form->display('updated_at');
            $form->disableEditingCheck();
            $form->disableCreatingCheck();
            $form->disableViewCheck();

            $form->submitted(function (Form $form) {
                $form->full_name    = $form->first_name . $form->last_name;
                $form->id_number    = DES3::encrypt($form->id_number);
                $form->audit_status = CardHolderModel::CARD_HOLDER_AUDIT_STATUS_PENDING;
            });

            $form->saving(function (Form $form) {
                $fileRes = $form->_file_del_;
                if (empty($fileRes)) {
                    if (DirectoryCountry::where('isoa3', $form->country)->where('phone_prefix', $form->calling_prefix)->count() <= 0) {
                        return $form->response()->error(admin_trans_label('国家/地区与手机号码前缀不匹配'));
                    }

                    if (!ToolService::isAgeInRange($form->birth_date)) {
                        return $form->response()->error(admin_trans_label('持卡人年龄在18~65岁之间'));
                    }

                    if ($form->is_default_cardholder == CardHolderModel::CARD_HOLDER_IS_DEFAULT_CARDHOLDER_YES &&
                        CardHolderModel::where('merchant_id', $form->merchant_id)->where('is_default_cardholder', CardHolderModel::CARD_HOLDER_IS_DEFAULT_CARDHOLDER_YES)->count()) {
                        return $form->response()->error(admin_trans_label('该商户下已有默认持卡人, 无法设置'));
                    }
                }

                if ($form->isEditing()) {
                    if (CardHolderModel::where('id', $form->model()->id)->where('audit_status', CardHolderModel::CARD_HOLDER_AUDIT_STATUS_PASS)->count()) {
                        return $form->response()->error(admin_trans_label('该持卡人已审核, 无法修改'));
                    }
                }
            });
        });
    }
}