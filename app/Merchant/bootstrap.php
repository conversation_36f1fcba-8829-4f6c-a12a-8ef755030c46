<?php

use Dcat\Admin\Admin;
use Dcat\Admin\Layout\Navbar;

/**
 * Dcat-admin - admin builder based on Lara<PERSON>.
 * <AUTHOR> <https://github.com/jqhph>
 *
 * Bootstraper for Admin.
 *
 * Here you can remove builtin form field:
 *
 * extend custom field:
 * Dcat\Admin\Form::extend('php', PHPEditor::class);
 * Dcat\Admin\Grid\Column::extend('php', PHPEditor::class);
 * Dcat\Admin\Grid\Filter::extend('php', PHPEditor::class);
 *
 * Or require js and css assets:
 * Admin::css('/packages/prettydocs/css/styles.css');
 * Admin::js('/packages/prettydocs/js/main.js');
 *
 */

app('view')->prependNamespace('merchant', resource_path('views/vendor/merchant'));

switch (env('APP_NAME', 'Laravel')) {
    case 'Embracy':
        $company = 'Embracy';
        break;

    case 'Hpaymerchants':
        $company = 'Hpaymerchants';
        break;

    case 'PunctualPay':
        $company = 'Punctual Pay';
        break;

    default:
        $company = 'Peachy Pay';
        break;
}

$date   = date('Y');
$footer = <<<html
<p class="clearfix blue-grey lighten-2 mb-0 text-center"><span class="text-center d-block d-md-inline-block mt-25">Copyright © {$date} {$company}. All Rights Reserved.</span></p>
html;

Admin::script(
    <<<JS
        $(".main-footer").html('{$footer}');

        //给包裹Table表格的父容器加上id
        $('#grid-table').parent().attr('id', 'table_id');
        let tableCont = document.querySelectorAll('tr th');

        //监控整个页面滚动
        var defaultTop = 130;
        window.onscroll = function() {
            let scrollTop = document.documentElement.scrollTop ||  document.body.scrollTop;
            if (scrollTop > 0) {
                scrollTop -= defaultTop;
                if (scrollTop > 0) {
                    for (let i = 0; i < tableCont.length; i++) {
                        tableCont[i].style.top = scrollTop + 'px';
                    }
                }
            } else {
                for (let i = 0; i < tableCont.length; i++) {
                    tableCont[i].style.top = '0px';
                }
            }
        }

        //监控表格内容滚动
        if (document.getElementById('table_id')) {
            document.getElementById('table_id').onscroll = function() {
                let scrollTop = document.getElementById('table_id').scrollTop;
                if (scrollTop > 0) {
                    scrollTop -= 5;
                    if (scrollTop > 0) {
                        for (let i = 0; i < tableCont.length; i++) {
                            tableCont[i].style.top = scrollTop + 'px';
                        }
                    }
                } else {
                    for (let i = 0; i < tableCont.length; i++) {
                        tableCont[i].style.top = '0px';
                    }
                }
            }
        }

    JS
);

//固定表格表头样式
Admin::style(
    <<<STYLE
        #table_id>table>thead>tr>th {
            position        : relative;
            background-color: #ececf1;
        }
        .table-fixed tr th {
            position        : relative;
            background-color: #ececf1;
        }
        .slider-panel {
            width: 600px;
            right: -600px;
        }
        .slider-panel .slider-content {
            width: 600px;
        }
    STYLE
);

Admin::navbar(function (Navbar $navbar) {
    $navbar->right(view('merchant.complaint.nav'));
    if (config('open_im.open_im')) {
        $navbar->right(view('navbar.openIm', ['system' => 'merchant']));
    }
});

config([
    'app.locale' => session('lang') ? session('lang') : 'zh_CN'
]);

if (env('APP_NAME', 'Laravel') == 'Embracy' || env('APP_NAME', 'Laravel') == 'Hpaymerchants') {
    //更改默认主色调
    app('admin.color')->extend('default', [
        'primary'        => '#1eadec',
        'primary-darker' => '#4c60a3',
        'link'           => '#4c60a3',
    ]);
    Admin::css('/css/embracy/main.css');
    Admin::css('/css/embracy/complaints/item.css');
    Admin::script(
        <<<JS
        //修改默认值
        defaultTop = 80;
        //移除筛选框,内容右侧框白底,小图标
        $("div .filter-button-group").removeClass('btn-group');
        $("div .pull-right").removeClass('btn-group');
        $('small').remove();
        $('.fa.fa-fw.feather.icon-circle').remove();
        let span = $(".content-header span").first();
        //标题下滑
        if(span && span.text() != "Dashboard"){
            let h1  = $(".content-header h1").first();
            let topBlock = $(".top.d-block").first();

            if(topBlock){
                let isTitle = topBlock.children('h4');

                if(isTitle.length > 0){
                    $(".content-header").remove();
                    return;
                }
                topBlock.prepend(h1)
                $(".content-header").remove();
            }
        }
        //搜索框右移
        let tempTop  = $(".top");
        let search   = $(".input-no-border.d-md-inline-block");
        let topRight = tempTop.children('div[class=pull-right]');

        if(search && topRight){
            topRight.prepend(search);
        }
        //详细内容中右上角按钮样式
        $('.pull-right button.btn-sm').addClass('btn-primary').removeClass('btn-white');
        $('.pull-right a.btn-sm').addClass('btn-primary').removeClass('btn-white');
        //提交弹窗按钮样式
        let popButReset  = $('button[type=reset]').removeClass('pull-left').css('margin','0px 30px');
        let popButSubmit = popButReset.next('button[type=submit]').addClass('submit').removeClass('pull-right').css('margin','0px 30px');
        let popFooter    = popButReset.parent().css('text-align','center').removeClass('btn-group');
        //nav用户头像框修改
        $('.user-status').remove();
        let userNav = $('div .user-nav.d-sm-flex.d-none');
        let userSpan = userNav.children('span');
        userSpan.css({'font-size':'16px','weight':'normal','margin-left':'8px'});
        userSpan.removeClass('user-name');
        userNav.next().insertBefore(userNav);
        JS
    );
}

if (env('APP_NAME', 'Laravel') == 'PunctualPay') {
    Admin::css('/css/punctualPay/main.css');
    Admin::css('/css/punctualPay/complaints/item.css');
    Admin::script(
        <<<JS
        //修改默认值
        defaultTop = 80;
        //移除筛选框,内容右侧框白底,小图标
        $("div .filter-button-group").removeClass('btn-group');
        $("div .pull-right").removeClass('btn-group');
        $('.content-header .breadcrumb').remove();
        $('.fa.fa-fw.feather.icon-circle').remove();
        let span = $(".content-header span").first();

        //标题下滑
        if(span && span.text() != "Dashboard"){
            let h1  = $(".content-header h1").first();
            let topBlock = $(".top.d-block").first();

            if(topBlock){
                let isTitle = topBlock.children('h4');

                if(isTitle.length > 0){
                    return;
                }
                topBlock.prepend(h1)
            }
        }

        //搜索框右移
        let tempTop  = $(".top");
        let search   = $(".input-no-border.d-md-inline-block");
        let topRight = tempTop.children('div[class=pull-right]');

        if(search && topRight){
            topRight.prepend(search);
        }
        //详细内容中右上角按钮样式
        $('.pull-right button.btn-sm').addClass('btn-primary').removeClass('btn-white');
        $('.pull-right a.btn-sm').addClass('btn-primary').removeClass('btn-white');
        //提交弹窗按钮样式
        let popButReset  = $('button[type=reset]').removeClass('pull-left').css('margin','0px 30px');
        let popButSubmit = popButReset.next('button[type=submit]').addClass('submit').removeClass('pull-right').css('margin','0px 30px');
        let popFooter    = popButReset.parent().css('text-align','center').removeClass('btn-group');
        //nav用户头像框修改
        $('.user-status').remove();
        let userNav = $('div .user-nav.d-sm-flex.d-none');
        let userSpan = userNav.children('span');
        userSpan.css({'font-size':'16px','weight':'normal','margin-left':'8px'});
        userSpan.removeClass('user-name');
        userNav.next().insertBefore(userNav);
        JS
    );
}
