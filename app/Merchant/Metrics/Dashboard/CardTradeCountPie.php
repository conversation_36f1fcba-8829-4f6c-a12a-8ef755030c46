<?php

namespace App\Merchant\Metrics\Dashboard;

use App\Services\CardTransactionService;
use App\Services\ToolService;
use Dcat\Admin\Widgets\ApexCharts\Chart;

class CardTradeCountPie extends Chart
{
    public function __construct($containerSelector = null, $options = [])
    {
        parent::__construct($containerSelector, $options);

        $this->setUpOptions();
    }

    // 初始化方法，主要是调用$this->options()方法，执行整个option的初始化操作。
    protected function setUpOptions()
    {
        // 随机获取颜色,组成数组。确保饼图颜色不重复
        $colors = ToolService::getRandomColor();
        $this->options([
            "chart"  => [
                "width" => '100%',
                "type"  => "pie"
            ],
            'colors' => $colors
        ]);
        // 获取筛选条件
        $searchData    = request()->all();
        $seriesData    = [];
        $labelData     = [];
        $statCountData = CardTransactionService::getCardTradeStatAmountData($searchData, 'count');
        foreach ($statCountData as $key => $value) {
            $seriesData[] = (int)$value['count'];
            $labelData[]  = $key;
        }

        if (empty($statCountData)) {
            $seriesData = [1];
            $labelData  = ['-:0'];
        }

        // 这个series的值数组value的值必须是int类型的。其他类型加载不出来
        $this->option("series", $seriesData);
        // 饼图右上角的标签
        $this->option("labels", $labelData);
    }
}