<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Model;

class CardVirtualBlack extends Model
{
    use HasDateTimeFormatter;

    protected $table = 'card_virtual_blacks';

    const CARD_BLACK_STATUS_DISABLE = 0; // 0: 禁用
    const CARD_BLACK_STATUS_ENABLE  = 1; // 1: 启用

    const CARD_BLACK_TYPE_MERCHANT_NAME = 1; // 交易商户名
    const CARD_BLACK_TYPE_MCC           = 2; // MCC

    const CARD_BLACK_SOURCE_CHANNEL = 1; // 渠道 (暂时不用)
    const CARD_BLACK_SOURCE_SYSTEM  = 2; // 系统添加

    const CARD_BLACK_IS_SYNC_NO  = 0; // 未同步渠道
    const CARD_BLACK_IS_SYNC_YES = 1; // 已同步渠道

    public static $statusMap = [
        self::CARD_BLACK_STATUS_DISABLE => '禁用',
        self::CARD_BLACK_STATUS_ENABLE  => '启用',
    ];

    public static $typeMap = [
        self::CARD_BLACK_TYPE_MERCHANT_NAME => '交易商户名',
        self::CARD_BLACK_TYPE_MCC           => 'MCC',
    ];

    public static $sourceMap = [
        self::CARD_BLACK_SOURCE_CHANNEL => '渠道',
        self::CARD_BLACK_SOURCE_SYSTEM  => '系统添加',
    ];
}