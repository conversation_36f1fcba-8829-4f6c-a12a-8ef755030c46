<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Model;

class CardTicket extends Model
{
    use HasDateTimeFormatter;

    protected $table = 'card_tickets';

    const TYPE_RECHARGE        = 0;
    const TYPE_CHARGE_OUT      = 1;
    const TYPE_TRANSACTION     = 2;
    const TYPE_REFUND          = 3;
    const TYPE_TRANSACTION_OUT = 4;

    const STATUS_FAILED             = 0;
    const STATUS_SUCCESS            = 1;
    const STATUS_PENDING            = 2;
    const STATUS_CHANNEL_PROCESSING = 3;
    const STATUS_PROCESSING         = 4;

    const ORIGINATOR_SYSTEM   = 0;
    const ORIGINATOR_MERCHANT = 1;

    public static $statusMap = ['失败', '成功', '待处理', '渠道处理中', '处理中'];

    public static $statusMapToEn = ['Failed', 'Success', 'Pending', 'Channel Processing', 'Processing'];

    public static $originatorMap = ['system', 'merchant'];

    public static $typeMap = ['转入', '转出', '交易消费', '退款', '交易撤销'];

    public static $apiTypeMapToEn = [
        self::TYPE_RECHARGE        => 'Recharge',
        self::TYPE_CHARGE_OUT      => 'ChargeOut',
        self::TYPE_TRANSACTION     => 'Transaction',
        self::TYPE_REFUND          => 'Refund',
        self::TYPE_TRANSACTION_OUT => 'TransactionOut',
    ];

    protected $guarded = [];

    public static function getUniqueId()
    {
        return microsecond() . mt_rand(10, 99);
    }
}
