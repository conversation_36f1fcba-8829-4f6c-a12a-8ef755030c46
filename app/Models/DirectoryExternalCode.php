<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;

use Illuminate\Database\Eloquent\Model;

class DirectoryExternalCode extends Model
{
    use HasDateTimeFormatter;

    protected $table = 'directory_external_codes';

    const THROW_DISABLE = 0;
    const THROW_ENABLE = 1;
    
    public static $throwMap = [
        self::THROW_DISABLE => '禁用',
        self::THROW_ENABLE  => '启用',
    ];
}
