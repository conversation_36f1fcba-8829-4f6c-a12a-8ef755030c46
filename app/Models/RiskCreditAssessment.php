<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class RiskCreditAssessment extends Model
{
    use HasDateTimeFormatter;
    use SoftDeletes;

    const EXPOSURE_BREAKDOWN_TOTAL_EXPOSURE = 'total_risk'; // 总风险
    const EXPOSURE_BREAKDOWN_REFUND_EXPOSURE = 'refund_risk'; // 退款风险
    const EXPOSURE_BREAKDOWN_CHARGEBACK_EXPOSURE = 'chargeback_risk'; // 拒付风险
    const EXPOSURE_BREAKDOWN_UN_DELIVERY_EXPOSURE = 'undelivered_risk'; // 未交付风险

    const EXPOSURE_BREAKDOWN_TOTAL_EXPOSURE_ZH = '总风险';
    const EXPOSURE_BREAKDOWN_REFUND_EXPOSURE_ZH = '退款风险';
    const EXPOSURE_BREAKDOWN_CHARGEBACK_EXPOSURE_ZH = '拒付风险';
    const EXPOSURE_BREAKDOWN_UN_DELIVERY_EXPOSURE_ZH = '未交付风险';

    const EXPOSURE_BREAKDOWN_TOTAL_EXPOSURE_EN = 'Total Exposure';
    const EXPOSURE_BREAKDOWN_REFUND_EXPOSURE_EN = 'Refund Exposure';
    const EXPOSURE_BREAKDOWN_CHARGEBACK_EXPOSURE_EN = 'Chargeback Exposure';
    const EXPOSURE_BREAKDOWN_UN_DELIVERY_EXPOSURE_EN = 'Non-Delivery Exposure (NDX)';

    // NDX详情必须存在的内容
    const NDX_DETAIL_REQUIRED_TEMPLATE_HEADER = [
        [
            'title' => self::EXPOSURE_BREAKDOWN_UN_DELIVERY_EXPOSURE,
            'days' => '',
            'rate' => '',
            'amount' => '',
        ]
    ];

    /**
     * 两个模板之间插入的是对应的MCC数据
     */

    const NDX_DETAIL_REQUIRED_TEMPLATE_FOOTER = [
        [
            'title' => self::EXPOSURE_BREAKDOWN_REFUND_EXPOSURE,
            'days' => '',
            'rate' => '',
            'amount' => '',
        ],
        [
            'title' => self::EXPOSURE_BREAKDOWN_CHARGEBACK_EXPOSURE,
            'days' => '',
            'rate' => '',
            'amount' => '',
        ],
        [
            'title' => self::EXPOSURE_BREAKDOWN_TOTAL_EXPOSURE,
            'days' => '',
            'rate' => '',
            'amount' => '',
        ],
    ];

    protected $table = 'risk_credit_assessments';

    protected $casts = [
        'mcc_table' => 'json',
        'ndx_details' => 'json',
    ];

    public function case(): BelongsTo
    {
        return $this->belongsTo(RiskCase::class, 'risk_case_id', 'id');
    }

    public function merchant(): BelongsTo
    {
        return  $this->belongsTo(Merchant::class, 'merchant_id', 'merchant_id');
    }
}