<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Model;

class OrderTrack extends Model
{
    use HasDateTimeFormatter;

    protected $primaryKey = 'order_id';
    protected $keyType = 'string';
    protected $guarded = [];
    public $incrementing = false;

    const API_RESULT_STATUS_ERROR     = 0;
    const API_RESULT_STATUS_DELIVERED = 1;
    const API_RESULT_STATUS_LAUNCHED  = 2;
    const API_RESULT_STATUS_WAITING   = 3;
    const API_RESULT_STATUS_EXPIRED   = 4;

    public static $apiResultStatusMap = [
        self::API_RESULT_STATUS_ERROR     => '错误',
        self::API_RESULT_STATUS_DELIVERED => '已完成',
        self::API_RESULT_STATUS_LAUNCHED  => '已上线',
        self::API_RESULT_STATUS_WAITING   => '未上线',
        self::API_RESULT_STATUS_EXPIRED   => '已过期'
    ];

    const IS_DELIVERED_NO        = 0;
    const IS_DELIVERED_DELIVERED = 1;
    const IS_DELIVERED_ING       = 2;

    public static $isDeliveredMap = [
        self::IS_DELIVERED_NO        => '未妥投',
        self::IS_DELIVERED_DELIVERED => '已妥投',
        self::IS_DELIVERED_ING       => '待确认',
    ];

    public function order()
    {
        return $this->belongsTo(Order::class, 'order_id', 'order_id');
    }

    public function task()
    {
        return $this->hasMany(OrderTrackTask::class, 'order_id', 'order_id');
    }
}
