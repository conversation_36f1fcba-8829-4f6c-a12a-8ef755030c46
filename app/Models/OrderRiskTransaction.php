<?php

namespace App\Models;

use App\Scopes\Bid;
use Dcat\Admin\Traits\HasDateTimeFormatter;

use Illuminate\Database\Eloquent\Model;

class OrderRiskTransaction extends Model
{
    use HasDateTimeFormatter;

    public    $incrementing = false;
    public    $timestamps   = false;
    protected $primaryKey   = 'order_id';
    protected $keyType      = 'string';
    protected $fillable     = ['order_id', 'order_number', 'merchant_id', 'business_id', 'merchant_name', 'channel', 'url_name', 'currency', 'amount', 'is_accept', 'is_3d', 'card_mask', 'card_name', 'card_email', 'ip', 'completed_at', 'created_at', 'updated_at'];
    
    const IS_ACCEPT_NOT = 0;
    const IS_ACCEPT     = 1;

    public static $isAcceptMap = [
        self::IS_ACCEPT_NOT => '不接受',
        self::IS_ACCEPT     => '接受'
    ];
    
    const PROCESSING_STATUS_NOT = 0;
    const PROCESSING_STATUS     = 1;
    
    public static $processingStatusMap = [
        self::PROCESSING_STATUS_NOT => '待处理',
        self::PROCESSING_STATUS     => '已处理',
    ];

    public function relation()
    {
        return $this->hasOne(OrderRelation::class, 'order_id', 'order_id');
    }
    
    /**
     * 启动添加scope
     */
    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope(new Bid());
    }
}
