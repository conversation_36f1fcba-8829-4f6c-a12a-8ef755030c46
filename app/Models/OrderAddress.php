<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;

use Illuminate\Database\Eloquent\Model;

class OrderAddress extends Model
{
	use HasDateTimeFormatter;
    protected $table = 'order_addresses';

    protected $primaryKey = 'id';

    protected $fillable = [
        'bill_first_name', 'bill_last_name', 'bill_email', 'bill_address', 'bill_city', 'bill_state', 'bill_postcode', 'bill_country_isoa2', 'bill_phone',
        'ship_first_name', 'ship_last_name', 'ship_email', 'ship_address', 'ship_city', 'ship_state', 'ship_postcode', 'ship_country_isoa2', 'ship_phone',
        'ip'
    ];

//    protected $hidden = [
//        'id'
//    ];

    public function orders()
    {
        return $this->hasMany(Order::class, 'address_id', 'id');
    }
}
