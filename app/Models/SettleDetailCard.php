<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;

use Illuminate\Database\Eloquent\Model;

class SettleDetailCard extends Model
{
    use HasDateTimeFormatter;

    protected $table = 'settle_detail_cards';
    protected $guarded = [];
    // 金额类型
    const SETTLE_DETAIL_CARDS_TYPE_00 = '00';
    const SETTLE_DETAIL_CARDS_TYPE_01 = '01';
    const SETTLE_DETAIL_CARDS_TYPE_10 = '10';
    const SETTLE_DETAIL_CARDS_TYPE_11 = '11';
    const SETTLE_DETAIL_CARDS_TYPE_12 = '12';
    const SETTLE_DETAIL_CARDS_TYPE_13 = '13';
    const SETTLE_DETAIL_CARDS_TYPE_20 = '20';
    const SETTLE_DETAIL_CARDS_TYPE_21 = '21';
    const SETTLE_DETAIL_CARDS_TYPE_30 = '30';
    const SETTLE_DETAIL_CARDS_TYPE_31 = '31';
    const SETTLE_DETAIL_CARDS_TYPE_90 = '90';

    public static $settleDetailCardsTypeMap = [
        self::SETTLE_DETAIL_CARDS_TYPE_00 => '充值金额',
        self::SETTLE_DETAIL_CARDS_TYPE_01 => '转出金额',
        self::SETTLE_DETAIL_CARDS_TYPE_10 => '卡转入金额',
        self::SETTLE_DETAIL_CARDS_TYPE_11 => '卡转出金额',
        self::SETTLE_DETAIL_CARDS_TYPE_12 => '退值比例手续费',
        self::SETTLE_DETAIL_CARDS_TYPE_13 => '销卡比例手续费',
        self::SETTLE_DETAIL_CARDS_TYPE_20 => '卡片开通费',
        self::SETTLE_DETAIL_CARDS_TYPE_21 => '卡片月管理费',
        self::SETTLE_DETAIL_CARDS_TYPE_30 => '共享卡交易消耗',
        self::SETTLE_DETAIL_CARDS_TYPE_31 => '交易单笔处理费',
        self::SETTLE_DETAIL_CARDS_TYPE_90 => '其他',
    ];

    public static $settleDetailCardsTypeMapToEn = [
        self::SETTLE_DETAIL_CARDS_TYPE_00 => 'recharge_amount',
        self::SETTLE_DETAIL_CARDS_TYPE_01 => 'transfer_out_amount',
        self::SETTLE_DETAIL_CARDS_TYPE_10 => 'card_transfer_in_amount',
        self::SETTLE_DETAIL_CARDS_TYPE_11 => 'card_transfer_out_amount',
        self::SETTLE_DETAIL_CARDS_TYPE_12 => 'refund_ratio_fee',
        self::SETTLE_DETAIL_CARDS_TYPE_13 => 'sale_ratio_fee',
        self::SETTLE_DETAIL_CARDS_TYPE_20 => 'card_opening_fee',
        self::SETTLE_DETAIL_CARDS_TYPE_21 => 'monthly_fee',
        self::SETTLE_DETAIL_CARDS_TYPE_30 => 'shared_card_transaction_consumption',
        self::SETTLE_DETAIL_CARDS_TYPE_31 => 'transaction_single_processing_fee',
        self::SETTLE_DETAIL_CARDS_TYPE_90 => 'other',
    ];

    public static function getUniqueId()
    {
        return time() . mt_rand(10000000, 99999999);
    }

}
