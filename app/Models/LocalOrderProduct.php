<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class LocalOrderProduct extends Model
{
	use HasDateTimeFormatter;
	protected $table = 'local_order_products';
	protected $primaryKey = 'id';

	protected $casts = [
		'order_id' => 'string'
	];

	protected $fillable = [
		'name', 'price', 'qty', 'url', 'sku', 'attribute', 'is_gift', 'is_virtual'
	];

	protected $hidden = [
		'id'
	];

	const TYPE_MERCHANT = 0;
	const TYPE_BANK = 1;

	public static $typeMap = [
		self::TYPE_MERCHANT => '商户',
		self::TYPE_BANK     => '银行',
	];

	public function adds($datas, $orderId = '')
	{
		if (!empty($orderId)) {
			foreach ($datas as $key => $value) {
				$datas[$key]['order_id']   = $orderId;
				$datas[$key]['created_at'] = date_create();
				$datas[$key]['updated_at'] = date_create();
				$datas[$key]['is_gift']    = empty($value['is_gift']) ? false : true;
				$datas[$key]['is_virtual'] = empty($value['is_virtual']) ? false : true;
				$datas[$key]['type'] 	   = self::TYPE_MERCHANT;
			}
		}

		return DB::table($this->table)->insert($datas);
	}
}
