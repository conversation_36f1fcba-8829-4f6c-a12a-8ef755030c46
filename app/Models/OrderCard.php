<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;

use Illuminate\Database\Eloquent\Model;
use DES3;

class OrderCard extends Model
{
	use HasDateTimeFormatter;
    protected $table = 'order_cards';
    protected $primaryKey = 'id';

    protected $casts = [
        'order_id' => 'string'
    ];

    protected $fillable = [
        'card_number', 'expiration_year', 'expiration_month', 'cvv', 'card_mask', 'cc_type', 'card_country'
    ];

    public function order()
    {
        return $this->hasMany(Order::class, 'card_id', 'id');
    }
}
