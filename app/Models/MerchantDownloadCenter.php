<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Model;

class MerchantDownloadCenter extends Model
{
    use HasDateTimeFormatter;
    protected $table = 'merchant_download_center';

    protected $casts = [
        'query_criteria' => 'array',
    ];

    protected $fillable = ['merchant_id', 'business_id', 'user_id', 'file_name', 'export_identity', 'query_criteria'];

    // 状态
    const EXPORT_STATUS_NOT     = 0;
    const EXPORT_STATUS_ON      = 1;
    const EXPORT_STATUS_SUCCESS = 2;
    const EXPORT_STATUS_ERROR   = 3;

    public static $exportStatusMap = [
        self::EXPORT_STATUS_NOT     => '未开始',
        self::EXPORT_STATUS_ON      => '进行中',
        self::EXPORT_STATUS_SUCCESS => '导出成功',
        self::EXPORT_STATUS_ERROR   => '导出失败',
    ];

    public static $exportIdentityMap = [
        'merchantOrder'             => '订单导出',
        'merchantTrackOrder'        => '运单申请导出',
        'merchantRefund'            => '退款信息导出',
        'merchantCardVirtual'       => '虚拟卡管理导出',
        'merchantChargeback'        => '拒付工单导出',
        'merchantChargebackCase'    => '拒付预警导出',
        'merchantSettlement'        => '对账单明细导出',
    ];

    public function user(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
