<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MerchantKycBeneficial extends Model
{
    use HasDateTimeFormatter;

    protected $table = 'merchant_kyc_beneficial';

    protected $guarded = [];

    protected $fillable = [];

    protected $casts = [
        'cert_file' => 'array',
    ];

    const CERT_TYPE_ID = 0;
    const CERT_TYPE_HK_ID = 1;
    const CERT_TYPE_PASSPORT = 2;

    public static $certTypeMap = [
        self::CERT_TYPE_ID        => '身份证',
        self::CERT_TYPE_HK_ID     => '香港身份证',
        self::CERT_TYPE_PASSPORT  => '彩色护照',
    ];

    const BENEFICIAL_TYPE_UBO = 0;
    const BENEFICIAL_TYPE_DIRECTOR = 1;
    const BENEFICIAL_TYPE_AUTHORITY = 2;

    public function merchantKyc(): BelongsTo
    {
        return $this->belongsTo(MerchantKyc::class, 'merchant_kyc_id', 'id');
    }
}
