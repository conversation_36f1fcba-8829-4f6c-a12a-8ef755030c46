<?php

namespace App\Admin\Controllers\Directory;

use App\Admin\Actions\Tools\ShellProductExportTool;
use App\Admin\Forms\Directory\ShellProductUpload;
use App\Admin\Repositories\ShellProduct;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Show;
use Dcat\Admin\Widgets\Modal;

class ShellProductController extends AdminController
{
    protected $exportTitle = [
        'shell_url' => '壳站url',
        'sku'       => 'SKU',
        'name'      => '产品名称',
        'url'       => '产品链接',
        'price'     => '产品单价',
    ];
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid(): Grid
    {
        return Grid::make(new ShellProduct(), function (Grid $grid) {
            $grid->model()->orderBy('id', 'desc');
            if (count(request()->toArray()) <= 1) {
                request()->offsetSet('created_at.start', date('Y-m-d 00:00:00', strtotime('-1 month')));
                request()->offsetSet('created_at.end', date('Y-m-d H:i:s'));
            }

            $grid->enableDialogCreate();
            $grid->showQuickEditButton();
            $grid->disableEditButton();
            $grid->showColumnSelector();
            $grid->hideColumns(['id', 'updated_at']);

            $grid->column('id');
            $grid->column('shell_url');
            $grid->column('name');
            $grid->column('url');
            $grid->column('attribute');
            $grid->column('price')->sortable();
            $grid->column('sku');
            $grid->column('created_at')->sortable();
            $grid->column('updated_at');

            $grid->tools(
                Modal::make()
                    ->lg()
                    ->title('导入')
                    ->body(ShellProductUpload::make())
                    ->button('<button class="btn btn-primary">导入</button>')
            );

            $grid->tools(function (Grid\Tools $tools) use ($grid) {
                $tools->append(new ShellProductExportTool($this->exportTitle));
            });

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('name');
                $filter->equal('shell_url')->select(\App\Models\ShellProduct::get()->pluck('shell_url', 'shell_url')->toArray());
                $filter->between('price');
                $filter->between('created_at')->datetime(['sideBySide'=>true]);
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id): Show
    {
        return Show::make($id, new ShellProduct(), function (Show $show) {
            $show->field('id');
            $show->field('shell_url');
            $show->field('name');
            $show->field('url');
            $show->field('attribute');
            $show->field('price');
            $show->field('sku');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form(): Form
    {
        return Form::make(new ShellProduct(), function (Form $form) {
            $form->display('id');
            $form->text('shell_url')->required();
            $form->text('name')->required();
            $form->text('url');
            $form->text('attribute');
            $form->text('price')->required();
            $form->text('sku');
            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
