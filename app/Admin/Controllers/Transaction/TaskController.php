<?php

namespace App\Admin\Controllers\Transaction;

use App\Admin\Actions\Grid\OrderNoticeTask\Task;
use App\Admin\Repositories\OrderNoticeTask;
use App\Models\OrderNoticeTask as OrderNoticeTaskModel;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class TaskController extends AdminController
{
	protected $title = '通知任务';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new OrderNoticeTask(), function (Grid $grid) {
            if (count(request()->toArray()) <= 1) {
                request()->offsetSet('created_at', [
                    'start' => date('Y-m-d 00:00:00', strtotime('-1 week')),
                    'end'   => date('Y-m-d H:i:s')
                ]);
            }
            $grid->model()->orderBy('created_at', 'desc')->orderBy('updated_at', 'desc');
            $grid->column('id')->sortable();
            $grid->column('order_id');
            $grid->column('content', '通知内容');
            $grid->column('notify_url');
            $grid->column('cnt');
            $grid->column('status', '通知状态')->display(function($val) {
	            return OrderNoticeTaskModel::$statusMap[$val] ?? '-';
            });
            $grid->column('result');
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();

	        // grid代码
	        $grid->actions(new Task());
	        $grid->fixColumns(1, -1);

	        $grid->disableEditButton();
	        $grid->disableViewButton();
	        $grid->disableDeleteButton();
            $grid->disableRowSelector();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
                $filter->equal('order_id');
                $filter->equal('status', '状态')->select(OrderNoticeTaskModel::$statusMap);
                $filter->equal('notify_url');
                $filter->between('created_at')->datetime(['sideBySide'=>true]);
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new OrderNoticeTask(), function (Show $show) {
            $show->field('id');
            $show->field('order_id');
            $show->field('content');
            $show->field('notify_url');
            $show->field('cnt');
            $show->field('status');
            $show->field('result');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new OrderNoticeTask(), function (Form $form) {
            $form->display('id');
            $form->text('order_id');
            $form->text('content');
            $form->text('notify_url');
            $form->text('cnt');
            $form->text('status');
            $form->text('result');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
