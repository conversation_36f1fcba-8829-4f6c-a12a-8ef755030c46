<?php

namespace App\Admin\Controllers\Transaction;

use App\Admin\Actions\Grid\Order\AbnormalRefund as OrderAbnormalRefund;
use App\Admin\Actions\Tools\AbnormalRefundsExportTool;
use App\Admin\Repositories\AbnormalRefund;
use App\Models\AbnormalPaymentRefund;
use App\Models\AbnormalRefund as ModelsAbnormalRefund;
use App\Models\ChannelSupplier;
use App\Models\Merchant;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class AbnormalRefundController extends AdminController
{
    protected $exportTitle = [
        'refund_id',
        'order_id',
        'payment_refund_id',
        'order_number',
        'channel',
        'original_order_currency',
        'original_order_amount',
        'refund_currency',
        'refund_amount',
        'refund_status',
        'code',
        'result',
        'remark',
        'completed_at',
        'created_at',
        'updated_at',
    ];

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new AbnormalRefund(['abnormalPaymentRefund', 'order']), function (Grid $grid) {
            // 添加默认时间
            if (count(request()->toArray()) <= 1) {
                request()->offsetSet('created_at', [
                    'start' => date('Y-m-d 00:00:00', strtotime('-1 week')),
                    'end'   => date('Y-m-d H:i:s')
                ]);
            }

            $grid->model()->orderBy('updated_at', 'desc')->orderBy('order_id', 'desc');
            $grid->refund_id->sortable();
            $grid->order_id;
            $grid->column('order.order_number', '商户订单号');
            $grid->column('order.merchant_name', '商户名');
            $grid->column('abnormal_payment_refund.payment_refund_id', '退款渠道订单号');
            $grid->column('order.channel', '账单标识');
            $grid->column('order.currency', '原始订单币种');
            $grid->column('order.amount', '原始订单金额');
            $grid->column('currency');
            $grid->column('amount');
            $grid->column('status')->display(function ($value) {
                return ModelsAbnormalRefund::$statusMap[$value] ?? '未知';
            })->dot(['0' => 'danger', '1' => 'success', '2' => 'primary', '3' => 'primary', '4' => 'success']);
            $grid->column('code/result/remark')->display(function () {
                return $this->code . '<br/>' . $this->result . '<br/>' . $this->remark;
            });
            $grid->column('paymentCode/paymentResult')->display(function () {
                return $this->abnormalPaymentRefund['code'] . '<br/>' . $this->abnormalPaymentRefund['result'];
            });
            $grid->completed_at;
            $grid->created_at;
            $grid->updated_at->sortable();
            $grid->fixColumns(1, -1);

            $grid->disableDeleteButton();
            $grid->disableCreateButton();
            $grid->disableEditButton();
            $grid->disableBatchDelete();

            $grid->tools(function (Grid\Tools $tools) use ($grid) {
                $tools->append(new AbnormalRefundsExportTool($this->exportTitle));
            });

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $orderId        = $actions->row->order_id;
                $abnormalRefund = ModelsAbnormalRefund::where('order_id', $actions->row->order_id)->whereIn('status', [ModelsAbnormalRefund::STATUS_APPROVED, ModelsAbnormalRefund::STATUS_PENDING, ModelsAbnormalRefund::STATUS_REVIEW])->first();
                if (!$abnormalRefund) {
                    $actions->append(new OrderAbnormalRefund($orderId));
                }
            });

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('refund_id');
                $filter->equal('order_id');
                $filter->where('merchant_id',function ($query){
                    $query->whereHas('order', function ($query) {
                        $query->where('merchant_id', "{$this->input}");
                    });
                })->select(
                    Merchant::where('status', Merchant::STATUS_ENABLE)
                        ->pluck('merchant_name', 'merchant_id')
                        ->map(static function ($item, $key) {
                            return $item . ':' . $key;
                        })
                        ->toArray()
                );
                $filter->equal('abnormalPaymentRefund.payment_refund_id', '退款渠道订单号');
                $filter->where('order_number', function ($query) {
                    $query->whereHas('order', function ($query) {
                        $query->where('order_number', "{$this->input}");
                    });
                }, '商户订单号');
                $filter->equal('status')->select(ModelsAbnormalRefund::$statusMap);
                $filter->equal('order.channelObj.channel_supplier_id','渠道')->select(ChannelSupplier::get()->pluck('supplier_name', 'id')->toArray());
                $filter->equal('code');
                $filter->between('created_at')->datetime(['sideBySide'=>true]);
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new AbnormalRefund(), function (Show $show) {
            $show->html();
            $show->disableEditButton();
            $show->disableDeleteButton();

            $show->row(function (Show\Row $show) {
                $show->width(4)->refund_id;
                $show->width(4)->order_id;
                $show->width(4)->currency;
                $show->width(4)->amount;
                $show->width(4)->status->using(ModelsAbnormalRefund::$statusMap);
                $show->width(4)->code;
                $show->width(4)->result;
                $show->width(4)->remark;
                $show->width(4)->completed_at;
                $show->width(4)->created_at;
                $show->width(4)->updated_at;
            });

            // 渠道信息
            $show->payment_info(function ($model) {
                return Show::make($model->abnormalPaymentRefund['id'], new AbnormalPaymentRefund(), function (Show $show) {
                    $show->row(function (Show\Row $show) {
                        $show->width(4)->order_number('提交给渠道的订单号');
                        $show->width(4)->payment_refund_id('渠道返回的订单号');
                        $show->width(4)->currency;
                        $show->width(4)->amount;
                        $show->width(4)->status->using(AbnormalPaymentRefund::$statusMap);
                        $show->width(4)->code;
                        $show->width(4)->result;
                    });

                    $show->disableDeleteButton();
                    $show->disableEditButton();
                    $show->disableListButton();
                });
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new AbnormalRefund(), function (Form $form) {
            $form->display('refund_id');
            $form->text('order_id');
            $form->text('currency');
            $form->text('amount');
            $form->text('status');
            $form->text('code');
            $form->text('result');
            $form->text('remark');
            $form->text('completed_at');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
