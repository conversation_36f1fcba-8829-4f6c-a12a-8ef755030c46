<?php

namespace App\Admin\Controllers\Dispute;

use App\Admin\Actions\Grid\Order\Refund;
use App\Admin\Repositories\DirectoryDictionary;
use App\Classes\Pay\Exceptions\Exception;
use App\Handlers\ImageUploadHandler;
use App\Models\Channel;
use App\Models\DirectoryCarrier;
use App\Models\Merchant;
use App\Models\Order;
use App\Models\OrderAddress;
use App\Models\OrderComplaint;
use App\Models\OrderProduct;
use App\Models\OrderRelation;
use App\Models\OrderTrack;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Widgets\Modal;
use Illuminate\Support\Facades\DB;
use Dcat\Admin\Admin;

class ComplaintController extends AdminController
{
    protected $title = '投诉工单';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        Admin::script(
            <<<JS
                $(".orderDetail").dblclick(function(){
                    var orderId = $(this).html();
                    orderId = orderId.replace(/(^\s*)|(\s*$)/g, "");
                    var orderIdArr = orderId.split(';');
                    val = '/admin/orders/' + orderIdArr[1];
                    window.open(val,'_blank').location;
                })
            JS
        );

        return Grid::make(OrderComplaint::with(['dictionary', 'orderRelation']), function (Grid $grid) {
            $grid->model()
                ->orderBy('updated_at', 'desc')
                ->orderBy('created_at', 'desc');

            $grid->column('notice_status', ' ')
                ->using(OrderComplaint::$noticeStatusMap)
                ->label([
                    0 => '',
                    1 => 'danger',
                    2 => 'warning',
                    3 => 'success',
                    4 => 'primary',
                ]);

            $grid->order_id('订单号')->display(function () {
                return '<button class="orderDetail fa fa-align-right"  style="border: none; color: #11088C;">' . '&nbsp;' . $this->order_id . '</button>';
            });

            $grid->column('merchant_id');
            $grid->column('merchant_name');
            $grid->column('business_id');
            $grid->column('order_number');
            $grid->column('bill_name');
            $grid->column('bill_email');
            $grid->column('dictionary.name');
            $grid->column('content')->display(function ($value) {
                $contents = !empty($value) ? json_decode($value, true) : array();
                $content  = current($contents);

                return isset($content['content']['text']) && !empty($content['content']['text']) ? substr_replace($content['content']['text'], '...', 50) : '...'; // 截取部分内容显示
            });
            $grid->column('is_reply')->display(function ($value) {
                return OrderComplaint::$isReplyMap[$value] ?? '-';
            })->dot(['0' => 'danger', '1' => 'success']);
            $grid->column('orderRelation.is_refund', '是否退款')->display(function ($value) {
                if (!$value){
                    return '-';
                }
                return OrderRelation::$isRefundMap[$value] ?? '-';
            })->dot(['0' => 'danger', '1' => 'success', '2' => 'primary']);
            $grid->column('orderRelation.is_chargeback', '是否拒付')->display(function ($value) {
                if (!$value){
                    return '-';
                }
                return OrderRelation::$isChargebackMap[$value] ?? '-';
            })->dot(['0' => 'danger', '1' => 'success']);
            $grid->column('device')->display(function ($value) {
                return $value ? 'M' : 'PC';
            });
            $grid->column('ip');
            $grid->column('confirmed_type')->display(function ($value) {
                return OrderComplaint::$confirmedTypesMap[$value] ?? '未知';
            });
            $grid->column('replied_at');
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $modal = Modal::make()
                    ->xl()
                    ->title('退 款')
                    ->body(Refund::make()->payload(['order_id' => $this->order_id]))
                    ->button('<button type="button" class="btn btn-outline-primary">退 款</button>');
                // prepend一个操作
                $actions->prepend($modal . '&nbsp;');
            });

            // grid代码
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $url = admin_route("complaints.item", $this->id);
                $actions->append('<a target="_blank" class="grid-expand btn btn-danger" href="' . $url . '">详 情</a>');
            });

            $grid->fixColumns(1, -1);

            $grid->quickSearch(['order_id', 'order_number']);
            $grid->disableEditButton();
            $grid->disableViewButton();
            $grid->disableDeleteButton();
            $grid->disableCreateButton();
            $grid->disableRowSelector();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('order_id');
                $filter->equal('order_number');
                $filter->equal('merchant_id')->select(
                    Merchant::where('status', Merchant::STATUS_ENABLE)
                        ->pluck('merchant_name', 'merchant_id')
                        ->map(static function ($item, $key) {
                            return $item . ':' . $key;
                        })
                        ->toArray()
                );
                $filter->equal('bill_email');
                $filter->equal('bill_name');
                $filter->equal('dictionary.id', '标题')->select((new DirectoryDictionary())->getTypeList('投诉标题'));
                $filter->equal('is_reply')->select(OrderComplaint::$isReplyMap);
                $filter->equal('orderRelation.is_refund', '是否退款')->select(OrderRelation::$isRefundMap);
                $filter->equal('orderRelation.is_chargeback', '是否拒付')->select(OrderRelation::$isChargebackMap);
                $filter->equal('notice_status')->select(OrderComplaint::$noticeStatusMap);
                $filter->between('created_at')->datetime(['sideBySide'=>true]);
                $filter->between('replied_at')->datetime(['sideBySide'=>true]);
            });
        });
    }

    public function item($id)
    {
        // 初始化数组
        $displayData = [
            'order'            => [],
            'orderAddress'     => [],
            'orderProduct'     => [],
            'orderDelivery'    => [],
            'orderComplaint'   => [],
            'trackingList'     => [],
            'complaintTagList' => [],
            'messageList'      => [],
            'id'               => $id
        ];

        // 投诉标题
        $dictionaryModel                 = new DirectoryDictionary();
        $displayData['complaintTagList'] = $dictionaryModel->getTypeList('投诉标题')->toArray();

        // 工单
        $complaint                     = OrderComplaint::with('order')->find($id)->toArray();
        if (!$complaint['order']){
            $complaint['order'] = new Order();
        }

        $displayData['orderComplaint'] = $complaint;
        $displayData['order']          = $complaint['order'];

        // 渠道信息
        $supplierData                          = Channel::with('channelSupplier')->where('id', $complaint['order']['channel_id'])->first();
        if ($supplierData){
            $displayData['order']['supplier_name'] = $supplierData->channelSupplier->supplier_name;
        }

        // 订单信息
        $orderRelation = OrderRelation::firstWhere('order_id', $complaint['order_id']);
        $orderRelation = $orderRelation ? $orderRelation->toArray() : [];

        // 状态显示
        $statusList = [];

        if (isset($orderRelation['is_refund']) && $orderRelation['is_refund']) {
            $statusList[] = 'Refunded';
        }

        if (isset($orderRelation['is_dishonour']) && $orderRelation['is_dishonour']) {
            $statusList[] = 'Chargebacked';
        }

        $displayData['order']['amount']     = isset($complaint['order']['amount']) ? number_format($complaint['order']['amount'], 2) : '0.00';
        $displayData['order']['status_des'] = empty($statusList) ? 'Approved' : implode('+', $statusList);

        // 订单地址
        $address                     = OrderAddress::with('orders')->where('id', $complaint['order']['address_id'])->first();
        $displayData['orderAddress'] = !empty($address) ? $address->toArray() : [];

        // 商品信息
        $product                     = OrderProduct::where('type', '0')->where('order_id', $complaint['order_id'])->get();
        $displayData['orderProduct'] = !empty($product) ? $product->toArray() : [];

        // 争议工单
        $complaint = OrderComplaint::with('order')->where('order_id', $complaint['order_id'])->first();

        if (!empty($complaint)) {
            $displayData['orderComplaint'] = $complaint->toArray();

            // 对话信息
            $displayData['messageList'] = json_decode($displayData['orderComplaint']['content'], true);
        }

        // 运单信息
        $deliveryTypeList = DirectoryCarrier::getTrackTypeList();
        $orderTrack       = OrderTrack::find($complaint['order_id']);

        if (!empty($orderTrack)) {
            $orderTrack->tracking_type    = $deliveryTypeList[$orderTrack->tracking_type] ?? '';
            $displayData['orderDelivery'] = $orderTrack->toArray();

            if (!empty($orderTrack->api_result_content)) {
                $tempData = json_decode($orderTrack->api_result_content, true);

                if (is_array($tempData)) {
                    foreach ($tempData as $list) {
                        $displayData['trackingList'][$list['tracking_number']] = array(
                            'original_country'       => isset($list['original_country']) ? $list['original_country'] : '-',
                            'original_event_list'    => isset($list['original_event_list']) ? $list['original_event_list'] : array(),
                            'destination_country'    => isset($list['destination_country']) ? $list['destination_country'] : '-',
                            'destination_event_list' => isset($list['destination_event_list']) ? $list['destination_event_list'] : array()
                        );
                    }
                }
            }
        }

        return view('admin.complaint.index', $displayData);
    }

    public function save()
    {
        $data = request()->toArray();

        // 数据校验
        $compliantId = request('id');
        $content     = isset($data['text']) ? $data['text'] : '';

        if (empty($compliantId)) {
            return ['error' => true, 'msg' => '非法请求!'];
        }

        // 内容过滤
        $content = preg_replace('/<script[\s\S]*?<\/script>/i', '', $content);

        // 争议工单
        $orderComplaint = OrderComplaint::with('order')->where('id', $compliantId)->first();

        if (empty($orderComplaint)) {
            return ['error' => true, 'msg' => '投诉工单信息不存在!'];
        }

        // 事务
        DB::beginTransaction();

        // 文件上传
        $fileList  = request()->file('imgUpload');
        $imageList = [];
        $uploader  = new ImageUploadHandler();

        if (!empty($fileList)) {
            foreach ($fileList as $key => $file) {
                $result = $uploader->save($file, 'admin_complaints');

                if ($result) {
                    $imageList[$key]['path'] = $result['path'];
                    $imageList[$key]['url']  = $result['url'];
                }
            }
        }

        if (empty($content) && empty($imageList)) {
            return ['error' => true, 'msg' => '回复信息不能为空'];
        }

        $contentList = json_decode($orderComplaint->content, true);

        if (strlen($content) > 0) {
            $tempData = [
                'type'       => 'Platform',
                'content'    => array('text' => $content, 'pic' => array()),
                'device'     => '0',
                'ip'         => request()->getClientIp(),
                'by_added'   => 'system',
                'date_added' => now()
            ];
            array_unshift($contentList, $tempData);
        } elseif (!empty($imageList)) {
            foreach ($imageList as $key => $list) {
                $tempData = [
                    'type'       => 'Platform',
                    'content'    => array('text' => '', 'pic' => $list),
                    'device'     => '0',
                    'ip'         => request()->getClientIp(),
                    'by_added'   => 'system',
                    'date_added' => now()
                ];
                array_unshift($contentList, $tempData);
            }
        }

        $contentList = clean($contentList, 'default');

        $data = [
            'order_id'    => $orderComplaint->order_id,
            'merchant_id' => $orderComplaint->merchant_id,
            'content'     => json_encode($contentList)
        ];

        try {
            OrderComplaint::updateOrCreate(['id' => $compliantId], $data);
        } catch (Exception $exception) {
            DB::rollBack();
            return ['error' => true, 'msg' => '操作失败,原因是:' . $exception->getMessage()];
        }

        DB::commit();

        return ['error' => false, 'msg' => 'success', 'content' => $content];
    }

    public function notice($id)
    {
        $status         = isset($_POST['notice_status']) ? $_POST['notice_status'] : '0';
        $orderComplaint = OrderComplaint::find($id);

        if (empty($orderComplaint)) {
            return ['error' => true, 'msg' => '投诉工单不存在'];
        }

        $orderComplaint->notice_status = $status;

        if ($orderComplaint->save()) {
            return ['error' => false, 'msg' => '操作成功'];
        } else {
            return ['error' => true, 'msg' => '操作失败'];
        }
    }
}
