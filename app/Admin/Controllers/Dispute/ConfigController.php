<?php

namespace App\Admin\Controllers\Dispute;

use App\Models\DirectoryDictionary;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class ConfigController extends AdminController
{
    protected $title = '拒付预警渠道配置';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new DirectoryDictionary(), function (Grid $grid) {
            $grid->model()->orderBy('id', 'desc');
            // 添加默认查询条件
            $grid->model()->whereIn('type', ['CDRN配置', 'Wintranx配置', 'embracyShield_config']);
            $grid->column('id')->sortable();
            $grid->column('type');
            $grid->column('name');
            $grid->column('status')->display(function ($value) {
		        return $value ? '启用' : '禁用';
	        });
            $grid->column('remarks', '内容');
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();

            $grid->disableViewButton();
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new DirectoryDictionary(), function (Form $form) {
            $form->display('id');
            $form->select('type')->options(['CDRN配置' => 'CDRN配置', 'Wintranx配置' => 'Wintranx配置', 'embracyShield_config' => 'embracyShield_config'])->required();
            $form->text('name');
	        $form->select('status')->default(1)->options([0 => '禁用', 1 => '启用'])->rules(['required', Rule::in('0', '1')]);
            $form->text('remarks', '内容')->required();

            $form->display('created_at');
            $form->display('updated_at');

            $form->disableViewCheck();
            $form->disableEditingCheck();
            $form->disableCreatingCheck();
            $form->disableViewButton();

            $form->saving(function (Form $form) {
                $name      = $form->name;
                $type      = $form->type;
                $formId    = $form->getKey() ?? null;
                $validator = Validator::make(['name' => $name], [
                    'name' => [
                        'required', 'string', 'max:64', 'in:partnerId,secretKey,version,url,userName,password',
                        Rule::unique('directory_dictionaries')->where(function ($query) use ($name, $type) {
                            return $query->where('name', $name)->where('type', $type);
                        })->ignore($formId)
                    ],
                ]);

                if ($validator->fails()) {
                    return $form->response()->error($validator->errors()->first());
                }
            });

            $form->saved(function (Form $form, $result) {
                if ($result) {
                    switch ($form->type) {
                        case 'CDRN配置':
                            if (Cache::has('Cdrn_Config_Data')) {
                                Cache::forget('Cdrn_Config_Data');
                            }
                            break;

                        case 'Wintranx配置':
                            if (Cache::has('Wintranx_Config_Data')) {
                                Cache::forget('Wintranx_Config_Data');
                            }
                            break;

                        case 'embracyShield_config':
                            if (Cache::has('Risk_Config:embracyShield')) {
                                Cache::forget('Risk_Config:embracyShield');
                            }
                            break;
                    }
                }
            });
        });
    }
}
