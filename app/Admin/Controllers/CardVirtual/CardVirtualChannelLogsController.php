<?php

namespace App\Admin\Controllers\CardVirtual;

use App\Models\CardVirtualChannelLog;
use App\Admin\Exceptions\CardVirtualChannelLog as CardVirtualChannelLogDetails;
use App\Models\Merchant;
use Dcat\Admin\Grid;
use Dcat\Admin\Widgets\Modal;
use App\Admin\Controllers\SecondaryValidationController;

class CardVirtualChannelLogsController extends CardBaseController
{
    protected $title = '卡渠道日志';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $is2faCheck = $this->is2faCheck(SecondaryValidationController::MODE_CARD_LOG);
        return Grid::make(new CardVirtualChannelLog(), function (Grid $grid) use ($is2faCheck) {
            $grid->model()->orderBy('created_at', 'desc')->orderBy('updated_at', 'desc');
            $grid->column('id')->sortable();
            $grid->column('virtual_id');
            $grid->column('type')->using(CardVirtualChannelLog::$typeMap);
            $grid->column('merchant_id', 'MID');
            $grid->column('merchant_name');
            $grid->column('operate_type', '操作类型')->using(CardVirtualChannelLog::$operateTypeMap);
            $grid->column('operate_name', '操作人');
            $grid->column('created_at');
            $grid->column('updated_at');
            $grid->fixColumns(1, -1);

            $grid->disableCreateButton();
            $grid->disableEditButton();
            $grid->disableViewButton();
            $grid->disableDeleteButton();
            $grid->disableRowSelector();

            $grid->actions(function (Grid\Displayers\Actions $actions) use ($is2faCheck) {
                if ($is2faCheck) {
                    $faView = admin_route('2fa.verificationPage', ['mark' => 'google2fa', 'mode' => SecondaryValidationController::MODE_CARD_LOG]);
                    $modal  = "<a href={$faView} class='btn btn-primary'" . ">" . admin_trans_field('详情') . "</a>";
                } else {
                    $modal = Modal::make()->lg()->body(CardVirtualChannelLogDetails::make()->payload(['id' => $this->id]))->button('<button class="btn btn-primary">详情</button>');
                }
                $actions->append($modal);
            });

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
                $filter->equal('virtual_id');
                $filter->in('merchant_id', 'MID')->multipleSelect(
                    Merchant::where('status', Merchant::STATUS_ENABLE)
                        ->pluck('merchant_name', 'merchant_id')
                        ->map(static function ($item, $key) {
                            return $item . ':' . $key;
                        })
                        ->toArray()
                );
                $filter->equal('type')->select(CardVirtualChannelLog::$typeMap);
                $filter->equal('operate_type', '操作类型')->select(CardVirtualChannelLog::$operateTypeMap);
                $filter->equal('operate_name', '操作人');
                $filter->between('created_at')->datetime(['sideBySide'=>true]);
            });
        });
    }
}
