<?php

namespace App\Admin\Repositories;

use Dcat\Admin\Grid;
use Dcat\Admin\Repositories\EloquentRepository;
use App\Models\CardWhiteList as Model;

class CardWhiteListCSV extends EloquentRepository
{
	protected $eloquentClass = Model::class;

	public function get(Grid\Model $model)
	{
		// 返回数据
		$data = [];

		// 导出路径
		$exportPath = public_path('data/export/cardWhiteListed');

		// 扫描导出路径下的所有文件
		// 名称示例：mid_123456_card_white_listed.csv
		if (is_dir($exportPath)) {
			$files = scandir($exportPath);
			foreach ($files as $file) {
				if ($file == '.' || $file == '..') {
					continue;
				}

				// 获取mid
				$merchantId = str_replace(['mid_', '_card_white_listed.csv'], '', $file);

				// 拼接完整文件路径
				$filePath = 'data/export/cardWhiteListed/' . $file;

				// 塞到数据
				$data[] = [
					'merchant_id' => $merchantId,
					'file_name'   => $file,
					'file_path'   => $filePath,
				];

			}
		}

		return $data;

	}
}
