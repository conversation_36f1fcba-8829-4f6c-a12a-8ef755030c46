<?php

namespace app\Admin\Repositories;

use App\Models\MerchantKyc as Model;
use Dcat\Admin\Form;
use Dcat\Admin\Repositories\EloquentRepository;

class MerchantKyc extends EloquentRepository
{
    /**
     * Model.
     *
     * @var string
     */
    protected $eloquentClass = Model::class;

    public function updating(Form $form)
    {
        return $this->edit($form);
    }

    public function edit(Form $form)
    {
        $query = $this->newQuery();

        if ($this->isSoftDeletes) {
            $query->withTrashed();
        }

        $this->model = $query
            ->with($this->getRelations())
            ->findOrFail($form->getKey(), $this->getFormColumns());

        return $this->model;
    }
}
