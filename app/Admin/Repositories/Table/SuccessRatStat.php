<?php

namespace App\Admin\Repositories\Table;


use App\Models\StatOrder;
use App\Services\TableService;
use Dcat\Admin\Grid;
use Dcat\Admin\Repositories\EloquentRepository;

class SuccessRatStat extends EloquentRepository
{
	/**
	 * Model.
	 *
	 * @var string
	 */
	protected $eloquentClass = StatOrder::class;


	public function get(Grid\Model $model)
	{
		// 获取搜索条件
		$searchData       = $this->relations;
		$statisticsData[] = TableService::getSuccessRatStatData($searchData);
		return $statisticsData;

	}

}
