<?php

use Dcat\Admin\Admin;
use Dcat\Admin\Layout\Navbar;

/**
 * Dcat-admin - admin builder based on Lara<PERSON>.
 * <AUTHOR> <https://github.com/jqhph>
 *
 * Bootstraper for Admin.
 *
 * Here you can remove builtin form field:
 *
 * extend custom field:
 * Dcat\Admin\Form::extend('php', PHPEditor::class);
 * Dcat\Admin\Grid\Column::extend('php', PHPEditor::class);
 * Dcat\Admin\Grid\Filter::extend('php', PHPEditor::class);
 *
 * Or require js and css assets:
 * Admin::css('/packages/prettydocs/css/styles.css');
 * Admin::js('/packages/prettydocs/js/main.js');
 *
 */
app('view')->prependNamespace('admin', resource_path('views/vendor/admin'));

$date   = date('Y');
switch (env('APP_NAME', 'Laravel')) {
    case 'Embracy':
        $company = 'Embracy';
        break;

    case 'Hpaymerchants':
        $company = 'Hpaymerchants';
        break;

    case 'PunctualPay':
        $company = 'Punctual Pay';
        break;

    default:
        $company = 'Peachy Pay';
        break;
}

$footer = <<<html
<p class="clearfix blue-grey lighten-2 mb-0 text-center"><span class="text-center d-block d-md-inline-block mt-25">Copyright © {$date} {$company}. All Rights Reserved.</span></p>
html;

Admin::script(
    <<<JS
        $(".main-footer").html('{$footer}');

        $('#grid-table').parent().attr('id', 'table_id');
        let tableCont = document.querySelectorAll('tr th');

        window.onscroll = function() {
            let scrollTop = document.documentElement.scrollTop ||  document.body.scrollTop;
            if (scrollTop > 0) {
                scrollTop -= 130;
                if (scrollTop > 0) {
                    for (let i = 0; i < tableCont.length; i++) {
                        tableCont[i].style.top = scrollTop + 'px';
                    }
                }
            } else {
                for (let i = 0; i < tableCont.length; i++) {
                    tableCont[i].style.top = '0px';
                }
            }
        }

        if (document.getElementById('table_id')) {
            document.getElementById('table_id').onscroll = function() {
                let scrollTop = document.getElementById('table_id').scrollTop;
                if (scrollTop > 0) {
                    scrollTop -= 5;
                    if (scrollTop > 0) {
                        for (let i = 0; i < tableCont.length; i++) {
                            tableCont[i].style.top = scrollTop + 'px';
                        }
                    }
                } else {
                    for (let i = 0; i < tableCont.length; i++) {
                        tableCont[i].style.top = '0px';
                    }
                }
            }
        }
    JS
);

//固定表格表头样式
Admin::style(
    <<<STYLE
        #table_id>table>thead>tr>th {
            position        : relative;
            background-color: #ececf1;
        }
        .table-fixed tr th {
            position        : relative;
            background-color: #ececf1;
        }
        .slider-panel {
            width: 600px;
            right: -600px;
        }
        .slider-panel .slider-content {
            width: 600px;
        }
        
        .modal-xxl {
            max-width: 95%;
        }
        
        .select2-search__field {
            width: 90% !important;
        }
        
        .grid__actions__ a {
            padding-right: 5px;
        }
        
        .grid__actions__ span {
            padding-right: 5px;
        }
    STYLE
);

Admin::navbar(function (Navbar $navbar) {
    if (config('open_im.open_im')) {
        $navbar->right(view('navbar.openIm', ['system' => 'admin']));
    }
});
