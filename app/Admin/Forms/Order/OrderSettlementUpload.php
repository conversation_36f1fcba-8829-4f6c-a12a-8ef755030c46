<?php

namespace App\Admin\Forms\Order;

use App\Models\OrderSettlement;
use App\Models\UploadCenter;
use Dcat\Admin\Admin;
use Dcat\Admin\Widgets\Form;
use PhpOffice\PhpSpreadsheet\IOFactory;

class OrderSettlementUpload extends Form
{
	/**
	 * Handle the form request.
	 *
	 * @param array $input
	 *
	 * @return mixed
	 */
	public function handle(array $input)
	{
		// 获取外部传递参数
		$files     = $input['excelFile'];
		$fileNames = [];
		foreach ($files as $file) {
			$fileUrl     = public_path('/data/orderSettlement') . '/' . $file;
			$fileName    = pathinfo($fileUrl)['basename'];
			$spreadsheet = IOFactory::load($fileUrl);
			$worksheet   = $spreadsheet->getActiveSheet();
			$count       = $worksheet->getHighestRow();
			$orderList   = $worksheet->toArray();
			if ($count > 10000) {
				return $this->response()->error($fileName . '文件行数超过限制');
			}
			$fileNames[] = $fileName; // 获取文件名
		}

		foreach ($orderList as $key => $tempOrder) {
			// 模板验证
			if ($key == 0) {
				if (array_values(OrderSettlement::$fieldList) != array_values(array_filter($tempOrder))) {
					return $this->response()->error('上传模板已更新,请重新下载模板上传');
				}
			}
		}

		$user   = Admin::user();
		$center = UploadCenter::create([
			'admin_user_id'   => $user['id'],
			'file_name'       => $fileNames,
			'import_identity' => 'orderSettlement',
			'url'             => $files,
		]);

		if ($center) {
			return $this->response()->success('导入任务已添加，请到上传中心查看任务进度')->refresh();
		}

		return $this->response()->error('添加导入任务失败');
	}

	/**
	 * Build a form here.
	 * @throws \Exception
	 */
	public function form()
	{
		$url = config('app.url') . '/download/orderSettlement.xlsx';
		$this->multipleFile('excelFile')->accept('xlsx')->autoUpload()->required()->maxSize(102400)->disk('orderSettlement')->help('最多导入10个文件;单个文件限制1w行。')->name(function ($file) {
			// 防止文件名重复情况，原文件名加上时间
			return time() . '-' . str_replace(' ', '', $file->getClientOriginalName());
		})->limit(10);
		$this->button('<a href="' . $url . '" download="orderSettlement.xlsx" style="color: #ffffff" target="_blank">示例文件下载</a>');
	}
}
