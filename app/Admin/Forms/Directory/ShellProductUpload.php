<?php

namespace App\Admin\Forms\Directory;

use App\Admin\Imports\ShellProductImport;
use Dcat\Admin\Widgets\Form;
use Maatwebsite\Excel\Facades\Excel;

class ShellProductUpload extends Form
{
    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return mixed
     */
    public function handle(array $input)
    {
        ini_set('memory_limit', '512M');
        set_time_limit(1800);

        // 获取外部传递参数
        $file = $input['file'];

        try {
            // 导入csv
            Excel::import(new ShellProductImport(), public_path('/data/updateTemp/' . $file));
            return $this->response()->success('成功')->refresh();
        } catch (\Exception $m) {
            return $this->response()->error($m->getMessage());
        }
    }

    /**
     * Build a form here.
     */
    public function form()
    {
        $url = config('app.url') . '/download/shellProduct.xlsx';
        $this->file('file')->accept('xlsx')->autoUpload()->required()->maxSize(102400);
        $this->button('<a href="' . $url . '" download="shellProduct.xlsx" style="color: #ffffff" target="_blank">示例文件下载</a>');
    }
}
