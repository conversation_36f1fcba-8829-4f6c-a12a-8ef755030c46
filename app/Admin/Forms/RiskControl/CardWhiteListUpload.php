<?php

namespace App\Admin\Forms\RiskControl;

use App\Models\UploadCenter;
use Dcat\Admin\Admin;
use Dcat\Admin\Widgets\Form;
use SplFileObject;

class CardWhiteListUpload extends Form
{
    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return mixed
     */
    public function handle(array $input)
    {
        // 获取外部传递参数
        $files     = $input['excelFile'];
        $fileNames = [];
        
        foreach ($files as $file) {
            $fileUrl    = public_path('/data/cardWhiteList') . '/' . $file;
            $fileName   = pathinfo($fileUrl)['basename'];
            $spl_object = new SplFileObject($fileUrl, 'rb');
            $spl_object->seek(filesize($fileUrl));
            $count = $spl_object->key();

            if ($count > 5000000) {
              return $this->response()->error($fileName . '文件行数超过限制');  
            }

            $fileNames[] = $fileName; // 获取文件名
        }

        $user   = Admin::user();
        $center = UploadCenter::create([
            'admin_user_id'   => $user['id'],
            'file_name'       => $fileNames,
            'import_identity' => 'cardWhiteListImport',
            'url'             => $files,
        ]);

        if ($center) {
            return $this->response()->success('导入任务已添加，请到上传中心查看任务进度')->refresh();
        }

        return $this->response()->error('添加导入任务失败');
    }

    /**
     * Build a form here.
     */
    public function form()
    {
        $url = config('app.url') . '/download/cardWhiteList.csv';
        $this->multipleFile('excelFile')->accept('csv')->autoUpload()->required()->maxSize(102400)->disk('cardWhiteList')->help('最多导入10个文件；单个文件限制500w行。')->name(function ($file) {
            // 防止文件名重复情况，原文件名加上时间
            return time() . '-' . str_replace(' ', '', $file->getClientOriginalName());
        })->limit(10);
        $this->button('<a href="' . $url . '" download="cardWhiteList.csv" style="color: #ffffff" target="_blank">示例文件下载</a>');
    }
}
