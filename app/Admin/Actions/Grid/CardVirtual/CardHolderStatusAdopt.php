<?php

namespace App\Admin\Actions\Grid\CardVirtual;

use Dcat\Admin\Admin;
use Dcat\Admin\Grid\RowAction;
use App\Models\CardHolder as CardHolderModel;
class CardHolderStatusAdopt extends RowAction
{
    protected $id;
    protected $status;

    public function __construct($id = 0, $status = '')
    {
        $this->id     = $id;
        $this->status = $status;
        Admin::css('/css/element/businessRiskSwitchCss.css');
    }

    public function title()
    {
        return $this->status == 'on' ?
            "<span class='switchery switchery-small' style='background-color: rgb(88, 108, 177); border-color: rgb(88, 108, 177); box-shadow: rgb(88, 108, 177) 0px 0px 0px 11px inset; transition: border 0.4s ease 0s, box-shadow 0.4s ease 0s, background-color 1.2s ease 0s;'>
<small style='left: 13px; background-color: rgb(255, 255, 255); transition: background-color 0.4s ease 0s, left 0.2s ease 0s;'></small></span>"
            : "<span class='switchery switchery-small' style='box-shadow: rgb(223, 223, 223) 0px 0px 0px 0px inset; border-color: rgb(223, 223, 223); background-color: rgb(255, 255, 255); transition: border 0.4s ease 0s, box-shadow 0.4s ease 0s;'>
<small style='left: 0px; transition: background-color 0.4s ease 0s, left 0.2s ease 0s;'></small></span>";

    }

    public function handle()
    {
        $id     = request()->input('id');
        $status = request()->input('status');
        if (empty($id) || empty($status)) {
            return $this->response()->error('修改失败,请检查重试')->refresh();
        }

        $updateStatus = $status == 'on' ? CardHolderModel::CARD_HOLDER_STATUS_DISABLE : CardHolderModel::CARD_HOLDER_STATUS_ENABLE;
        $updateRet    = CardHolderModel::where('id', $id)->update(['status' => $updateStatus, 'updated_at' => now()->toDateTimeString()]);
        if (!$updateRet) {
            return $this->response()->error('修改失败')->refresh();
        }

        return $this->response()->success('修改成功')->refresh();
    }

    public function confirm()
    {
        return ["是否修改该持卡人状态？"];
    }

    public function parameters()
    {
        return ['id' => $this->id, 'status' => $this->status];
    }
}