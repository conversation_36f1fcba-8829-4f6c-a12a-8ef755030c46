<?php

namespace App\Classes\Track\Gateways;

use App\Classes\Supports\Traits\HasHttpRequest;
use App\Classes\Track\Contracts\Gateway;
use App\Classes\Track\Parser\Tracking51Parser;
use App\Models\OrderTrack;
use App\Models\OrderTrackTask;

class Tracking51Gateway extends Gateway
{
    use HasHttpRequest;

    /**
     * 添加运单添加任务
     *
     * @param $orderTrack
     * @return array
     */
    public function addTrackAddTask(OrderTrack $orderTrack)
    {
        $deliveryNumberList = array_unique(explode(',', $orderTrack->tracking_number));
        $taskData           = [];

        foreach ($deliveryNumberList as $number) {
            $contents   = [
                'tracking_number' => $number,
                'carrier_code'    => $orderTrack->tracking_type
            ];
            $taskData[] = array(
                'order_id' => $orderTrack->order_id,
                'action'   => OrderTrackTask::ACTION_ADD,
                'api_type' => $orderTrack->api_type,
                'content'  => json_encode($contents),
                'status'   => OrderTrackTask::STATUS_WAITING
            );
        }

        return $taskData;
    }

    /**
     * 添加运单修改任务
     *
     * @param OrderTrack $orderTrack
     * @param array $update
     * @return array
     */
    public function addTrackUpdateTask(OrderTrack $orderTrack, array $update)
    {
        $deliveryNumberList = array_unique(explode(',', $orderTrack->tracking_number));
        $taskData           = [];

        foreach ($deliveryNumberList as $number) {
            $contents   = array(
                'delivery_number'      => $number,
                'delivery_type'        => $orderTrack->tracking_type,
                'delivery_type_update' => $update['tracking_type']
            );
            $taskData[] = array(
                'order_id' => $orderTrack->order_id,
                'action'   => OrderTrackTask::ACTION_UPDATE,
                'api_type' => $orderTrack->api_type,
                'content'  => json_encode($contents),
                'status'   => OrderTrackTask::STATUS_WAITING
            );
        }

        return $taskData;
    }

    /**
     * 添加运单至51tracking
     *
     * @param array $trackTaskList
     * @return array[]
     */
    public function taskAddTrack(array $trackTaskList)
    {
        $deliveryList = [];
        $maxRow       = 40;  // 接口限制一次只能传40条
        $cnt          = 0;
        $i            = 0;

        foreach ($trackTaskList as $value) {
            $deliveryList[$cnt][$value['id']] = isset($value['content']) && !empty($value['content']) ? json_decode($value['content'], true) : [];

            if ($i == $maxRow - 1) {
                $i = 0;
                $cnt++;
            } else {
                $i++;
            }
        }

        // 请求地址
        $requestUrl = $this->requestUrl . '/index.php?method=createMultipleTracking';
        $returnData = ['success' => [], 'error' => []];

        foreach ($deliveryList as $key => $list) {
            $tempResultStr = $this->post($requestUrl, $list);
            $tempResultArr = json_decode($tempResultStr, true);
            $parserResult  = Tracking51Parser::apiTrackAddParser($tempResultArr);

            if ($parserResult['error']) {
                continue;
            }

            $returnData['success'] += isset($parserResult['data']['success']) ? $parserResult['data']['success'] : [];
            $returnData['error']   += isset($parserResult['data']['error']) ? $parserResult['data']['error'] : [];
        }

        return $returnData;
    }

    /**
     * 更新运单至51tracking
     *
     * @param array $trackTaskList
     * @return array[]
     */
    public function taskUpdateTrack(array $trackTaskList)
    {
        // 请求地址
        $requestUrl = $this->requestUrl . '/index.php?method=updateCarrierCode';
        $returnData = ['success' => [], 'error' => []];

        foreach ($trackTaskList as $value) {
            $contentArr    = isset($value['content']) ? json_decode($value['content'], true) : [];
            $trackNum      = isset($contentArr['delivery_number']) ? $contentArr['delivery_number'] : '';
            $carrier       = isset($contentArr['delivery_type']) ? $contentArr['delivery_type'] : '';
            $updateCarrier = isset($contentArr['delivery_type_update']) ? $contentArr['delivery_type_update'] : '';

            if (empty($trackNum) || empty($carrier) || empty($updateCarrier)) {
                $returnData['error'][$value['id']] = ['message' => '更新信息缺失'];
                continue;
            }

            $requestData   = array(
                'delivery_number'      => $trackNum,
                'delivery_type'        => $carrier,
                'delivery_type_update' => $updateCarrier,
            );
            $resultStr     = $this->post($requestUrl, $requestData);
            $tempResultArr = json_decode($resultStr, true);
            $parserResult  = Tracking51Parser::apiTrackUpdateParser($tempResultArr);

            if ($parserResult['error']) {
                continue;
            }

            $successData = isset($parserResult['data']['success']) ? $parserResult['data']['success'] : array();
            $errorData   = isset($parserResult['data']['error']) ? $parserResult['data']['error'] : array();

            if (!empty($successData)) {
                $returnData['success'][$value['id']] = $successData;
            } else {
                $returnData['error'][$value['id']] = $errorData;
            }
        }

        return $returnData;
    }

    /**
     * 运单追踪
     *
     * @param array $orderTrackList
     * @return mixed
     */
    public function track($orderTrackList)
    {
        $trackNumbers = implode(',', array_unique(array_column($orderTrackList, 'tracking_number')));

        // api请求
        $requestData    = ['delivery_numbers' => $trackNumbers];
        $requestUrl     = $this->requestUrl . '/index.php?method=getTrackingsList';
        $trackResultStr = $this->post($requestUrl, $requestData);
        $trackResultArr = json_decode($trackResultStr, true);

        if (empty($trackResultArr) || $trackResultArr['error']) { // 无返回
            return ['error' => true, 'msg' => sprintf('%s:物流信息返回错误', $trackNumbers)];
        }

        $resultCode = isset($trackResultArr['data']['meta']['code']) ? $trackResultArr['data']['meta']['code'] : '';
        $resultList = isset($trackResultArr['data']['data']['items']) ? $trackResultArr['data']['data']['items'] : array();

        if ($resultCode == '4031') { // 4031未上线运单不计次数、不修改状态
            $this->_appendTrackTask($orderTrackList);
            return ['error' => true, 'msg' => sprintf('%s:未追踪到该运单号', $trackNumbers)];
        }

        if ($resultCode != '200') { // 发生其他类型错误
            $message = isset($trackResultArr['data']['meta']['message']) ? $trackResultArr['data']['meta']['message'] : '请求错误';

            return ['error' => true, 'msg' => sprintf('%s:api请求错误,原因为:%s', $trackNumbers, $message)];
        }

        if (empty($resultList)) {
            return ['error' => true, 'msg' => sprintf('%s:无物流信息', $trackNumbers)];
        }

        return Tracking51Parser::apiTrackParser($resultList);
    }
}
