<?php

namespace App\Classes\Track\Gateways;

use App\Classes\Supports\Traits\HasHttpRequest;
use App\Classes\Track\Contracts\Gateway;
use App\Models\DirectoryCarrier;
use App\Models\OrderTrack;
use App\Models\OrderTrackTask;

class Track17Gateway extends Gateway
{
    use HasHttpRequest;

    /**
     * 添加运单添加任务
     *
     * @param $orderTrack
     * @return array
     */
    public function addTrackAddTask(OrderTrack $orderTrack)
    {
        $trackingNumberList = array_unique(explode(',', $orderTrack->tracking_number));
        $carrier            = DirectoryCarrier::select(['code_num'])->firstWhere('code', $orderTrack->tracking_type);
        $taskData           = [];

        foreach ($trackingNumberList as $number) {
            $contents   = [
                'number'  => $number,
                'carrier' => $carrier->code_num
            ];
            $taskData[] = [
                'order_id' => $orderTrack->order_id,
                'action'   => OrderTrackTask::ACTION_ADD,
                'api_type' => $orderTrack->api_type,
                'content'  => json_encode($contents),
                'status'   => OrderTrackTask::STATUS_WAITING
            ];
        }

        return $taskData;
    }

    /**
     * 添加运单修改任务
     *
     * @param OrderTrack $orderTrack
     * @param array $update
     * @return array
     */
    public function addTrackUpdateTask(OrderTrack $orderTrack, array $update)
    {
        return []; // todo 待定
    }

    /**
     * 添加运单至17track
     *
     * @param array $trackTaskList
     * @return array[]
     */
    public function taskAddTrack(array $trackTaskList)
    {
        return []; // todo 待定
    }

    /**
     * 更新运单至17track
     *
     * @param array $trackTaskList
     * @return array[]
     */
    public function taskUpdateTrack(array $trackTaskList)
    {
        return []; // todo 待定
    }

    /**
     * 运单追踪
     *
     * @param array $orderTrackList
     * @return mixed
     */
    public function track($orderTrackList)
    {
        return ['error' => true, 'msg' => '接口暂未开放'];// todo 待定
    }
}
