<?php

namespace App\Classes\Track\Contracts;

use App\Classes\Supports\Collection;
use App\Models\OrderTrack;
use App\Models\OrderTrackTask;
use Symfony\Component\HttpFoundation\Response;

class Gateway implements GatewayApplicationInterface
{
    /**
     * payload.
     *
     * @var array
     */
    protected $payload;

    /**
     * gateway.
     *
     * @var string
     */
    protected $gateway;

    /**
     * extends.
     *
     * @var array
     */
    protected $extends;

    /**
     * 运单请求中转地址
     *
     * @var string
     */
    protected $requestUrl = 'http://8.129.116.228:9999';

    /**
     * Magic pay.
     *
     * @param string $method
     * @param array  $params
     *
     * @return Response|Collection
     */
    public function __call($method, $params)
    {
        if (isset($this->extends[$method])) {
            return $this->makeExtend($method, ...$params);
        }
    }

    public function apply($gateway, $params)
    {
        // TODO: Implement apply() method.
    }

    public function track(OrderTrack $orderTrack)
    {
        // TODO: Implement track() method.
    }

    /**
     * 补充漏提交数据
     *
     * @param array $orderTrackList
     * @return bool
     */
    protected function _appendTrackTask($orderTrackList)
    {
        $taskData = [];

        foreach ($orderTrackList as $value) {
            if ($value['tracking_type'] != 'other') {
                $contents   = [
                    'tracking_number' => $value['tracking_number'],
                    'carrier_code'    => $value['tracking_type']
                ];
                $taskData[] = array(
                    'order_id' => $value['order_id'],
                    'action'   => OrderTrackTask::ACTION_ADD,
                    'api_type' => $value['api_type'],
                    'content'  => json_encode($contents),
                    'status'   => OrderTrackTask::STATUS_WAITING
                );
            }
        }

        if (!empty($taskData)) {
            return (new OrderTrackTask())->insert($taskData);
        }

        return false;
    }
}
