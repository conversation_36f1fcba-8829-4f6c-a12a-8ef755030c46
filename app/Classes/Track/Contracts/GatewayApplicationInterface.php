<?php

namespace App\Classes\Track\Contracts;

use App\Models\OrderTrack;
use Symfony\Component\HttpFoundation\Response;
use App\Classes\Supports\Collection;

interface GatewayApplicationInterface
{
    /**
     * To apply.
     *
     * @param string $gateway
     * @param array  $params
     *
     * @return Collection|Response
     */
    public function apply($gateway, $params);

    /**
     * track an orderTrack.
     *
     * @param OrderTrack $orderTrack
     * @return Collection|Response
     */
    public function track(OrderTrack $orderTrack);
}
