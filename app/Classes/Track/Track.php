<?php

namespace App\Classes\Track;

use App\Classes\Supports\Str;
use App\Classes\Track\Contracts\GatewayApplicationInterface;
use App\Classes\Track\Gateways\Track17Gateway;
use App\Classes\Track\Gateways\Tracking51Gateway;
use Exception;

/**
 * @method static Tracking51Gateway Tracking51Gateway() Tracking51Gateway
 * @method static Track17Gateway Track17Gateway() Track17Gateway
 */
class Track
{
    /**
     * Magic static call.
     *
     * @param string $method
     * @param array $params
     * @return GatewayApplicationInterface
     * @throws Exception
     */
    public static function __callStatic($method, $params) : GatewayApplicationInterface
    {
        $app = new self(...$params);

        return $app->create($method);
    }

    /**
     * Create a instance.
     *
     * @param string $method
     * @return GatewayApplicationInterface
     * @throws Exception
     */
    protected function create($method): GatewayApplicationInterface
    {
        $gateway = __NAMESPACE__ . '\\Gateways\\' . Str::studly($method) . 'Gateway';

        if (class_exists($gateway)) {
            return self::make($gateway);
        }

        throw new Exception("Gateway [{$method}] Not Exists");
    }

    /**
     * Make a gateway.
     *
     * @param string $gateway
     *
     * @return GatewayApplicationInterface
     * @throws Exception
     */
    protected function make($gateway): GatewayApplicationInterface
    {
        $app = new $gateway();

        if ($app instanceof GatewayApplicationInterface) {
            return $app;
        }

        throw new Exception("Gateway [{$gateway}] Must Be An Instance Of GatewayApplicationInterface");
    }
}
