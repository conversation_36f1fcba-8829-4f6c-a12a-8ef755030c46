<?php

namespace App\Classes\Track\Parser;

class Track17Parser
{
    /**
     * 添加运单接口返回解析
     *
     * @param $responseData
     * @return array
     */
    public static function apiTrackAddParser($responseData)
    {

    }

    /**
     * 更新运单接口返回解析
     *
     * @param $responseData
     * @return array
     */
    public static function apiTrackUpdateParser($responseData)
    {

    }

    /**
     * 运单追踪接口返回解析
     *
     * @param $responseData
     * @return array
     */
    public static function apiTrackParser($responseData)
    {

    }
}
