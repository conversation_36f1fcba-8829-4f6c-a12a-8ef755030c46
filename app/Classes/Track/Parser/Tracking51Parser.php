<?php

namespace App\Classes\Track\Parser;

use App\Models\OrderTrack;

class Tracking51Parser
{
    /**
     * 添加运单接口返回解析
     *
     * @param $responseData
     * @return array
     */
    public static function apiTrackAddParser($responseData)
    {
        $returnData = ['error' => false, 'msg' => '', 'data' => []];

        if ($responseData['error']) {
            $returnData['error'] = true;
            $returnData['msg']   = sprintf('trackingmore添加运单提交出错,原因是:%s', $responseData['msg']);

            return $returnData;
        }

        $resultArr     = isset($responseData['data']) ? $responseData['data'] : [];
        $tempTrackList = isset($resultArr['data']['trackings']) ? $resultArr['data']['trackings'] : [];
        $tempErrorList = isset($resultArr['data']['errors']) ? $resultArr['data']['errors'] : [];

        foreach ($tempTrackList as $value) {
            $returnData['data']['success'][$value['tracking_number']] = $value;
        }

        foreach ($tempErrorList as $value) {
            $returnData['data']['error'][$value['tracking_number']] = $value;
        }

        return $returnData;
    }

    /**
     * 更新运单接口返回解析
     *
     * @param $responseData
     * @return array
     */
    public static function apiTrackUpdateParser($responseData)
    {
        $returnData = ['error' => false, 'msg' => '', 'data' => []];

        if (empty($responseData) || $responseData['error']) {
            $returnData['error'] = true;
            $returnData['msg']   = sprintf('trackingmore更新运单提交出错,原因是:%s', $responseData['msg']);

            return $returnData;
        }

        $tempData = isset($responseData['data']['meta']) ? $responseData['data']['meta'] : [];

        if ($tempData['code'] == 200) {
            $returnData['data']['success'] = $tempData;
        } else {
            $returnData['data']['error'] = $tempData;
        }

        return $returnData;
    }

    /**
     * 运单追踪接口返回解析
     *
     * @param $responseData
     * @return array
     */
    public static function apiTrackParser($responseData)
    {
        $returnData = ['error' => false, 'msg' => '', 'data' => []];
        $trackList  = [];

        if (empty($responseData)) {
            $returnData['error'] = true;
            $returnData['msg']   = '无物流信息';

            return $returnData;
        }

        foreach ($responseData as $value) {
            // 初始化
            $trackList[$value['tracking_number']] = [
                'api_result_status'  => OrderTrack::API_RESULT_STATUS_WAITING,
                'api_result_content' => ''
            ];

            // 运单状态判断
            $trackStatus = isset($value['status']) ? $value['status'] : '';
            $tempStatus  = OrderTrack::API_RESULT_STATUS_LAUNCHED;

            switch ($trackStatus) {
                case 'delivered':
                    $tempStatus = OrderTrack::API_RESULT_STATUS_DELIVERED;
                break;
                case 'exception':
                case 'expired':
                    $tempStatus = OrderTrack::API_RESULT_STATUS_ERROR;
                break;
                case 'notfound':
                    if (empty($item['lastEvent'])) {
                        $tempStatus = OrderTrack::API_RESULT_STATUS_WAITING;
                    }
                break;
                case 'pending':
                    $returnData['error'] = true;
                    $returnData['msg']   = sprintf('%s:暂时无法查询', $value['tracking_number']);
                break 2;
            }

            $trackList[$value['tracking_number']]['api_result_status'] = $tempStatus;

            // 组装显示数据
            $lastEvent     = [
                'date'    => isset($value['lastUpdateTime']) ? $value['lastUpdateTime'] : '',
                'address' => '',
                'details' => isset($value['lastEvent']) ? $value['lastEvent'] : ''
            ];
            $tempLastEvent = array_filter($lastEvent);  // 判断是否为空

            $tempOriginalEventList    = isset($value['origin_info']['trackinfo']) ? $value['origin_info']['trackinfo'] : array();
            $tempDestinationEventList = isset($value['destination_info']['trackinfo']) ? $value['destination_info']['trackinfo'] : array();
            $originalEventList        = array();
            $destinationEventList     = array();

            if (!empty($tempOriginalEventList)) {
                foreach ($tempOriginalEventList as $k => $v) {
                    $originalEventList[$k]['date']    = isset($v['Date']) ? $v['Date'] : '';
                    $originalEventList[$k]['address'] = isset($v['Details']) ? $v['Details'] : '';
                    $originalEventList[$k]['details'] = isset($v['StatusDescription']) ? $v['StatusDescription'] : '';
                }
            }

            if (!empty($tempDestinationEventList)) {
                foreach ($tempDestinationEventList as $k => $v) {
                    $destinationEventList[$k]['date']    = isset($v['Date']) ? $v['Date'] : '';
                    $destinationEventList[$k]['address'] = isset($v['Details']) ? $v['Details'] : '';
                    $destinationEventList[$k]['details'] = isset($v['StatusDescription']) ? $v['StatusDescription'] : '';
                }
            }

            if (empty($tempLastEvent) && empty($originalEventList) && empty($destinationEventList)) {
                continue;
            }

            $trackList[$value['tracking_number']]['api_result_content'] = array(
                'original_country'       => isset($value['original_country']) ? $value['original_country'] : '',
                'destination_country'    => isset($value['destination_country']) ? $value['destination_country'] : '',
                'last_event'             => $lastEvent,
                'original_event_list'    => $originalEventList,
                'destination_event_list' => $destinationEventList
            );
        }

        $returnData['data'] = $trackList;

        return $returnData;
    }
}
