<?php

namespace App\Classes\Pay\Events;

class SlsLog extends Event
{
    /**
     * Content.
     *
     * @var array
     */
    public $content;
    
    /**
     * RequestId.
     *
     * @var string
     */
    public $requestId;

    /**
     * Message.
     *
     * @var string
     */
    public $message;
    
    /**
     * Level.
     *
     * @var string
     */
    public $level;
    
    /**
     * customize.
     *
     * @var array
     */
    public $customize;

    /**
     * Bootstrap.
     */
    public function __construct(string $driver, string $message, array $content, string $requestId, string $level = 'info', array $customize = [], string $gateway = '')
    {
        $this->content   = $content;
        $this->message   = $message;
        $this->requestId = $requestId;
        $this->level     = $level;
        $this->customize = $customize;

        parent::__construct($driver, $gateway);
    }
}
