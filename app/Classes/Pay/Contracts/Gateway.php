<?php

namespace App\Classes\Pay\Contracts;

use App\Classes\Pay\Exceptions\GatewayException;
use App\Classes\Pay\Exceptions\InvalidArgumentException;
use App\Classes\Pay\Exceptions\InvalidGatewayException;
use App\Classes\Pay\Exceptions\InvalidSignException;
use App\Classes\Supports\Collection;
use App\Classes\Supports\Config;
use App\Classes\Supports\Str;
use App\Jobs\SendNotice;
use App\Jobs\SendSlsLog;
use App\Models\DirectoryCurrency;
use App\Models\DirectoryDictionary;
use App\Models\OrderProduct;
use App\Models\ShellProduct;
use App\Services\UrlService;
use DES3;
use Symfony\Component\HttpFoundation\Response;

/**
 * @method Response   auth(array $config)      预授权交易
 * @method Response   capture(array $config)   请款交易
 */
class Gateway implements GatewayApplicationInterface
{
    /**
     * Const mode_normal.
     */
    const MODE_NORMAL = 'normal';

    /**
     * Const mode_dev.
     */
    const MODE_DEV = 'dev';

    /**
     * payload.
     *
     * @var array
     */
    protected $payload;

    /**
     * gateway.
     *
     * @var string
     */
    protected $gateway;

    /**
     * extends.
     *
     * @var array
     */
    protected $extends;

    /**
     * 类名
     *
     * @var string
     */
    protected $gatewayName;

    /**
     * Bootstrap.
     *
     * @param Config $config
     */
    public function __construct(Config $config)
    {
        $this->payload = [
            'pay_config' => $config->toArray()
        ];
    }

    /**
     * Magic pay.
     *
     * @param string $method
     * @param array $params
     *
     * @return Response|Collection
     * @throws InvalidArgumentException
     * @throws InvalidGatewayException
     * @throws InvalidSignException
     *
     * @throws GatewayException
     */
    public function __call($method, $params)
    {
        if (isset($this->extends[$method])) {
            return $this->makeExtend($method, ...$params);
        }

        return $this->pay($method, ...$params);
    }

    /**
     * Pay an order.
     *
     * @param string $gateway
     * @param array $params
     *
     * @return Response|Collection
     * @throws InvalidGatewayException
     *
     */
    public function pay($gateway, $params = [])
    {
        if (isset($params['card'])) {
            $params['card']['card_number']      = DES3::decrypt($params['card']['card_number']);
            $params['card']['expiration_year']  = DES3::decrypt($params['card']['expiration_year']);
            $params['card']['expiration_month'] = DES3::decrypt($params['card']['expiration_month']);
            $params['card']['cvv']              = DES3::decrypt($params['card']['cvv']);
        }

        // 首次交易产品名黑名单过滤
        if (isset($params['products'])) {
            //针对渠道替换产品
            $params = $this->productReplacement($params, $this->payload['pay_config'] ?? '');

            $params['products'] = $this->filterProduct($params['products'], $params['throw_cnt']);
        }

        $this->payload = array_merge($this->payload, $params);
        $gateway       = get_class($this) . '\\' . Str::studly($gateway) . 'Gateway';

        if (class_exists($gateway)) {
            return $this->makePay($gateway);
        }

        throw new InvalidGatewayException("Pay Gateway [{$gateway}] not exists");
    }

    /**
     * Verify sign.
     *
     * @param array|null $data
     *
     * @param bool $refund
     * @return Collection
     * @throws InvalidSignException
     */
    public function verify($data = null, bool $refund = false): Collection
    {
    }

    /**
     * Query an order.
     *
     * @param string|array $order
     *
     * @param string $type
     * @return Collection
     * @throws GatewayException
     * @throws InvalidSignException
     */
    public function find($order, string $type = 'wap'): Collection
    {
    }

    /**
     * Cancel an order.
     *
     * @param array|string $order
     *
     * @return Collection
     * @throws GatewayException
     * @throws InvalidSignException
     */
    public function cancel($order): Collection
    {
    }

    /**
     * Close an order.
     *
     * @param string|array $order
     *
     * @return Collection
     * @throws GatewayException
     * @throws InvalidSignException
     */
    public function close($order): Collection
    {
    }

    /**
     * Reply success.
     */
    public function success(): Response
    {
    }

    /**
     * Make pay gateway.
     *
     * @param string $gateway
     * @return Response|Collection
     * @throws InvalidGatewayException
     */
    protected function makePay(string $gateway)
    {
        $app = new $gateway();

        if ($app instanceof GatewayInterface) {
            return $app->pay($this->gateway, $this->payload);
        }

        throw new InvalidGatewayException("Pay Gateway [{$gateway}] Must Be An Instance Of GatewayInterface");
    }

    /**
     * 产品名黑名单过滤
     *
     * @param $products
     * @param $throwCnt
     * @return mixed
     */
    protected function filterProduct($products, $throwCnt)
    {
        $tempInfo      = $products;
        $tempThrowInfo = [];

        // 获取多抛提交渠道产品数据
        if ($throwCnt != 0) {
            foreach ($tempInfo as $key => $value) {
                if ($value['type'] == '1') {
                    $tempThrowInfo[] = $value;
                }
            }

            if (!empty($tempThrowInfo)) {
                return $tempThrowInfo;
            }
        }

        // 针对产品名做词汇黑名单过滤
        $blackList = DirectoryDictionary::where('type', '=', '词汇黑名单')->get()->pluck('name', 'id')->toArray();

        foreach ($tempInfo as $key => $value) {
            $tempInfo[$key]['type'] = '1'; // type = 1 表示渠道产品

            if (!empty($blackList)) {
                foreach ($blackList as $k => $v) {
                    if (stripos($value['name'], $v) || stripos($value['name'], $v) == 0) {
                        $tempProductName        = str_ireplace($v, '', $tempInfo[$key]['name']);
                        $tempArr                = array_filter(explode(' ', $tempProductName));
                        $tempName               = implode(' ', $tempArr);
                        $tempInfo[$key]['name'] = !empty($tempName) ? $tempName : 'clothes';
                    }
                }
            }
        }

        if (!empty($tempInfo)) {
            // 保存渠道产品数据
            $orderProduct = new OrderProduct();
            $orderProduct->adds($tempInfo);
        }

        return $tempInfo;
    }

    /**
     * 针对渠道替换产品
     * @param $params
     * @return array
     */
    public function productReplacement($params, $channelConfig): array
    {
        $channelSupplier = $channelConfig['channel_supplier'] ?? '';

        if (isset($channelSupplier) && isset($params['url_name']) && isset($params['products'])) {
            //壳站默认价格梯位
            $basePriceList = [5999, 4999, 4604, 4394, 4155, 3999, 3946, 3827, 3694, 2999, 2898, 2699, 2574, 2463, 1999, 1742, 1684, 1598, 1497, 999];

            if (in_array($channelSupplier, ['pp', 'll', 'spray', 'novatti', 'ppae', 'yq', 'brad', 'commute', 'code', 'fdc', 'source', 'keep', 'kf', 'gee', 'ecard', 'hai', 'code', 'tender', 'linking', 'inno', 'innolink'])) {
                $priceList = json_decode($channelConfig['productList'] ?? '', true) ?? $basePriceList;
                $params    = $this->getProducts($params, $priceList);
            }
        }

        return $params;
    }

    /**
     * 获取替换产品
     * @param $params
     * @param $priceList
     * @return array
     */
    public function getProducts($params, $priceList): array
    {
        $shellUrl = $this->payload['pay_config']['shellUrl'] ?? '';

        if (empty($shellUrl) || (UrlService::_getMainUrlName($shellUrl) == UrlService::_getMainUrlName($params['url_name']) && $params['merchant_id'] != '165958156391322')) {
            return $params;
        }

        rsort($priceList);
        // 1. 计算金额数组
        $prices   = [];
        $currency = $params['payment_order']['currency'];
        $amount   = amount_format($params['payment_order']['amount'] * 100, 0);
        $minPrice = min($priceList);

        if ($currency != 'USD') {
            // 非USD币种汇率转换
            $currencyArr = DirectoryCurrency::whereIn('code', [$currency, 'USD'])->get()->pluck('rate', 'code');
            $amount      = Support::amount_format(($params['payment_order']['amount'] * ($currencyArr['USD'] / $currencyArr[$currency])) * 100, 0);
        }

        if ($amount > $minPrice) {
            $getKey = 0;
            foreach ($priceList as $key => $price) {
                if ($price < $amount) {
                    $getKey = $key;
                    break;
                }
            }

            $priceList       = array_slice($priceList, $getKey);
            $actualPriceList = count($priceList) > 5 ? array_rand(array_flip($priceList), 5) : $priceList;
            array_pop($actualPriceList);
            array_push($actualPriceList, $minPrice);

            while (1) {
                $index = array_rand($actualPriceList);
                $price = $actualPriceList[$index];
                $temp  = $amount - $price;

                if ($temp > 0) {
                    $amount   = $temp;
                    $prices[] = amount_format($price / 100);

                    continue;
                }

                if ($amount <= min($priceList)) {
                    break;
                }
            }
        } else {
            $prices[] = amount_format(min($priceList) / 100);
        }

        $shellProductList = ShellProduct::select(['sku', 'name', 'url', 'price', 'attribute'])->where('shell_url', $shellUrl)->whereIn('price', $prices)->get()->groupBy('price')->toArray();
        $tempProductList  = [];

        foreach ($prices as $value) {
            $data = $shellProductList[$value] ?? [];

            if (empty($data)) {
                // 添加系统告警
                logger()->channel('intercept')->info(
                    sprintf('获取 %s 壳站金额为 %s 的 sku数据失败,详细返回请查看网关日志', $shellUrl, $value),
                    ['order_id' => $params['order_id']]
                );
                dispatch(new SendSlsLog(
                    ['message' => sprintf('获取 %s 壳站金额为 %s 的 sku数据失败,详细返回请查看网关日志', $shellUrl, $value)],
                    ['order_id' => $params['order_id']],
                    'info',
                    'intercept'
                ));

                $data = [
                    'level'             => 1,
                    'contents'          => '[壳站产品获取失败]' . PHP_EOL .
                    '壳站地址:' . $shellUrl . PHP_EOL .
                        '失败金额:' . $value . PHP_EOL .
                        '来源:' . config('app.url'),
                    'notice_user_roles' => 'Technical Support',
                    'type'              => 3,
                    'status'            => 2,
                ];
                dispatch(new SendNotice($data, 5));
                break;
            }

            $product               = $data[array_rand($data)];
            $product['order_id']   = $params['order_id'];
            $product['type']       = OrderProduct::TYPE_MERCHANT;
            $product['is_gift']    = 0;
            $product['is_virtual'] = 0;
            $product['created_at'] = date('Y-m-d H:i:s');
            $product['updated_at'] = date('Y-m-d H:i:s');

            if (!isset($tempProductList[$value])) {
                $tempProductList[$value]        = $product;
                $tempProductList[$value]['qty'] = 1;
            } else {
                $tempProductList[$value]['qty'] += 1;
            }
        }

        if (!empty($tempProductList)) {
            //抛投到需要进行产品替换的渠道需要清空
            if ($params['throw_cnt'] != 0) {
                OrderProduct::where('order_id', $params['order_id'])->where('type', OrderProduct::TYPE_BANK)->delete();
            }

            $params['products'] = $tempProductList;
        }

        return $params;
    }
}
