<?php

namespace App\Classes\Pay\Gateways;

use App\Classes\Pay\Contracts\Gateway;
use App\Classes\Pay\Gateways\Innolink\Support;
use App\Classes\Supports\Config;

class Innolink extends Gateway
{
    protected $gatewayName = 'Innolink';

    /**
     * Const url.
     */
    const URL = [
        self::MODE_NORMAL => 'https://gateway.innolink.vip',
        self::MODE_DEV    => 'https://gateway.innolink.vip',
    ];

    public function __construct(Config $config)
    {
        $this->gateway = Support::create($config)->getBaseUri();
        parent::__construct($config);
    }
}
