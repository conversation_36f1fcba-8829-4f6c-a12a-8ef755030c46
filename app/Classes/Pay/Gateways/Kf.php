<?php

namespace App\Classes\Pay\Gateways;

use App\Classes\Pay\Contracts\Gateway;
use App\Classes\Pay\Gateways\Kf\Support;
use App\Classes\Supports\Config;

class Kf extends Gateway
{
    protected $gatewayName = 'Kf';

    /**
     * Const url.
     */
    const URL = [
        self::MODE_NORMAL => 'https://api.nsdpayhub.com/border/apis',
        self::MODE_DEV    => 'https://api.nsdpayhub.com/border/apis',
    ];

    public function __construct(Config $config)
    {
        $this->gateway = Support::create($config)->getBaseUri();
        parent::__construct($config);
    }
}
