<?php

namespace App\Classes\Pay\Gateways\Austpay;

use App\Classes\Supports\Collection;
use Illuminate\Support\Facades\Cache;
use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Austpay;

class AuthGateway extends Gateway
{
	/**
	 * 货币代码
	 *
	 * @var array
	 */
	protected $_countryData = array(
        'AD' => 'AND',
        'AE' => 'ARE',
        'AF' => 'AFG',
        'AG' => 'ATG',
        'AI' => 'AIA',
        'AL' => 'ALB',
        'AM' => 'ARM',
        'AO' => 'AGO',
        'AQ' => 'ATA',
        'AR' => 'ARG',
        'AS' => 'ASM',
        'AT' => 'AUT',
        'AU' => 'AUS',
        'AW' => 'ABW',
        'AX' => 'ALA',
        'AZ' => 'AZE',
        'BA' => 'BIH',
        'BB' => 'BRB',
        'BD' => 'BGD',
        'BE' => 'BEL',
        'BF' => 'BFA',
        'BG' => 'BGR',
        'BH' => 'BHR',
        'BI' => 'BDI',
        'BJ' => 'BEN',
        'BL' => 'BLM',
        'BM' => 'BMU',
        'BN' => 'BRN',
        'BO' => 'BOL',
        'BQ' => 'BES',
        'BR' => 'BRA',
        'BS' => 'BHS',
        'BT' => 'BTN',
        'BV' => 'BVT',
        'BW' => 'BWA',
        'BY' => 'BLR',
        'BZ' => 'BLZ',
        'CA' => 'CAN',
        'CC' => 'CCK',
        'CF' => 'CAF',
        'CH' => 'CHE',
        'CL' => 'CHL',
        'CM' => 'CMR',
        'CO' => 'COL',
        'CR' => 'CRI',
        'CU' => 'CUB',
        'CV' => 'CPV',
        'CX' => 'CXR',
        'CY' => 'CYP',
        'CZ' => 'CZE',
        'DE' => 'DEU',
        'DJ' => 'DJI',
        'DK' => 'DNK',
        'DM' => 'DMA',
        'DO' => 'DOM',
        'DZ' => 'DZA',
        'EC' => 'ECU',
        'EE' => 'EST',
        'EG' => 'EGY',
        'EH' => 'ESH',
        'ER' => 'ERI',
        'ES' => 'ESP',
        'FI' => 'FIN',
        'FJ' => 'FJI',
        'FK' => 'FLK',
        'FM' => 'FSM',
        'FO' => 'FRO',
        'FR' => 'FRA',
        'GA' => 'GAB',
        'GD' => 'GRD',
        'GE' => 'GEO',
        'GF' => 'GUF',
        'GH' => 'GHA',
        'GI' => 'GIB',
        'GL' => 'GRL',
        'GN' => 'GIN',
        'GP' => 'GLP',
        'GQ' => 'GNQ',
        'GR' => 'GRC',
        'GS' => 'SGS',
        'GT' => 'GTM',
        'GU' => 'GUM',
        'GW' => 'GNB',
        'GY' => 'GUY',
        'HK' => 'HKG',
        'HM' => 'HMD',
        'HN' => 'HND',
        'HR' => 'HRV',
        'HT' => 'HTI',
        'HU' => 'HUN',
        'ID' => 'IDN',
        'IE' => 'IRL',
        'IL' => 'ISR',
        'IM' => 'IMN',
        'IN' => 'IND',
        'IO' => 'IOT',
        'IQ' => 'IRQ',
        'IR' => 'IRN',
        'IS' => 'ISL',
        'IT' => 'ITA',
        'JE' => 'JEY',
        'JM' => 'JAM',
        'JO' => 'JOR',
        'JP' => 'JPN',
        'KH' => 'KHM',
        'KI' => 'KIR',
        'KM' => 'COM',
        'KW' => 'KWT',
        'KY' => 'CYM',
        'LB' => 'LBN',
        'LI' => 'LIE',
        'LK' => 'LKA',
        'LR' => 'LBR',
        'LS' => 'LSO',
        'LT' => 'LTU',
        'LU' => 'LUX',
        'LV' => 'LVA',
        'LY' => 'LBY',
        'MA' => 'MAR',
        'MC' => 'MCO',
        'MD' => 'MDA',
        'ME' => 'MNE',
        'MF' => 'MAF',
        'MG' => 'MDG',
        'MH' => 'MHL',
        'MK' => 'MKD',
        'ML' => 'MLI',
        'MM' => 'MMR',
        'MO' => 'MAC',
        'MQ' => 'MTQ',
        'MR' => 'MRT',
        'MS' => 'MSR',
        'MT' => 'MLT',
        'MV' => 'MDV',
        'MW' => 'MWI',
        'MX' => 'MEX',
        'MY' => 'MYS',
        'NA' => 'NAM',
        'NE' => 'NER',
        'NF' => 'NFK',
        'NG' => 'NGA',
        'NI' => 'NIC',
        'NL' => 'NLD',
        'NO' => 'NOR',
        'NP' => 'NPL',
        'NR' => 'NRU',
        'OM' => 'OMN',
        'PA' => 'PAN',
        'PE' => 'PER',
        'PF' => 'PYF',
        'PG' => 'PNG',
        'PH' => 'PHL',
        'PK' => 'PAK',
        'PL' => 'POL',
        'PN' => 'PCN',
        'PR' => 'PRI',
        'PS' => 'PSE',
        'PW' => 'PLW',
        'PY' => 'PRY',
        'QA' => 'QAT',
        'RE' => 'REU',
        'RO' => 'ROU',
        'RS' => 'SRB',
        'RU' => 'RUS',
        'RW' => 'RWA',
        'SB' => 'SLB',
        'SC' => 'SYC',
        'SD' => 'SDN',
        'SE' => 'SWE',
        'SG' => 'SGP',
        'SI' => 'SVN',
        'SJ' => 'SJM',
        'SK' => 'SVK',
        'SL' => 'SLE',
        'SM' => 'SMR',
        'SN' => 'SEN',
        'SO' => 'SOM',
        'SR' => 'SUR',
        'SS' => 'SSD',
        'ST' => 'STP',
        'SV' => 'SLV',
        'SY' => 'SYR',
        'SZ' => 'SWZ',
        'TC' => 'TCA',
        'TD' => 'TCD',
        'TG' => 'TGO',
        'TH' => 'THA',
        'TK' => 'TKL',
        'TL' => 'TLS',
        'TN' => 'TUN',
        'TO' => 'TON',
        'TR' => 'TUR',
        'TV' => 'TUV',
        'TZ' => 'TZA',
        'UA' => 'UKR',
        'UG' => 'UGA',
        'US' => 'USA',
        'UY' => 'URY',
        'VA' => 'VAT',
        'VE' => 'VEN',
        'VG' => 'VGB',
        'VI' => 'VIR',
        'VN' => 'VNM',
        'WF' => 'WLF',
        'WS' => 'WSM',
        'YE' => 'YEM',
        'YT' => 'MYT',
        'ZA' => 'ZAF',
        'ZM' => 'ZMB',
        'ZW' => 'ZWE',
        'CN' => 'CHN',
        'CG' => 'COG',
        'CD' => 'COD',
        'MZ' => 'MOZ',
        'GG' => 'GGY',
        'GM' => 'GMB',
        'MP' => 'MNP',
        'ET' => 'ETH',
        'NC' => 'NCL',
        'VU' => 'VUT',
        'TF' => 'ATF',
        'NU' => 'NIU',
        'UM' => 'UMI',
        'CK' => 'COK',
        'GB' => 'GBR',
        'TT' => 'TTO',
        'VC' => 'VCT',
        'TW' => 'TWN',
        'NZ' => 'NZL',
        'SA' => 'SAU',
        'LA' => 'LAO',
        'KP' => 'PRK',
        'KR' => 'KOR',
        'PT' => 'PRT',
        'KG' => 'KGZ',
        'KZ' => 'KAZ',
        'TJ' => 'TJK',
        'TM' => 'TKM',
        'UZ' => 'UZB',
        'KN' => 'KNA',
        'PM' => 'SPM',
        'SH' => 'SHN',
        'LC' => 'LCA',
        'MU' => 'MUS',
        'CI' => 'CIV',
        'KE' => 'KEN',
        'MN' => 'MNG',
	);

    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     * @throws \App\Classes\Pay\Exceptions\GatewayException
     * @throws \App\Classes\Pay\Exceptions\InvalidSignException
     */
    public function pay($endpoint, array $payload): Collection
    {
	    $payConfig = $payload['pay_config'];
	    $address   = $payload['address'];
	    $card      = $payload['card'];

	    // 缓存key
	    $mark    = MD5($payload['order_id'] . 'austpay');
	    $markArr = [
		    'order_id' => $payload['order_id'],
		    'class'    => 'Austpay',
	    ];
	    Cache::add($mark, $markArr, 24*60*60);

	    $data = array(
            'cardid'        => $card['card_number'],
            'month'         => $card['expiration_month'],
            'year'          => '20' . $card['expiration_year'],
            'cvv'           => $card['cvv'],
            'Amount'        => Support::amount_format($payload['payment_order']['amount']),
            'currency_code' => $payload['payment_order']['currency'],
            'merchantid'    => $payConfig['merchantId'],
            'siteid'        => $payConfig['siteId'],
            'order_id'      => $payload['order_id'],
            'name'          => $address['bill_name'],
            'firstname'     => $address['bill_first_name'],
            'lastname'      => $address['bill_last_name'],
            'address'       => $address['bill_address'],
            'city'          => $address['bill_city'],
            'state'         => $address['bill_state'],
            'country'       => $this->_countryData[$address['bill_country_isoa2']] ?? 'AUS',
            'postcode'      => $address['bill_postcode'],
            'phone'         => $address['bill_phone'],
            'email'         => $address['bill_email'],
//			'product'       => '',
            'customer_ip'   => $address['ip'],
            'version'       => '3.0',
            'secure_string' => '',
            'rebilling'     => 'yes',
	    );

        $transitIp = '';
        if (!empty($payConfig['transitIp'])) {
            // 有中转网址
            $transitIp = $payConfig['transitIp'] . '?' . Support::urlPayment($payConfig);
        }

	    // 签名
        $mmString              = md5(hash("sha256", $data['order_id'] . $data['Amount'] . $data['merchantid'] . $data['siteid']
            . $payConfig['secretKey'] . $data['currency_code'] . $data['cardid'] . $data['year'] . $data['month'] . $data['cvv']));
        $data['secure_string'] = md5(hash("sha256", $mmString));

	    // 卡信息加密
        $iv             = '';
        $data['cardid'] = $this->encryptECB($data['cardid'], $payConfig['secretKey'], $iv);
        $data['month']  = $this->encryptECB($data['month'], $payConfig['secretKey'], $iv);
        $data['year']   = $this->encryptECB($data['year'], $payConfig['secretKey'], $iv);
        $data['cvv']    = $this->encryptECB($data['cvv'], $payConfig['secretKey'], $iv);

        $post = array(
            'data'       => $data,
            'header'     => [],
            'transit_ip' => $transitIp
        );

        return Austpay::authParser(Support::requestApi('/eng/ccgate/billing/acquirer/securepay_new.php', $post, $payload['order_id']));
    }

    protected function encryptECB($value, $key, $iv)
    {
	    $data = openssl_encrypt($value, 'aes-128-ecb', $key, OPENSSL_RAW_DATA, $iv);
	    return strtoupper(bin2hex($data));
    }
}
