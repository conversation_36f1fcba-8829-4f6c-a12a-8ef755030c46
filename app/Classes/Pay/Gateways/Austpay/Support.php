<?php

namespace App\Classes\Pay\Gateways\Austpay;

use App\Classes\Pay\Exceptions\GatewayException;
use App\Classes\Pay\Exceptions\InvalidSignException;
use App\Classes\Pay\Gateways\Austpay;
use App\Classes\Supports\Collection;
use App\Classes\Supports\Config;
use App\Classes\Pay\Contracts\Support as BaseSupport;
use App\Classes\Pay\Events;

/**
 * @property array http http options
 * @property string mode current mode
 * @property array log log options
 * @property string sign_key
 */
class Support
{
    use BaseSupport;

    /**
     * Instance.
     *
     * @var Support
     */
    private static $instance;

    /**
     * Bootstrap.
     *
     * @param Config $config
     */
    public function __construct(Config $config)
    {
        self::$gatewayName = 'Austpay';
        $this->baseUri = Austpay::URL[$config->get('mode', Austpay::MODE_NORMAL)];
        $this->config = $config;
        $this->setHttpOptions();
    }

	/**
	 * Get API result.
	 *
	 * @param string $endpoint
	 * @param array $data
	 * @param string $requestId
	 * @return Collection
	 * @throws GatewayException
	 * @throws InvalidSignException
	 */
	public static function requestApi(string $endpoint, array $data, string $requestId = '' ): Collection
	{
        $tempUrl = !empty($data['transit_ip']) ? $data['transit_ip'] : self::$instance->getBaseUri() . $endpoint;
		Events::dispatch(new Events\ApiRequesting(self::$gatewayName, '', $tempUrl, $data));
        Events::dispatch(new Events\SlsLog(
            self::$gatewayName,
            'Requesting To Api',
            [self::$instance->getBaseUri() . $endpoint, $data],
            $requestId
        ));

        try {
            if (!empty($data['transit_ip'])) {
                // 转发到中转
                $data['embMethod'] = 'post';
                $data['doUrl']     = self::$instance->getBaseUri() . $endpoint;

                if (!empty($data['header'])) {
                    foreach ($data['header'] as $key => $vo) {
                        $header[] = $key . ':' . $vo;
                    }

                    $data['header'] = $header;
                }

                $result = self::$instance->post($data['transit_ip'], $data);
            }else{
                $result = self::$instance->post($endpoint, $data['data']);
            }

            $result = is_string($result) ? json_decode($result, true) ?? ['declined_reason' => $result] : $result;
        } catch (\Exception $e) {
            //返回数据格式特殊处理
            $content = $e->getMessage();
            if ($e instanceof \GuzzleHttp\Exception\RequestException) {
                $content = $e->getResponse()->getBody()->getContents();
            }

            $result = self::returnDateHandle($content, $requestId);
        }

		Events::dispatch(new Events\ApiRequested(self::$gatewayName, '', self::$instance->getBaseUri() . $endpoint, $result));
        Events::dispatch(new Events\SlsLog(
            self::$gatewayName,
            'Result Of Api',
            $result,
            $requestId
        ));

		return self::processingApiResult($result);
	}

    /**
     * processingApiResult.
     *
     * @param $result
     *
     * @return Collection
     * @throws GatewayException
     * @throws InvalidSignException
     */
	public static function processingApiResult($result): Collection
    {
        if (!empty($result)) {
            $result['pay_config'] = self::$instance->config;
        }

        if (!isset($result['secure_string'])) {
            return new Collection($result);
        }

        if (self::verifySign($result)) {
            return new Collection($result);
        }

        Events::dispatch(new Events\SignFailed(self::$gatewayName, '', $result));

        throw new InvalidSignException(self::$gatewayName . ' Sign Verify FAILED', $result);
    }

    /**
     * Verify sign.
     *
     * @param array $data
     * @return bool
     */
    public static function verifySign(array $data): bool
    {
        $sign    = $data['secure_string'];
        $signStr = '';

	    // 组装签名
        if (isset($data['payment_result'])) { // 交易
            $signStr = $data['payment_result'] . $data['austpay_id'] . $data['order_id'] . $data['order_amount']
                . $data['merchantid'] . self::$instance->secretKey . $data['siteid'] . $data['return_url']
                . $data['recure_id'] . $data['currency_code'] . $data['billing_info'] . $data['version'];
            if (isset($data['gotourl']) && !empty($data['gotourl'])) {
                $signStr .= $data['gotourl'];
            }
        } elseif (isset($data['refund_status'])) { // 退款
            $signStr =$data['refund_status'] . $data['refund_amount'] . $data['austpay_id'] . $data['order_id']
                . $data['merchantid'] . $data['siteid'] . $data['order_amount'] .self::$instance->secretKey . $data['version'];
        } elseif (isset($data['status'])) { // 退款
            $signStr = $data['status'] . $data['austpay_id'] . $data['order_id'] . $data['merchantid'] . $data['siteid']
                . self::$instance->secretKey . $data['version'];
        }

	    $signMsg = md5(hash("sha256", $signStr));

        return $signMsg === $sign;
    }
}
