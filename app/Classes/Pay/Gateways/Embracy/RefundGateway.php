<?php

namespace App\Classes\Pay\Gateways\Embracy;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Embracy;
use Illuminate\Support\Facades\Cache;

class RefundGateway extends Gateway
{
	/**
	 * Pay an order.
	 *
	 * @param string $endpoint
	 *
	 * @param array $payload
	 * @return Collection
	 */
	public function pay($endpoint, array $payload)
	{
        $payConfig = $payload['pay_config'];

        // 缓存key
        $mark    = MD5($payload['payment_order']['payment_order_id'] . 'embracy');
        $markArr = [
            'order_id' => $payload['order_id'],
            'class'    => 'Embracy',
        ];
        Cache::add($mark, $markArr, 7 * 24 * 60 * 60);

        $data = [
            'merchantId'      => $payConfig['merchantId'],
            'businessId'      => $payConfig['businessId'],
            'orderId'         => $payload['payment_order']['payment_order_id'],
            'transactionType' => 'refund',
            'currency'        => $payload['payment_refund']['currency'],
            'amount'          => Support::amount_format($payload['payment_refund']['amount']),
        ];

        // 组装请求头
        $header = [
            "Authorization" => 'Bearer ' . $payConfig['Bearer'],
        ];

        $post = [
            'data'   => $data,
            'header' => $header,
        ];

        return Embracy::refundParser(Support::requestApi('/api/v1/orders/refund', $post, '', 'post', $payload['refund_id']));
    }
}
