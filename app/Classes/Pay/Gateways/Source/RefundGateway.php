<?php

namespace App\Classes\Pay\Gateways\Source;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Parser\Source;
use Illuminate\Support\Facades\Cache;

class RefundGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload)
    {
        $payConfig = $payload['pay_config'];
        $payRefund = $payload['payment_refund'];

        // 缓存key
        $mark    = MD5($payRefund['refund_id'] . 'source');
        $markArr = [
            'order_id'  => $payload['order_id'],
            'refund_id' => $payRefund['refund_id'],
            'class'     => 'Source',
        ];

        Cache::add($mark, $markArr, 24 * 60 * 60);

        $data = [
            'mchNo'        => $payConfig['mchNo'],
            'appId'        => $payConfig['appId'],
            'payOrderId'   => $payload['payment_order']['payment_order_id'],
            'mchRefundNo'  => $payRefund['refund_id'],
            'refundAmount' => Support::amount_format($payRefund['amount'] * 100, 0),   //支付金额 （单位：分)
            'currency'     => $payRefund['currency'],
            'refundReason' => 'refund',
            'notifyUrl'    => route('api.v1.notify', ['mark' => $mark], true),
            'reqTime'      => Support::millisecond(),
            'version'      => '1.0',
            'signType'     => 'MD5',
        ];

        //生成签名
        $sign         = Support::signature($data, $payConfig['key']);
        $data['sign'] = $sign;

        //组装请求头
        $header = [
            'Content-Type'  => 'application/json'
        ];

        $post = [
            'data'   => $data,
            'header' => $header
        ];

        return Source::refundParser(Support::requestApi('/api/refund/refundOrder', $post, $payload['refund_id']));
    }
}
