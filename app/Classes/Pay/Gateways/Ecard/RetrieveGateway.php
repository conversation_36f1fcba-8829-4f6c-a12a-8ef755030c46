<?php

namespace App\Classes\Pay\Gateways\Ecard;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Parser\Ecard;

class RetrieveGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig = $payload['pay_config'];

        // 组装请求头
        $header = [
            'Content-Type' => 'application/json',
            'Accept'       => 'application/json',
            'x-api-key'    => $payConfig['key'],
            'x-app-id'     => $payConfig['merchantNo'],
        ];

        // 中转地址
        $transitIp = '';
        if (!empty($payConfig['transitIp'])) {
            // 有中转网址
            $transitIp = $payConfig['transitIp'] . '?' . Support::urlPayment($payConfig);
        }

        $post = [
            'header'    => $header,
            'transitIp' => $transitIp
        ];

        return Ecard::retrieveParser(Support::requestApi("/openapi/enquiry/merchant/{$payConfig['merchantNo']}/payment/?clientRequestNo={$payload['order_id']}&transType=sale", $post, 'get', $payload['order_id']));
    }
}
