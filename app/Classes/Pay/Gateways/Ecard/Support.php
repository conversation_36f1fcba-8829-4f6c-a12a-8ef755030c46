<?php

namespace App\Classes\Pay\Gateways\Ecard;

use App\Classes\Pay\Gateways\Ecard;
use App\Classes\Supports\Config;
use App\Classes\Pay\Contracts\Support as BaseSupport;
use App\Classes\Pay\Events;
use App\Classes\Supports\Collection;

/**
 * @property array http  http options
 * @property string mode current mode
 * @property array log   log options
 * @property string sign_key
 */
class Support
{
    use BaseSupport;

    /**
     * Instance.
     *
     * @var Support
     */
    private static $instance;

    /**
     * Bootstrap.
     *
     * @param Config $config
     */
    public function __construct(Config $config)
    {
        self::$gatewayName = 'Ecard';
        $this->baseUri     = Ecard::URL[$config->get('mode', Ecard::MODE_NORMAL)];
        $this->config      = $config;
        $this->setHttpOptions();
    }

    /**
     * Get API result.
     *
     * @param string $endpoint
     * @param array $data
     * @param string $requestId
     * @param array $customize
     * @return Collection
     */
    public static function requestApi(string $endpoint, array $data, string $method, string $requestId = ''): Collection
    {
        // 设置中转请求渠道地址
        $tempUrl = !empty($data['transitIp']) ? $data['transitIp'] : self::$instance->getBaseUri() . $endpoint;
        $logData = Support::handleLogData($data);
        Events::dispatch(new Events\ApiRequesting(self::$gatewayName, '', $tempUrl, $logData));
        Events::dispatch(new Events\SlsLog(
            self::$gatewayName,
            'Requesting To Api',
            [$tempUrl, $logData],
            $requestId,
            'info'
        ));

        $result = [];
        try {
            if (!empty($data['transitIp'])) {
                // 转发到中转
                $methodArr = [
                    'get'  => 'curlGet',
                    'post' => 'curlPost',
                ];

                $data['embMethod'] = $methodArr[$method];
                $data['doUrl']     = self::$instance->getBaseUri() . $endpoint;

                if (!empty($data['header'])) {
                    foreach ($data['header'] as $key => $vo) {
                        $header[] = $key . ':' . $vo;
                    }

                    $data['header'] = $header;
                }

                $result = self::$instance->post($tempUrl, $data);

                if (gettype($result) === 'string') {
                    $result = json_decode($result, true) ?? [];
                }
            } else {
                $res    = $method == 'get' ? $data['header'] : ['json' => $data['data'], 'headers' => $data['header']];
                $result = self::$instance->$method($tempUrl, [], $res);
            }
        } catch (\Exception $e) {
            //返回数据格式特殊处理
            $content = $e->getMessage();
            if ($e instanceof \GuzzleHttp\Exception\RequestException) {
                $content = $e->getResponse()->getBody()->getContents();
            }

            $result = self::returnDateHandle($content, $requestId);
        }

        Events::dispatch(new Events\ApiRequested(self::$gatewayName, '', $tempUrl, $result));
        Events::dispatch(new Events\SlsLog(
            self::$gatewayName,
            'Result Of Api',
            $result,
            $requestId
        ));

        return self::processingApiResult($result);
    }

    /**
     * 处理日志数据
     *
     * @param array $data
     * @return array
     */
    public static function handleLogData($data)
    {
        $card = $data['data']['paymentAccount'] ?? [];
        if (isset($card['cardNo'], $card['cardValidDate'], $card['cvv2'])) {
            $card                  = &$data['data']['paymentAccount'];
            $card['cardValidDate'] = get_mark_data($card['cardValidDate']);
            $card['cvv2']          = get_mark_data($card['cvv2']);
            $card['cardNo']        = get_markcard($card['cardNo']);
        }

        return $data;
    }
}
