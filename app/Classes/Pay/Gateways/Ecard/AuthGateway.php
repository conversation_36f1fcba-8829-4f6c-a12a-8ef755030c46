<?php

namespace App\Classes\Pay\Gateways\Ecard;

use App\Classes\Supports\Collection;
use Illuminate\Support\Facades\Cache;
use App\Classes\Pay\Parser\Ecard;

class AuthGateway extends Gateway
{
     /**
     * 卡种数据
     *
     * @var array
     */
    protected $_cardData = [
        'V' => 'visa',
        'M' => 'mastercard',
        'J' => 'jcb',
        'A' => 'amex',
        'D' => 'diners',
    ];

    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig   = $payload['pay_config'];
        $payOrder    = $payload['payment_order'];
        $payCard     = $payload['card'];
        $payAddress  = $payload['address'];

        // 缓存key
        $mark    = MD5($payOrder['order_id'] . 'eCard');
        $markArr = [
            'order_id' => $payOrder['order_id'],
            'class'    => 'Ecard',
        ];

        Cache::add($mark, $markArr, 24 * 60 * 60);

        $data = [
            'transType'         => 'sale',
            'merchantNo'        => $payConfig['merchantNo'],
            'clientRequestNo'   => $payload['order_id'],
            'clientOrderNo'     => $payload['order_id'],
            'requestTime'       => now()->toAtomString(),
            'amount'            => Support::amount_format($payload['payment_order']['amount'], 2),
            'currency'          => $payOrder['currency'],
            'dynamicDescriptor' => $payConfig['cardBill'] ?? '-', // 卡账单
            'paymentAccount'    => [
                'payType'        => $this->_cardData[$payCard['cc_type']] ?? $payCard['cc_type'],
                'cardHolderName' => (strlen($payAddress['bill_name']) > 24) ? mb_strcut($payAddress['bill_name'], 0, 24) : $payAddress['bill_name'],
                'cardNo'         => $payCard['card_number'],
                'cardValidDate'  => $payCard['expiration_month'] . $payCard['expiration_year'],
                'cvv2'           => $payCard['cvv'],
            ],
            'billing' => [
                'street1'    => (strlen($payAddress['bill_address']) > 50) ? mb_strcut($payAddress['bill_address'], 0, 50) : $payAddress['bill_address'],
                'city'       => $payAddress['bill_city'],
                'country'    => $payAddress['bill_country_isoa2'],
                'postalCode' => $payAddress['bill_postcode']
            ],
            'shipping' => [
                'street1'    => (strlen($payAddress['ship_address']) > 50) ? mb_strcut($payAddress['ship_address'], 0, 50) : $payAddress['ship_address'],
                'city'       => $payAddress['ship_city'],
                'country'    => $payAddress['ship_country_isoa2'],
                'postalCode' => $payAddress['ship_postcode']
            ],
        ];

        if (isset($payConfig['subMerchantId']) && $payConfig['subMerchantName']) {
            $data['subMerchant'] = [
                'id'   => $payConfig['subMerchantId'],
                'name' => $payConfig['subMerchantName']
            ];
        }

        // 组装请求头
        $header = [
            'Content-Type' => 'application/json',
            'Accept'       => 'application/json',
            'x-api-key'    => $payConfig['key'],
            'x-app-id'     => $payConfig['merchantNo'],
        ];

        // 中转地址
        $transitIp = '';
        if (!empty($payConfig['transitIp'])) {
            // 有中转网址
            $transitIp = $payConfig['transitIp'] . '?' . Support::urlPayment($payConfig);
        }

        $post = [
            'data'       => $data,
            'header'     => $header,
            'transitIp'  => $transitIp
        ];

        return Ecard::authParser(Support::requestApi('/openapi/payment/', $post, 'post', $payload['order_id']));
    }
}