<?php

namespace App\Classes\Pay\Gateways\Ecard;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Parser\Ecard;
use Illuminate\Support\Facades\Cache;

class RefundGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload)
    {
        $payConfig = $payload['pay_config'];
        $payRefund = $payload['payment_refund'];

        // 缓存key
        $mark    = MD5($payRefund['refund_id'] . 'eCard');
        $markArr = [
            'order_id'  => $payload['order_id'],
            'refund_id' => $payRefund['refund_id'],
            'class'     => 'Ecard',
        ];

        Cache::add($mark, $markArr, 7 * 24 * 60 * 60);

        // 组装退款数据
        $data = [
            'transType'                 => 'refund',
            'merchantNo'                => $payConfig['merchantNo'],
            'clientRequestNo'           => $payRefund['refund_id'],
            'requestTime'               => now()->toAtomString(),
            'referencedClientRequestNo' => $payload['order_id'],
            'amount'                    => Support::amount_format($payRefund['amount'], 2), // 退款金额
            'currency'                  => $payRefund['currency']
        ];

        // 组装请求头
        $header = [
            'Content-Type' => 'application/json',
            'Accept'       => 'application/json',
            'x-api-key'    => $payConfig['key'],
            'x-app-id'     => $payConfig['merchantNo'],
        ];

        // 中转地址
        $transitIp = '';
        if (!empty($payConfig['transitIp'])) {
            // 有中转网址
            $transitIp = $payConfig['transitIp'] . '?' . Support::urlPayment($payConfig);
        }

        $post = [
            'data'      => $data,
            'header'    => $header,
            'transitIp' => $transitIp
        ];

        return Ecard::refundParser(Support::requestApi('/openapi/payment/', $post, 'post', $payload['refund_id']));
    }
}
