<?php

namespace App\Classes\Pay\Gateways\Booty;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Booty;

class RefundGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload)
    {
        $payConfig = $payload['pay_config'];

        // 组装支付数据
        $amount = Support::amount_format($payload['payment_refund']['amount']);
 
        $data = [
            'transaction' => [
                'uuid'             => Support::getUuid(),
                'transaction_type' => 'refund',
                'origin_brn'       => $payload['payment_order']['payment_order_id'],
                'amount'           => $amount,
                'currency'         => $payload['payment_refund']['currency'],
            ]
        ];

        // 组装请求头
        $header = array(
            'X-USER-ACCESS-TOKEN' => $payConfig['access_token'],
            'Content-Type'        => 'application/json; charset=UTF-8',
        );

        $post = array(
            'data'   => $data,
            'header' => $header,
        );

        return Booty::refundParser(Support::requestApi('/gateway/v3/stores/' . $payConfig['store_id'] . '/payment/refund', $post, 'post', $payload['refund_id']));
    }
}
