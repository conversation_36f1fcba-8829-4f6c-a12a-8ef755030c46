<?php

namespace App\Classes\Pay\Gateways;

use App\Classes\Pay\Contracts\Gateway;
use App\Classes\Pay\Gateways\Inno\Support;
use App\Classes\Supports\Config;

class Inno extends Gateway
{
    protected $gatewayName = 'Inno';

    /**
     * Const url.
     */
    const URL = [
        self::MODE_NORMAL => 'https://secure.tchatpay.com',
        self::MODE_DEV    => 'https://secure.tchatpay.com',
    ];

    public function __construct(Config $config)
    {
        $this->gateway = Support::create($config)->getBaseUri();
        parent::__construct($config);
    }
}
