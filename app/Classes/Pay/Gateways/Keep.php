<?php

namespace App\Classes\Pay\Gateways;

use App\Classes\Pay\Contracts\Gateway;
use App\Classes\Pay\Gateways\Keep\Support;
use App\Classes\Supports\Config;

class Keep extends Gateway
{
    protected $gatewayName = 'Keep';

    /**
     * Const url.
     */
    const URL = [
        self::MODE_NORMAL => 'https://edc.keepmall.net',
        self::MODE_DEV    => 'https://edc.keepmall.net',
    ];

    public function __construct(Config $config)
    {
        $this->gateway = Support::create($config)->getBaseUri();
        parent::__construct($config);
    }
}
