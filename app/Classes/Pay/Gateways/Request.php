<?php

namespace App\Classes\Pay\Gateways;

use App\Classes\Pay\Contracts\Gateway;
use App\Classes\Pay\Gateways\Request\Support;
use App\Classes\Supports\Config;

class Request extends Gateway
{
    protected $gatewayName = 'Request';

    /**
     * Const url.
     */
    const URL = [
        self::MODE_NORMAL => 'https://www.peachy-pay.com/api/v1/orders',
        self::MODE_DEV    => 'https://test.peachy-pay.com/api/v1/orders',
    ];

    public function __construct(Config $config)
    {
        $this->gateway = Support::create($config)->getBaseUri();
        parent::__construct($config);
    }
}
