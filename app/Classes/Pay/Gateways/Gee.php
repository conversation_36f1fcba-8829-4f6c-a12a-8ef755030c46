<?php

namespace App\Classes\Pay\Gateways;

use App\Classes\Pay\Contracts\Gateway;
use App\Classes\Pay\Gateways\Gee\Support;
use App\Classes\Supports\Config;

class Gee extends Gateway
{
    protected $gatewayName = 'Gee';

    /**
     * Const url.
     */
    const URL = [
        self::MODE_NORMAL => 'https://gateway.uneepay.com/api',
        self::MODE_DEV    => 'https://ci.celer365.com/geepay-payment-ci/api',
    ];

    public function __construct(Config $config)
    {
        $this->gateway = Support::create($config)->getBaseUri();
        parent::__construct($config);
    }
}
