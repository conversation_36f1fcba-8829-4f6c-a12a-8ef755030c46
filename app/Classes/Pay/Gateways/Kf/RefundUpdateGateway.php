<?php

namespace App\Classes\Pay\Gateways\Kf;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Parser\Kf;
use App\Models\Order;

class RefundUpdateGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig = $payload['pay_config'];
        $payRefund = $payload['payment_refund'];

        // 组装查询数据
        $data = [
            'refundOrderNo' => $payRefund['refund_id'],
        ];

        $post = [
            'appid'    => $payConfig['appid'],
            'reqTrx'   => Order::findAvailableNo(),
            'dateTime' => now('PRC')->format('YmdHis'),
            'service'  => 'REFUND_QUERY',
            'charset'  => 'UTF-8',
            'signType' => 'MD5',
            'format'   => 'JSON',
            'content'  => Support::aesEncrypt(json_encode($data, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE), $payConfig['key'])
        ];

        // MD5加密
        $sign         = Support::sign($post, $payConfig['key']);
        $post['sign'] = $sign;

        // 如果配置有中转配置参数，则处理中转网址和跳转/通知地址
        $transitIp = '';
        if (!empty($payConfig['transitIp'])) {
            $transitIp = $payConfig['transitIp'] . '?' . Support::urlPayment($payConfig);
        }

        $post = [
            'data'       => $post,
            'transit_ip' => $transitIp,
        ];

        return Kf::refundUpdate(Support::requestApi('', $post, $payload['order_id']));
    }
}
