<?php

namespace App\Classes\Pay\Gateways\Kq;

use App\Classes\Pay\Contracts\GatewayInterface;
use App\Classes\Pay\Exceptions\InvalidArgumentException;
use App\Classes\Supports\Collection;

abstract class Gateway implements GatewayInterface
{
    /**
     * Mode.
     *
     * @var string
     */
    protected $mode;

    /**
     * Bootstrap.
     *
     * @throws InvalidArgumentException
     */
    public function __construct()
    {
        $this->mode = Support::getInstance()->mode;
    }

    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @return Collection
     */
    abstract public function pay($endpoint, array $payload);
}
