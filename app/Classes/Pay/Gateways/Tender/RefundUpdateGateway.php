<?php

namespace App\Classes\Pay\Gateways\Tender;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Parser\Tender;
use App\Models\Order;

class RefundUpdateGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig = $payload['pay_config'];
        $randomKey = Support::getKey();

        $data = [
            'requestId' => Order::findAvailableNo(),
            'agentId'   => $payConfig['agentId'],
            'version'   => '2.0',
            'randomKey' => $randomKey,
        ];

        $bizData = [
            'merchantId' => $payConfig['merchantId'],
            'billNo'     => $payload['refund_id'],
        ];

        $data['bizData'] = json_encode($bizData);

        //生成签名
        $data['signature'] = Support::sign($data, $payConfig['privateKey']);
        //RSA公钥加密
        $data['randomKey'] = Support::genSignPublic($randomKey, $payConfig['paytendPublicKey']);
        //AES加密
        $data['bizData']   = Support::aesEncrypt($data['bizData'], $randomKey);
        $data['signType']  = 'RSA';

        $post = [
            'transitIp' => $payConfig['transitIp'] . '?' . Support::urlPayment($payConfig),
            'data'      => $data,
            'plaintext' => $bizData
        ];

        return Tender::refundUpdate(Support::requestApi('/a/qpay_orderquery', $post, $payload['refund_id']));
    }
}
