<?php

namespace App\Classes\Pay\Gateways\Yq;

use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Yq;
use App\Classes\Supports\Collection;
use Illuminate\Support\Facades\Cache;

class AuthGateway extends Gateway
{
    public $methodId = [
        'V' => [
            'no_three_d' => '1200',
            'three_d'    => '2500',
        ],
        'M' => [
            'no_three_d' => '1201',
            'three_d'    => '2501',
        ],
    ];
    
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig = $payload['pay_config'];
        $address   = $payload['address'];
        $card      = $payload['card'];
        $products  = current($payload['products']);

        // 拦截新加坡交易
        if ($card['card_country'] == 'Singapore' || $address['ip_country_isoa2'] == 'SG' || $address['ship_country_isoa2'] == 'SG' || $address['bill_country_isoa2'] == 'SG') {
            return Yq::_interceptParser(get_system_code('806'));
        }
        
        // 是否走3D支付方式
        if (!empty($payConfig['isThreeD'])) {
            $methodId = $this->methodId[$card['cc_type']]['three_d'] ?? '2500';
        } else {
            $methodId = $this->methodId[$card['cc_type']]['no_three_d'] ?? '1200';
        }

        // 缓存key
        $mark    = MD5($payload['order_id'] . 'yq');
        $markArr = [
            'order_id' => $payload['order_id'],
            'class'    => 'Yq',
        ];
        Cache::add($mark, $markArr, 24 * 60 * 60);

        // 组装支付数据
        $data = [
            'merchantid'     => $payConfig['merchantId'],
            'transtype'      => 'pay',
            'orderid'        => $payload['order_id'],
            'methodid'       => $methodId,// 支付方式ID
            'amount'         => Support::amount_format($payload['payment_order']['amount']),
            'currency'       => $payload['payment_order']['currency'],
            'firstname'      => $address['bill_first_name'],
            'lastname'       => $address['bill_last_name'],
            'cardnum'        => $card['card_number'],
            'cvv'            => $card['cvv'],
            'expireyear'     => $card['expiration_year'],
            'expiremonth'    => $card['expiration_month'],
            'addresscountry' => $address['bill_country_isoa2'],
            'address'        => $address['bill_address'],
            'addresscity'    => $address['bill_city'],
            'addressstate'   => $address['bill_state'],
            'zip'            => $address['bill_postcode'],
            'transname'      => $products['name'],
            'quantity'       => $products['qty'],
            'callbackurl'    => route('api.v1.asyncNotify', ['mark' => $mark], true),
            'returnurl'      => route('api.v1.syncNotify', ['mark' => $mark], true),
            'date'           => Support::millisecond(),//时间戳（毫秒）
            'clienttype'     => 1,
            'clientip'       => $address['ip'],
        ];
        
        // 配置机构ID
        if (isset($payConfig['agentId'])) {
            $data['agentid'] = $payConfig['agentId'];
        }
        
        // 生成签名
        $sign             = Support::sign($data, $payConfig['privateKey']);
        $data['signtype'] = 'RSA';
        $data['sign']     = $sign;
        
        return Yq::authParser(Support::requestApi('/pay', $data, $payload['order_id']));
    }
}
