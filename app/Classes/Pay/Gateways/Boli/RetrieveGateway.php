<?php

namespace App\Classes\Pay\Gateways\Boli;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Boli;

class RetrieveGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig = $payload['pay_config'];

        $data = [
            'merNo'     => $payConfig['merNo'],
            'gatewayNo' => $payConfig['gatewayNo'],
            'orderNo'   => $payload['payment_order']['payment_order_id'],
        ];

        $sign             = $data['merNo'] . $data['gatewayNo'] . $payConfig['key'];
        $data['signInfo'] = hash("sha256", $sign);

        // 组装请求头
        $header = array(
            'Content-Type' => 'application/x-www-form-urlencoded; charset=UTF-8',
        );

        $post = array(
            'data'   => $data,
            'header' => $header,
        );

        return Boli::retrieveParser(Support::requestApi('https://merchant.ucepay.com/api/customerCheck', $post, 'retrieve', $payload['order_id']));
    }
}
