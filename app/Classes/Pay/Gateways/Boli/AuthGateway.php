<?php

namespace App\Classes\Pay\Gateways\Boli;

use App\Classes\Supports\Collection;
use Illuminate\Support\Facades\Cache;
use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Boli;

class AuthGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig  = $payload['pay_config'];
        $payAddress = $payload['address'];
        $payCard    = $payload['card'];

        // 缓存key
        $mark    = MD5($payload['order_id'] . 'boli');
        $markArr = [
            'order_id' => $payload['order_id'],
            'class'    => 'Boli',
        ];

        Cache::add($mark . '_async', $markArr, 24 * 60 * 60);

        /* $goodsInfo = '';
        foreach ($payload['products'] as $value) {
            $goodsInfo .= $value['name'] . "#,#" . $value['sku'] . "#,#" . $value['price'] . "#,#" . $value['qty'] . '#;#';
        } */

        // 组装支付数据
        $amount = Support::amount_format($payload['payment_order']['amount']);
        $data   = [
            'merNo'            => $payConfig['merNo'],
            'gatewayNo'        => $payConfig['gatewayNo'],
            'orderNo'          => $payload['order_id'],
            'orderCurrency'    => $payload['payment_order']['currency'],
            'orderAmount'      => $amount,
            'cardNo'           => $payCard['card_number'],
            'cardExpireMonth'  => $payCard['expiration_month'],
            'cardExpireYear'   => '20' . $payCard['expiration_year'],
            'cardSecurityCode' => $payCard['cvv'],
            'firstName'        => $payAddress['bill_first_name'],
            'lastName'         => $payAddress['bill_last_name'],
            'email'            => $payAddress['bill_email'],
            'ip'               => $payAddress['ip'],
            'phone'            => $payAddress['bill_phone'],
            'country'          => $payAddress['bill_country_isoa2'],
            'state'            => $payAddress['bill_state'],
            'city'             => $payAddress['bill_city'],
            'address'          => $payAddress['bill_address'],
            'zip'              => $payAddress['bill_postcode'],
            'brower'           => Support::randomBrowerInfo(),
            'isCopyCard'       => '0',
            'interfaceMode'    => 'PHP',
            'notifyUrl'        => $payConfig['asyncUrl'],
            'returnUrl'        => $payConfig['syncUrl'],
        ];

        $sign             = $data['merNo'] . $data['gatewayNo'] . $data['orderNo'] . $data['orderCurrency'] . $data['orderAmount'] . $data['cardNo'] . $data['cardExpireYear'] . $data['cardExpireMonth'] . $data['cardSecurityCode'] . $payConfig['key'];
        $data['signInfo'] = hash("sha256", $sign);

        // 组装请求头
        $header = array(
            'Content-Type' => 'application/x-www-form-urlencoded; charset=UTF-8',
        );

        $post = array(
            'data'   => $data,
            'header' => $header,
        );

        return Boli::authParser(Support::requestApi('', $post, 'auth', $payload['order_id']));
    }
}
