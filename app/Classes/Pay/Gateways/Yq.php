<?php

namespace App\Classes\Pay\Gateways;

use App\Classes\Pay\Contracts\Gateway;
use App\Classes\Pay\Gateways\Yq\Support;
use App\Classes\Supports\Config;

class Yq extends Gateway
{
    protected $gatewayName = 'Yq';

    /**
     * Const url.
     */
    const URL = [
        self::MODE_NORMAL => 'https://paygate.uqpay.com',
        self::MODE_DEV    => 'https://paygate.uqpay.net',
    ];

    public function __construct(Config $config)
    {
        $this->gateway = Support::create($config)->getBaseUri();
        parent::__construct($config);
    }
}
