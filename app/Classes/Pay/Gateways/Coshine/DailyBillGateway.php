<?php

namespace App\Classes\Pay\Gateways\Coshine;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Parser\Coshine;

class DailyBillGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return array
     */
    public function pay($endpoint, array $payload): array
    {

        if (empty($payload['date'])) {
            return [];
        }

        $payConfig = $payload['pay_config'];
        $data      = [
            'entityId'    => $payConfig['entityId'],
            'entityType'  => 'PARTICIPATOR',
            'merchant.id' => $payConfig['merchantId']
        ];

        // 组装请求头
        $post = [
            'header' => [
                'Authorization' => 'Bearer ' . $payConfig['token'],
            ]
        ];

        return Coshine::downloadParser(Support::requestFile('/pay/v3/reports/' . $payload['date'] . '?' . http_build_query($data), $post, 'get', $payload['date']), $payload);
    }
}
