<?php

namespace App\Classes\Pay\Gateways\Coshine;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Parser\Coshine;
use Illuminate\Support\Facades\Cache;

class RefundGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload)
    {
        $payConfig = $payload['pay_config'];
        $payRefund = $payload['payment_refund'];

        $data = [
            'entityId'              => $payConfig['entityId'],
            'entityType'            => 'PARTICIPATOR',
            'merchant.id'           => $payConfig['merchantId'],
            'merchantTransactionId' => $payRefund['refund_id'],
            'originalTransactionId' => $payload['order_id'],
            'currency'              => $payRefund['currency'],
            'amount'                => Support::amount_format($payRefund['amount']),
            'paymentType'           => 'RF',
        ];

        //组装请求头
        $header = [
            'Content-Type'  => 'application/x-www-form-urlencoded',
            'Authorization' => 'Bearer ' . $payConfig['token'],
        ];

        $post = [
            'data'   => $data,
            'header' => $header
        ];

        return Coshine::refundParser(Support::requestApi('/pay/v3/payments', $post, 'post',$payload['refund_id']));
    }
}
