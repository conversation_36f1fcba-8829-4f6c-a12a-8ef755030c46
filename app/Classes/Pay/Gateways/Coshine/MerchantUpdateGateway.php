<?php

namespace App\Classes\Pay\Gateways\Coshine;

use App\Classes\Pay\Parser\Coshine;
use Ramsey\Uuid\Uuid;

class MerchantUpdateGateway extends Gateway
{
    /**
     * 商户申请
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return array
     */
    public function pay($endpoint, array $payload): array
    {
        $payConfig = $payload['pay_config'];
        $merchant  = $payload['merchant'];
        $data      = [
            'entityId'              => $payConfig['entityId'],
            'entityType'            => "PARTICIPATOR",
            'merchant.id'           => $merchant['merchant_channel_id'],
            'merchant.website'      => $merchant['website'],
            'merchant.business'     => $merchant['business'],
            'merchant.street'       => $merchant['street'],
            'merchant.postcode'     => $merchant['postcode'],
            'merchant.city'         => $merchant['city'],
            'merchant.state'        => $merchant['state'],
            'merchant.country'      => $merchant['country'],
            'merchant.mmcc'         => $merchant['mmcc'],
            'merchant.vmcc'         => $merchant['vmcc'],
            'merchant.amcc'         => $merchant['amcc'],
            'merchant.dmcc'         => $merchant['dmcc'],
            'merchant.jmcc'         => $merchant['jmcc'],
            'merchant.cmcc'         => $merchant['cmcc'],
            'merchantTransactionId' => (string)Uuid::uuid4(),
        ];

        if (!empty($merchant['mcc_list'])) {
            foreach ($merchant['mcc_list'] as $value) {
                if ($value['_remove_']) {
                    continue;
                }

                $data['merchant.'.$value['mcc']] = $value['value'];
            }
        }

        //组装请求头
        $header = [
            'Content-Type'  => 'application/x-www-form-urlencoded',
            'Authorization' => 'Bearer ' . $payConfig['token'],
        ];

        $post = [
            'data'   => $data,
            'header' => $header
        ];

        return Coshine::merchantParser(Support::requestApi('/pay/v3/merchant/update', $post, 'post', $merchant['merchant_channel_id']));
    }
}
