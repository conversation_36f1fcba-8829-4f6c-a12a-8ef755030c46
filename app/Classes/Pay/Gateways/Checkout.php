<?php

namespace App\Classes\Pay\Gateways;

use App\Classes\Pay\Contracts\Gateway;
use App\Classes\Pay\Gateways\Checkout\Support;
use App\Classes\Supports\Config;

class Checkout extends Gateway
{
    protected $gatewayName = 'Checkout';

    /**
     * Const url.
     */
    const URL = [
        self::MODE_NORMAL => 'https://api.checkout.com',
        self::MODE_DEV    => 'https://api.sandbox.checkout.com',
    ];

    public function __construct(Config $config)
    {
        $this->gateway = Support::create($config)->getBaseUri();
        parent::__construct($config);
    }
}
