<?php

namespace App\Classes\Pay\Gateways\Fomo;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Fomo;

class RetrieveGateway extends Gateway
{
	/**
	 * Pay an order.
	 *
	 * @param string $endpoint
	 *
	 * @param array $payload
	 * @return Collection
	 */
	public function pay($endpoint, array $payload): Collection
	{
		$payConfig    = $payload['pay_config'];
		$paymentOrder = $payload['payment_order'];

		// 组装请求头
		$header = array(
			"Content-Type"  => "application/json",
			"Authorization" => 'Basic ' . base64_encode($payConfig['mid'] . ':' . $payConfig['psk'])
		);

		$post = array(
			'data'   => [],
			'header' => $header,
		);
		
		// 默认返回PENDING
		$result = [
			'orderNo' => $payload['order_id'],
			'status'  => 'CREATED',
		];
		if (!empty($paymentOrder['payment_order_id']) && !empty($paymentOrder['remark'])) {
			$url    = sprintf('/api/orders/%s/transactions/%s', $paymentOrder['payment_order_id'], $paymentOrder['remark']);
			$result = Support::requestApi($url, $post, '', 'get', $payload['order_id']);
		}
		
		return Fomo::retrieveParser($result);
	}
}
