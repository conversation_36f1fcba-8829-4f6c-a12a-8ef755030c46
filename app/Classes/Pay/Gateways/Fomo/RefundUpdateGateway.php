<?php

namespace App\Classes\Pay\Gateways\Fomo;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Fomo;

class RefundUpdateGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig     = $payload['pay_config'];
        $paymentOrder  = $payload['payment_order'];
        $paymentRefund = $payload['payment_refund'];

        // 组装请求头
        $header = array(
            "Content-Type"  => "application/json",
            "Authorization" => 'Basic ' . base64_encode($payConfig['mid'] . ':' . $payConfig['psk'])
        );

        $post = array(
            'data'   => [],
            'header' => $header,
        );

        return Fomo::refundUpdate(Support::requestApi(sprintf('/api/orders/%s/transactions/%s', $paymentOrder['payment_order_id'], $paymentRefund['payment_refund_id']), $post, '', 'get', $payload['refund_id']));
    }
}
