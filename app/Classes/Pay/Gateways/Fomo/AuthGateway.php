<?php

namespace App\Classes\Pay\Gateways\Fomo;

use App\Classes\Supports\Collection;
use Illuminate\Support\Facades\Cache;
use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Fomo;

class AuthGateway extends Gateway
{
    /**
     * 卡种数据
     *
     * @var array
     */
    protected $_cardData = array(
        'V' => 'visa',
        'M' => 'mastercard',
        'J' => 'jcb',
        'A' => 'amex',
        'D' => 'discover',
    );

    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig = $payload['pay_config'];
        $address   = $payload['address'];
        $card      = $payload['card'];

        // 缓存key
        $mark    = MD5($payload['order_id'] . 'fomo');
        $markArr = [
            'order_id'   => $payload['order_id'],
            'orderNo'    => $payload['order_id'],
            'class'      => 'Fomo',
            'channel_id' => $payload['channel_id'],
        ];

        Cache::add($mark, $markArr, 24 * 60 * 60);

        // cancel key
        $cancelMark    = MD5($payload['order_id'] . 'fomocancel');
        $cancelMarkArr = [
            'order_id' => $payload['order_id'],
            'orderNo'  => $payload['order_id'],
            'class'    => 'Fomo',
            'status'   => 'CLOSED',
        ];
        Cache::add($cancelMark, $cancelMarkArr, 24 * 60 * 60);

        // 组装支付数据
        $data = array(
            'mode'               => 'DIRECT',
            'orderNo'            => $payload['order_id'],
            'subMid'             => $payConfig['tid'],
            'subject'            => $payConfig['cardBill'],
            'description'        => $payConfig['cardBill'],
            'amount'             => Support::amount_format($payload['payment_order']['amount']),
            'currencyCode'       => $payload['payment_order']['currency'],
            'notifyUrl'          => \route('api.v1.asyncNotify', ['mark' => $mark], true),
            'returnUrl'          => \route('api.v1.syncNotify', ['mark' => $mark], true),
            'backUrl'            => \route('api.v1.syncNotify', ['mark' => $cancelMark], true),
            'sourceOfFund'       => 'CARD',
            'transactionOptions' => array(
                'timeout'      => 3600,
                'expiryYear'   => $card['expiration_year'],
                'expiryMonth'  => (string)intval($card['expiration_month']),
                'nameOnCard'   => $address['bill_name'],
                'number'       => $card['card_number'],
                'securityCode' => $card['cvv'],
                'ip'           => $address['ip'],
                'threeDSecure' => $payConfig['threeDSecure'] ?? 'disabled', // enforced auto disabled
            )
        );

        // 组装请求头
        $header = array(
            'Content-Type'  => 'application/json',
            'Authorization' => 'Basic ' . base64_encode($payConfig['mid'] . ':' . $payConfig['psk'])
        );

        $post = array(
            'data'   => $data,
            'header' => $header,
        );

        return Fomo::authParser(Support::requestApi('/api/orders', $post, 'json', 'post', $payload['order_id']));
    }
}
