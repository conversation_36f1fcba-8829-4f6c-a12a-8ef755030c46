<?php

namespace App\Classes\Pay\Gateways;

use App\Classes\Pay\Contracts\Gateway;
use App\Classes\Pay\Gateways\Dlp\Support;
use App\Classes\Supports\Config;

class Dlp extends Gateway
{
    protected $gatewayName = 'Dlp';

    /**
     * Const url.
     */
    const URL = [
        self::MODE_NORMAL => 'https://gpapi.LianLianpay.com/v3/merchants/',
        self::MODE_DEV    => 'https://celer-api.LianLianpay-inc.com/v3/merchants/',
    ];

    public function __construct(Config $config)
    {
        $this->gateway = Support::create($config)->getBaseUri();
        parent::__construct($config);
    }
}
