<?php

namespace App\Classes\Pay\Gateways\Fdc;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Fdc;
use Illuminate\Support\Facades\Cache;

class RefundGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload)
    {
        $payConfig = $payload['pay_config'];

        // 组装数据
        $data   = [
            'platformMerchantId'  => $payConfig['platformMerchantId'],
            'originalOrderNo'     => $payload['payment_order']['payment_order_id'], // 要传渠道返回的交易订单号
            'tradeRequestOrderNo' => $payload['refund_id'],
        ];

        $signature = Support::sign($data, $payConfig['privateKey']);

        // 组装请求头
        $header = [
            'sign'         => $signature,
            'coagencyCode' => $payConfig['coagencyCode'],
        ];

        $post = [
            'data'   => $data,
            'header' => $header,
        ];
        
        return Fdc::refundParser(Support::requestApi('/mhlapi/bussiness/bussinessXl-api/refund', $post, $payload['refund_id']));
    }
}
