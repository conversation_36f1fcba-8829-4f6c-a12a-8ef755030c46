<?php

namespace App\Classes\Pay\Gateways\Fdc;

use App\Classes\Supports\Collection;
use Illuminate\Support\Facades\Cache;
use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Fdc;

class AuthGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig  = $payload['pay_config'];
        // 缓存key
        $mark       = MD5($payload['order_id'] . 'fdc');
        $markArr    = [
            'order_id' => $payload['order_id'],
            'class'    => 'Fdc',
        ];
        Cache::add($mark, $markArr, 24 * 60 * 60);

        // 组装支付数据
        $iv   = "0123456789ABCDEF";
        $data = [
            'platformMerchantId'  => $payConfig['platformMerchantId'],
            'tradeRequestOrderNo' => $payload['order_id'],
            'currency'            => $payload['payment_order']['currency'],
            'value'               => Support::amount_format($payload['payment_order']['amount'] * 100, 0), //支付金额 （单位：分，整数)
            'cardNumber'          => Support::encryptNew($payload['card']['card_number'], $payConfig['aesKey'], $iv),
            'expiryDate'          => Support::encryptNew($payload['card']['expiration_month'] . $payload['card']['expiration_year'], $payConfig['aesKey'], $iv),
            'cvc'                 => Support::encryptNew($payload['card']['cvv'], $payConfig['aesKey'], $iv),
            'sid'                 => $payConfig['sid'],
            'signkey'             => $payConfig['signKey'],
            'callBackUrl'         => \route('api.v1.asyncNotify', ['mark' => $mark], true),
        ];

        $signature = Support::sign($data, $payConfig['privateKey']);

        // 组装请求头
        $header = [
            'sign'         => $signature,
            'coagencyCode' => $payConfig['coagencyCode'],
        ];

        $post = [
            'data'   => $data,
            'header' => $header,
        ];

        return Fdc::authParser(Support::requestApi('/mhlapi/bussiness/bussinessXl-api/payment', $post, $payload['order_id']));
    }

}
