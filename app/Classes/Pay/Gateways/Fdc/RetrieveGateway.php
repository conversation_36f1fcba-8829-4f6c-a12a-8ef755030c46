<?php

namespace App\Classes\Pay\Gateways\Fdc;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Fdc;

class RetrieveGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig = $payload['pay_config'];

        // 组装数据
        $data   = [
            'platformMerchantId' => $payConfig['platformMerchantId'],
            'tradeNo'            => $payload['payment_order']['payment_order_id'],   // 要传渠道返回的交易订单号
        ];

        $signature = Support::sign($data, $payConfig['privateKey']);

        // 组装请求头
        $header = [
            'sign'         => $signature,
            'coagencyCode' => $payConfig['coagencyCode'],
        ];

        $post = [
            'data'   => $data,
            'header' => $header,
        ];

        return Fdc::retrieveParser(Support::requestApi('/mhlapi/bussiness/bussinessXl-api/getTransInfo', $post, $payload['order_id']));
    }
}
