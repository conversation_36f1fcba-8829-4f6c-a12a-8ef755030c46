<?php

namespace App\Classes\Pay\Gateways\Acapay;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Acapay;

class RetrieveGateway extends Gateway
{
	/**
	 * Pay an order.
	 *
	 * @param string $endpoint
	 *
	 * @param array $payload
	 * @return Collection
	 */
	public function pay($endpoint, array $payload): Collection
	{
		$payConfig = $payload['pay_config'];

        // 组装查询数据
        $data = [
            "isv_code"      => $payConfig['isv_code'],
            "mer_code"      => $payConfig['mer_code'],
            "sn"            => $payload['order_id'],
            "param" => [
            ],
            "timestamp" => time(),
        ];

        $data['sign'] = Support::sign($data, $payConfig['privateKey']);

        $post = array(
            'data'   => $data,
            'header' => ['content-type' => 'application/json;charset=utf-8'],
        );

		return Acapay::retrieveParser(Support::requestApi('/institution/api/payment/fetch', $post, $payload['order_id']));
	}
}
