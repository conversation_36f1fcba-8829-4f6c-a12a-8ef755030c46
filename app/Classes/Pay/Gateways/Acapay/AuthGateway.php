<?php

namespace App\Classes\Pay\Gateways\Acapay;

use App\Classes\Supports\Collection;
use Illuminate\Support\Facades\Cache;
use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Acapay;

class AuthGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig  = $payload['pay_config'];
        $payAddress = $payload['address'];
        // 缓存key
        $mark    = MD5($payload['order_id'] . 'acapay');
        $markArr = [
            'order_id' => $payload['order_id'],
            'class'    => 'Acapay',
        ];
        Cache::add($mark, $markArr, 24 * 60 * 60);

        // 组装支付数据
        $amount    = Support::amount_format($payload['payment_order']['amount']); //支付金额 （单位：分，整数)
        $data      = [
            "isv_code"      => $payConfig['isv_code'],
            "mer_code"      => $payConfig['mer_code'],
            "sn"            => $payload['order_id'],
            "param" => [
                "expiry_month"  => $payload['card']['expiration_month'],
                "cvv"           => $payload['card']['cvv'],
                // "pin"        => "1234",
                "amount"        => $amount,
                "card_number"   => $payload['card']['card_number'],
                "last_name"     => substr($payAddress['bill_last_name'], 0, 22),
                "currency"      => $payload['payment_order']['currency'],
                "first_name"    => substr($payAddress['bill_first_name'], 0, 22),
                "expiry_year"   => $payload['card']['expiration_year'],
                "email"         => $payAddress['bill_email'],
                "redirect_url"  => \route('api.v1.syncNotify', ['mark' => $mark], true),
            ],
            "timestamp" => time(),
        ];

        $data['sign'] = Support::sign($data, $payConfig['privateKey']);

        $post = array(
            'data'   => $data,
            'header' => ['content-type' => 'application/json;charset=utf-8'],
        );

        return Acapay::authParser(Support::requestApi('/institution/api/payment/make', $post, $payload['order_id']));
    }
}
