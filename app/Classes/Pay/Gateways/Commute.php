<?php

namespace App\Classes\Pay\Gateways;

use App\Classes\Pay\Contracts\Gateway;
use App\Classes\Pay\Gateways\Commute\Support;
use App\Classes\Supports\Config;

class Commute extends Gateway
{
    protected $gatewayName = 'Commute';

    /**
     * Const url.
     */
    const URL = [
        self::MODE_NORMAL => 'https://oats.allinpay.com',
        self::MODE_DEV    => 'https://test.allinpayhk.com',
    ];

    public function __construct(Config $config)
    {
        $this->gateway = Support::create($config)->getBaseUri();
        parent::__construct($config);
    }
}
