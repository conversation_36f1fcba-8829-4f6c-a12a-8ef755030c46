<?php

namespace App\Classes\Pay\Gateways\Stripe;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Stripe;

class RefundGateway extends Gateway
{
	/**
	 * Pay an order.
	 *
	 * @param string $endpoint
	 *
	 * @param array $payload
	 * @return Collection
	 */
	public function pay($endpoint, array $payload)
	{
		$payConfig    = $payload['pay_config'];
		$paymentOrder = $payload['payment_order'];

		// 请求url
		$url  = 'https://' . $payConfig['url'] . '/zmalqp/535452495045/' . (isset($payConfig['mode']) && $payConfig['mode'] == 'dev' ? 'testRefund.php' : 'refund.php');
		$data = array(
			'transaction_id' => $payload['refund_id'],
			'reference_id'   => $paymentOrder['payment_order_id'],
			'amount'         => Support::amount_format($payload['payment_refund']['amount'] * Support::_getAmountRate($payload['payment_refund']['currency']), 0)
		);

		return Stripe::refundParser(Support::requestApi($url, $data, $payload['refund_id']));
	}
}
