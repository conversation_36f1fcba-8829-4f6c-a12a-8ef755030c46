<?php

namespace App\Classes\Pay\Gateways\Stripe;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Stripe;

class RetrieveGateway extends Gateway
{
	/**
	 * Pay an order.
	 *
	 * @param string $endpoint
	 *
	 * @param array $payload
	 * @return Collection
	 */
	public function pay($endpoint, array $payload): Collection
	{
		$payConfig    = $payload['pay_config'];
		$paymentOrder = $payload['payment_order'];

		// 请求url
		$url  = 'https://' . $payConfig['url'] . '/zmalqp/535452495045/' . (isset($payConfig['mode']) && $payConfig['mode'] == 'dev' ? 'testRetrieve.php' : 'retrieve.php');
		$data = array(
			'transaction_id' => $paymentOrder['order_id'],
			'reference_id'   => $paymentOrder['payment_order_id']
		);

		return Stripe::retrieveParser(Support::requestApi($url, $data, $payload['order_id']));
	}
}
