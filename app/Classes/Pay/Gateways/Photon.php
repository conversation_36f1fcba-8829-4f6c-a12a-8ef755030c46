<?php

namespace App\Classes\Pay\Gateways;

use App\Classes\Pay\Contracts\Gateway;
use App\Classes\Pay\Gateways\Photon\Support;
use App\Classes\Supports\Config;

class Photon extends Gateway
{
    protected $gatewayName = 'Photon';

    /**
     * Const url.
     */
    const URL = [
        self::MODE_NORMAL => 'https://x-api.photonpay.com/payment/txncore/s2s/',
        self::MODE_DEV    => 'https://x-api1.uat.photontech.cc/payment/txncore/s2s/',
    ];

    /**
     * Const token url.
     */
    const TOKENURL = [
        self::MODE_NORMAL => 'https://x-api.photonpay.com/oauth2/token/accessToken',
        self::MODE_DEV    => 'https://x-api1.uat.photontech.cc/oauth2/token/accessToken',
    ];

    public function __construct(Config $config)
    {
        $this->gateway = Support::create($config)->getBaseUri();
        parent::__construct($config);
    }
}
