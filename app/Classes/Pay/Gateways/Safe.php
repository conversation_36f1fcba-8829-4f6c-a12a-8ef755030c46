<?php

namespace App\Classes\Pay\Gateways;

use App\Classes\Pay\Contracts\Gateway;
use App\Classes\Pay\Gateways\Safe\Support;
use App\Classes\Supports\Config;

class Safe extends Gateway
{
    protected $gatewayName = 'Safe';

    /**
     * Const url.
     */
    const URL = [
        self::MODE_NORMAL => 'https://secure.safecharge.com/ppp/api/v1',
        self::MODE_DEV    => 'https://ppp-test.nuvei.com/ppp/api/v1',
    ];

    public function __construct(Config $config)
    {
        $this->gateway = Support::create($config)->getBaseUri();
        parent::__construct($config);
    }
}
