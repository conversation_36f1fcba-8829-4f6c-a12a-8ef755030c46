<?php

namespace App\Classes\Pay\Gateways\Estuary;

use App\Classes\Supports\Collection;
use Illuminate\Support\Facades\Cache;
use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Estuary;

class AuthGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig  = $payload['pay_config'];
        $payAddress = $payload['address'];

        // 缓存key
        $mark    = MD5($payload['order_id'] . 'estuary');
        $markArr = [
            'order_id' => $payload['order_id'],
            'class'    => 'Estuary',
        ];
        Cache::add($mark, $markArr, 24 * 60 * 60);

        // 组装支付数据
        $iv        = "0123456789ABCDEF";
        $amount    = Support::amount_format($payload['payment_order']['amount'] * 100, 0); //支付金额 （单位：分，整数)
        $data      = [
            'payOrdNo'       => $payload['order_id'],
            'payPattern'     => $payConfig['pay_pattern'],
            'payType'        => '01',
            'payChannelNo'   => $payConfig['pay_channel_no'],
            'merchantId'     => $payConfig['merchant_id'],
            'merchantName'   => $payConfig['merchant_name'],
            'payAmt'         => $amount,
            'payFee'         => '0',
            'payNetrecAmt'   => $amount,
            'notityUrl'      => \route('api.v1.asyncNotify', ['mark' => $mark], true),
            'frontUrl'       => \route('api.v1.syncNotify', ['mark' => $mark], true),
            'payCardNo'      => $this->encryptNew($payload['card']['card_number'], $payConfig['aesKey'], $iv),
            'payCardCvn2'    => $this->encryptNew($payload['card']['cvv'], $payConfig['aesKey'], $iv),
            'payCardExpired' => $this->encryptNew($payload['card']['expiration_month'] . '-20' . $payload['card']['expiration_year'], $payConfig['aesKey'], $iv),
            'orderDesc'      => $payload['card_bill'],
            'mobile'         => $this->encryptNew($payAddress['bill_phone'], $payConfig['aesKey'], $iv),
            'nameOnCard'     => substr($payAddress['bill_name'], 0, 22),
            'ip'             => substr($payAddress['ip'],0,32),
            'email'          => $payAddress['bill_email'],
            'websiteId'      => $payConfig['websiteId'] ?? '',
        ];
        $signature = Support::sign($data, $payConfig['privateKey']);

        // 组装请求头
        $header = array(
            'sign'         => $signature,
            'coagencyCode' => $payConfig['coagency_code'],
        );

        $post = array(
            'data'   => $data,
            'header' => $header,
        );

        return Estuary::authParser(Support::requestApi('/mhlapi/bussiness/unionintl-api/purchase', $post, $payload['order_id']));
    }

    /**
     * AES/CBC/PKCS5Padding Encrypter
     *
     * @param $str
     * @param $key
     * @return string
     */
    function encryptNew($str, $key,$iv)
    {
        return base64_encode(openssl_encrypt($str, "AES-128-CBC", $key, OPENSSL_RAW_DATA,$iv));
    }
}
