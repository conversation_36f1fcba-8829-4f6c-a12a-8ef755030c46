<?php

namespace App\Classes\Pay\Gateways;

use App\Classes\Pay\Contracts\Gateway;
use App\Classes\Pay\Gateways\Hai\Support;
use App\Classes\Supports\Config;

class Hai extends Gateway
{
    protected $gatewayName = 'Hai';

    /**
     * Const url.
     */
    const URL = [
        self::MODE_NORMAL => 'https://intl.icardpay.com',
        self::MODE_DEV    => 'https://intltest.icardpay.com',
    ];

    public function __construct(Config $config)
    {
        $this->gateway = Support::create($config)->getBaseUri();
        parent::__construct($config);
    }
}
