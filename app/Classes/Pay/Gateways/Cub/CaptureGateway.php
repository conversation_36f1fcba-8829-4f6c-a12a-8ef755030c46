<?php

namespace App\Classes\Pay\Gateways\Cub;

use App\Classes\Pay\Parser\Cub;
use App\Classes\Supports\Collection;
use App\Classes\Pay\Events;

class CaptureGateway extends Gateway
{
	/**
	 * Pay an order.
	 *
	 * @param string $endpoint
	 *
	 * @param array $payload
	 * @return Collection
	 */
	public function pay($endpoint, array $payload): Collection
	{
		$payConfig          = $payload['pay_config'];
		$paymentOrder       = $payload['payment_order'];
		$parentPaymentOrder = $payload['parent_payment_order'];

		$data = array(
			'MSGID'            => 'ORD0005',
			'CAPTUREORDERINFO' => array(
				'STOREID'     => $payConfig['storeId'],
				'ORDERNUMBER' => $parentPaymentOrder['payment_order_id'],
				'AMOUNT'      => Support::amount_format($paymentOrder['amount'], '0'),
				'AUTHCODE'    => $parentPaymentOrder['result'],
			),
		);

		$md5Str          = implode($data['CAPTUREORDERINFO']) . $payConfig['cubKey'];
		$data['CAVALUE'] = md5($md5Str);
		$xml             = Support::xmlEncode($data, 'MERCHANTXML');

		// 请求url
		$url  = 'https://' . $payConfig['url'] . '/zmalqp/435542/common.php';
		$post = array(
			'url' => 'https://sslpayment.uwccb.com.tw/EPOSService/CRDOrderService.asmx?wsdl',
			'xml' => $xml,
		);

		Events::dispatch(new Events\PayStarted('Cub', 'Web/Wap', $endpoint, $payload));
		return Cub::captureParser(Support::requestApi($url, $post));
	}
}
