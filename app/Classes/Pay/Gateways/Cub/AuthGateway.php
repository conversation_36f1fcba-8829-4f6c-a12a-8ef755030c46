<?php

namespace App\Classes\Pay\Gateways\Cub;

use App\Classes\Supports\Collection;
use Illuminate\Support\Facades\Cache;
use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Cub;

class AuthGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
	    $payConfig = $payload['pay_config'];
	    $card      = $payload['card'];

	    // 同步回调地址
	    $syncUrl             = 'https://' . $payConfig['url'] . '/zmalqp/435542/' . (isset($payConfig['mode']) && $payConfig['mode'] == 'dev' ? 'testSync.php' : 'sync.php');
	    $tempData            = ['RETURL' => $syncUrl];
	    $tempData['CAVALUE'] = MD5($payConfig['url'] . $payConfig['cubKey']);
	    $xml                 = Support::xmlEncode($tempData, 'MERCHANTXML');

	    // 缓存key
	    $mark    = MD5($payload['order_id'] . 'cub');
	    $markArr = [
		    'order_id' => $payload['order_id'],
		    'class'    => 'Cub',
		    'xml'      => $xml,
	    ];
	    Cache::add($mark, $markArr, 24 * 60 * 60);

	    if ($payload['access_type'] == 's2s') {
		    $channelendpoint = 'PaymentInitial.aspx';
		    $data            = array(
			    'MSGID'       => 'TRS0001', // MSGID
			    'ORDERINFO'   => array(
				    'STOREID'     => $payConfig['storeId'], // STOREID
				    'ORDERNUMBER' => $payload['order_id'], // 订单id
				    'AMOUNT'      => Support::amount_format($payload['payment_order']['amount'], '0'),
			    ),
			    'PAYMENTINFO' => array(
				    'CARDNO' => $card['card_number'],
				    'EXPIRE' => '20' . $card['expiration_year'] . $card['expiration_month'],
				    'CVC2'   => $card['cvv'],
			    )
		    );

		    // 组装签名
		    $str             = implode('', $data['ORDERINFO']) . $data['PAYMENTINFO']['CARDNO'] . $payConfig['cubKey'];
		    $data['CAVALUE'] = MD5($str);
	    } else {
		    $channelendpoint = 'OrderInitial.aspx';
		    $data            = array(
			    'MSGID'     => 'TRS0004', // MSGID
			    'ORDERINFO' => array(
				    'STOREID'     => $payConfig['storeId'], // STOREID
				    'ORDERNUMBER' => $payload['order_id'], // 订单id
				    'AMOUNT'      => Support::amount_format($payload['payment_order']['amount'], '0'),
				    'LANGUAGE'    => 'EN-US',
			    )
		    );

		    // 组装签名
		    $str             = implode('', $data['ORDERINFO']) . $payConfig['cubKey'];
		    $data['CAVALUE'] = MD5($str);
	    }

	    // 请求url
	    $url  = 'https://' . $payConfig['url'] . '/zmalqp/435542/' . (isset($payConfig['mode']) && $payConfig['mode'] == 'dev' ? 'testPayment.php' : 'payment.php');
	    $post = array(
		    'order_number'   => $payload['order_id'],
		    'redirect_url'   => $url,
		    'redirect_param' => array(
			    'url'            => 'https://sslpayment.uwccb.com.tw/EPOSService/Payment/' . $channelendpoint,
			    'redirect_param' => $data,
		    )
	    );
		
		$logData = Support::handleLogData($data);
		
		Events::dispatch(new Events\ApiRequesting('Cub', '',  $endpoint, $data));
        Events::dispatch(new Events\SlsLog(
            'Cub', 
            'Requesting To Api',
            [$endpoint, $logData], 
            $payload['order_id']
        ));

	    return Cub::authParser($post);
    }
}