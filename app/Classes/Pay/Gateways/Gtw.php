<?php

namespace App\Classes\Pay\Gateways;

use App\Classes\Pay\Contracts\Gateway;
use App\Classes\Pay\Gateways\Gtw\Support;
use App\Classes\Supports\Config;

class Gtw extends Gateway
{
    protected $gatewayName = 'Gtw';

    /**
     * Const url.
     */
    const URL = [
        self::MODE_NORMAL => 'https://gtw.efaka.com',
        self::MODE_DEV    => 'https://gtw.efaka.com',
    ];

    public function __construct(Config $config)
    {
        $this->gateway = Support::create($config)->getBaseUri();
        parent::__construct($config);
    }
}
