<?php

namespace App\Classes\Pay\Gateways\Keep;

use App\Classes\Pay\Contracts\Support as BaseSupport;
use App\Classes\Pay\Events;
use App\Classes\Pay\Gateways\Keep;
use App\Classes\Pay\Log;
use App\Classes\Supports\Collection;
use App\Classes\Supports\Config;

/**
 * @property array http http options
 * @property string mode current mode
 * @property array log log options
 * @property string sign_key
 */
class Support
{
    use BaseSupport;

    /**
     * Instance.
     *
     * @var Support
     */
    private static $instance;

    /**
     * Bootstrap.
     *
     * @param Config $config
     */
    public function __construct(Config $config)
    {
	    self::$gatewayName = 'Keep';
	    $this->baseUri     = Keep::URL[$config->get('mode', Keep::MODE_NORMAL)];
	    $this->config      = $config;
	    $this->setHttpOptions();
    }

	/**
	 * Get API result.
	 *
	 * @param array $data
	 * @param string $endpoint
	 * @param string $requestId
	 * @return Collection
	 */
	public static function requestApi(string $endpoint, array $data, string $requestId = '' ): Collection
	{
        $logData = Support::handleLogData($data);

        $doUrl   = self::$instance->getBaseUri() . $endpoint; // 渠道最终请求地址
        $tempUrl = $data['transit_ip'] ?: $doUrl; // 当前发起的请求地址，有中转则走中转

		Events::dispatch(new Events\ApiRequesting(self::$gatewayName, '', $tempUrl, $logData));
        Events::dispatch(new Events\SlsLog(
            self::$gatewayName,
            'Requesting To Api',
            [$tempUrl, $logData],
            $requestId
        ));

        $result = [];
        try {
            if (!empty($data['transit_ip'])) {
                // 转发到中转
                $data['embMethod'] = 'post';
                $data['doUrl']     = $doUrl;

                if (!empty($data['header'])) {
                    $header = [];
                    foreach ($data['header'] as $key => $vo) {
                        $header[] = $key . ':' . $vo;
                    }

                    $data['header'] = $header;
                }

                $result = self::$instance->post($data['transit_ip'], $data);

                if (gettype($result) === 'string') {
                    $result = json_decode($result, true) ?? [];
                }
            } else {
                $result = self::$instance->post($endpoint, $data['data']);
            }
        } catch (\Exception $e) {
            // 记录日志
            Log::error($e->getMessage());
            Events::dispatch(new Events\SlsLog(
                self::$gatewayName,
                $e->getMessage(),
                [],
                $requestId,
                'error'
            ));
        }

		Events::dispatch(new Events\ApiRequested(self::$gatewayName, '', $tempUrl, $result));
        Events::dispatch(new Events\SlsLog(
            self::$gatewayName,
            'Result Of Api',
            $result,
            $requestId
        ));

		return self::processingApiResult($result);
	}

	/**
     * 处理日志数据
     *
     * @param array $data
     * @return array
     */
    public static function handleLogData($data)
    {
        if (isset($data['expirationYear'], $data['expirationMonth'], $data['cvv'], $data['cardNo'])) {
            $data['expirationYear']  = get_mark_data($data['expirationYear']);
            $data['expirationMonth'] = get_mark_data($data['expirationMonth']);
            $data['cvv']             = get_mark_data($data['cvv']);
            $data['cardNo']          = get_markcard($data['cardNo']);
        }

        return $data;
    }

    public static function signature(string $str)
    {
        return hash('sha256', $str);
    }
}
