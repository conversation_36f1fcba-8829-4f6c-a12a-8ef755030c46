<?php

namespace App\Classes\Pay\Gateways\Keep;

use App\Classes\Supports\Collection;
use Illuminate\Support\Facades\Cache;
use App\Classes\Pay\Parser\Keep;

class AuthGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
	    $payConfig = $payload['pay_config'];
	    $address   = $payload['address'];
	    $card      = $payload['card'];
	    $products  = $payload['products'];

		//年份处理
        if (strlen($card['expiration_year']) == 2) {
            $card['expiration_year'] = substr(date('Y'), 0, 2) . $card['expiration_year'];
        }

        // 组装商品数据
	    $productInfo = [];
        if (!empty($products)) {
        	foreach ($products as $product) {
		        $productInfo['data'][] = [
					'itemName' => (strlen($product['name']) > 50) ? mb_strcut($product['name'], 0, 50) : $product['name'],
					'price'    => $product['price'],
					'quantity' => $product['qty'],
					'sku'      => $product['sku']
		        ];
	        }
        }

	    // 缓存key
	    $mark    = MD5($payload['order_id'] . 'keep');
	    $markArr = [
		    'order_id' => $payload['order_id'],
		    'class'    => 'Keep',
	    ];
	    Cache::add($mark, $markArr, 24 * 60 * 60);

		$signData = [
			'merchantNo' => $payConfig['merchantNo'],
			'terminalNo' => $payConfig['terminalNo'],
			'website'    => $payConfig['shellUrl'],
			'orderNo'    => $payload['order_id'],
			'currency'   => $payload['payment_order']['currency'],
			'payIp'      => $address['ip'],
			'payMethod'  => '1',
		];

		//组装签名
		$signStr               = implode('', $signData) . $payConfig['signKey'];
		$signData['signature'] = Support::signature($signStr);

	    $data = [
			'amount'          => Support::amount_format($payload['payment_order']['amount']),
			'sourceURL'       => $payConfig['shellUrl'],
			'returnURL'       => \route('api.v1.syncNotify', ['mark' => $mark], true),
			'asynReturnURL'   => \route('api.v1.asyncNotify', ['mark' => $mark], true),
			'cardNo'          => $card['card_number'],
			'cvv'             => $card['cvv'],
			'expirationYear'  => $card['expiration_year'],
			'expirationMonth' => $card['expiration_month'],
			'language'        => 'en_US',
			'email'           => $address['bill_email'],
			'billFirstName'   => $address['bill_first_name'],
			'billLastName'    => $address['bill_last_name'],
			'billAddress'     => (strlen($address['bill_address']) > 100) ? mb_strcut($address['bill_address'], 0, 100) : $address['bill_address'],
			'billCity'        => $address['bill_city'],
			'billState'       => $address['bill_state'],
			'billCountry'     => $address['bill_country_isoa2'],
			'billZip'         => $address['bill_postcode'],
			'billPhone'       => $address['bill_phone'],
			'shipFirstName'   => $address['ship_first_name'],
			'shipLastName'    => $address['ship_last_name'],
			'shipAddress'     => (strlen($address['ship_address']) > 100) ? mb_strcut($address['ship_address'], 0, 100) : $address['ship_address'],
			'shipCity'        => $address['ship_city'],
			'shipState'       => $address['ship_state'],
			'shipCountry'     => $address['ship_country_isoa2'],
			'shipZip'         => $address['ship_postcode'],
			'shipPhone'       => $address['bill_phone'],
			'goodsJson'       => json_encode($productInfo),
			'userAgent'       => $_SERVER['HTTP_USER_AGENT'] ?? 'HTTP_USER_AGENT',
		    'acceptLanguage'  => $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? 'zh-CN,zh;q=0.9'
		];

        // 如果配置有中转配置参数，则处理中转网址和跳转/通知地址
        $transitIp = '';
        if (!empty($payConfig['transitUrl']) && !empty($payConfig['transitIp'])) {
            $transitIp             = $payConfig['transitIp'] . '?' . Support::urlPayment($payConfig);
            $data['returnURL']     = $payConfig['transitUrl'] . '/Sync.php?' . Support::urlPayment($payConfig, ['mark' => $mark]);
            $data['asynReturnURL'] = $payConfig['transitUrl'] . '/Async.php?' . Support::urlPayment($payConfig, ['mark' => $mark]);
        }

		//合并数据
		$data = array_merge($signData, $data);

        $post = [
            'data'       => $data,
            'transit_ip' => $transitIp,
        ];

	    return Keep::authParser(Support::requestApi('/payment/api', $post, $payload['order_id']));
    }
}