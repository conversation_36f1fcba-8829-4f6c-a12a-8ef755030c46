<?php

namespace App\Classes\Pay\Gateways\Keep;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Parser\Keep;
use App\Models\Order;

class RefundGateway extends Gateway
{
	/**
	 * Pay an order.
	 *
	 * @param string $endpoint
	 *
	 * @param array $payload
	 * @return Collection
	 */
	public function pay($endpoint, array $payload)
	{
		$payConfig     = $payload['pay_config'];
		$paymentOrder  = $payload['payment_order'];
		$paymentRefund = $payload['payment_refund'];

		$data = [
			'merchantNo' => $payConfig['merchantNo'],
			'terminalNo' => $payConfig['terminalNo'],
			'tradeNo'    => $paymentOrder['payment_order_id'],
			'currency'   => $paymentRefund['currency'],
		];

		$signStr           = implode('', $data) . $payConfig['signKey'] . 'SHA256UTF8';
		$data['signature'] = Support::signature($signStr);
		$part              = '2'; //1部分退款，2全额退款
		$amount            = Order::query()->where('order_id', $payload['order_id'])->value('amount');
		if ($payload['amount'] < $amount) {
			$part = '1';
		}

		$data['part']   = $part;
		$data['amount'] = Support::amount_format($paymentRefund['amount']);

		// 如果配置有中转配置参数，则处理中转网址和跳转/通知地址
		$transitIp = '';
        if (!empty($payConfig['transitIp'])) {
            $transitIp = $payConfig['transitIp'] . '?' . Support::urlPayment($payConfig);
        }

        $post = [
			'data'       => $data,
			'transit_ip' => $transitIp,
        ];

		return Keep::refundParser(Support::requestApi('/api/refund', $post, $payload['refund_id']));
	}
}
