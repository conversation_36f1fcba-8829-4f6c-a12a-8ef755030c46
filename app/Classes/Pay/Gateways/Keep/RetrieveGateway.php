<?php

namespace App\Classes\Pay\Gateways\Keep;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Parser\Keep;

class RetrieveGateway extends Gateway
{
	/**
	 * Pay an order.
	 *
	 * @param string $endpoint
	 *
	 * @param array $payload
	 * @return Collection
	 */
	public function pay($endpoint, array $payload): Collection
	{
		$payConfig    = $payload['pay_config'];
		$paymentOrder = $payload['payment_order'];

		$data = [
			'merchantNo' => $payConfig['merchantNo'],
			'terminalNo' => $payConfig['terminalNo'],
			'orderNo'    => $paymentOrder['order_id'],
		];

		$signStr           = implode('', $data) . $payConfig['signKey'] . 'SHA256UTF8';
		$data['signature'] = Support::signature($signStr);

        // 如果配置有中转配置参数，则处理中转网址和跳转/通知地址
		$transitIp = '';
        if (!empty($payConfig['transitIp'])) {
            $transitIp = $payConfig['transitIp'] . '?' . Support::urlPayment($payConfig);
        }

        $post = [
			'data'       => $data,
			'transit_ip' => $transitIp,
        ];

		return Keep::retrieveParser(Support::requestApi('/api/query-order', $post, $payload['order_id']));
	}
}
