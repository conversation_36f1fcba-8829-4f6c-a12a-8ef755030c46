<?php

namespace App\Classes\Pay\Gateways\Brad;

use App\Classes\Supports\Collection;
use Illuminate\Support\Facades\Cache;
use App\Classes\Pay\Parser\Brad;
use App\Models\Merchant;

class AuthGateway extends Gateway
{
    /**
     * 卡种数据
     *
     * @var array
     */
    protected $_cardData = [
        'V' => 'VISA',
        'M' => 'MASTERCARD',
        'J' => 'JCB',
        'A' => 'AE',
        'D' => 'DC',
    ];

    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig   = $payload['pay_config'];
        $payOrder    = $payload['payment_order'];
        $payAddress  = $payload['address'];
        $payCard     = $payload['card'];
        $payProducts = $payload['products'];

        //缓存key
        $mark    = MD5($payOrder['order_id'] . 'brad');
        $markArr = [
            'order_id'   => $payOrder['order_id'],
            'class'      => 'Brad',
            'channel_id' => $payload['channel_id'],
        ];
        Cache::add($mark, $markArr, 24 * 60 * 60);

        //商品信息
        $product = [];
        if (!empty($payProducts)) {
            $tempProducts = reset($payProducts);
            $product      = [
                'name'      => $tempProducts['name'],
                'qty'       => $tempProducts['qty'],
                'price'     => $tempProducts['price'],
                'sku'       => $tempProducts['sku'],
                'url'       => $tempProducts['url'],
                'isVirtual' => 0,
            ];
        }

        //卡信息
        $card = [
            'cardNumber' => $payCard['card_number'],
            'cardMonth'  => $payCard['expiration_month'],
            'cardYear'   => $payCard['expiration_year'],
            'cardCvv'    => $payCard['cvv'],
        ];

        //地址信息
        $address = [
            'firstName'       => $payAddress['bill_first_name'],
            'lastName'        => $payAddress['bill_last_name'],
            'email'           => $payAddress['bill_email'],
            'address'         => $payAddress['bill_address'],
            'city'            => $payAddress['bill_city'],
            'state'           => $payAddress['bill_state'],
            'postCode'        => $payAddress['bill_postcode'],
            'countryIsoa'     => $payAddress['bill_country_isoa2'],
            'phone'           => $payAddress['bill_phone'],
            'shipFirstName'   => $payAddress['ship_first_name'],
            'shipLastName'    => $payAddress['ship_last_name'],
            'shipEmail'       => $payAddress['ship_email'],
            'shipAddress'     => $payAddress['ship_address'],
            'shipCity'        => $payAddress['ship_city'],
            'shipState'       => $payAddress['ship_state'],
            'shipPostCode'    => $payAddress['ship_postcode'],
            'shipCountryIsoa' => $payAddress['ship_country_isoa2'],
            'shipPhone'       => $payAddress['ship_phone'],
            'ip'              => $payAddress['ip']
        ];

        $data = [
            'mchNo'      => $payConfig['mchNo'],
            'productNo'  => $payConfig['productNo'],
            'mchOrderNo' => $payload['order_id'],
            'reqTime'    => time(),
            'wayCode'    => $this->_cardData[$payCard['cc_type']] ?? $payCard['cc_type'],
            'currency'   => $payOrder['currency'],
            'amount'     => Support::amount_format($payOrder['amount']),
            'returnUrl'  => route('api.v1.syncNotify', ['mark' => 'brad'], true),
            'notifyUrl'  => route('api.v1.asyncNotify', ['mark' => 'brad'], true),
            'website'    => $payConfig['shellUrl'],
            'product'    => json_encode($product, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE),
            'card'       => json_encode($card, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE),
            'address'    => json_encode($address, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE),
        ];

        // 如果配置有中转配置参数，则处理中转网址和跳转/通知地址
        $transitIp = '';
        if (!empty($payConfig['transitUrl']) && !empty($payConfig['transitIp'])) {
            $transitIp         = $payConfig['transitIp'] . '?' . Support::urlPayment($payConfig);
            $data['returnUrl'] = $payConfig['transitUrl'] . '/Sync.php?' . Support::urlPayment($payConfig, ['mark' => 'brad']);
            $data['notifyUrl'] = $payConfig['transitUrl'] . '/Async.php?' . Support::urlPayment($payConfig, ['mark' => 'brad']);
        }

        //数据签名
        $sign         = Support::sign($data, $payConfig['key']);
        $data['sign'] = $sign;

        //组装请求头
        $header = [
            'Content-Type' => 'application/x-www-form-urlencoded;charset=utf-8',
        ];

        $timeOut = 0; // 请求限制时间
        if (in_array($payload['merchant_id'], Merchant::$specialMerchant)) {
            $key  = 'Order_Execution_Time_' . $payload['order_id'];
            $time = Cache::get($key);
            if ($time) {
                $endTime       = microtime(true);          // 当前时间
                $executionTime = 14 - (int)($endTime - $time);  // 计算执行时间
                if ($executionTime > 0) {
                    $timeOut = $executionTime;
                }
            }
        }

        $post = [
            'data'       => $data,
            'header'     => $header,
            'transit_ip' => $transitIp,
            'time_out'   => $timeOut,
        ];

        $result = Support::requestApi('/vmaster/v1/orders', $post, $payload['order_id']);
        // 把MID和order_id塞进返回数据
        $result['merchant_id'] = $payload['merchant_id'];
        $result['order_id']    = $payload['order_id'];

        return Brad::authParser($result);
    }
}
