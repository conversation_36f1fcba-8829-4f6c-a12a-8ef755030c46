<?php

namespace App\Classes\Pay\Gateways\Brad;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Parser\Brad;
use App\Services\RateLimiterServices;

class RetrieveGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig = $payload['pay_config'];

        //请求信息
        $data = [
            'mchNo'      => $payConfig['mchNo'],
            'mchOrderNo' => $payload['order_id']
        ];

        //数据签名
        $sign         = Support::sign($data, $payConfig['key']);
        $data['sign'] = $sign;

        //组装请求头
        $header = [
            'Content-Type' => 'application/x-www-form-urlencoded;charset=utf-8',
        ];

        // 如果配置有中转配置参数，则处理中转网址和跳转/通知地址
        $transitIp = '';
        if (!empty($payConfig['transitIp'])) {
            $transitIp = $payConfig['transitIp'] . '?' . Support::urlPayment($payConfig);
        }

        $post = [
            'data'       => $data,
            'header'     => $header,
            'transit_ip' => $transitIp
        ];

        RateLimiterServices::canMakeRequest('brad');
        return Brad::retrieveParser(Support::requestApi('/vmaster/v1/query/pay/order', $post, $payload['order_id']));
    }
}
