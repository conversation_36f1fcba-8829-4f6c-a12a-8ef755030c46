<?php

namespace App\Classes\Pay\Gateways;

use App\Classes\Pay\Contracts\Gateway;
use App\Classes\Pay\Gateways\Goodiespay\Support;
use App\Classes\Supports\Config;

class Goodiespay extends Gateway
{
    protected $gatewayName = 'Goodiespay';

    /**
     * Const url.
     */
    const URL = [
        self::MODE_NORMAL => 'https://pay.goodiespay.com/',
        self::MODE_DEV    => 'https://test.goodiespay.com/',
    ];

    public function __construct(Config $config)
    {
        $this->gateway = Support::create($config)->getBaseUri();
        parent::__construct($config);
    }
}
