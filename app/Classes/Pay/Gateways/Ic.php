<?php

namespace App\Classes\Pay\Gateways;

use App\Classes\Pay\Contracts\Gateway;
use App\Classes\Pay\Gateways\Ic\Support;
use App\Classes\Supports\Config;

class Ic extends Gateway
{
    protected $gatewayName = 'Ic';

    /**
     * Const url.
     */
    const URL = [
        self::MODE_NORMAL => 'https://pay.quanyuwenlv.com/border/apis',
        self::MODE_DEV    => 'https://pay.quanyuwenlv.com/border/apis',
    ];

    public function __construct(Config $config)
    {
        $this->gateway = Support::create($config)->getBaseUri();
        parent::__construct($config);
    }
}
