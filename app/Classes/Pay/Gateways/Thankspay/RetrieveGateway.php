<?php

namespace App\Classes\Pay\Gateways\Thankspay;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Thankspay;

class RetrieveGateway extends Gateway
{
	/**
	 * Pay an order.
	 *
	 * @param string $endpoint
	 *
	 * @param array $payload
	 * @return Collection
	 */
	public function pay($endpoint, array $payload): Collection
	{
		$payConfig    = $payload['pay_config'];
		$paymentOrder = $payload['payment_order'];

		$data = array(
			'merchantNo'    => $payConfig['merchantNo'],
			'terminalNo'    => $payConfig['terminalNo'],
			'orderNo'       => $paymentOrder['order_id'],
		);

		$signStr               = implode('', $data) . $payConfig['signKey'] . 'SHA256UTF8';
		$data['securityValue'] = hash('sha256',$signStr);

		return Thankspay::retrieveParser(Support::requestApi('api/query', $data, $payload['order_id']));
	}
}
