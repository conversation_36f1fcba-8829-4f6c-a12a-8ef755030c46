<?php

namespace App\Classes\Pay\Gateways\Thankspay;

use App\Classes\Supports\Collection;
use Illuminate\Support\Facades\Cache;
use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Thankspay;

class AuthGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
	    $payConfig = $payload['pay_config'];
	    $address   = $payload['address'];
	    $card      = $payload['card'];
	    $products  = $payload['products'];

        // 组装支付数据
	    $productInfo = array();

        if (!empty($products)) {
        	foreach ($products as $product) {
		        $productInfo['data'][] = array(
			        'itemName' => (strlen($product['name']) > 50) ? mb_strcut($product['name'], 0, 50) : $product['name'],
			        'price'    => $product['price'],
			        'quantity' => $product['qty'],
		        );
	        }
        }

	    // 缓存key
	    $mark    = MD5($payload['order_id'] . 'thankspay');
	    $markArr = [
		    'order_id' => $payload['order_id'],
		    'class'    => 'Thankspay',
	    ];
	    Cache::add($mark, $markArr, 24 * 60 * 60);

	    $data = array(
		    'orderNo'     => $payload['order_id'], //订单id
		    'payIp'       => $address['ip'],
		    'amount'      => Support::amount_format($payload['payment_order']['amount']),
		    'currency'    => $payload['payment_order']['currency'], //币种类型
		    'merchantNo'  => $payConfig['merchantNo'],
		    'terminalNo'  => $payConfig['terminalNo'],
		    'payType'     => '1',
		    'serviceType' => '0',
		    'payMethod'   => '0',
	    );

	    // 组装签名
	    $signStr               = implode('', $data) . $payConfig['signKey'];
	    $data['securityValue'] = hash('sha256',$signStr);

	    $post = array(
		    'merchantNo'      => $data['merchantNo'],
		    'terminalNo'      => $data['terminalNo'],
		    'orderNo'         => $data['orderNo'],
		    'amount'          => $data['amount'],
		    'currency'        => $data['currency'],
		    'securityValue'   => $data['securityValue'],
		    'returnURL'       =>  \route('api.v1.syncNotify', ['mark' => $mark], true), //服务端同步回调地址
		    'asynReturnURL'   =>  \route('api.v1.asyncNotify', ['mark' => $mark], true), //服务端回调地址
		    'goodsJson'       => json_encode($productInfo),
		    'website'         => $payConfig['urlName'] ?? $payload['url_name'],
		    'payIp'           => $data['payIp'],
		    'language'        => 'en_US',
		    'remark'          => '',
		    'payType'         => $data['payType'],
		    'serviceType'     => $data['serviceType'],
		    'payMethod'       => $data['payMethod'],
		    'cardNo'          => $card['card_number'],
		    'cvv'             => $card['cvv'],
		    'expirationYear'  => '20' . $card['expiration_year'],
		    'expirationMonth' => $card['expiration_month'],
		    'billFirstName'   => $address['bill_first_name'],
		    'billLastName'    => $address['bill_last_name'],
		    'billAddress'     => (strlen($address['bill_address']) > 100) ? mb_strcut($address['bill_address'], 0, 100) : $address['bill_address'],
		    'billCity'        => $address['bill_city'],
		    'billState'       => $address['bill_state'],
		    'billCountry'     => $address['bill_country_isoa2'],
		    'billZip'         => $address['bill_postcode'],
		    'shipFirstName'   => $address['ship_first_name'],
		    'shipLastName'    => $address['ship_last_name'],
		    'shipAddress'     => (strlen($address['ship_address']) > 100) ? mb_strcut($address['ship_address'], 0, 100) : $address['ship_address'],
		    'shipCity'        => $address['ship_city'],
		    'shipState'       => $address['ship_state'],
		    'shipCountry'     => $address['ship_country_isoa2'],
		    'shipZip'         => $address['ship_postcode'],
		    'phone'           => $address['bill_phone'],
		    'email'           => $address['bill_email'],
		    'userAgent'       => $_SERVER['HTTP_USER_AGENT'] ?? 'HTTP_USER_AGENT',
		    'acceptLanguage'  => $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? 'zh-CN,zh;q=0.9',
	    );

	    return Thankspay::authParser(Support::requestApi('payment/api', $post, $payload['order_id']));
    }
}
