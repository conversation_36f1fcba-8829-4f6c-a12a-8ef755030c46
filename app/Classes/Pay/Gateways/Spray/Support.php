<?php

namespace App\Classes\Pay\Gateways\Spray;

use App\Classes\Pay\Events;
use App\Classes\Pay\Gateways\Spray;
use App\Classes\Pay\Log;
use App\Classes\Supports\Collection;
use App\Classes\Supports\Config;
use App\Classes\Pay\Contracts\Support as BaseSupport;

/**
 * @property array http http options
 * @property string mode current mode
 * @property array log log options
 * @property string sign_key
 */
class Support
{
    use BaseSupport;

    /**
     * Instance.
     *
     * @var Support
     */
    private static $instance;

    private static $SM4_CK = [
        0x00070e15, 0x1c232a31, 0x383f464d,
        0x545b6269, 0x70777e85, 0x8c939aa1, 0xa8afb6bd, 0xc4cbd2d9,
        0xe0e7eef5, 0xfc030a11, 0x181f262d, 0x343b4249, 0x50575e65,
        0x6c737a81, 0x888f969d, 0xa4abb2b9, 0xc0c7ced5, 0xdce3eaf1,
        0xf8ff060d, 0x141b2229, 0x30373e45, 0x4c535a61, 0x686f767d,
        0x848b9299, 0xa0a7aeb5, 0xbcc3cad1, 0xd8dfe6ed, 0xf4fb0209,
        0x10171e25, 0x2c333a41, 0x484f565d, 0x646b7279
    ];

    private static $SM4_SBOX = [
        0xd6, 0x90, 0xe9, 0xfe, 0xcc, 0xe1, 0x3d, 0xb7, 0x16, 0xb6, 0x14, 0xc2, 0x28, 0xfb, 0x2c, 0x05,
        0x2b, 0x67, 0x9a, 0x76, 0x2a, 0xbe, 0x04, 0xc3, 0xaa, 0x44, 0x13, 0x26, 0x49, 0x86, 0x06, 0x99,
        0x9c, 0x42, 0x50, 0xf4, 0x91, 0xef, 0x98, 0x7a, 0x33, 0x54, 0x0b, 0x43, 0xed, 0xcf, 0xac, 0x62,
        0xe4, 0xb3, 0x1c, 0xa9, 0xc9, 0x08, 0xe8, 0x95, 0x80, 0xdf, 0x94, 0xfa, 0x75, 0x8f, 0x3f, 0xa6,
        0x47, 0x07, 0xa7, 0xfc, 0xf3, 0x73, 0x17, 0xba, 0x83, 0x59, 0x3c, 0x19, 0xe6, 0x85, 0x4f, 0xa8,
        0x68, 0x6b, 0x81, 0xb2, 0x71, 0x64, 0xda, 0x8b, 0xf8, 0xeb, 0x0f, 0x4b, 0x70, 0x56, 0x9d, 0x35,
        0x1e, 0x24, 0x0e, 0x5e, 0x63, 0x58, 0xd1, 0xa2, 0x25, 0x22, 0x7c, 0x3b, 0x01, 0x21, 0x78, 0x87,
        0xd4, 0x00, 0x46, 0x57, 0x9f, 0xd3, 0x27, 0x52, 0x4c, 0x36, 0x02, 0xe7, 0xa0, 0xc4, 0xc8, 0x9e,
        0xea, 0xbf, 0x8a, 0xd2, 0x40, 0xc7, 0x38, 0xb5, 0xa3, 0xf7, 0xf2, 0xce, 0xf9, 0x61, 0x15, 0xa1,
        0xe0, 0xae, 0x5d, 0xa4, 0x9b, 0x34, 0x1a, 0x55, 0xad, 0x93, 0x32, 0x30, 0xf5, 0x8c, 0xb1, 0xe3,
        0x1d, 0xf6, 0xe2, 0x2e, 0x82, 0x66, 0xca, 0x60, 0xc0, 0x29, 0x23, 0xab, 0x0d, 0x53, 0x4e, 0x6f,
        0xd5, 0xdb, 0x37, 0x45, 0xde, 0xfd, 0x8e, 0x2f, 0x03, 0xff, 0x6a, 0x72, 0x6d, 0x6c, 0x5b, 0x51,
        0x8d, 0x1b, 0xaf, 0x92, 0xbb, 0xdd, 0xbc, 0x7f, 0x11, 0xd9, 0x5c, 0x41, 0x1f, 0x10, 0x5a, 0xd8,
        0x0a, 0xc1, 0x31, 0x88, 0xa5, 0xcd, 0x7b, 0xbd, 0x2d, 0x74, 0xd0, 0x12, 0xb8, 0xe5, 0xb4, 0xb0,
        0x89, 0x69, 0x97, 0x4a, 0x0c, 0x96, 0x77, 0x7e, 0x65, 0xb9, 0xf1, 0x09, 0xc5, 0x6e, 0xc6, 0x84,
        0x18, 0xf0, 0x7d, 0xec, 0x3a, 0xdc, 0x4d, 0x20, 0x79, 0xee, 0x5f, 0x3e, 0xd7, 0xcb, 0x39, 0x48
    ];

    private static $SM4_FK = [0xA3B1BAC6, 0x56AA3350, 0x677D9197, 0xB27022DC];

    private static $_rk = [];
    private static $_block_size = 16;

    /**
     * Bootstrap.
     *
     * @param Config $config
     */
    public function __construct(Config $config)
    {
        self::$gatewayName = 'Spray';
        $this->baseUri     = Spray::URL[$config->get('mode', Spray::MODE_NORMAL)];
        $this->config      = $config;
        $this->setHttpOptions();
    }

    /**
     * Get API result.
     *
     * @param string $endpoint
     * @param array $data
     * @param string $mode
     * @param string $requestId
     * @return Collection
     */
    public static function requestApi(string $endpoint, array $data, $mode, string $requestId = '' ): Collection
    {
        Events::dispatch(new Events\ApiRequesting(self::$gatewayName, '', self::$instance->getBaseUri() . $endpoint, $data));
        
        Events::dispatch(new Events\SlsLog(
            self::$gatewayName, 
            'Requesting To Api',
            [self::$instance->getBaseUri() . $endpoint, $data], 
            $requestId
        ));

        $result = [];

        try {
            if ($mode == 'Pay') {
                $result = self::$instance->post($endpoint, $data);
            }

            if ($mode == 'Refund' || $mode == 'Retrieve') {
                $result = self::$instance->post($endpoint, [], ['json' => $data]);
            }
            
            $result = is_string($result) ? json_decode($result, true) ?? ['errorMsg' => $result] : $result;
        } catch (\Exception $e) {
            // 记录日志
            Log::error($e->getMessage());
            Events::dispatch(new Events\SlsLog(
                self::$gatewayName, 
                $e->getMessage(),
                [], 
                $requestId,
                'error'
            ));
        }

        Events::dispatch(new Events\ApiRequested(self::$gatewayName, '', self::$instance->getBaseUri() . $endpoint, $result));
        Events::dispatch(new Events\SlsLog(
            self::$gatewayName, 
            'Result Of Api',
            $result, 
            $requestId
        ));

        return self::processingApiResult($result);
    }

    public static function createSign(string $data, string $salt): string
    {

        $salt = "-----BEGIN PRIVATE KEY-----\n" .
            wordwrap($salt, 64, "\n", true) .
            "\n-----END PRIVATE KEY-----";

        openssl_sign(utf8_encode($data), $binarySignature, $salt, OPENSSL_ALGO_SHA256);
        return base64_encode($binarySignature);
    }

    /**
     * 银行卡加密
     * @return string
     */
    public static function createCardData($key, $str)
    {
        return self::encrypt($key, $str);
    }

    public static function encrypt($key, $data)
    {
        self::sM4KeySchedule($key);

        $bytes  = self::pad($data, self::$_block_size);
        $chunks = array_chunk($bytes, self::$_block_size);

        $ciphertext = "";
        foreach ($chunks as $chunk) {
            $ciphertext .= self::sM4Encrypt($chunk);
        }

        return base64_encode($ciphertext);
    }

    private static function sM4Encrypt($plainText)
    {
        $x = [];
        for ($j = 0; $j < 4; $j++) {
            $x[$j] = ($plainText[$j * 4] << 24) | ($plainText[$j * 4 + 1] << 16) | ($plainText[$j * 4 + 2] << 8) | ($plainText[$j * 4 + 3]);
        }

        for ($i = 0; $i < 32; $i++) {
            $tmp       = $x[$i + 1] ^ $x[$i + 2] ^ $x[$i + 3] ^ self::$_rk[$i];
            $buf       = (self::$SM4_SBOX[($tmp >> 24) & 0xFF]) << 24 | (self::$SM4_SBOX[($tmp >> 16) & 0xFF]) << 16 | (self::$SM4_SBOX[($tmp >> 8) & 0xFF]) << 8 | (self::$SM4_SBOX[$tmp & 0xFF]);
            $x[$i + 4] = $x[$i] ^ ($buf ^ self::sm4Rotl32(($buf), 2) ^ self::sm4Rotl32(($buf), 10) ^ self::sm4Rotl32(($buf), 18) ^ self::sm4Rotl32(($buf), 24));
        }

        $cipherText = [];
        for ($k = 0; $k < 4; $k++) {
            $cipherText[4 * $k]     = ($x[35 - $k] >> 24) & 0xFF;
            $cipherText[4 * $k + 1] = ($x[35 - $k] >> 16) & 0xFF;
            $cipherText[4 * $k + 2] = ($x[35 - $k] >> 8) & 0xFF;
            $cipherText[4 * $k + 3] = ($x[35 - $k]) & 0xFF;
        }

        return self::bytesToString($cipherText);
    }

    private static function stringToBytes($string)
    {
        return unpack('C*', $string);
    }

    private static function bytesToString($bytes)
    {
        return vsprintf(str_repeat('%c', count($bytes)), $bytes);
    }

    private static function pad($data)
    {
        $bytes = self::stringToBytes($data);
        $rem   = self::$_block_size - count($bytes) % self::$_block_size;
        for ($i = 0; $i < $rem; $i++) {
            array_push($bytes, $rem);
        }
        return $bytes;
    }

    private static function sm4Rotl32($buf, $n)
    {
        return (($buf << $n) & 0xffffffff) | ($buf >> (32 - $n));
    }

    private static function sM4KeySchedule($key)
    {
        self::$_rk = [];
        $key       = array_values(unpack("C*", $key));

        $k = [];
        for ($i = 0; $i < 4; $i++) {
            $k[$i] = self::$SM4_FK[$i] ^ (($key[4 * $i] << 24) | ($key[4 * $i + 1] << 16) | ($key[4 * $i + 2] << 8) | ($key[4 * $i + 3]));
        }

        for ($j = 0; $j < 32; $j++) {
            $tmp = $k[$j + 1] ^ $k[$j + 2] ^ $k[$j + 3] ^ self::$SM4_CK[$j];
            $buf = (self::$SM4_SBOX[($tmp >> 24) & 0xFF]) << 24 | (self::$SM4_SBOX[($tmp >> 16) & 0xFF]) << 16 | (self::$SM4_SBOX[($tmp >> 8) & 0xFF]) << 8 | (self::$SM4_SBOX[$tmp & 0xFF]);

            $k[$j + 4]     = $k[$j] ^ (($buf) ^ (self::sm4Rotl32(($buf), 13)) ^ (self::sm4Rotl32(($buf), 23)));
            self::$_rk[$j] = $k[$j + 4];
        }
    }
}
