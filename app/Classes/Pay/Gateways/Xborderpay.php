<?php

namespace App\Classes\Pay\Gateways;

use App\Classes\Pay\Contracts\Gateway;
use App\Classes\Pay\Gateways\Xborderpay\Support;
use App\Classes\Supports\Config;

class Xborderpay extends Gateway
{
    protected $gatewayName = 'Xborderpay';

    /**
     * Const url.
     */
    const URL = [
        self::MODE_NORMAL => 'https://live-xapi.xborderpay.com/payment/interface/do',
        self::MODE_DEV    => 'https://test-xapi.xborderpay.com/payment/interface/do',
    ];

    public function __construct(Config $config)
    {
        $this->gateway = Support::create($config)->getBaseUri();
        parent::__construct($config);
    }
}
