<?php

namespace App\Classes\Pay\Gateways\Authorize;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Authorize;

class AuthGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig  = $payload['pay_config'];
        $payAddress = $payload['address'];

        // 组装支付数据
        $amount = Support::amount_format($payload['payment_order']['amount']);

        $data = [
            "createTransactionRequest" => [
                "merchantAuthentication" => [
                    "name"           => $payConfig['name'],
                    "transactionKey" => $payConfig['transaction_key']
                ],
                "refId"                  => $payload['order_id'],
                "transactionRequest"     => [
                    "transactionType"            => "authCaptureTransaction",
                    "amount"                     => $amount,
                    "currencyCode"               => $payload['payment_order']['currency'],
                    "payment"                    => [
                        "creditCard" => [
                            "cardNumber"     => $payload['card']['card_number'],
                            "expirationDate" => '20' . $payload['card']['expiration_year'] . '-' . $payload['card']['expiration_month'],
                            "cardCode"       => $payload['card']['cvv'],
                        ]
                    ],
                    "billTo"                     => [
                        "firstName" => $payAddress['bill_first_name'],
                        "lastName"  => $payAddress['bill_last_name'],
                        "address"   => $payAddress['bill_address'],
                        "city"      => $payAddress['bill_city'],
                        "state"     => $payAddress['bill_state'],
                        "zip"       => $payAddress['bill_postcode'],
                        "country"   => $payAddress['bill_country'],
                    ],
                    "shipTo"                     => [
                        "firstName" => $payAddress['ship_first_name'],
                        "lastName"  => $payAddress['ship_last_name'],
                        "address"   => $payAddress['ship_address'],
                        "city"      => $payAddress['ship_city'],
                        "state"     => $payAddress['ship_state'],
                        "zip"       => $payAddress['ship_postcode'],
                        "country"   => $payAddress['ship_country'],
                    ],
                    "customerIP"                 => $payAddress['ip'],
                ]
            ]
        ];

        // 组装请求头
        $header = array(
            'content-type'   => 'application/json',
        );

        $post = array(
            'data'   => $data,
            'header' => $header,
        );

        return Authorize::authParser(Support::requestApi('', $post, 'post', $payload['order_id']));
    }
}
