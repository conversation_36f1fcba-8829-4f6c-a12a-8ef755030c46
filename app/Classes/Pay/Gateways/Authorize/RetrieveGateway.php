<?php

namespace App\Classes\Pay\Gateways\Authorize;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Authorize;

class RetrieveGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig = $payload['pay_config'];

        // 组装支付数据
        $data = [
            "getTransactionDetailsRequest" => [
                "merchantAuthentication" => [
                    "name"           => $payConfig['name'],
                    "transactionKey" => $payConfig['transaction_key']
                ],
                "transId"                => $payload['payment_order']['payment_order_id']
            ]
        ];


        // 组装请求头
        $header = array(
            'content-type'   => 'application/json',
        );

        $post = array(
            'data'   => $data,
            'header' => $header,
        );

        return Authorize::retrieveParser(Support::requestApi('', $post, 'post', $payload['order_id']));
    }
}
