<?php

namespace App\Classes\Pay\Gateways;

use App\Classes\Pay\Contracts\Gateway;
use App\Classes\Pay\Gateways\Airclick\Support;
use App\Classes\Supports\Config;

class Airclick extends Gateway
{
    protected $gatewayName = 'Airclick';

    /**
     * Const url.
     */
    const URL = [
        self::MODE_NORMAL => '',
        self::MODE_DEV    => 'https://paytest.tapxyz.com/',
    ];

    public function __construct(Config $config)
    {
        $this->gateway = Support::create($config)->getBaseUri();
        parent::__construct($config);
    }
}
