<?php

namespace App\Classes\Pay\Gateways\Airclick;

use App\Classes\Supports\Collection;
use Illuminate\Support\Facades\Cache;
use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Airclick;

class AuthGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig = $payload['pay_config'];
        $address   = $payload['address'];
        $card      = $payload['card'];

        // 缓存key
        $mark    = MD5($payload['order_id'] . 'airclick');
        $markArr = [
            'order_id' => $payload['order_id'],
            'class'    => 'Airclick',
        ];
        Cache::add($mark, $markArr, 24 * 60 * 60);

        // 接收需要MD5加密的9个参数
        $data = array(
          'MerNo'            => $payConfig['MerNo'],
          'OrderNo'          => $payload['order_id'],
          'Amount'           => Support::amount_format($payload['payment_order']['amount']),
          'CurrencyCode'     => $payload['payment_order']['currency'],
          'CardNo'           => $card['card_number'],
          'CardExpireYear'   => '20'.$card['expiration_year'],
          'CardExpireMonth'  => $card['expiration_month'],
          'CardSecurityCode' => $card['cvv'],
          'PrivateKey'       => $payConfig['PrivateKey'],
        );
    
        // MD5加密
        $signStr  = implode('', $data);
        $signInfo = strtoupper(md5($signStr));

        // 组装支付数据
        $post = array(
            'MerNo'             => $data['MerNo'],
            'PrivateKey'        => $data['PrivateKey'],
            'OrderNo'           => $data['OrderNo'],
            'Freight'           => '0',
            'Amount'            => $data['Amount'],
            'CurrencyCode'      => $data['CurrencyCode'],
            'CardNo'            => $data['CardNo'],
            'CardExpireMonth'   => $data['CardExpireMonth'],
            'CardExpireYear'    => $data['CardExpireYear'],
            'CardSecurityCode'  => $data['CardSecurityCode'],
            'BillFirstName'     => $address['bill_first_name'],
            'BillLastName'      => $address['bill_last_name'],
            'BillAddress'       => (strlen($address['bill_address']) > 100) ? mb_strcut($address['bill_address'], 0, 100) : $address['bill_address'],
            'BillCity'          => $address['bill_city'],
            'BillState'         => $address['bill_state'],
            'BillCountry'       => $address['bill_country_isoa2'],
            'BillZip'           => $address['bill_postcode'],
            'BillEmail'         => $address['bill_email'],
            'BillPhone'         => $address['bill_phone'],
            'ShipFirstName'     => $address['ship_first_name'],
            'ShipLastName'      => $address['ship_last_name'],
            'ShipAddress'       => (strlen($address['ship_address']) > 100) ? mb_strcut($address['ship_address'], 0, 100) : $address['ship_address'],
            'ShipCity'          => $address['ship_city'],
            'ShipState'         => $address['ship_state'],
            'ShipCountry'       => $address['ship_country_isoa2'],
            'ShipZip'           => $address['ship_postcode'],
            'ShipEmail'         => $address['bill_email'],
            'ShipPhone'         => $address['bill_phone'],
            'SignInfo'          => $signInfo,
            'IPAddress'         => $address['ip'],
            'ReturnURL'         => \route('api.v1.syncNotify', ['mark' => $mark], true), //服务端同步回调地址,
            'NotifyURL'         => \route('api.v1.asyncNotify', ['mark' => $mark], true), //服务端回调地址,
            'DynamicDescriptor' => $payConfig['cardBill'],
            'Version'           => '2.0',
        );

        return Airclick::authParser(Support::requestApi('payment/Interface2party', $post, $payload['order_id']));
    }
}
