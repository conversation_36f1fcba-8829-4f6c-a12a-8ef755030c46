<?php

namespace App\Classes\Pay\Gateways\Novatti;

use App\Classes\Supports\Collection;
use Illuminate\Support\Facades\Cache;
use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Novatti;

class AuthGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig = $payload['pay_config'];
        $address   = $payload['address'];
        $products  = $payload['products'];
        
        //年份处理
        if (strlen($payload['card']['expiration_year']) == 2) {
            $payload['card']['expiration_year'] = substr(date('Y'), 0, 2) . $payload['card']['expiration_year'];
        }
        
        //组装商品
        $goods     = [];
        $priceTemp = 0;
        foreach ($products as $product) {
            $price     = Support::amount_format($product['price'] * 1000, 0);
            $priceTemp += $price;
            $goods[]   = [
                'ItemName'     => $product['name'],
                'ItemCode'     => $product['sku'],
                'ItemAmount'   => $price,
                'ItemQuantity' => $product['qty'],
                'ItemCategory' => [$product['name']],
            ];
        }

        //计算运费
        $amount         = Support::amount_format($payload['payment_order']['amount'] * 1000, 0);
        $shippingAmount = $amount - $priceTemp;
        
        if ($shippingAmount <= 0) {
            $shippingAmount = '000';
            $shippingMethod = 'Free Shipping';
        } else {
            $shippingMethod = 'Ocean Transport';
        }
        
        $data = [
          'Header' => [
            'TransactionType' => 'OneStepPayment',
            'UserID'          => '',
            'MerchantID'      => $payConfig['merchantId'],
            'Version'         => '2'
          ],
          'Transaction' => [
            'MerchantTxnID'  => $payload['payment_order']['order_id'],
            'Currency'       => $payload['payment_order']['currency'],
            'Method'         => 'CC',
            'Amount'         => $amount,
            'ShippingAmount' => $shippingAmount,
            'ClientType'     => 'desktop_web',
            'ChannelType'    => '07',
            'Language'       => 'en-US',
            'RequestorType'  => 'physical'
          ],
          'BillingAddress' => [
            'CustomerFirstName' => $address['bill_first_name'],
            'CustomerName'      => $address['bill_last_name'],
            'Street1'           => $address['bill_address'],
            'Country'           => $address['bill_country_isoa2'],
            'City'              => $address['bill_city'],
            'Zip'               => $address['bill_postcode'],
            'State'             => $address['bill_state'],
          ],
          'CardDetails' => [
            'CardNumber'      => $payload['card']['card_number'],
            'CardHolder'      => $address['bill_name'],
            'CardExpiryMonth' => $payload['card']['expiration_month'],
            'CardExpiryYear'  => $payload['card']['expiration_year'],
            'CardCVV'         => $payload['card']['cvv'],
          ],
          'Customer' => [
            'EmailAddress'     => $address['ship_email'],
            'ExistingCustomer' => 'guest',
            'CreatedAt'        => date(DATE_ISO8601),
            'CustomerID'       => rand(200000000, 999999999),
            'IPAddress'        => $address['ip'],
            'TelNo'            => $address['ship_phone'],
            'SessionID'        => empty($payload['customer']['session_id']) ? Support::getKey() : $payload['customer']['session_id'],
            'Browser'          => 'Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Trident/5.0)',
          ],
          'ShippingAddress' => [
            'Street1'          => $address['ship_address'],
            'City'             => $address['ship_city'],
            'Country'          => $address['ship_country_isoa2'],
            'CustomerName'     => $address['ship_name'],
            'ShippingPhone'    => $address['ship_phone'],
            'State'            => $address['ship_state'],
            'Zip'              => $address['ship_postcode'],
            'ShippingMethod'   => $shippingMethod,
            'ShippingProvider' => 'TRUE',
          ],
          'SoftDescriptor' => [
            'DescriptorType' => $payConfig['url'] ?? '-'
          ],
          'Item' => $goods,
        ];
        
        // 获取accessToken
        $tokenData = Support::getAccessToken($payConfig, $endpoint);

        // 组装请求头
        $header = [
            'Content-Type'  => 'application/json',
            'Authorization' => 'Bearer ' . $tokenData['access_token'],
        ];
        
        $post = [
          'data'   => $data,
          'header' => $header
        ];

        return Novatti::authParser(Support::requestApi('', $post, 'post', $payload['order_id']));
    }
}
