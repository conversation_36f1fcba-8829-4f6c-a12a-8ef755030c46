<?php

namespace App\Classes\Pay\Gateways\Novatti;

use App\Classes\Pay\Parser\Novatti;
use App\Classes\Supports\Collection;
use App\Classes\Pay\Events;

class CaptureGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        
    }
}
