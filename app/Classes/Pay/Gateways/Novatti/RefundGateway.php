<?php

namespace App\Classes\Pay\Gateways\Novatti;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Novatti;

class RefundGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload)
    {
        $payConfig  = $payload['pay_config'];
        $payRefund  = $payload['payment_refund'];
        $payOrder   = $payload['payment_order'];
 
        $data = [
            'Header' => [
                'TransactionType' => 'RefundPayment',
                'UserID'          => '',
                'MerchantID'      => $payConfig['merchantId'],
                'Version'         => '2'
            ],
            'Transaction' => [
                'MerchantTxnID' => $payOrder['order_id'],
                'Currency'      => $payRefund['currency'],
                'Amount'        => Support::amount_format($payRefund['amount'] * 1000, 0),
            ]
        ];

        // 获取accessToken
        $tokenData = Support::getAccessToken($payConfig, $endpoint);

        // 组装请求头
        $header = [
            'Content-Type'  => 'application/json',
            'Authorization' => 'Bearer ' . $tokenData['access_token'],
        ];
        
        $post = [
          'data'   => $data,
          'header' => $header
        ];

        return Novatti::refundParser(Support::requestApi('', $post, 'post', $payload['refund_id']));
    }
}
