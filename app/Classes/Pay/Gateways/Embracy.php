<?php

namespace App\Classes\Pay\Gateways;

use App\Classes\Pay\Contracts\Gateway;
use App\Classes\Pay\Gateways\Embracy\Support;
use App\Classes\Supports\Config;

class Embracy extends Gateway
{
    protected $gatewayName = 'Embracy';

    /**
     * Const url.
     */
    const URL = [
        self::MODE_NORMAL => 'https://www.embracy.com',
        self::MODE_DEV    => 'https://test.embracy.com',
    ];

    public function __construct(Config $config)
    {
        $this->gateway = Support::create($config)->getBaseUri();
        parent::__construct($config);
    }
}
