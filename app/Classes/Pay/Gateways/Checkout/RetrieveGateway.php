<?php

namespace App\Classes\Pay\Gateways\Checkout;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Parser\Checkout;

class RetrieveGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig = $payload['pay_config'];
        $payOrder  = $payload['payment_order'];
        // 组装请求头
        $post = [
            'Authorization' => "Bearer {$payConfig['secretKey']}",
            'Content-Type' => 'application/json',
        ];

        return Checkout::retrieveParser(Support::requestApi("/payments/{$payOrder['payment_order_id']}", $post, 'get', $payload['order_id']));
    }
}
