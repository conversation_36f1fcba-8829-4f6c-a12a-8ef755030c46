<?php

namespace App\Classes\Pay\Gateways\Checkout;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Parser\Checkout;
use Illuminate\Support\Facades\Cache;

class RefundGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload)
    {
        $payConfig = $payload['pay_config'];
        $payOrder  = $payload['payment_order'];
        $payRefund = $payload['payment_refund'];
        // 缓存key
        $mark    = MD5($payload['refund_id'] . 'checkout');
        $markArr = [
            'order_id'  => $payload['order_id'],
            'refund_id' => $payload['refund_id'],
            'class'     => 'Checkout',
        ];
        Cache::add($mark, $markArr, 30 * 24 * 60 * 60);

        // 组装退款数据
        $data = [
            'reference' => $payload['refund_id'],
            'amount'    => Support::amountHandle($payRefund['amount'], $payRefund['currency']),
        ];

        // 组装请求头
        $header = [
            'Authorization' => "Bearer {$payConfig['secretKey']}",
            'Content-Type' => 'application/json',
        ];

        $post = [
            'json'    => $data,
            'headers' => $header
        ];

        return Checkout::refundParser(Support::requestApi("/payments/{$payOrder['payment_order_id']}/refunds", $post, 'post', $payload['refund_id']));
    }
}
