<?php

namespace App\Classes\Pay\Gateways\Checkout;

use App\Classes\Supports\Collection;
use Illuminate\Support\Facades\Cache;
use App\Classes\Pay\Parser\Checkout;

class AuthGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig = $payload['pay_config'];
        $payOrder  = $payload['payment_order'];
        $payCard   = $payload['card'];

        // 缓存key
        $mark    = MD5($payOrder['order_id'] . 'checkout');
        $markArr = [
            'order_id' => $payOrder['order_id'],
            'class'    => 'Checkout',
        ];
        Cache::add($mark, $markArr, 24 * 60 * 60);
        Cache::add($mark . '_valid', $markArr, 30 * 60);// 3d验证缓存

        //年份处理
        if (strlen($payCard['expiration_year']) == 2) {
            $payCard['expiration_year'] = substr(date('Y'), 0, 2) . $payCard['expiration_year'];
        }

        $data = [
            'source' => [
                'type'         => 'card',
                'number'       => $payCard['card_number'],
                'expiry_month' => $payCard['expiration_month'],
                'expiry_year'  => $payCard['expiration_year'],
                'cvv'          => $payCard['cvv'],
            ],
            'amount'                => Support::amountHandle($payOrder['amount'], $payOrder['currency']),
            'currency'              => $payOrder['currency'],
            'processing_channel_id' => $payConfig['processingChannelId'],
            'reference'             => $payOrder['order_id'],
            '3ds' => [
                'enabled'     => true,
                'attempt_n3d' => true
            ],
            'success_url' => route('api.v1.syncNotify', ['mark' => $mark], true),
            'failure_url' => route('api.v1.syncNotify', ['mark' => $mark], true)
        ];
        
        if (!empty($payConfig['transitUrl'])) {
            $data['success_url'] = $payConfig['transitUrl'] . '/Sync.php?' . Support::urlPayment($payConfig, ['mark' => $mark]);
            $data['failure_url'] = $payConfig['transitUrl'] . '/Sync.php?' . Support::urlPayment($payConfig, ['mark' => $mark]);
        }

        // 组装请求头
        $header = [
            'Authorization' => "Bearer {$payConfig['secretKey']}",
            'Content-Type' => 'application/json',
        ];

        $post = [
            'json'    => $data,
            'headers' => $header
        ];

        return Checkout::authParser(Support::requestApi('/payments', $post, 'post', $payload['order_id']));
    }
}
