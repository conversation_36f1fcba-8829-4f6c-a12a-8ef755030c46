<?php

namespace App\Classes\Pay\Gateways;

use App\Classes\Pay\Contracts\Gateway;
use App\Classes\Pay\Gateways\Kq\Support;
use App\Classes\Supports\Config;

class Kq extends Gateway
{
    protected $gatewayName = 'Kq';

    /**
     * Const url.
     */
    const URL = [
        self::MODE_NORMAL => 'https://umgw.99bill.com/umgw/common/distribute.html',
        self::MODE_DEV    => 'https://sandbox.99bill.com:7445/umgw/common/distribute.html',
    ];

    public function __construct(Config $config)
    {
        $this->gateway = Support::create($config)->getBaseUri();
        parent::__construct($config);
    }
}
