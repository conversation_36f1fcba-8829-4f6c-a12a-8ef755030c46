<?php

namespace App\Classes\Pay\Gateways\Inno;

use App\Classes\Pay\Exceptions\GatewayException;
use App\Classes\Pay\Exceptions\InvalidSignException;
use App\Classes\Pay\Parser\Inno;
use App\Classes\Supports\Collection;
use Ramsey\Uuid\Uuid;

class RetrieveGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     * @throws GatewayException
     * @throws InvalidSignException
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig = $payload['pay_config'];
        $data      = [
            'voucherNo' => $payload['payment_order']['payment_order_id'],
        ];

        $transitIp = '';
        if (!empty($payConfig['transitIp'])) {
            // 有中转网址
            $transitIp = $payConfig['transitIp'] . '?' . Support::urlPayment($payConfig);
        }

        // 生成签名
        $nonceStr = (string)Uuid::uuid4(); // uuid
        $token    = Support::getAccessToken($payConfig, $endpoint);

        // 组装请求头
        $header = [
            'Authorization' => 'Bearer ' . $token['access_token'],
            'Content-Type'  => 'application/json',
            'secretId'      => $payConfig['secretId'],
            'nonceStr'      => $nonceStr,
            'signature'     => Support::sign($data, $nonceStr, $payConfig),
        ];

        // 测试环境
        if ($payConfig['mode'] == 'dev') {
            $header['sandbox'] = 'sandbox';
        }

        $post = [
            'data'      => $data,
            'header'    => $header,
            'transitIp' => $transitIp
        ];

        return Inno::retrieveParser(Support::requestApi('/merchant/retrieve/vouchers/voucher-no', $post, 'post', $payload['order_id']));
    }
}
