<?php

namespace App\Classes\Pay\Gateways\Inno;

use App\Classes\Pay\Exceptions\GatewayException;
use App\Classes\Pay\Exceptions\InvalidSignException;
use App\Classes\Pay\Parser\Inno;
use App\Classes\Supports\Collection;
use Illuminate\Support\Facades\Cache;
use Ramsey\Uuid\Uuid;

class RefundGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     * @throws GatewayException
     * @throws InvalidSignException
     */
    public function pay($endpoint, array $payload)
    {
        $payConfig = $payload['pay_config'];
        $payRefund = $payload['payment_refund'];

        // 缓存key
        $mark    = MD5($payRefund['refund_id'] . 'inno');
        $markArr = [
            'order_id'  => $payload['order_id'],
            'refund_id' => $payRefund['refund_id'],
            'class'     => 'Inno',
        ];

        Cache::add($mark, $markArr, 7 * 24 * 60 * 60);

        // 组装退款数据
        $data = [
            'outOrderNo'    => $payRefund['refund_id'],
            'orderCurrency' => $payRefund['currency'],
            'orderAmount'   => Support::amount_format($payRefund['amount'], 2), // 退款金额
            'paymentMethod' => 'CR01', // 支付方式 CR01：信用卡
            'transType'     => '20', // 交易类型：20：退款
            'reason'        => 'refund', // 退款原因
        ];

        // 中转地址
        $transitIp = '';
        if (!empty($payConfig['transitIp'])) {
            // 有中转网址
            $transitIp = $payConfig['transitIp'] . '?' . Support::urlPayment($payConfig);
        }

        // 生成签名
        $nonceStr = (string)Uuid::uuid4(); // uuid
        $token    = Support::getAccessToken($payConfig, $endpoint);

        // 组装请求头
        $header = [
            'Authorization' => 'Bearer ' . $token['access_token'],
            'Content-Type'  => 'application/json',
            'secretId'      => $payConfig['secretId'],
            'nonceStr'      => $nonceStr,
            'signature'     => Support::sign($data, $nonceStr, $payConfig),
        ];

        // 测试环境
        if ($payConfig['mode'] == 'dev') {
            $header['sandbox'] = 'sandbox';
        }

        $post = [
            'data'      => $data,
            'header'    => $header,
            'transitIp' => $transitIp
        ];

        return Inno::refundParser(Support::requestApi('/transactions/refunds/' . $payload['payment_order']['payment_order_id'], $post, 'post', $payload['refund_id']));
    }
}
