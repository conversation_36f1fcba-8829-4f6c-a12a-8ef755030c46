<?php

namespace App\Classes\Pay\Gateways\Inno;

use App\Classes\Pay\Contracts\Support as BaseSupport;
use App\Classes\Pay\Events;
use App\Classes\Pay\Exceptions\GatewayException;
use App\Classes\Pay\Exceptions\InvalidSignException;
use App\Classes\Pay\Gateways\Inno;
use App\Classes\Pay\Log;
use App\Classes\Supports\Collection;
use App\Classes\Supports\Config;
use Exception;
use Illuminate\Support\Facades\Cache;

/**
 * @property array http  http options
 * @property string mode current mode
 * @property array log   log options
 * @property string sign_key
 */
class Support
{
    use BaseSupport;

    /**
     * Instance.
     *
     * @var Support
     */
    private static $instance;

    /**
     * Bootstrap.
     *
     * @param Config $config
     */
    public function __construct(Config $config)
    {
        self::$gatewayName = 'Inno';
        $this->baseUri     = Inno::URL[$config->get('mode', Inno::MODE_NORMAL)];
        $this->config      = $config;
        $this->setHttpOptions();
    }

    /**
     * Get API result.
     *
     * @param string $endpoint
     * @param array $data
     * @param $method
     * @param string $requestId
     * @return Collection
     * @throws GatewayException
     * @throws InvalidSignException
     */
    public static function requestApi(string $endpoint, array $data, $method, string $requestId = ''): Collection
    {
        // 设置中转请求渠道地址
        $tempUrl = !empty($data['transitIp']) ? $data['transitIp'] : self::$instance->getBaseUri() . $endpoint;
        $logData = Support::handleLogData($data);
        Events::dispatch(new Events\ApiRequesting(self::$gatewayName, '', $tempUrl, $logData));
        Events::dispatch(new Events\SlsLog(
                             self::$gatewayName,
                             'Requesting To Api',
                             [$tempUrl, $logData],
                             $requestId,
                             'info'
                         ));

        $result = [];
        try {
            if (!empty($data['transitIp'])) {
                // 转发到中转
                $methodArr = [
                    'post' => 'curlPost',
                ];

                $data['embMethod'] = $methodArr[$method];
                $data['doUrl']     = self::$instance->getBaseUri() . $endpoint;

                if (!empty($data['header'])) {
                    $header = [];
                    foreach ($data['header'] as $key => $vo) {
                        $header[] = $key . ':' . $vo;
                    }

                    $data['header'] = $header;
                }

                $result = self::$instance->post($tempUrl, $data);
                if (gettype($result) === 'string') {
                    $result = json_decode($result, true) ?? [];
                }
            } else {
                $result = self::$instance->$method($endpoint, [], ['json' => $data['data'], 'headers' => $data['header']]);
            }
        } catch (Exception $e) {
            // 记录日志
            Log::error($e->getMessage());
            Events::dispatch(new Events\SlsLog(
                                 self::$gatewayName,
                                 $e->getMessage(),
                                 [],
                                 $requestId,
                                 'error'
                             ));
        }

        Events::dispatch(new Events\ApiRequested(self::$gatewayName, '', $tempUrl, $result));
        Events::dispatch(new Events\SlsLog(
                             self::$gatewayName,
                             'Result Of Api',
                             $result,
                             $requestId
                         ));

        return self::processingApiResult($result);
    }

    /**
     * 处理日志数据
     *
     * @param array $data
     * @return array
     */
    public static function handleLogData(array $data): array
    {
        if (!isset($data['data']['extra']['card'])) {
            return $data;
        }

        $card = $data['data']['extra']['card'];
        if (isset($card['cardNumber'], $card['expiryYear'], $card['expiryMonth'], $card['cvvCode'])) {
            $card['cardNumber']  = get_markcard($card['cardNumber']);
            $card['expiryYear']  = get_mark_data($card['expiryYear']);
            $card['expiryMonth'] = get_mark_data($card['expiryMonth']);
            $card['cvvCode']     = get_mark_data($card['cvvCode']);
        }

        $data['data']['extra']['card'] = $card;
        return $data;
    }

    /**
     * MD5签名
     *
     * @param $data
     * @param $nonceStr
     * @param $config
     * @return string
     */
    public static function sign($data, $nonceStr, $config): string
    {
        $str = $config['secretId'] . '|' . $nonceStr . '|' . json_encode($data);
        return base64_encode(hash_hmac('sha256', $str, $config['secretKey'], true));
    }

    /**
     * 获取请求头token
     *
     * @param array $payConfig
     * @param string $endpoint
     * @return array
     */
    public static function getAccessToken(array $payConfig, string $endpoint): array
    {
        $data = [];
        $key  = 'Inno_Access_Token_' . $payConfig['clientId'];
        // 从cache 获取token
        if (!Cache::has($key)) {
            $lockKey = $key . '_Lock';
            $i       = 0;

            // 排队请求 添加有效期防止死锁
            while (!Cache::add($lockKey, 1, 2 * 60)) {
                sleep(1);
                $i++;

                if ($i > 30) {
                    break;
                }
            }

            if (!Cache::has($key)) {
                $data = self::_getToken($key, $payConfig, $endpoint);
            } else {
                $data['access_token'] = Cache::get($key);
            }

            // 解锁
            Cache::forget($lockKey);
        } else {
            $data['access_token'] = Cache::get($key);
        }

        return $data;
    }

    /**
     * 获取token(子方法)
     *
     * @param string $key
     * @param array $payConfig
     * @param string $endpoint
     * @return array
     */
    protected static function _getToken(string $key, array $payConfig, string $endpoint): array
    {
        $result = [
            'access_token' => ''
        ];

        $url = $endpoint . '/access-token';
        try {
            if (!empty($payConfig['transitIp'])) {
                $tempUrl = $payConfig['transitIp'];
                // 转发到中转
                $data['embMethod'] = 'post';
                $data['doUrl']     = self::$instance->getBaseUri() . '/access-token';
                $data['header'][]  = "Authorization:Basic " . base64_encode($payConfig['clientId'] . ':' . $payConfig['clientSecret']);

                $res = self::$instance->post($tempUrl, $data);
                if (gettype($res) === 'string') {
                    $res = json_decode($res, true) ?? [];
                }
            } else {
                $res = self::$instance->post($url, [], ['auth' => [$payConfig['clientId'], $payConfig['clientSecret']]]);
            }

            if (isset($res['access_token'])) {
                Cache::put($key, $res['access_token'], 7190);
                $result['access_token'] = $res['access_token'];
            }
        } catch (Exception $e) {
            // 记录日志
            Log::warning('获取access_token失败，' . json_encode([
                                                                   'clientId'     => $payConfig['clientId'],
                                                                   'clientSecret' => $payConfig['clientSecret'],
                                                                   'tokenUrl'     => $url
                                                               ]));
            Events::dispatch(new Events\SlsLog(
                                 self::$gatewayName,
                                 '获取access_token失败',
                                 [
                                     'clientId'     => $payConfig['clientId'],
                                     'clientSecret' => $payConfig['clientSecret'],
                                     'tokenUrl'     => $url
                                 ],
                                 '',
                                 'warning'
                             ));
        }

        return $result;
    }
}
