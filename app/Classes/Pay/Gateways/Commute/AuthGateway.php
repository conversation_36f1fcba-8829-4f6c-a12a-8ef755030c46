<?php

namespace App\Classes\Pay\Gateways\Commute;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Commute;
use Illuminate\Support\Facades\Cache;

class AuthGateway extends Gateway
{
    /**
     * 州数据
     *
     * @var array
    */
    protected $_stateData = [
        'DELAWARE'                       => 'DE',
        'HAWAII'                         => 'HI',
        'PUERTO RICO'                    => 'PR',
        'TEXAS'                          => 'TX',
        'PALAU'                          => 'PW',
        'MASSACHUSETTS'                  => 'MA',
        'MARYLAND'                       => 'MD',
        'IOWA'                           => 'IA',
        'MAINE'                          => 'ME',
        'IDAHO'                          => 'ID',
        'MARSHALL ISLANDS'               => 'MH',
        'MICHIGAN'                       => 'MI',
        'UTAH'                           => 'UT',
        'ARMED FORCES AMERICAS'          => 'AA',
        'MINNESOTA'                      => 'MN',
        'MISSOURI'                       => 'MO',
        'ILLINOIS'                       => 'IL',
        'NORTHERN MARIANA ISLANDS'       => 'MP',
        'ARMED FORCES EUROPE'            => 'AE',
        'INDIANA'                        => 'IN',
        'MISSISSIPPI'                    => 'MS',
        'MONTANA'                        => 'MT',
        'ALASKA'                         => 'AK',
        'ALABAMA'                        => 'AL',
        'VIRGINIA'                       => 'VA',
        'ARMED FORCES PACIFIC'           => 'AP',
        'ARKANSAS'                       => 'AR',
        'AMERICAN SAMOA'                 => 'AS',
        'VIRGIN ISLANDS'                 => 'VI',
        'NORTH CAROLINA'                 => 'NC',
        'NORTH DAKOTA'                   => 'ND',
        'NEBRASKA'                       => 'NE',
        'RHODE ISLAND'                   => 'RI',
        'ARIZONA'                        => 'AZ',
        'NEW HAMPSHIRE'                  => 'NH',
        'NEW JERSEY'                     => 'NJ',
        'VERMONT'                        => 'VT',
        'NEW MEXICO'                     => 'NM',
        'FLORIDA'                        => 'FL',
        'FEDERATED STATES OF MICRONESIA' => 'FM',
        'NEVADA'                         => 'NV',
        'WASHINGTON'                     => 'WA',
        'NEW YORK'                       => 'NY',
        'SOUTH CAROLINA'                 => 'SC',
        'SOUTH DAKOTA'                   => 'SD',
        'WISCONSIN'                      => 'WI',
        'OHIO'                           => 'OH',
        'GEORGIA'                        => 'GA',
        'OKLAHOMA'                       => 'OK',
        'CALIFORNIA'                     => 'CA',
        'WEST VIRGINIA'                  => 'WV',
        'WYOMING'                        => 'WY',
        'OREGON'                         => 'OR',
        'KANSAS'                         => 'KS',
        'COLORADO'                       => 'CO',
        'GUAM'                           => 'GU',
        'KENTUCKY'                       => 'KY',
        'CONNECTICUT'                    => 'CT',
        'PENNSYLVANIA'                   => 'PA',
        'LOUISIANA'                      => 'LA',
        'TENNESSEE'                      => 'TN',
        'DISTRICT OF COLUMBIA'           => 'DC',

        'ALBERTA'                   => 'AB',
        'BRITISH COLUMBIA'          => 'BC',
        'NOVA SCOTIA'               => 'NS',
        'NORTHWEST TERRITORIES'     => 'NT',
        'NUNAVUT'                   => 'NU',
        'QUEBEC'                    => 'QC',
        'NEW BRUNSWICK'             => 'NB',
        'MANITOBA'                  => 'MB',
        'PRINCE EDWARD ISLAND'      => 'PE',
        'SASKATCHEWAN'              => 'SK',
        'YUKON'                     => 'YT',
        'NEWFOUNDLAND AND LABRADOR' => 'NL',
        'ONTARIO'                   => 'ON',
    ];

    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig   = $payload['pay_config'];
        $payOrder    = $payload['payment_order'];
        $payAddress  = $payload['address'];
        $payCard     = $payload['card'];
        
        // 缓存key
        $mark    = MD5($payOrder['order_id'] . 'commute');
        $markArr = [
            'order_id' => $payOrder['order_id'],
            'class'    => 'Commute',
        ];
        Cache::add($mark, $markArr, 24 * 60 * 60);

        //美国、加拿大地区二字码
        if (in_array($payAddress['bill_country_isoa2'], ['US', 'CA'])) {
            $payAddress['bill_state'] = $this->_stateData[mb_strtoupper($payAddress['bill_state'])] ?? $payAddress['bill_state'];
        }

        if (in_array($payAddress['ship_country_isoa2'], ['US', 'CA'])) {
            $payAddress['ship_state'] = $this->_stateData[mb_strtoupper($payAddress['ship_state'])] ?? $payAddress['ship_state'];
        }

        //年份处理
        if (strlen($payCard['expiration_year']) == 2) {
            $payCard['expiration_year'] = substr(date('Y'), 0, 2) . $payCard['expiration_year'];
        }

        $data = [
            'version'           => 'V2.0.0',
            'mchtId'            => $payConfig['mchtId'],
            'transType'         => 'QuickPay',
            'accessOrderId'     => $payOrder['order_id'],
            'currency'          => $payOrder['currency'],
            'amount'            => Support::amount_format($payOrder['amount']),
            'email'             => $payAddress['bill_email'],
            'signType'          => 'RSA2',
            'cardHolder'        => $payAddress['bill_name'],
            'acctNo'            => $payCard['card_number'],
            'expiryMonth'       => $payCard['expiration_month'],
            'expiryYear'        => $payCard['expiration_year'],
            'acctCvv'           => $payCard['cvv'],
            'productInfo'       => '[]',
            'shippingFirstName' => $payAddress['ship_first_name'],
            'shippingLastName'  => $payAddress['ship_last_name'],
            'shippingAddress1'  => $payAddress['ship_address'],
            'shippingCity'      => $payAddress['ship_city'],
            'shippingState'     => $payAddress['ship_state'],
            'shippingCountry'   => $payAddress['ship_country_isoa2'],
            'shippingZipCode'   => $payAddress['ship_postcode'],
            'shippingPhone'     => $payAddress['ship_phone'],
            'billingFirstName'  => $payAddress['bill_first_name'],
            'billingLastName'   => $payAddress['bill_last_name'],
            'billingAddress1'   => $payAddress['bill_address'],
            'billingCity'       => $payAddress['bill_city'],
            'billingState'      => $payAddress['bill_state'],
            'billingCountry'    => $payAddress['bill_country_isoa2'],
            'billingZipCode'    => $payAddress['bill_postcode'],
            'billingPhone'      => $payAddress['bill_phone'],
            'userAgent'         => '360Brower',
            'ipAddress'         => $payAddress['ip'],
            'panIsPaste'        => 0,
            'notifyUrl'         => \route('api.v1.asyncNotify', ['mark' => $mark], true),
            'returnUrl'         => \route('api.v1.syncNotify', ['mark' => $mark], true),
        ];
        
        //生成签名
        $signature    = Support::sign($data, $payConfig['privateKey']);
        $data['sign'] = $signature;

        // 组装请求头
        $header = [
            'Content-Type' => 'application/x-www-form-urlencoded; charset=UTF-8',
        ];

        $post = [
            'data'   => $data,
            'header' => $header,
        ];

        return Commute::authParser(Support::requestApi("/gateway/cnp/quickpay", $post, 'post', $payload['order_id']));
    }
}
