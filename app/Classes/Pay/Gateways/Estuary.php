<?php

namespace App\Classes\Pay\Gateways;

use App\Classes\Pay\Contracts\Gateway;
use App\Classes\Pay\Gateways\Estuary\Support;
use App\Classes\Supports\Config;

class Estuary extends Gateway
{
    protected $gatewayName = 'Estuary';

    /**
     * Const url.
     */
    const URL = [
        self::MODE_NORMAL => 'https://hiker.icardpay.com',
        self::MODE_DEV    => 'https://intltest.icardpay.com',
    ];

    public function __construct(Config $config)
    {
        $this->gateway = Support::create($config)->getBaseUri();
        parent::__construct($config);
    }
}
