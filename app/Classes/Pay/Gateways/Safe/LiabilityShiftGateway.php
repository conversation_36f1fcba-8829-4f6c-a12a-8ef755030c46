<?php

namespace App\Classes\Pay\Gateways\Safe;

use App\Classes\Supports\Collection;
use Illuminate\Support\Facades\Cache;
use App\Classes\Pay\Parser\Safe;
use App\Models\Order;

class LiabilityShiftGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig   = $payload['pay_config'];
        $payOrder    = $payload['payment_order'];
        $payAddress  = $payload['address'];
        $payCard     = $payload['card'];

        //缓存key
        $mark    = MD5($payOrder['order_id'] . 'safe');
        $markArr = [
            'order_id'   => $payOrder['order_id'],
            'class'      => 'Safe',
            'channel_id' => $payload['channel_id'],
        ];
        Cache::add($mark, $markArr, 24 * 60 * 60);

        // 年份处理
        if (strlen($payCard['expiration_year']) == 2) {
            $payCard['expiration_year'] = substr(date('Y'), 0, 2) . $payCard['expiration_year'];
        }

        // 金额格式化
        $amount = Support::amount_format($payOrder['amount']);

        // 如果配置有中转配置参数，则处理中转网址
        $transitIp = '';
        if (!empty($payConfig['transitIp'])) {
            $transitIp = $payConfig['transitIp'] . '?' . Support::urlPayment($payConfig);
        }

        $token = Support::_getToken($payConfig, $endpoint);
        $data  = [
            'merchantId'      => $payConfig['merchantId'],
            'merchantSiteId'  => $payConfig['merchantSiteId'],
            'sessionToken'    => $token['sessionToken'],
            'currency'        => $payOrder['currency'],
            'amount'          => $amount,
            'clientRequestId' => $payOrder['order_id'],
            'relatedTransactionId' => $payOrder['payment_order_id'],
            'paymentOption'   => [
                'card' => [
                    'cardNumber'      => $payCard['card_number'],
                    'cardHolderName'  => $payAddress['bill_name'],
                    'expirationMonth' => $payCard['expiration_month'],
                    'expirationYear'  => $payCard['expiration_year'],
                    'CVV'             => $payCard['cvv'],
                ],
            ],
            'deviceDetails' => [
                'ipAddress' => $payAddress['ip'],
            ],
            'billingAddress' => [
                'firstName' => $payAddress['bill_first_name'],
                'lastName'  => $payAddress['bill_last_name'],
                'email'     => $payAddress['bill_address'],
                'country'   => $payAddress['bill_country_isoa2'],
            ],
            'urlDetails' => [
                'notificationUrl' => route('api.v1.asyncNotify', ['mark' => $mark], true),
            ],
            'timeStamp' => get_carbon()->format('YmdHis'),
        ];

        // 如果配置有中转配置参数，则处理跳转/通知地址
        if (!empty($payConfig['transitUrl'])) {
            $data['urlDetails']['notificationUrl'] = $payConfig['transitUrl'] . '/Async.php?' . Support::urlPayment($payConfig, ['mark' => $mark]);
        }

        // 数据签名
        $str              = $data['merchantId'] . $data['merchantSiteId'] . $data['clientRequestId'] . $data['amount'] . $data['currency'] . $data['timeStamp'] . $payConfig['merchantSecretKey'];
        $sign             = Support::sign($str);
        $data['checksum'] = $sign;

        // 组装请求头
        $header = array(
            'Content-Type' => 'application/json',
        );

        //组装请求头
        $post = [
            'data'       => $data,
            'header'     => $header,
            'transit_ip' => $transitIp,
        ];

        $result = Support::requestApi('/payment.do', $post, $payload['order_id']);
        return Safe::liabilityShiftParser($result);
    }
}
