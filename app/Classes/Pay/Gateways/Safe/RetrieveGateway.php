<?php

namespace App\Classes\Pay\Gateways\Safe;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Parser\Safe;

class RetrieveGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig = $payload['pay_config'];

        // 请求信息
        $data = [
            'merchantId'     => $payConfig['merchantId'],
            'merchantSiteId' => $payConfig['merchantSiteId'],
            'timeStamp'      => get_carbon()->format('YmdHis'),
            'transactionId'  => $payload['payment_order']['payment_order_id'],
        ];

        // 数据签名
        $str              = $data['merchantId'] . $data['merchantSiteId'] . $data['transactionId'] . $data['timeStamp'] . $payConfig['merchantSecretKey'];
        $sign             = Support::sign($str);
        $data['checksum'] = $sign;

        // 如果配置有中转配置参数，则处理中转网址
        $transitIp = '';
        if (!empty($payConfig['transitIp'])) {
            $transitIp = $payConfig['transitIp'] . '?' . Support::urlPayment($payConfig);
        }

        // 组装请求头
        $header = array(
            'Content-Type' => 'application/json',
        );

        $post = [
            'data'       => $data,
            'header'     => $header,
            'transit_ip' => $transitIp
        ];

        return Safe::retrieveParser(Support::requestApi('/getTransactionDetails.do', $post, $payload['order_id']));
    }
}
