<?php

namespace App\Classes\Pay\Gateways\Request;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Request;

class RetrieveGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $post = [
            'status'          => $payload['status'],
            'orderId'         => $payload['order_id'],
            'code'            => $payload['code'],
            'result'          => $payload['result'],
            'remark'          => $payload['remark']
        ];

        Events::dispatch(new Events\ApiRequesting('Request', '',  $endpoint, $payload));
        Events::dispatch(new Events\SlsLog(
            'Request', 
            'Requesting To Api',
            [$endpoint, $payload], 
            $payload['order_id']
        ));
        
        return Request::retrieveParser($post);
    }
}
