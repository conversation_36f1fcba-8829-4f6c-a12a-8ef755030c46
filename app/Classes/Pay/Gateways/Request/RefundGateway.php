<?php

namespace App\Classes\Pay\Gateways\Request;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Request;
use App\Models\Refund;

class RefundGateway extends Gateway
{
	/**
	 * Pay an order.
	 *
	 * @param string $endpoint
	 *
	 * @param array $payload
	 * @return Collection
	 */
	public function pay($endpoint, array $payload)
	{
        $post = [
            'status'          => Refund::STATUS_APPROVED,
            'orderId'         => $payload['order_id'],
            'code'            => get_system_code('000'),
            'transactionType' => 'refund',
            'result'          => 'Transaction is approved',
            'remark'          => '交易成功'
        ];

		Events::dispatch(new Events\ApiRequesting('Request', '',  $endpoint, $payload));
        Events::dispatch(new Events\SlsLog(
            'Request', 
            'Requesting To Api',
            [$endpoint, $payload], 
            $payload['refund_id']
        ));

		return Request::refundParser($post);
	}
}
