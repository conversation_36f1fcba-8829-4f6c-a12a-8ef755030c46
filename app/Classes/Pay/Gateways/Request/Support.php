<?php

namespace App\Classes\Pay\Gateways\Request;

use App\Classes\Pay\Contracts\Gateway;
use App\Classes\Pay\Gateways\Request;
use App\Classes\Supports\Config;
use App\Classes\Pay\Contracts\Support as BaseSupport;

/**
 * @property array http http options
 * @property string mode current mode
 * @property array log log options
 * @property string sign_key
 */
class Support
{
    use BaseSupport;

    /**
     * Bootstrap.
     *
     * @param Config $config
     */
    public function __construct(Config $config)
    {
        self::$gatewayName = 'Request';
        $this->baseUri     = Request::URL[$config->get('mode', Gateway::MODE_NORMAL)];
        $this->config      = $config;
        $this->setHttpOptions();
    }
    
    /**
     * 卡信息脱敏
     *
     * @param array $data
     * @return array
     */
    public static function handleLogData($data)
    {
        if (isset($data['card']['card_number'], $data['card']['card_mask'], $data['card']['expiration_year'], $data['card']['expiration_month'], $data['card']['cvv'])) {
            $data['card']['card_number']      = $data['card']['card_mask'];
            $data['card']['expiration_year']  = get_mark_data($data['card']['expiration_year']);
            $data['card']['expiration_month'] = get_mark_data($data['card']['expiration_month']);
            $data['card']['cvv']              = get_mark_data($data['card']['cvv']);
        }

        return $data;
    } 
}
