<?php

namespace App\Classes\Pay\Gateways\Kess;

use App\Classes\Supports\Collection;
use Illuminate\Support\Facades\Cache;
use App\Classes\Pay\Parser\Kess;

class AuthGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig  = $payload['pay_config'];
        $payOrder   = $payload['payment_order'];
        $payCard    = $payload['card'];
        $payAddress = $payload['address'];

        //缓存key
        $mark    = MD5($payOrder['order_id'] . 'kess');
        $markArr = [
            'order_id' => $payOrder['order_id'],
            'class'    => 'Kess',
        ];
        Cache::add($mark, $markArr, 24 * 60 * 60);

        //组装支付数据
        //卡信息
        $card   = [
            'number'       => $payCard['card_number'],
            'securityCode' => $payCard['cvv'],
            'expiry' => [
                'month' => $payCard['expiration_month'],
                'year'  => $payCard['expiration_year']
            ],
        ];
        $card = Support::encrypt($card, $payConfig['publicKey']);

        //客户信息
        $customer = [
            'phone_number' => $payAddress['bill_phone'],
            'email'        => $payAddress['bill_email'],
            'first_name'   => $payAddress['bill_first_name'],
            'last_name'    => $payAddress['bill_last_name'],
            'address'      => $payAddress['bill_address'],
            'city'         => $payAddress['bill_city'],
            'postcode'     => $payAddress['bill_postcode']
        ];
        $customer = Support::encrypt($customer, $payConfig['publicKey']);

        $data = [
            'service'      => 'webpay.acquire.directPay',
            'sign_type'    => 'HMAC-SHA256',
            'sign'         => '',
            'seller_code'  => $payConfig['sellerCode'],
            'out_trade_no' => $payOrder['order_id'],
            'body'         => $payConfig['cardBill'],
            'total_amount' => Support::amount_format($payOrder['amount']),
            'currency'     => $payOrder['currency'],
            'notify_url'   => route('api.v1.asyncNotify', ['mark' => $mark], true),
            'redirect_url' => route('api.v1.syncNotify', ['mark' => $mark], true),
            'card'         => $card,
            'customer'     => $customer,
            'ip_address'   => $payAddress['ip']
        ];

        //生成签名
        $sign         = Support::signature($data, $payConfig['apiSecretKey']);
        $data['sign'] = $sign;

        //获取accessToken
        $tokenData = Support::getAccessToken($payConfig, $endpoint);

        //组装请求头
        $header = [
            'Content-Type'  => 'application/json',
            'Authorization' => 'Bearer ' . $tokenData['access_token'],
        ];

        $post = [
            'data'   => $data,
            'header' => $header
        ];

        $post['transitIp'] = $payConfig['transitIp'] . '?' . Support::urlPayment($payConfig); // 中转服务器地址

        return Kess::authParser(Support::requestApi('/api/mch/v2/gateway', $post, 'post', $payload['order_id']));
    }
}
