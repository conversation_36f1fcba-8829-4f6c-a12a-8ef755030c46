<?php

namespace App\Classes\Pay\Gateways\Kess;

use App\Classes\Pay\Gateways\Kess;
use App\Classes\Supports\Collection;
use App\Classes\Supports\Config;
use App\Classes\Pay\Contracts\Support as BaseSupport;
use App\Classes\Pay\Events;
use App\Classes\Pay\Log;
use Illuminate\Support\Facades\Cache;

/**
 * @property array http  http options
 * @property string mode current mode
 * @property array log   log options
 * @property string sign_key
 */
class Support
{
    use BaseSupport;

    /**
     * Instance.
     *
     * @var Support
     */
    private static $instance;

    /**
     * Bootstrap.
     *
     * @param Config $config
     */
    public function __construct(Config $config)
    {
        self::$gatewayName = 'Kess';
        $this->baseUri     = Kess::URL[$config->get('mode', Kess::MODE_NORMAL)];
        $this->config      = $config;
        $this->setHttpOptions();
    }

    /**
     * Get API result.
     *
     * @param string $endpoint
     * @param array $data
     * @param string $method
     * @param string $requestId
     * @return Collection
     */
    public static function requestApi(string $endpoint, array $data, string $method, string $requestId = ''): Collection
    {
        $data['doUrl'] = self::$instance->getBaseUri() . $endpoint;
        Events::dispatch(new Events\ApiRequesting(self::$gatewayName, '', $data['transitIp'], $data));
        Events::dispatch(new Events\SlsLog(
            self::$gatewayName,
            'Requesting To Api',
            [$data['transitIp'], $data],
            $requestId
        ));

        $result = [];
        $header = [];
        try {
            if (!empty($data['header'])) {
                foreach ($data['header'] as $key => $vo) {
                    $header[] = $key . ':' . $vo;
                }

                $data['header'] = $header;
            }

            $data['embMethod'] = 'curlPost';
            $result            = self::$instance->post($data['transitIp'], $data);
            if (gettype($result) === 'string') {
                $result = json_decode($result, true) ?? [];
            }
        } catch (\Exception $e) {
            //返回数据格式特殊处理
            $content = $e->getMessage();
            if ($e instanceof \GuzzleHttp\Exception\RequestException) {
                $content = $e->getResponse()->getBody()->getContents();
            }

            $result = self::returnDateHandle($content, $requestId);
        }

        Events::dispatch(new Events\ApiRequested(self::$gatewayName, '', $data['transitIp'], $result));
        Events::dispatch(new Events\SlsLog(
            self::$gatewayName,
            'Result Of Api',
            $result,
            $requestId
        ));

        return self::processingApiResult($result);
    }

    //RSA公钥加密
    public static function encrypt(array $toSign, $publicKey)
    {
        $publicKey = "-----BEGIN PUBLIC KEY-----\n" .
            wordwrap($publicKey, 64, "\n", true) .
            "\n-----END PUBLIC KEY-----";

        $toSign = json_encode($toSign);
        openssl_public_encrypt($toSign, $encrypted, $publicKey);

        return bin2hex($encrypted);
    }

    /**
     * 获取请求头token
     *
     * @param array  $payConfig
     * @param string $endpoint
     * @return array
     */
    public static function getAccessToken($payConfig, $endpoint)
    {
        $data = [];
        $key  = 'Kess_Access_Token_' . $payConfig['username'];
        // 从cache 获取token
        if (!Cache::has($key)) {
            $lockKey = $key . '_Lock';
            $i       = 0;

            // 排队请求 添加有效期防止死锁
            while (!Cache::add($lockKey, 1, 2 * 60)) {
                sleep(1);
                $i++;

                if ($i > 30) {
                    break;
                }
            }

            if (!Cache::has($key)) {
                $data = self::_getToken($key, $payConfig, $endpoint);
            } else {
                $data['access_token'] = Cache::get($key);
            }

            // 解锁
            Cache::forget($lockKey);
        } else {
            $data['access_token'] = Cache::get($key);
        }

        return $data;
    }

    /**
     * 获取token(子方法)
     *
     * @param string $key
     * @param array  $payConfig
     * @param string $endpoint
     * @return array
     */
    protected static function _getToken($key, $payConfig, $endpoint)
    {
        $result = [
            'access_token' => ''
        ];

        //请求参数
        $data = [
            'data' => [
                'grant_type'    => 'password',
                'client_id'     => $payConfig['clientId'],
                'client_secret' => $payConfig['clientSecret'],
                'username'      => $payConfig['username'],
                'password'      => $payConfig['password']
            ],
            'headers' => ['Content-Type' => 'application/json']
        ];

        $data['doUrl'] = self::$instance->getBaseUri() . '/oauth/token';
        $transitIp     = $payConfig['transitIp'] . '?' . Support::urlPayment($payConfig);

        Events::dispatch(new Events\ApiRequesting(self::$gatewayName, '', $transitIp, $data));
        Events::dispatch(new Events\SlsLog(
            self::$gatewayName,
            'Requesting To Api',
            [$transitIp, $data],
            0
        ));

        $res = [];
        try {
            $res = self::$instance->post($transitIp, $data);
            if (gettype($res) === 'string') {
                $res = json_decode($res, true) ?? [];
            }

            if (isset($res['access_token']) && isset($res['expires_in'])) {
                Cache::put($key, $res['access_token'], $res['expires_in'] - 10);
                $result['access_token'] = $res['access_token'];
            }
        } catch (\Exception $e) {
            //记录日志
            Log::warning('获取access_token失败，' . json_encode([
                'data'     => $data,
                'tokenUrl' => $payConfig['transitIp'],
                'error'    => $e->getMessage() ?? ''
            ]));
            Events::dispatch(new Events\SlsLog(
                self::$gatewayName,
                '获取access_token失败',
                [
                    'data'  => $data,
                    'url'   => $payConfig['transitIp'],
                    'error' => $e->getMessage() ?? ''
                ],
                '',
                'warning'
            ));
        }

        Events::dispatch(new Events\ApiRequested(self::$gatewayName, '', $transitIp, $res));
        Events::dispatch(new Events\SlsLog(
            self::$gatewayName,
            'Result Of Api',
            $res,
            0
        ));

        return $result;
    }

    public static function signature(array $params, $api_secret_key)
    {
        $signType = $params['sign_type'] ?? "MD5";

        $string = self::toUrlParams($params);
        $string = $string . "&key=" . $api_secret_key;

        if ($signType == "MD5")
            $string = md5($string);
        else if ($signType == "HMAC-SHA256")
            $string = hash_hmac("sha256", $string, $api_secret_key);

        return $string;
    }

    public static function toUrlParams(array $values)
    {
        ksort($values);

        $values = array_filter($values, function ($var) {
            return !is_null($var);
        });

        $buff = "";

        foreach ($values as $k => $v) {
            if ($k != "sign" && $v !== "" && !is_array($v) && !is_object($v)) {
                $buff .= $k . "=" . $v . "&";
            }
        }

        $buff = trim($buff, "&");

        return $buff;
    }

    /**
     * 处理日志数据
     *
     * @param array $data
     * @return array
     */
    public static function handleLogData($data)
    {
        if (isset($data['number'], $data['securityCode'], $data['expiry']['month'], $data['expiry']['year'])) {
            $data['securityCode']    = get_mark_data($data['securityCode']);
            $data['expiry']['month'] = get_mark_data($data['expiry']['month']);
            $data['expiry']['year']  = get_mark_data($data['expiry']['year']);
            $data['number']          = get_markcard($data['number']);
        }

        return $data;
    }

    /**
     * 生成中转路径参数
     *
     * @param array $payConfig
     * @param array $data
     * @return void
     */
    public static function urlPayment($payConfig, $data = [])
    {
        $systemTag = (isset($payConfig['mode']) && $payConfig['mode'] == 'dev') ? $payConfig['mode'] : env('APP_NAME', 'Laravel');

        $payment = [
            'systemTag' => md5($systemTag)
        ];

        if (!empty($data)) {
            foreach ($data as $key => $vo) {
                $payment[$key] = $vo;
            }
        }

        return http_build_query($payment);
    }
}
