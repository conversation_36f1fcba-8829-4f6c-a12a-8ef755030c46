<?php

namespace App\Classes\Pay\Gateways\Ppae;

use App\Classes\Pay\Parser\Ppae;
use App\Classes\Supports\Collection;
use App\Classes\Pay\Events;
use Illuminate\Support\Facades\Cache;

class RefundGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig    = $payload['pay_config'];
        $paymentOrder = $payload['payment_order'];

        // 缓存key
        $mark    = MD5($payload['refund_id'] . 'ppae');
        $markArr = [
            'order_id'  => $payload['order_id'],
            'refund_id' => $payload['refund_id'],
            'class'     => 'Ppae',
        ];
        Cache::add($mark, $markArr, 30 * 24 * 60 * 60);

        $amount = Support::amountHandle($payload['payment_refund']['amount'], $payload['payment_refund']['currency']);
        $data   = [
            'requestId'             => $payload['refund_id'],
            'accId'                 => $payConfig['accId'],
            'merchantTransactionId' => $payload['refund_id'],
            'paymentType'           => 'REFUND',
            'amount'                => $amount,
            'currency'              => $payload['payment_refund']['currency'],
            'signType'              => 'SHA256',
            'sign'                  => '',
            'notificationUrl'       => route('api.v1.notify', ['mark' => $mark], true),
        ];

        $md5Arr = [
            'requestId'             => $data['requestId'],
            'accId'                 => $data['accId'],
            'amount'                => $data['amount'],
            'signType'              => $data['signType'],
            'currency'              => $data['currency'],
            'merchantTransactionId' => $data['merchantTransactionId'],
        ];
        $data['sign'] = Support::createSign($md5Arr, trim($payConfig['salt']));

        return Ppae::refundParser(Support::requestApi('/v3/payment/' . $paymentOrder['payment_order_id'], $data, $payload['refund_id']));
    }
}
