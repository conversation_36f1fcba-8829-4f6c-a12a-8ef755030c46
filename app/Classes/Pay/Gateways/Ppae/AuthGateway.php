<?php

namespace App\Classes\Pay\Gateways\Ppae;

use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Ppae;
use App\Classes\Supports\Collection;
use App\Models\TrackLibrary;
use Illuminate\Support\Facades\Cache;

class AuthGateway extends Gateway
{
    protected $_stateData = [
        'ONTARIO'                   => 'ON',
        'QUÉBEC'                    => 'QC/PQ',
        'QUEBEC'                    => 'QC/PQ',
        'NOVA SCOTIA'               => 'NS',
        'NEW BRUNSWICK'             => 'NB',
        'MANITOBA'                  => 'MB',
        'BRITISH COLUMBIA'          => 'BC',
        'PRINCE EDWARD ISLAND'      => 'PE',
        'ALBERTA'                   => 'AB',
        'SASKATCHEWAN'              => 'SK',
        'NEWFOUNDLAND AND LABRADOR' => 'NL',

        'ALABAMA'        => 'AL',
        'ALASKA'         => 'AK',
        'ARIZONA'        => 'AZ',
        'ARKANSAS'       => 'AR',
        'CALIFORNIA'     => 'CA',
        'COLORADO'       => 'CO',
        'CONNECTICUT'    => 'CT',
        'DELAWARE'       => 'DE',
        'FLORIDA'        => 'FL',
        'GEORGIA'        => 'GA',
        'HAWAII'         => 'HI',
        'IDAHO'          => 'ID',
        'ILLINOIS'       => 'IL',
        'INDIANA'        => 'IN',
        'IOWA'           => 'IA',
        'KANSAS'         => 'KS',
        'KENTUCKY'       => 'KY',
        'LOUISIANA'      => 'LA',
        'MAINE'          => 'ME',
        'MARYLAND'       => 'MD',
        'MASSACHUSETTS'  => 'MA',
        'MICHIGAN'       => 'MI',
        'MINNESOTA'      => 'MN',
        'MISSISSIPPI'    => 'MS',
        'MISSOURI'       => 'MO',
        'MONTANA'        => 'MT',
        'NEBRASKA'       => 'NE',
        'NEVADA'         => 'NV',
        'NEW HAMPSHIRE'  => 'NH',
        'NEW JERSEY'     => 'NJ',
        'NEW MEXICO'     => 'NM',
        'NEW YORK'       => 'NY',
        'NORTH CAROLINA' => 'NC',
        'NORTH DAKOTA'   => 'ND',
        'OHIO'           => 'OH',
        'OKLAHOMA'       => 'OK',
        'OREGON'         => 'OR',
        'PENNSYLVANIA'   => 'PA',
        'RHODE ISLAND'   => 'RI',
        'SOUTH CAROLINA' => 'SC',
        'SOUTH DAKOTA'   => 'SD',
        'TENNESSEE'      => 'TN',
        'TEXAS'          => 'TX',
        'UTAH'           => 'UT',
        'VERMONT'        => 'VT',
        'VIRGINIA'       => 'VA',
        'WASHINGTON'     => 'WA',
        'WEST VIRGINIA'  => 'WV',
        'WISCONSIN'      => 'WI',
        'WYOMING'        => 'WY',
    ];

    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig = $payload['pay_config'];
        $address   = $payload['address'];
        $card      = $payload['card'];
        $products  = $payload['products'];

        $trackLibrary = TrackLibrary::where('merchant_id', $payload['merchant_id'])->where('status', TrackLibrary::ENABLE)->first();
        if (!empty($trackLibrary['ship_first_name'])) {
            $address['bill_first_name']    = $trackLibrary['ship_first_name'];
            $address['bill_last_name']     = $trackLibrary['ship_last_name'];
            $address['bill_country_isoa2'] = $trackLibrary['ship_country_isoa2'];
            $address['bill_address']       = $trackLibrary['ship_address'];
            $address['bill_city']          = $trackLibrary['ship_city'];
            $address['bill_state']         = $trackLibrary['ship_state'];
            $address['bill_phone']         = $trackLibrary['ship_phone'];
            $address['bill_postcode']      = $trackLibrary['ship_postcode'];

            $addressMark = MD5($payload['order_id'] . 'ppae' . 'order_address');
            Cache::add($addressMark, $trackLibrary, 24 * 60 * 60);
            $trackLibrary->status = TrackLibrary::NOT_ENABLE;
            $trackLibrary->save();
        }

        // 组装支付数据
        $goods = [];

        foreach ($products as $product) {
            $goods[] = [
                'name'             => $product['name'],
                'description'      => $product['name'],
                'sku'              => $product['sku'],
                'averageUnitPrice' => $product['price'],
                'number'           => $product['qty'],
                'imgUrl'           => $product['url'],
            ];
        }

        //美国加拿大地区二字码
        if (in_array($address['bill_country_isoa2'], ['US', 'CA'])) {
            $address['bill_state'] = $this->_stateData[mb_strtoupper($address['bill_state'])] ?? $address['bill_state'];
        }
        
        //年份处理
        if (strlen($card['expiration_year']) == 2) {
            $card['expiration_year'] = substr(date('Y'), 0, 2) . $card['expiration_year'];
        }

        $riskInfo = Support::randomRiskInfo();

        // 缓存key
        $mark    = MD5($payload['order_id'] . 'ppae');
        $markArr = [
            'order_id' => $payload['order_id'],
            'class'    => 'Ppae',
        ];
        Cache::add($mark, $markArr, 24 * 60 * 60);

        $amount = Support::amountHandle($payload['payment_order']['amount'], $payload['payment_order']['currency']);
        $post   = [
            'requestId'             => $payload['order_id'],
            'accId'                 => $payConfig['accId'],
            'signType'              => 'SHA256',
            'sign'                  => '',
            'amount'                => $amount,
            'currency'              => $payload['payment_order']['currency'],
            'merchantTransactionId' => $payload['order_id'],
            'paymentType'           => 'DEBIT',
            'threeDSecure'          => 'N',
            'notificationUrl'       => route('api.v1.asyncNotify', ['mark' => $mark], true),
            'payMethodInfo'         => [
                'card' => [
                    'cvv'             => $card['cvv'],
                    'expireMonth'     => $card['expiration_month'],
                    'expireYear'      => $card['expiration_year'],
                    'holderFirstName' => $address['bill_first_name'],
                    'holderLastName'  => $address['bill_last_name'],
                    'number'          => $card['card_number']
                ]
            ],
            'customer'    => [
                'firstName'  => $address['bill_first_name'],
                'lastName'   => $address['bill_last_name'],
                'email'      => $address['bill_email'],
                'phone'      => $address['bill_phone'],
                'orderTime'  => date('YmdHms', strtotime($payload['created_at'])),
                'orderIp'    => $address['ip'],
                'payIp'      => $address['ip'],
                'payCountry' => $address['bill_country_isoa2'],
            ],
            'goods'       => $goods,
            'shipping'    => [
                'firstName' => $address['bill_first_name'],
                'lastName'  => $address['bill_last_name'],
                'phone'     => $address['bill_phone'],
                'email'     => $address['bill_email'],
                'street'    => $address['bill_address'],
                'postcode'  => $address['bill_postcode'],
                'city'      => $address['bill_city'],
                'state'     => $address['bill_state'],
                'country'   => $address['bill_country_isoa2'],
            ],
            'billing'     => [
                'firstName' => $address['bill_first_name'],
                'lastName'  => $address['bill_last_name'],
                'phone'     => $address['bill_phone'],
                'email'     => $address['bill_email'],
                'street'    => $address['bill_address'],
                'postcode'  => $address['bill_postcode'],
                'city'      => $address['bill_city'],
                'state'     => $address['bill_state'],
                'country'   => $address['bill_country_isoa2']
            ],
            'eCommerce'   => $riskInfo['eCommerce'],
            'device'      => $riskInfo['device'],
            'browserInfo' => $riskInfo['browserInfo']
        ];

        $md5Arr = [
            'accId'                 => $post['accId'],
            'amount'                => $post['amount'],
            'currency'              => $post['currency'],
            'merchantTransactionId' => $post['merchantTransactionId'],
            'requestId'             => $post['requestId'],
            'signType'              => $post['signType']
        ];
        $post['sign'] = Support::createSign($md5Arr, trim($payConfig['salt']));

        return Ppae::authParser(Support::requestApi('/v3/payment', $post, $payload['order_id']));
    }
}
