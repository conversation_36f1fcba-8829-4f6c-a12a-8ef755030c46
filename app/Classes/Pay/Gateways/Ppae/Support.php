<?php

namespace App\Classes\Pay\Gateways\Ppae;

use App\Classes\Pay\Events;
use App\Classes\Pay\Gateways\Ppae;
use App\Classes\Pay\Log;
use App\Classes\Supports\Collection;
use App\Classes\Supports\Config;
use App\Classes\Pay\Contracts\Support as BaseSupport;

/**
 * @property array http http options
 * @property string mode current mode
 * @property array log log options
 * @property string sign_key
 */
class Support
{
    use BaseSupport;

    /**
     * Instance.
     *
     * @var Support
     */
    private static $instance;

    /**
     * Bootstrap.
     *
     * @param Config $config
     */
    public function __construct(Config $config)
    {
        self::$gatewayName = 'Ppae';
        $this->baseUri     = Ppae::URL[$config->get('mode', Ppae::MODE_NORMAL)];
        $this->config      = $config;
        $this->setHttpOptions();
    }

    /**
     * Get API result.
     *
     * @param string $endpoint
     * @param array $data
     * @param string $data
     * @return Collection
     */
    public static function requestApi(string $endpoint, array $data, string $requestId = '' ): Collection
    {

		$logData = self::handleLogData($data);

		Events::dispatch(new Events\ApiRequesting(self::$gatewayName, '', self::$instance->getBaseUri() . $endpoint, $logData));
        Events::dispatch(new Events\SlsLog(
            self::$gatewayName, 
            'Requesting To Api',
            [self::$instance->getBaseUri() . $endpoint, $logData],
            $requestId
        ));

        $result = [];

        try {
            $result = self::$instance->post($endpoint, '', ['json' => $data]);
        } catch (\Exception $e) {
            // 记录日志
            Log::error($e->getMessage());
            Events::dispatch(new Events\SlsLog(
                self::$gatewayName, 
                $e->getMessage(),
                [], 
                $requestId,
                'error'
            ));
        }

        Events::dispatch(new Events\ApiRequested(self::$gatewayName, '', self::$instance->getBaseUri() . $endpoint, $result));
        Events::dispatch(new Events\SlsLog(
            self::$gatewayName, 
            'Result Of Api',
            $result, 
            $requestId
        ));

        return self::processingApiResult($result);
    }

    public static function createSign(array $data, string $salt): string
    {
        ksort($data);
        $arr = array();

        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $arr[] = $key . '=' . json_encode($value);
            } elseif (trim($value) != "") {
                $arr[] = $key . '=' . trim($value);
            }
        }

        $signInfo = $salt . implode('&', $arr);

        return strtoupper(hash('sha256', $signInfo));
    }

    /**
     *
     * 随机获取⻛控信息
     *
     * @return string[][]
     */
    public static function randomRiskInfo(): array
    {
        $jetLag = rand(479, 481);

        $riskInfo = [
            [
                'eCommerce'   => [
                    'freeShipping'   => 'N',
                    'shippingMethod' => 'sea',
                ],
                'device'      => [
                    'orderTerminal' => '02'
                ],
                'browserInfo' => [
                    'browserInfo'  => '02',
                    'windowSize'   => '05',
                    'acceptHeader' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
                    'colorDepth'   => '24',
                    'screenHeight' => '1080',
                    'jetLag'       => $jetLag,
                    'userAgent'    => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:96.0) Gecko/20100101 Firefox/96.0',
                ]
            ],
            [
                'eCommerce'   => [
                    'freeShipping'   => 'N',
                    'shippingMethod' => 'air',
                ],
                'device'      => [
                    'orderTerminal' => '02'
                ],
                'browserInfo' => [
                    'browserInfo'  => '02',
                    'windowSize'   => '05',
                    'acceptHeader' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
                    'colorDepth'   => '24',
                    'screenHeight' => '1080',
                    'jetLag'       => $jetLag,
                    'userAgent'    => 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.71 Safari/537.36 Edg/97.0.1072.62',
                ]
            ],
            [
                'eCommerce'   => [
                    'freeShipping'   => 'N',
                    'shippingMethod' => 'MultiTransport',
                ],
                'device'      => [
                    'orderTerminal' => '01'
                ],
                'browserInfo' => [
                    'browserInfo'  => '01',
                    'windowSize'   => '05',
                    'acceptHeader' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
                    'colorDepth'   => '24',
                    'screenHeight' => '865',
                    'jetLag'       => $jetLag,
                    'userAgent'    => 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.2 Mobile/15E148 Safari/604.1',
                ]
            ],
            [
                'eCommerce'   => [
                    'freeShipping'   => 'N',
                    'shippingMethod' => 'MultiTransport',
                ],
                'device'      => [
                    'orderTerminal' => '02'
                ],
                'browserInfo' => [
                    'browserInfo'  => '02',
                    'windowSize'   => '05',
                    'acceptHeader' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
                    'colorDepth'   => '24',
                    'screenHeight' => '1080',
                    'jetLag'       => $jetLag,
                    'userAgent'    => 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.71 Safari/537.36 Edg/97.0.1072.62',
                ]
            ],
            [
                'eCommerce'   => [
                    'freeShipping'   => 'N',
                    'shippingMethod' => 'MultiTransport',
                ],
                'device'      => [
                    'orderTerminal' => '01'
                ],
                'browserInfo' => [
                    'browserInfo'  => '01',
                    'windowSize'   => '05',
                    'acceptHeader' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
                    'colorDepth'   => '32',
                    'screenHeight' => '844',
                    'jetLag'       => $jetLag,
                    'userAgent'    => 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.17(0x18001127) NetType/WIFI Language/zh_CN',
                ]
            ],
            [
                'eCommerce'   => [
                    'freeShipping'   => 'N',
                    'shippingMethod' => 'MultiTransport',
                ],
                'device'      => [
                    'orderTerminal' => '01'
                ],
                'browserInfo' => [
                    'browserInfo'  => '01',
                    'windowSize'   => '05',
                    'acceptHeader' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
                    'colorDepth'   => '24',
                    'screenHeight' => '800',
                    'jetLag'       => $jetLag,
                    'userAgent'    => 'Mozilla/5.0 (Linux; Android 10; HarmonyOS; OXF-AN10; HMSCore 6.3.0.317) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.105 HuaweiBrowser/12.0.2.301 Mobile Safari/537.36',
                ]
            ]
        ];

        return $riskInfo[rand(0, count($riskInfo) - 1)];
    }
    
    /**
     * 金额格式处理
     *
     * @param $amount
     * @param $currency
     * @return string
    */
    public static function amountHandle($amount, $currency): string
    {
        //需要处理格式的货币
        $currencyData = [
            'BIF' => 0,
            'XOF' => 0,
            'XAF' => 0,
            'XPF' => 0,
            'CLP' => 0,
            'KMF' => 0,
            'DJF' => 0,
            'VND' => 0,
            'PYG' => 0,
            'GNF' => 0,
            'ISK' => 0,
            'RWF' => 0,
            'UGX' => 0,
            'UYI' => 0,
            'VUV' => 0,
            'KRW' => 0,
            'JPY' => 0,
            'BHD' => 3,  
            'IQD' => 3, 
            'JOD' => 3,
            'KWD' => 3,  
            'LYD' => 3, 
            'OMR' => 3,
            'TND' => 3,
            'CLF' => 4,
        ];
        
        $decimals = $currencyData[$currency] ?? 2;

        return self::amount_format($amount, $decimals);
    }
    
    /**
     * 处理日志数据
     *
     * @param array $data
     * @return array
     */
    public static function handleLogData($data)
    {
        $card = $data['payMethodInfo']['card'] ?? [];

        if (isset($card['number'], $card['expireMonth'], $card['expireYear'], $card['cvv'])) {
            $card                = &$data['payMethodInfo']['card'];
            $card['expireMonth'] = get_mark_data($card['expireMonth']);
            $card['expireYear']  = get_mark_data($card['expireYear']);
            $card['cvv']         = get_mark_data($card['cvv']);
            $card['number']      = get_markcard($card['number']);
        }

        return $data;
    }
}
