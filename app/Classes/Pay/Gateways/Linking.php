<?php

namespace App\Classes\Pay\Gateways;

use App\Classes\Pay\Contracts\Gateway;
use App\Classes\Pay\Gateways\Linking\Support;
use App\Classes\Supports\Config;

class Linking extends Gateway
{
    protected $gatewayName = 'Linking';

    /**
     * Const url.
     */
    const URL = [
        self::MODE_NORMAL => 'https://www.paylinking.com',
        self::MODE_DEV    => 'https://www.paylinking.com',
    ];

    public function __construct(Config $config)
    {
        $this->gateway = Support::create($config)->getBaseUri();
        parent::__construct($config);
    }
}
