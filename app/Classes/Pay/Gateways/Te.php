<?php

namespace App\Classes\Pay\Gateways;

use App\Classes\Pay\Contracts\Gateway;
use App\Classes\Pay\Gateways\Te\Support;
use App\Classes\Supports\Config;

class Te extends Gateway
{
    protected $gatewayName = 'Te';

    /**
     * Const url.
     */
    const URL = [
        self::MODE_NORMAL => 'https://api.ocepayment.com/directpay',
        self::MODE_DEV    => 'https://api.ocepayment.com/Testdirectpay',
    ];

    public function __construct(Config $config)
    {
        $this->gateway = Support::create($config)->getBaseUri();
        parent::__construct($config);
    }
}
