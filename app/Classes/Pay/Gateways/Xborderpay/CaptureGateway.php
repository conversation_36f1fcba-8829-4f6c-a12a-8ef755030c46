<?php

namespace App\Classes\Pay\Gateways\Xborderpay;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Xborderpay;

class CaptureGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     * @throws \App\Classes\Pay\Exceptions\GatewayException
     * @throws \App\Classes\Pay\Exceptions\InvalidSignException
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig          = $payload['pay_config'];
        $paymentOrder       = $payload['payment_order'];
        $parentPaymentOrder = $payload['parent_payment_order'];

        $data = array(
            'version'           => '3.0',
            'merchant_id'       => $payConfig['merchant_id'],
            'business_id'       => $payConfig['business_id'],
            'access_type'       => 's2s',
            'trans_channel'     => '',
            'original_order_id' => $parentPaymentOrder['payment_order_id'],
            'trans_type'        => 'capture',
            'amount'            => Support::amount_format($paymentOrder['amount']),
            'notify_url'        => '',
            'req_reserved'      => '',
            'reserved'          => '',
            'sign_type'         => 'MD5',
            'sign'              => ''
        );

        $md5Str       = implode($data) . $payConfig['sign_key'];
        $data['sign'] = md5($md5Str);

        Events::dispatch(new Events\PayStarted('Xborderpay', 'Web/Wap', $endpoint, $payload));
        return Xborderpay::captureParser(Support::requestApi($data));
    }
}
