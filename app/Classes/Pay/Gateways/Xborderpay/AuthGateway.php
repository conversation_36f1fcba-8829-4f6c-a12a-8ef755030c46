<?php

namespace App\Classes\Pay\Gateways\Xborderpay;

use App\Classes\Supports\Collection;
use Illuminate\Support\Facades\Cache;
use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Xborderpay;

class AuthGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     * @throws \App\Classes\Pay\Exceptions\GatewayException
     * @throws \App\Classes\Pay\Exceptions\InvalidSignException
     */
    public function pay($endpoint, array $payload): Collection
    {
	    $payConfig = $payload['pay_config'];
	    $address   = $payload['address'];
	    $card      = $payload['card'];
	    $products  = $payload['products'];

        // 组装支付数据
	    $productInfo = array();
	    $goods       = array();

        if (!empty($products)) {
        	foreach ($products as $product) {
		        $productInfo[] = array(
			        'qty'       => $product['qty'],
			        'name'      => $product['name'],
			        'price'     => $product['price'],
			        'url'       => !empty($product['url']) ? $product['url'] : '',
			        'attribute' => !empty($product['attribute']) ? $product['attribute']: '',
			        'image'     => ''
		        );

		        $goods[] =  array(
			        'sku'        => $product['sku'],
			        'name'       => $product['name'],
			        'price'      => $product['price'],
			        'qty'        => $product['qty'],
                    'url'        => !empty($product['url']) ? $product['url'] : '',
			        'attribute'  => !empty($product['attribute']) ? $product['attribute']: '',
			        'is_gift'    => $product['is_gift'],
			        'is_virtual' => $product['is_virtual']
		        );
	        }
        }

        $riskInfo      = array(
            'adjustment_factor' => '',
            'retry_num'         => '',
            'trade'             => array(
                'code' => '',
                'item' => '',
            ),
            'device' => array(
                'finger_print_id' => 'XCookie',
                'user_agent'      => $_SERVER['HTTP_USER_AGENT'] ?? 'HTTP_USER_AGENT',
                'accept_lang'     => $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? 'zh-CN,zh;q=0.9'
            ),
            'cust' => array(
                'register_user_id'   => '',
                'ip'                 => $address['ip'],
                'email'              => $address['bill_email'],
                'phone'              => $address['bill_phone'],
                'registration_time'  => '',
                'level'              => '',
                'last_shopping_time' => ''
            ),
            'goods' => $goods,
            'buried' => array(
                array(
                    'code' => '',
                    'item' => '',
                )
            ),
            'ship' => array(
                'first_name'               => $address['bill_first_name'],
                'last_name'                => $address['bill_last_name'],
                'email'                    => $address['bill_email'],
                'phone'                    => $address['bill_phone'],
                'address'                  => $address['bill_address'],
                'city'                     => $address['bill_city'],
                'state'                    => $address['bill_state'],
                'postcode'                 => $address['bill_postcode'],
                'country'                  => $address['bill_country_isoa2'],
                'address_last_modify_time' => '',
                'phone_last_modify_time'   => '',
            ),
            'bill' => array(
                'first_name' => $address['ship_first_name'],
                'last_name'  => $address['ship_last_name'],
                'email'      => $address['ship_email'],
                'phone'      => $address['ship_phone'],
                'address'    => $address['ship_address'],
                'city'       => $address['ship_city'],
                'state'      => $address['ship_state'],
                'postcode'   => $address['ship_postcode'],
                'country'    => $address['ship_country_isoa2'],
            ),
        );

        $payMethodInfo = json_encode(array(
            'card_no'          => $card['card_number'],
            'expiration_month' => $card['expiration_month'],
            'expiration_year'  => $card['expiration_year'],
            'cvv'              => $card['cvv'],
            'first_name'       => $address['bill_first_name'],
            'last_name'        => $address['bill_last_name'],
            'billing_desc'     => '-'
        ));

	    // 缓存key
	    $mark    = MD5($payload['order_id'] . 'xborderpay');
	    $markArr = [
		    'order_id' => $payload['order_id'],
		    'class'    => 'Xborderpay',
	    ];
	    Cache::add($mark, $markArr, 24*60*60);

        $data = array(
            'version'         => '3.0',
            'merchant_id'     => $payConfig['merchant_id'],
            'business_id'     => $payConfig['business_id'],
            'access_type'     => 's2s',
            'order_number'    => $payload['order_id'],
            'trans_type'      => 'authorization',
            'trans_channel'   => 'cc',
            'pay_method'      => 'normal',
            'trans_timeout'   => '4320',
            'url'             => $payConfig['url'],
            'currency'        => $payload['payment_order']['currency'],
            'amount'          => Support::amount_format($payload['payment_order']['amount']),
            'settle_currency' => $payload['payment_order']['currency'],
            'product_info'    => json_encode($productInfo),
            'pay_method_info' => $payMethodInfo,
            'country'         => 'US',
            'language'        => 'zh',
            'terminal_type'   => '0',
            'risk_info'       => json_encode($riskInfo),
            'skin_code'       => '',
            'logo'            => '',
            'dcc'             => '',
            'notify_url'      => route('api.v1.asyncNotify', ['mark' => $mark], true),
            'redirect_url'    => route('api.v1.syncNotify', ['mark' => $mark], true),
            'req_reserved'    => '',
            'reserved'        => '',
            'sign_type'       => 'MD5',
            'sign'            => ''
        );

        $md5Str       = $data['merchant_id'] . $data['business_id'] . $data['order_number'] . $data['trans_type'] . $data['trans_channel'] . $data['pay_method'] . $data['url'] . $data['currency'] . $data['amount'] . $data['settle_currency'] . $payConfig['sign_key'];
        $data['sign'] = md5($md5Str);

        return Xborderpay::authParser(Support::requestApi($data, $payload['order_id']));
    }
}
