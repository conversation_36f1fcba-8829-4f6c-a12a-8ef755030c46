<?php

namespace App\Classes\Pay\Gateways;

use App\Classes\Pay\Contracts\Gateway;
use App\Classes\Pay\Gateways\Bill\Support;
use App\Classes\Supports\Config;

class Bill extends Gateway
{
    protected $gatewayName = 'Bill';

    /**
     * Const url.
     */
    const URL = [
        self::MODE_NORMAL => 'https://gate.vollpay.com',
        self::MODE_DEV    => 'https://gate.vollpay.com',
    ];

    public function __construct(Config $config)
    {
        $this->gateway = Support::create($config)->getBaseUri();
        parent::__construct($config);
    }
}
