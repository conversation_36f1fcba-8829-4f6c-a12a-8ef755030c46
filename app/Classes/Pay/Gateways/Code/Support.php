<?php

namespace App\Classes\Pay\Gateways\Code;

use App\Classes\Pay\Gateways\Code;
use App\Classes\Supports\Config;
use App\Classes\Pay\Contracts\Support as BaseSupport;
use App\Classes\Pay\Events;
use App\Classes\Pay\Log;
use App\Classes\Supports\Collection;

/**
 * @property array http  http options
 * @property string mode current mode
 * @property array log   log options
 * @property string sign_key
 */
class Support
{
    use BaseSupport;

    /**
     * Instance.
     *
     * @var Support
     */
    private static $instance;

    /**
     * Bootstrap.
     *
     * @param Config $config
     */
    public function __construct(Config $config)
    {
        self::$gatewayName = 'Code';
        $this->baseUri     = Code::URL[$config->get('mode', Code::MODE_NORMAL)];
        $this->config      = $config;
        $this->setHttpOptions();
    }

    /**
     * Get API result.
     *
     * @param string $endpoint
     * @param array $data
     * @param string $requestId
     * @return Collection
     */
    public static function requestApi(string $endpoint, array $data, string $requestId = ''): Collection
    {
        // 设置中转请求渠道地址
        $data['doUrl'] = self::$instance->getBaseUri() . $endpoint;
        Events::dispatch(new Events\ApiRequesting(self::$gatewayName, '', $data['transitIp'], $data));
        Events::dispatch(new Events\SlsLog(
            self::$gatewayName,
            'Requesting To Api',
            [$data['transitIp'], $data],
            $requestId,
            'info'
        ));

        $result = [];
        try {
            $result = self::$instance->post($data['transitIp'], $data);
            if (gettype($result) === 'string') {
                $jsonString = str_replace("\'", "'", $result);
                $result     = json_decode($jsonString, true) ?? [];
            }
        } catch (\Exception $e) {
            // 记录日志
            Log::error($e->getMessage());
            Events::dispatch(new Events\SlsLog(
                self::$gatewayName,
                $e->getMessage(),
                [],
                $requestId,
                'error'
            ));
        }

        Events::dispatch(new Events\ApiRequested(self::$gatewayName, '', $data['transitIp'], $result));
        Events::dispatch(new Events\SlsLog(
            self::$gatewayName,
            'Result Of Api',
            $result,
            $requestId
        ));

        return self::processingApiResult($result);
    }

    /**
     * 签名
     *
     * @param $data
     * @param $privateKey
     * @return string
     */
    public static function sign(&$data, $privateKey)
    {
        return self::genSign(self::genSignContent($data), $privateKey);
    }

    /**
     * 生成签名内容
     *
     * @param $req
     * @return string
     */
    private static function genSignContent(&$req): string
    {
        $arr  = array($req);
        $strs = array();

        ksort($arr);
        self::items(0, $arr, $strs);
        $msg = implode('&', $strs);
        return $msg;
    }

    /**
     * 递归深度优先排序
     * @param $x
     * @param $y
     * @param $strs
     */
    private static function items($x, $y, &$strs)
    {
        if ($y == null) {
            return;
        }
        if (is_array($y)) {
            ksort($y);
            foreach ($y as $key => $value) {
                if ($value != null) {
                    self::items($key, $value, $strs);
                }
            }
            return;
        }
        $strs[] = $x . "=" . $y;
    }

    /**
     * 生成签名
     *
     * @param $toSign
     * @param $privateKey
     * @return string
     */
    public static function genSign($toSign, $privateKey): string
    {
        //这里他是拼接成和pem文件一样的格式
        $privateKey = "-----BEGIN RSA PRIVATE KEY-----\n" .
            wordwrap($privateKey, 64, "\n", true) .
            "\n-----END RSA PRIVATE KEY-----";

        $key = openssl_get_privatekey($privateKey);
        openssl_sign($toSign, $signature, $key, 'SHA256');
        openssl_free_key($key);
        $sign = base64_encode($signature);
        return $sign;
    }

    //RSA公钥加密
    public static function genSignPublic($toSign, $publicKey) {
        $publicKey = "-----BEGIN PUBLIC KEY-----\n" .
                wordwrap($publicKey, 64, "\n", true) .
                "\n-----END PUBLIC KEY-----";

        $key = openssl_pkey_get_public($publicKey);
        openssl_public_encrypt($toSign, $signature, $key);
        openssl_free_key($key);
        $sign = base64_encode($signature);
        return $sign;
    }

    //AES加密
    public static function aesEncrypt($data, $key) {
        return openssl_encrypt($data, 'AES-128-ECB', $key, 0);
    }

    //获取16位随机key
    public static function getKey() {
        return substr(str_shuffle("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"), 0, 16);
    }

    /**
     * 处理日志数据
     *
     * @param array $data
     * @return array
     */
    public static function handleLogData($data)
    {
        if (isset($data['validity'], $data['cvv'], $data['cardAsn'])) {
            $data['validity'] = get_mark_data($data['validity']);
            $data['cvv']      = get_mark_data($data['cvv']);
            $data['cardAsn']  = get_markcard($data['cardAsn']);
        }

        return $data;
    }
}
