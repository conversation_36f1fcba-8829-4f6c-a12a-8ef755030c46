<?php

namespace App\Classes\Pay\Gateways;

use App\Classes\Pay\Contracts\Gateway;
use App\Classes\Pay\Gateways\Ecard\Support;
use App\Classes\Supports\Config;

class Ecard extends Gateway
{
    protected $gatewayName = 'Ecard';

    /**
     * Const url.
     */
    const URL = [
        self::MODE_NORMAL => 'https://uat-apiecardnow.dcscc.com',
        self::MODE_DEV    => 'https://uat-apiecardnow.dcscc.com',
    ];

    public function __construct(Config $config)
    {
        $this->gateway = Support::create($config)->getBaseUri();
        parent::__construct($config);
    }
}
