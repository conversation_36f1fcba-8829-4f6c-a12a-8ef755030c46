<?php

namespace App\Classes\Pay\Gateways\Photon;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Photon;
use App\Models\Order;

class RetrieveGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig = $payload['pay_config'];

        $data = [
            'reqId'           => $payload['payment_order']['order_number'],
            'transactionType' => 'sale',
        ];
        $signature = Support::sign($data, $payConfig['privateKey']);

        // 获取accessToken
        $key       = 'Photon_Access_Token_' . $payConfig['appId'];
        $tokenData = Support::getAccessToken($key, $payConfig['appId'], $payConfig['appSecret']);

        // 组装请求头
        $header = array(
            'X-PD-SIGN'  => $signature,
            'X-PD-TOKEN' => $tokenData['accessToken'],
        );

        $post = array(
            'data'   => $data,
            'header' => $header,
        );

        return Photon::retrieveParser(Support::requestApi($endpoint . 'query' , $post, 'get', $payload['order_id']));
    }
}
