<?php

namespace App\Classes\Pay\Gateways;

use App\Classes\Pay\Contracts\Gateway;
use App\Classes\Pay\Gateways\Lakala\Support;
use App\Classes\Supports\Config;

class Lakala extends Gateway
{
    protected $gatewayName = 'Lakala';

    /**
     * Const url.
     */
    const URL = [
        self::MODE_NORMAL => 'https://s2.lakala.com',
        self::MODE_DEV    => 'https://test.wsmsd.cn/sit',
    ];

    public function __construct(Config $config)
    {
        $this->gateway = Support::create($config)->getBaseUri();
        parent::__construct($config);
    }
}
