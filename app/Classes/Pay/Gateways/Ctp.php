<?php

namespace App\Classes\Pay\Gateways;

use App\Classes\Pay\Contracts\Gateway;
use App\Classes\Pay\Gateways\Ctp\Support;
use App\Classes\Supports\Config;

class Ctp extends Gateway
{
    protected $gatewayName = 'Ctp';

    /**
     * Const url.
     */
    const URL = [
        self::MODE_NORMAL => 'https://newapi.onctp.com',
        self::MODE_DEV    => 'http://81.69.97.129:20108',
    ];

    public function __construct(Config $config)
    {
        $this->gateway = Support::create($config)->getBaseUri();
        parent::__construct($config);
    }
}
