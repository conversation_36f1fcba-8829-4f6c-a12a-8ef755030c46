<?php

namespace App\Classes\Pay\Gateways;

use App\Classes\Pay\Contracts\Gateway;
use App\Classes\Pay\Gateways\Booty\Support;
use App\Classes\Supports\Config;

class Booty extends Gateway
{
    protected $gatewayName = 'Booty';

    /**
     * Const url.
     */
    const URL = [
        self::MODE_NORMAL => 'https://payment.bindo.co',
        self::MODE_DEV    => 'https://stg-payment.bindo.co',
    ];

    public function __construct(Config $config)
    {
        $this->gateway = Support::create($config)->getBaseUri();
        parent::__construct($config);
    }
}
