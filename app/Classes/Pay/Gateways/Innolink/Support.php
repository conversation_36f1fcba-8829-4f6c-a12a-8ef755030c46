<?php

namespace App\Classes\Pay\Gateways\Innolink;

use App\Classes\Pay\Contracts\Support as BaseSupport;
use App\Classes\Pay\Events;
use App\Classes\Pay\Exceptions\InvalidSignException;
use App\Classes\Pay\Gateways\Innolink;
use App\Classes\Pay\Log;
use App\Classes\Supports\Collection;
use App\Classes\Supports\Config;

/**
 * @property array http  http options
 * @property string mode current mode
 * @property array log   log options
 * @property string sign_key
 */
class Support
{
    use BaseSupport;

    /**
     * Instance.
     *
     * @var Support
     */
    private static $instance;

    /**
     * Bootstrap.
     *
     * @param Config $config
     */
    public function __construct(Config $config)
    {
        self::$gatewayName = 'Innolink';
        $this->baseUri     = Innolink::URL[$config->get('mode', Innolink::MODE_NORMAL)];
        $this->config      = $config;
        $this->setHttpOptions();
    }

    /**
     * Get API result.
     *
     * @param string $endpoint
     * @param array $data
     * @param string $requestId
     * @return Collection
     */
    public static function requestApi(string $endpoint, array $data, string $requestId = ''): Collection
    {
        $mark    = false; // 是否特殊商户标识
        $logData = Support::handleLogData($data);

        $doUrl   = self::$instance->getBaseUri() . $endpoint; // 渠道最终请求地址
        $tempUrl = $data['transit_ip'] ?: $doUrl; // 当前发起的请求地址，有中转则走中转

        Events::dispatch(new Events\ApiRequesting(self::$gatewayName, '', $tempUrl, $logData));
        Events::dispatch(new Events\SlsLog(
            self::$gatewayName,
            'Requesting To Api',
            [$tempUrl, $logData],
            $requestId
        ));

        $result = [];
        try {
            if (!empty($data['transit_ip'])) {
                // 转发到中转
                $data['embMethod'] = 'post';
                $data['doUrl']     = $doUrl;

                if (!empty($data['header'])) {
                    $header = [];
                    foreach ($data['header'] as $key => $vo) {
                        $header[] = $key . ':' . $vo;
                    }

                    $data['header'] = $header;
                }

                $result = self::$instance->post($data['transit_ip'], $data);

                if (gettype($result) === 'string') {
                    $result = json_decode($result, true) ?? [];
                }
            } else {
                $result = self::$instance->post($endpoint, $data['data'], ['headers' => $data['header']]);
            }
        } catch (\Exception $e) {
            //返回数据格式特殊处理
            $content = $e->getMessage();
            if ($e instanceof \GuzzleHttp\Exception\RequestException) {
                $content = $e->getResponse()->getBody()->getContents();
            }

            $result = self::returnDateHandle($content, $requestId, $mark);
        }

        Events::dispatch(new Events\ApiRequested(self::$gatewayName, '', $tempUrl, $result));
        Events::dispatch(new Events\SlsLog(
            self::$gatewayName,
            'Result Of Api',
            $result,
            $requestId
        ));

        return self::processingApiResult($result);
    }

    /**
     * 签名
     *
     * @param $data
     * @param $key
     * @return string
     */
    public static function sign(&$data, $key)
    {
        return self::genSign(self::genSignContent($data) . '&' . $key);
    }

    /**
     * 生成签名内容
     *
     * @param $req
     * @return string
     */
    private static function genSignContent(&$req): string
    {
        $arr  = array($req);
        $strs = array();

        ksort($arr);
        self::items(0, $arr, $strs);
        $msg = implode('&', $strs);
        return $msg;
    }

    /**
     * 递归深度优先排序
     * @param $x
     * @param $y
     * @param $strs
     */
    private static function items($x, $y, &$strs)
    {
        if ($y == null) {
            return;
        }
        if (is_array($y)) {
            ksort($y);
            foreach ($y as $key => $value) {
                if ($value != null) {
                    self::items($key, $value, $strs);
                }
            }
            return;
        }
        $strs[] = $x . "=" . $y;
    }

    /**
     * 生成签名
     *
     * @param $toSign
     * @param $privateKey
     * @return string
     */
    public static function genSign($toSign): string
    {
        $sign = hash('sha256', $toSign);
        return $sign;
    }

    /**
     * 处理日志数据
     *
     * @param array $data
     * @return array
     */
    public static function handleLogData($data)
    {
        if (isset($data['cardNO'], $data['expYear'], $data['expMonth'], $data['cvv'])) {
            $data['cardNO']   = get_markcard($data['cardNO']);
            $data['expMonth'] = get_mark_data($data['expMonth']);
            $data['expYear']  = get_mark_data($data['expYear']);
            $data['cvv']      = get_mark_data($data['cvv']);
        }

        return $data;
    }

    /**
     * 返回数据格式特殊处理
     *
     * @param string $content
     * @param string $requestId
     * @param boolean $mark
     * @return array
     */
    private static function returnDateHandle($content, $requestId, $mark = false)
    {
        try {
            $row = json_decode($content, true) ?? [];

            if (empty($row)) {
                $msg = explode('response:', $content)[1] ?? '';

                if (!empty($msg)) {
                    $row = json_decode($msg, true) ?? [];

                    if (empty($row)) {
                        $msg = explode(',', $msg);
                        array_pop($msg);
                        $msg = trim(implode(',', $msg), '"');
                        $row = json_decode($msg . '"}', true) ?? [];
                    }
                }
            }
        } catch (\Exception $e) {
            // 记录日志
            Log::warning($e->getMessage());
            Events::dispatch(new Events\SlsLog(
                self::$gatewayName,
                '格式解析错误',
                ['error' => $e->getMessage()],
                $requestId,
                'warning'
            ));

            $row = [];
        }

        if (empty($row)) {
            $level = 'error';
            if ($mark && strpos($content, 'Operation timed out after') !== false) {
                $level = 'info';
            }

            // 记录日志
            Log::$level($content);
            Events::dispatch(new Events\SlsLog(
                self::$gatewayName,
                $content,
                [],
                $requestId,
                $level
            ));
        }

        return $row;
    }

    /**
     * processingApiResult.
     *
     * @param $result
     *
     * @return Collection
     * @throws GatewayException
     * @throws InvalidSignException
     */
	public static function processingApiResult($result): Collection
    {
        if (!empty($result)) {
            $result['pay_config'] = self::$instance->config;
        }

        return new Collection($result);
    }
}
