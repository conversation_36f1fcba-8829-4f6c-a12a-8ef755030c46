<?php

namespace App\Classes\Pay\Gateways\Innolink;

use App\Classes\Supports\Collection;
use App\Models\DirectoryCountry;
use Illuminate\Support\Facades\Cache;
use App\Classes\Pay\Parser\Innolink;

class AuthGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig   = $payload['pay_config'];
        $payOrder    = $payload['payment_order'];
        $payAddress  = $payload['address'];
        $payCard     = $payload['card'];
        $payProducts = $payload['products'];

        //缓存key
        $mark    = MD5($payOrder['order_id'] . 'innolink');
        $markArr = [
            'order_id'   => $payOrder['order_id'],
            'class'      => 'Innolink',
            'channel_id' => $payload['channel_id'],
        ];
        Cache::add($mark, $markArr, 24 * 60 * 60);

        //商品信息
        $product = [];
        if (!empty($payProducts)) {
            $tempProducts = reset($payProducts);
            $product      = [
                'goodsInfo' => [[
                    'goodsName'  => $tempProducts['name'],
                    'quantity'   => $tempProducts['qty'],
                    'goodsPrice' => $tempProducts['price'],
                ]]
            ];
        }

        // 年份处理
        if (strlen($payCard['expiration_year']) == 2) {
            $payCard['expiration_year'] = substr(date('Y'), 0, 2) . $payCard['expiration_year'];
        }

        $areanum = DirectoryCountry::whereIn('isoa2', [$payAddress['ship_country_isoa2'], $payAddress['bill_country_isoa2']])->pluck('areanum', 'isoa2')->toArray();

        $data = [
            'merNo'         => $payConfig['mchNo'],
            'terNo'         => $payConfig['terNo'],
            'transType'     => 'sales',
            'transModel'    => 'M',
            'amount'        => Support::amount_format($payOrder['amount']),
            'currencyCode'  => $payOrder['currency'],
            'orderNo'       => $payload['order_id'],
            'payIP'         => $payAddress['ip'],
            'returnURL'     => route('api.v1.syncNotify', ['mark' => $mark], true),
            'merNotifyURL'  => route('api.v1.asyncNotify', ['mark' => $mark], true),
            'merMgrURL'     => $payConfig['merMgrURL'],
            'webInfo'       => Support::randomBrowerInfo(),
            'language'      => 'en_US',
            'cardCountry'   => $payAddress['bill_country_isoa2'],
            'cardState'     => $payAddress['bill_state'],
            'cardCity'      => $payAddress['bill_city'],
            'cardAddress'   => $payAddress['bill_address'],
            'cardZipCode'   => $payAddress['bill_postcode'],
            'grCountry'     => $payAddress['ship_country_isoa2'],
            'grState'       => $payAddress['ship_state'],
            'grCity'        => $payAddress['ship_city'],
            'grAddress'     => $payAddress['ship_address'],
            'grZipCode'     => $payAddress['ship_postcode'],
            'grEmail'       => $payAddress['ship_email'],
            'grphoneNumber' => '+' . $areanum[$payAddress['ship_country_isoa2']] . $payAddress['ship_phone'],
            'grPerName'     => $payAddress['ship_name'],
            'goodsString'   => json_encode($product, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE),
            'cardNO'        => $payCard['card_number'],
            'expYear'       => $payCard['expiration_year'],
            'expMonth'      => $payCard['expiration_month'],
            'cvv'           => $payCard['cvv'],
            'cardFullName'  => $payAddress['bill_name'],
            'cardFullPhone' => '+' . $areanum[$payAddress['bill_country_isoa2']] . $payAddress['bill_phone'],
            'shipName'      => $product['goodsInfo'][0]['goodsName'] ?? '',
        ];

        // 如果配置有中转配置参数，则处理中转网址和跳转/通知地址
        $transitIp = '';
        if (!empty($payConfig['transitUrl']) && !empty($payConfig['transitIp'])) {
            $transitIp            = $payConfig['transitIp'] . '?' . Support::urlPayment($payConfig);
            $data['returnURL']    = $payConfig['transitUrl'] . '/Sync.php?' . Support::urlPayment($payConfig, ['mark' => $mark]);
            $data['merNotifyURL'] = $payConfig['transitUrl'] . '/Async.php?' . Support::urlPayment($payConfig, ['mark' => $mark]);
        }

        //数据签名
        $sign             = Support::sign($data, $payConfig['key']);
        $data['hashcode'] = $sign;

        //组装请求头
        $header = [
            'Content-Type' => 'application/x-www-form-urlencoded;charset=utf-8',
        ];

        $post = [
            'data'       => $data,
            'header'     => $header,
            'transit_ip' => $transitIp,
        ];

        $result = Support::requestApi('/api/payment', $post, $payload['order_id']);
        return Innolink::authParser($result);
    }
}
