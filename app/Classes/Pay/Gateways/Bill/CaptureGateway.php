<?php

namespace App\Classes\Pay\Gateways\Bill;

use App\Classes\Supports\Collection;

class CaptureGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     * @throws \App\Classes\Pay\Exceptions\GatewayException
     * @throws \App\Classes\Pay\Exceptions\InvalidSignException
     */
    public function pay($endpoint, array $payload): Collection
    {
    }
}
