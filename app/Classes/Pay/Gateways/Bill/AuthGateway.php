<?php

namespace App\Classes\Pay\Gateways\Bill;

use App\Classes\Supports\Collection;
use Illuminate\Support\Facades\Cache;
use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Bill;

class AuthGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     * @throws \App\Classes\Pay\Exceptions\GatewayException
     * @throws \App\Classes\Pay\Exceptions\InvalidSignException
     */
    public function pay($endpoint, array $payload): Collection
    {
	    $payConfig = $payload['pay_config'];
	    $address   = $payload['address'];
	    $card      = $payload['card'];
	    $products  = $payload['products'];

        // 组装支付数据
        $goods = array('goodsInfo' => array());

        if (!empty($products)) {
        	foreach ($products as $product) {
                $goods['goodsInfo'][] = array(
                    'goodsName'  => $product['name'],
                    'quantity'   => $product['qty'],
                    'goodsPrice' => $product['price'],
                );
	        }
        }

	    // 缓存key
	    $mark    = MD5($payload['order_id'] . 'bill');
	    $markArr = [
		    'order_id' => $payload['order_id'],
		    'class'    => 'Bill',
	    ];
	    Cache::add($mark, $markArr, 24*60*60);

        $data = array(
            'mid'           => $payConfig['mid'],
            'chNo'          => $payConfig['chNo'],
            'amount'        => Support::amount_format($payload['payment_order']['amount']),
            'currency'      => $payload['payment_order']['currency'],
            'orderNo'       => $payload['payment_order']['order_number'],
            'mchUrl'        => $payConfig['mchUrl'],
            'ip'            => $address['ip'],
            'language'      => 'us_EN',
            'userAgent'     => $_SERVER['HTTP_USER_AGENT'] ?? 'HTTP_USER_AGENT',
            'cardNo'        => $card['card_number'],
            'cvv'           => $card['cvv'],
            'year'          => '20' . $card['expiration_year'],
            'month'         => $card['expiration_month'],
            'firstName'     => $address['bill_first_name'],
            'lastName'      => $address['bill_last_name'],
            'country'       => $address['bill_country_isoa2'],
            'state'         => $address['bill_state'],
            'city'          => $address['bill_city'],
            'address'       => $address['bill_address'],
            'zipCode'       => $address['bill_postcode'],
            'email'         => $address['bill_email'],
            'phone'         => $address['bill_phone'],
            'shipFirstName' => $address['ship_first_name'],
            'shipLastName'  => $address['ship_last_name'],
            'shipCountry'   => $address['ship_country_isoa2'],
            'shipState'     => $address['ship_state'],
            'shipCity'      => $address['ship_city'],
            'shipAddress'   => $address['ship_address'],
            'shipZipCode'   => $address['ship_postcode'],
            'shipEmail'     => $address['ship_email'],
            'shipPhone'     => $address['ship_phone'],
            'goods'         => json_encode($goods),
            'timestamp'     => Support::timestamp(),
            'sign'          => ''
        );

        $data['sign'] = Support::sign($data, $payConfig['signKey']);

        return Bill::authParser(Support::requestApi('/v1/api/pay', $data, $payload['order_id']));
    }
}
