<?php

namespace App\Classes\Pay\Gateways;

use App\Classes\Pay\Contracts\Gateway;
use App\Classes\Pay\Gateways\Fomo\Support;
use App\Classes\Supports\Config;

class Fomo extends Gateway
{
    protected $gatewayName = 'Fomo';

    /**
     * Const url.
     */
    const URL = [
        self::MODE_NORMAL => 'https://ipg.fomopay.net',
        self::MODE_DEV    => 'https://ipg.fomopay.net',
    ];

    public function __construct(Config $config)
    {
        $this->gateway = Support::create($config)->getBaseUri();
        parent::__construct($config);
    }
}
