<?php

namespace App\Classes\Pay\Gateways;

use App\Classes\Pay\Contracts\Gateway;
use App\Classes\Pay\Gateways\Ppae\Support;
use App\Classes\Supports\Config;

class Ppae extends Gateway
{
    protected $gatewayName = 'Ppae';

    /**
     * Const url.
     */
    const URL = [
        self::MODE_NORMAL => 'https://acquirer-payment.pingpongx.com',
        self::MODE_DEV    => 'https://sandbox-acquirer-payment.pingpongx.com',
    ];

    public function __construct(Config $config)
    {
        $this->gateway = Support::create($config)->getBaseUri();
        parent::__construct($config);
    }
}
