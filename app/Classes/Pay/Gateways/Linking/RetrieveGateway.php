<?php

namespace App\Classes\Pay\Gateways\Linking;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Parser\Linking;

class RetrieveGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig = $payload['pay_config'];
        $data      = [
            'agentId' => $payConfig['agentId'],
            'orderNo' => $payload['order_id'],
        ];

        $transitIp = '';
        if (!empty($payConfig['transitIp'])) {
            // 有中转网址
            $transitIp = $payConfig['transitIp'] . '?' . Support::urlPayment($payConfig);
        }

        // 生成签名
        $data['sign'] = Support::sign($data['agentId'] . $data['orderNo'] . $payConfig['key']);
        $post = [
            'transitIp' => $transitIp,
            'data'      => $data
        ];

        return Linking::retrieveParser(Support::requestApi('/out/query', $post, 'post', $payload['order_id']));
    }
}
