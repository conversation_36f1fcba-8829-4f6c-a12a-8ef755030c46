<?php

namespace App\Classes\Pay\Gateways\Baiwanpay;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Baiwanpay;

class RetrieveGateway extends Gateway
{
	/**
	 * Pay an order.
	 *
	 * @param string $endpoint
	 *
	 * @param array $payload
	 * @return Collection
	 */
	public function pay($endpoint, array $payload): Collection
	{
		$payConfig    = $payload['pay_config'];
		$paymentOrder = $payload['payment_order'];

		$data = array(
			'merchantNo'    => $payConfig['merchantNo'],
			'terminalNo'    => $payConfig['terminalNo'],
			'orderNo'       => $paymentOrder['order_id'],
		);

		$signStr               = implode('', $data) . $payConfig['signKey'];
		$data['securityValue'] = hash('sha256',$signStr);

		return Baiwanpay::retrieveParser(Support::requestApi('after-sales/query-by-order.json', $data, $payload['order_id']));
	}
}
