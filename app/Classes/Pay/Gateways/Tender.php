<?php

namespace App\Classes\Pay\Gateways;

use App\Classes\Pay\Contracts\Gateway;
use App\Classes\Pay\Gateways\Tender\Support;
use App\Classes\Supports\Config;

class Tender extends Gateway
{
    protected $gatewayName = 'Tender';

    /**
     * Const url.
     */
    const URL = [
        self::MODE_NORMAL => 'https://pay-eu.paytend.com',
        self::MODE_DEV    => 'https://test.paytend.com',
    ];

    public function __construct(Config $config)
    {
        $this->gateway = Support::create($config)->getBaseUri();
        parent::__construct($config);
    }
}
