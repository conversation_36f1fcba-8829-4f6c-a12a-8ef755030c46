<?php

namespace App\Classes\Pay\Gateways;

use App\Classes\Pay\Contracts\Gateway;
use App\Classes\Pay\Gateways\Acapay\Support;
use App\Classes\Supports\Config;

class Acapay extends Gateway
{
    protected $gatewayName = 'Acapay';

    /**
     * Const url.
     */
    const URL = [
        self::MODE_NORMAL => 'http://bud.pengpaying.com/gateway',
        self::MODE_DEV    => 'http://bud.pengpaying.com/gateway',
    ];

    public function __construct(Config $config)
    {
        $this->gateway = Support::create($config)->getBaseUri();
        parent::__construct($config);
    }
}
