<?php

namespace App\Classes\Pay\Gateways\Goodiespay;

use App\Classes\Supports\Collection;
use Illuminate\Support\Facades\Cache;
use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Goodiespay;

class AuthGateway extends Gateway
{
	/**
	 * 卡种数据
	 *
	 * @var array
	 */
	protected $_cardData = array(
		'V' => 'Visa',
		'M' => 'MasterCard',
		'J' => 'JCB',
		'A' => 'AmericanExpress',
		'D' => 'Diners',
	);

    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     * @throws \App\Classes\Pay\Exceptions\GatewayException
     * @throws \App\Classes\Pay\Exceptions\InvalidSignException
     */
    public function pay($endpoint, array $payload): Collection
    {
	    $payConfig = $payload['pay_config'];
	    $address   = $payload['address'];
	    $card      = $payload['card'];
	    $products  = $payload['products'];

        // 组装支付数据
	    $productInfo = array();

        if (!empty($products)) {
        	foreach ($products as $product) {
		        $productInfo[] = array(
			        'skuId'        => $product['sku'],
			        'productTitle' => $product['name'],
			        'price'        => $product['price'],
			        'count'        => $product['qty'],
			        'productUrl'   => $product['url'],
			        'oldOrderNo'   => $payload['order_number'],
		        );
	        }
        }

	    // 缓存key
	    $mark    = MD5($payload['order_id'] . 'goodiespay');
	    $markArr = [
		    'order_id' => $payload['order_id'],
		    'class'    => 'Goodiespay',
	    ];
	    Cache::add($mark, $markArr, 24*60*60);

	    $data = array(
		    'bgRetUrl'        => \route('api.v1.asyncNotify', ['mark' => $mark], true), //服务端回调地址
		    'cardnum'         => $card['card_number'],
		    'currencyCode'    => $payload['payment_order']['currency'], //币种类型
		    'cvv2'            => $card['cvv'],
		    'grandTotal'      => Support::amount_format($payload['payment_order']['amount'] * $this->_getAmountRate($payload['payment_order']['currency']), 0), //支付金额 （单位：分，整数）
		    'merchantId'      => $payConfig['merchantId'], //商户id
		    'orderAmount'     => Support::amount_format($payload['payment_order']['amount']),
		    'orderId'         => $payload['order_id'], //订单id
		    'paymentProduct'  => $this->_cardData[$card['cc_type']] ?? '',
		    'platformtype'    => '0',
		    'timestamp'       => Support::millisecond(), //时间戳（毫秒）
	    );

	    // 组装签名
	    $signStr = '';
	    foreach ($data as $key => $value) {
		    $signStr .= $key . '=' . $value . '&';
	    }

	    $signStr      = rtrim($signStr, '&');
	    $signStr      = strtolower($signStr) . $data['merchantId'] . $payConfig['signKey'];
	    $data['sign'] = sha1($signStr);

	    $post = array(
		    'merchantId'                     => $data['merchantId'], //商户id
		    'orderId'                        => $data['orderId'], //订单id
		    'orderAmount'                    => $data['orderAmount'],
		    'discount'                       => '0', //优惠金额（单位：元，两位小数）
		    'postage'                        => '0', //邮费（单位：元，两位小数）
		    'grandTotal'                     => $data['grandTotal'], //支付金额 （单位：分，整数）
		    'currencyCode'                   => $data['currencyCode'], //币种类型
		    'countryCode'                    => $address['bill_country_isoa2'], //国家编码
		    'bid'                            => '',
		    'timestamp'                      => $data['timestamp'], //时间戳（毫秒）
		    'bgRetUrl'                       => $data['bgRetUrl'], //服务端回调地址
		    'sign'                           => $data['sign'],
		    'cardInfo.paymentProduct'        => $data['paymentProduct'],
		    'cardInfo.cardholderName'        => $address['bill_name'],
		    'cardInfo.cardnum'               => $data['cardnum'],
		    'cardInfo.cvv2'                  => $data['cvv2'],
		    'cardInfo.month'                 => $card['expiration_month'],
		    'cardInfo.year'                  => '20' . $card['expiration_year'],
		    'cardInfo.cardbank'              => '',
		    'customer.firstname'             => $address['bill_first_name'],
		    'customer.lastname'              => $address['bill_last_name'],
		    'customer.email'                 => $address['bill_email'],
		    'customer.phone'                 => $address['bill_phone'],
		    'customer.country'               => $address['bill_country_isoa2'],
		    'customer.state'                 => $address['bill_state'],
		    'customer.city'                  => $address['bill_city'],
		    'customer.address'               => $address['bill_address'],
		    'customer.houseNumber'           => '',
		    'customer.zipcode'               => $address['ship_postcode'],
		    'shippingCustomer.firstname'     => $address['ship_first_name'],
		    'shippingCustomer.lastname'      => $address['ship_last_name'],
		    'shippingCustomer.email'         => $address['ship_email'],
		    'shippingCustomer.phone'         => $address['ship_phone'],
		    'shippingCustomer.country'       => $address['ship_country_isoa2'],
		    'shippingCustomer.state'         => $address['ship_state'],
		    'shippingCustomer.city'          => $address['ship_city'],
		    'shippingCustomer.address'       => $address['ship_address'],
		    'shippingCustomer.houseNumber'   => '',
		    'shippingCustomer.zipcode'       => $address['ship_postcode'],
		    'billingAddressInfo.state'       => '',
		    'billingAddressInfo.city'        => '',
		    'billingAddressInfo.address'     => '',
		    'billingAddressInfo.houseNumber' => '',
		    'billingAddressInfo.zipcode'     => '',
	    );

		//订单明细
	    if (!empty($productInfo)) {
		    foreach ($productInfo  as $num => $value) {
			    foreach ($value as $k => $val) {
				    $key        = 'orderDetails['. $num . '].' . $k;
				    $post[$key] = $val;
			    }
		    }
	    }

        return Goodiespay::authParser(Support::requestApi('paypage/api', $post, '', $payload['order_id']));
    }

	/**
	 * 获取金额比例
	 *
	 * @param $currency
	 * @return mixed|string
	 */
	private function _getAmountRate($currency)
	{
		$currencyDes = array(
			'CNY' => '100',
			'USD' => '100',
			'GBP' => '100',
			'EUR' => '100',
			'CAD' => '100',
			'AUD' => '100',
			'HKD' => '100',
			'JPY' => '1',
			'KRW' => '1',
			'SGD' => '100',
			'TWD' => '100',
			'DKK' => '100',
			'INR' => '100',
			'THB' => '100',
			'MYR' => '100',
			'RUB' => '100',
			'SEK' => '100',
			'ZAR' => '100',
			'CHF' => '100',
			'PLN' => '100',
			'NOK' => '100',
			'BRL' => '100',
			'ARS' => '100',
			'IDR' => '100',
			'CZK' => '100'
		);

		return isset($currencyDes[$currency]) ? $currencyDes[$currency] : '1';
	}
}