<?php

namespace App\Classes\Pay\Gateways\Goodiespay;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Goodiespay;
use Illuminate\Support\Facades\Cache;

class RefundGateway extends Gateway
{
	/**
	 * Pay an order.
	 *
	 * @param string $endpoint
	 *
	 * @param array $payload
	 * @return Collection
	 */
	public function pay($endpoint, array $payload)
	{
		$payConfig    = $payload['pay_config'];
		$paymentOrder = $payload['payment_order'];

		// 缓存key
		$mark    = MD5($payload['refund_id'] . 'goodiespay');
		$markArr = [
			'order_id'  => $payload['order_id'],
			'refund_id' => $payload['refund_id'],
			'class'     => 'Goodiespay',
		];
		Cache::add($mark, $markArr, 30 * 24 * 60 * 60);

		$data = array(
			'applyRefundAmount' => Support::amount_format($payload['payment_refund']['amount']),
			'callbackServer'    => route('api.v1.notify', ['mark' => $mark], true),
			'merchantId'        => $payConfig['merchantId'],
			'payId'             => $paymentOrder['payment_order_id'],
		);

		// 组装签名
		$signStr      = strtolower('merchantid=' . $payConfig['merchantId'] . '&payid=' . $data['payId']) . $payConfig['merchantId'] . $payConfig['signKey'];
		$data['sign'] = sha1($signStr);

		return Goodiespay::refundParser(Support::requestApi('pay/addPayRefund', $data, 'json', $payload['refund_id']));
	}
}
