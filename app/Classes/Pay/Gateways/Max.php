<?php

namespace App\Classes\Pay\Gateways;

use App\Classes\Pay\Contracts\Gateway;
use App\Classes\Pay\Gateways\Max\Support;
use App\Classes\Supports\Config;

class Max extends Gateway
{
    protected $gatewayName = 'Max';

    /**
     * Const url.
     */
    const URL = [
        self::MODE_NORMAL => 'https://gateway.upluspay.com',
        self::MODE_DEV    => 'http://e.my03.com:8074',
    ];

    public function __construct(Config $config)
    {
        $this->gateway = Support::create($config)->getBaseUri();
        parent::__construct($config);
    }
}
