<?php

namespace App\Classes\Pay\Gateways\Gtw;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Gtw;

class RetrieveGateway extends Gateway
{
	/**
	 * Pay an order.
	 *
	 * @param string $endpoint
	 *
	 * @param array $payload
	 * @return Collection
	 * @throws \App\Classes\Pay\Exceptions\GatewayException
	 * @throws \App\Classes\Pay\Exceptions\InvalidSignException
	 */
	public function pay($endpoint, array $payload)
	{
		$payConfig    = $payload['pay_config'];
		$paymentOrder = $payload['payment_order'];

		$data = array(
			'merchantId'    => $payConfig['merchantId'],
			'version'       => 'v1.0',
			'signType'      => '0',
			'orderNo'       => $paymentOrder['order_id'],
			'orderDatetime' => date('YmdHis', strtotime($paymentOrder['created_at'])),
			'queryDatetime' => date('YmdHis'),
			'secretKey'     => $payConfig['secretKey'],
			'signMsg'       => '',
		);

		$str               = implode('&', $data);
		$data['signMsg']   = hash("sha256", rtrim($str, '&'));

		return Gtw::authParser(Support::requestApi('/pay-gateway/gateway/order_query', $data, '', $payload['order_id']));
	}
}
