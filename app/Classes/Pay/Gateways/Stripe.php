<?php

namespace App\Classes\Pay\Gateways;

use App\Classes\Pay\Contracts\Gateway;
use App\Classes\Pay\Gateways\Stripe\Support;
use App\Classes\Supports\Config;

class Stripe extends Gateway
{
    protected $gatewayName = 'Stripe';

    /**
     * Const url.
     */
    const URL = [
        self::MODE_NORMAL => '',
        self::MODE_DEV    => '',
    ];

    public function __construct(Config $config)
    {
        $this->gateway = Support::create($config)->getBaseUri();
        parent::__construct($config);
    }
}
