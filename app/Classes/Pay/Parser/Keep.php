<?php

namespace App\Classes\Pay\Parser;

use App\Classes\Pay\Gateways\Keep\Support;
use App\Classes\Supports\Arr;
use App\Classes\Supports\Collection;
use App\Models\Order;
use App\Models\Refund;
use Illuminate\Support\Facades\Cache;

class Keep extends BaseParser
{
	protected static $_supplierName = 'Keep';

    public static function _authParser($params) : Collection
    {
	    $status = Order::STATUS_DECLINED;
	    $code   = $params['data']['status'] ?: $params['code'] ?: 'time out';
		$result = $params['data']['message'] ?: $params['message'] ?: '';
        $result = strlen($result) > 128 ?  mb_strcut($result, 0, 128) : $result;
	    $html   = '';

	    if (isset($params['data']['status']) && isset($params['data']['is3D'])) {
		    if ($params['data']['status'] == '2' && $params['data']['is3D'] == 'false') { // 1失败，2成功，3、4待处理
			    $status = Order::STATUS_APPROVED;
		    } else if (in_array($params['data']['status'], ['3', '4'])) {
			    $status = Order::STATUS_PENDING;
		    }

		    if ($params['data']['is3D'] == 'true') {

                $url = urldecode($params['data']['redirectURL']);
                if (!empty($params['pay_config']['transitUrl'])) {
                    $mark       = MD5($params['data']['tradeNo'] . 'll3d');
                    $verifyHtml = self::formPackage('', $url);
                    $markArr    = [
                        'transitVerify' => base64_encode($verifyHtml),
                    ];

                    Cache::add($mark, $markArr, 1800);

                    $url = $params['pay_config']['transitUrl'] . '/Verify.php?' . Support::urlPayment($params['pay_config'], ['mark' => $mark]);
                }

			    $html = $params['data']['is3DHtml'] == 'false' ? self::formPackage('', $url) : base64_decode($params['data']['htmlContent']);
		    }
	    }

		if ($code == 'time out') {
			$status = Order::STATUS_PENDING;
		}

        $paymentOrderCollection = new Collection();
        $paymentOrderCollection->set('payment_order_id', $params['data']['tradeNo'] ?? '0');
        $paymentOrderCollection->set('type', Order::TYPES_SALE);
        $paymentOrderCollection->set('code', $code);
        $paymentOrderCollection->set('result', $result);
        $paymentOrderCollection->set('remark', $params['code'] ?? '');
        $paymentOrderCollection->set('html', $html);
        $paymentOrderCollection->set('status', $status);

	    $code = self::$_channelExternalCode[$paymentOrderCollection->result] ?? get_system_code('099');

	    if ($status == Order::STATUS_APPROVED) {
		    $code = get_system_code('000');
	    } elseif ($status == Order::STATUS_PENDING) {
		    $code = get_system_code('200');
	    }

		//超时默认用p095
        if ($paymentOrderCollection->code == 'time out') {
            $code = get_system_code('095');
        }

	    $orderCollection = new Collection();
	    $orderCollection->set('type', Order::TYPES_SALE);
	    $orderCollection->set('status', self::$_respCode[$code][0]);
	    $orderCollection->set('code', $code);
	    $orderCollection->set('result', self::$_respCode[$code][1]);
	    $orderCollection->set('remark', self::$_respCode[$code][2]);

	    // 判断是否3d
	    if ($code == get_system_code('200') && $paymentOrderCollection->html) {
		    $orderCollection->set('is_3d', 1);
	    }

	    // 卡账单获取
	    if (isset($params['data']['billDescription']) && !empty($params['data']['billDescription'])) {
		    $cardBill = strlen($params['data']['billDescription']) > 64 ? mb_strcut($params['data']['billDescription'], 0, 64) : $params['data']['billDescription'];
		    $orderCollection->set('card_bill', $cardBill);
	    }

        $collection = new Collection();
        $collection->set('order', $orderCollection->toArray());
        $collection->set('payment_order', $paymentOrderCollection->toArray());

        return $collection;
    }

    public static function captureParser($params) : Collection
    {
        return self::authParser($params);
    }

	public static function _refundParser($params) : Collection
	{
		$status = Refund::STATUS_DECLINED;
		$code   = $params['code'] ?: 'time out';
		$result = $params['message'] ?: '';
        $result = strlen($result) > 128 ?  mb_strcut($result, 0, 128) : $result;

		switch ($code) {
			case '00':
				$status = Refund::STATUS_APPROVED;
				break;
			case 'time out':
				$status = Refund::STATUS_PENDING;
				break;
		}

		$paymentRefundCollection = new Collection();
		$paymentRefundCollection->set('payment_refund_id', $params['tradeNo'] ?? '0');
		$paymentRefundCollection->set('code', $code);
		$paymentRefundCollection->set('result', $result);
		$paymentRefundCollection->set('remark', '');
		$paymentRefundCollection->set('status', $status);

		$code = self::$_channelExternalCode[$paymentRefundCollection->result] ?? get_system_code('099');

		if ($status == Refund::STATUS_PENDING) {
            $code = get_system_code('200');
        } elseif ($status == Refund::STATUS_APPROVED) {
            $code = get_system_code('000');
        }

        //超时默认用p095
        if ($paymentRefundCollection->code == 'time out') {
            $code = get_system_code('095');
        }

		$refundCollection = new Collection();
		$refundCollection->set('status', $status);
		$refundCollection->set('code', $code);
		$refundCollection->set('result', self::$_respCode[$code][1]);
		$refundCollection->set('remark', self::$_respCode[$code][2]);

		$collection = new Collection();
		$collection->set('refund', $refundCollection->toArray());
		$collection->set('payment_refund', $paymentRefundCollection->toArray());

		return $collection;
	}

	public static function _retrieveParser($params) : Collection
	{
		$receive = self::authParser($params);

		// 移除不更新的选项
		if (empty($receive['payment_order']['payment_order_id'])) {
			$receive['payment_order'] = Arr::except($receive['payment_order'], ['payment_order_id']);
		}

		return $receive;
	}

    public static function syncParser($params) : Collection
    {
	    return self::asyncParser($params);
    }

	public static function _asyncParser($params) : Collection
	{
		$status = Order::STATUS_DECLINED;
	    $code   = $params['status'] ?: 'time out';
		$result = $params['message'] ?: '';
        $result = strlen($result) > 128 ?  mb_strcut($result, 0, 128) : $result;

	    if (isset($params['status'])) {
		    if ($params['status'] == '2') { // 1失败，2成功，3、4待处理
			    $status = Order::STATUS_APPROVED;
		    } else if (in_array($params['status'], ['3', '4'])) {
			    $status = Order::STATUS_PENDING;
		    }
	    }

		if ($code == 'time out') {
			$status = Order::STATUS_PENDING;
		}

        $paymentOrderCollection = new Collection();
        $paymentOrderCollection->set('payment_order_id', $params['tradeNo'] ?? '0');
        $paymentOrderCollection->set('type', Order::TYPES_SALE);
        $paymentOrderCollection->set('code', $code);
        $paymentOrderCollection->set('result', $result);
        $paymentOrderCollection->set('html', '');
        $paymentOrderCollection->set('status', $status);

	    $code = self::$_channelExternalCode[$paymentOrderCollection->result] ?? get_system_code('099');

	    if ($status == Order::STATUS_APPROVED) {
		    $code = get_system_code('000');
	    } elseif ($status == Order::STATUS_PENDING) {
		    $code = get_system_code('200');
	    }

		//超时默认用p095
        if ($paymentOrderCollection->code == 'time out') {
            $code = get_system_code('095');
        }

	    $orderCollection = new Collection();
	    $orderCollection->set('type', Order::TYPES_SALE);
	    $orderCollection->set('status', self::$_respCode[$code][0]);
	    $orderCollection->set('code', $code);
	    $orderCollection->set('result', self::$_respCode[$code][1]);
	    $orderCollection->set('remark', self::$_respCode[$code][2]);

	    // 卡账单获取
	    if (isset($params['billDescription']) && !empty($params['billDescription'])) {
		    $cardBill = strlen($params['billDescription']) > 64 ? mb_strcut($params['billDescription'], 0, 64) : $params['billDescription'];
		    $orderCollection->set('card_bill', $cardBill);
	    }

        $collection = new Collection();
        $collection->set('order', $orderCollection->toArray());
        $collection->set('payment_order', $paymentOrderCollection->toArray());

        return $collection;
	}
}