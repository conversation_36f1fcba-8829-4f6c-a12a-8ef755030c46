<?php

namespace App\Classes\Pay\Parser;

use App\Classes\Supports\Collection;
use App\Models\Order;
use App\Models\Refund;
use Illuminate\Support\Facades\Cache;

class Ppae extends BaseParser
{
    protected static $_supplierName = 'Ppae';

    public static function _authParser($params): Collection
    {
        $code   = $params['status'] ?? $params['code'] ?? 'time out';
        $result = $params['description'] ?? $params['msg'] ?? '';
        $result = strlen($result) > 128 ?  mb_strcut($result, 0, 128) : $result;
        
        switch ($code) {
            case 'SUCCESS':
                $status = Order::STATUS_APPROVED;
                break;
            case 'PROCESSING':
            case 'PENDING':
            case 'REVIEW':
            case 'time out':
            case '001000':
            case '002000':
            case '600002':
                $status = Order::STATUS_PENDING;
                break;
            default:
                $status = Order::STATUS_DECLINED;
                break;
        }

        $paymentOrderCollection = new Collection();
        $paymentOrderCollection->set('type', Order::TYPES_SALE);
        $paymentOrderCollection->set('payment_order_id', $params['transactionId'] ?? '0');
        $paymentOrderCollection->set('code', $params['code'] ?? '');
        $paymentOrderCollection->set('result', $result);
        $paymentOrderCollection->set('status', $status);
        $paymentOrderCollection->set('remark', '');
        $paymentOrderCollection->set('html', '');

        $code = self::$_channelExternalCode[$paymentOrderCollection->code] ?? get_system_code('099');

        if ($status == Order::STATUS_APPROVED) {
            $code = get_system_code('000');
        } elseif ($status == Order::STATUS_PENDING) {
            $code = get_system_code('200');
        }
        
        if (in_array($paymentOrderCollection->code, ['time out', '600002'])) {
            $code = get_system_code('095');
        }

        $orderCollection = new Collection();
        if (isset($params['acsUrl']) && !empty($params['acsUrl'])) {
            if (!empty($params['pay_config']['interceptFeeling']) && $params['pay_config']['interceptFeeling'] == 1) {
                $code = get_system_code('151');
                $paymentOrderCollection->set('status', Order::STATUS_DECLINED);
            } else {
                $code = get_system_code('200');
                $html = self::formPackage('', $params['acsUrl']);
                $paymentOrderCollection->set('html', $html);
            }

            $orderCollection->set('is_3d', 1);
        }
        
        $orderCollection->set('status', self::$_respCode[$code][0]);
        $orderCollection->set('code', $code);
        $orderCollection->set('type', Order::TYPES_SALE);
        $orderCollection->set('result', self::$_respCode[$code][1]);
        $orderCollection->set('remark', self::$_respCode[$code][2]);

        // 无感3D处理
        if (isset($params['threeDSecure'])) {
            if ($params['threeDSecure'] == 'Y') {
                $orderCollection->set('is_3d', 1);
            }
        } elseif (!empty($params['transactionId'])) {
            // 从缓存中获取3D标识并给is_3d字段赋值
            $keyName      = md5($params['transactionId'] . 'threeDSecure');
            $threeDSecure = Cache::get($keyName);

            if ($threeDSecure && $threeDSecure == 'Y') {
                $orderCollection->set('is_3d', 1);
            }
        }

        $collection = new Collection();
        $collection->set('order', $orderCollection->toArray());
        $collection->set('payment_order', $paymentOrderCollection->toArray());
        return $collection;
    }

    public static function _refundParser($params): Collection
    { 
        $code   = $params['status'] ?? $params['code'] ?? 'time out';
        $result = $params['description'] ?? $params['msg'] ?? '';
        $result = strlen($result) > 128 ?  mb_strcut($result, 0, 128) : $result;
        
        switch ($code) {
            case 'SUCCESS':
                $status = Refund::STATUS_APPROVED;
                break;
            case 'PROCESSING':
            case 'time out':
            case '001000':
            case '002000':
            case '600002':
                $status = Refund::STATUS_PENDING;
                break;
            default:
                $status = Refund::STATUS_DECLINED;
                break;
        }

        $paymentRefundCollection = new Collection();
        $paymentRefundCollection->set('payment_refund_id', $params['transactionId'] ?? '0');
        $paymentRefundCollection->set('code', $params['code'] ?? '');
        $paymentRefundCollection->set('result', $result);
        $paymentRefundCollection->set('status', $status);
        $paymentRefundCollection->set('remark', '');

        $code = self::$_channelExternalCode[$paymentRefundCollection->code] ?? get_system_code('099');

        if ($status == Refund::STATUS_APPROVED) {
            $code = get_system_code('000');
        } elseif ($status == Refund::STATUS_PENDING) {
            $code = get_system_code('200');
        }
        
        if (in_array($paymentRefundCollection->code, ['time out', '600002'])) {
            $code = get_system_code('095');
        }

        $refundCollection = new Collection();
        $refundCollection->set('status', self::$_respCode[$code][0]);
        $refundCollection->set('code', $code);
        $refundCollection->set('result', self::$_respCode[$code][1]);
        $refundCollection->set('remark', self::$_respCode[$code][2]);

        $collection = new Collection();
        $collection->set('refund', $refundCollection->toArray());
        $collection->set('payment_refund', $paymentRefundCollection->toArray());

        return $collection;
    }

    public static function syncParser($params): Collection
    {
        return self::authParser($params);
    }

    public static function asyncParser($params): Collection
    {
        // 异步缓存无感3D标识
        if (!empty($params['threeDSecure']) && !empty($params['transactionId'])) {
            $keyName = md5($params['transactionId'] . 'threeDSecure');
            
            Cache::put($keyName, $params['threeDSecure'], 180);
        }
        
        return self::authParser($params);
    }

    public static function _notifyParser($params): Collection
    {
        return self::_refundParser($params);
    }

    public static function _retrieveParser($params): Collection
    {
        return self::authParser($params);
    }
}
