<?php

namespace App\Classes\Pay\Parser;

use App\Classes\Supports\Collection;
use App\Models\Order;
use App\Models\Refund;

class Booty extends BaseParser
{
    protected static $_supplierName = 'Booty';

    public static function _authParser($params): Collection
    {
        $status            = Order::STATUS_PENDING;
        $transactionState  = $params['transaction']['transaction_state'] ?? 'time out';
        
        if ($transactionState == 'success') {
            $status = Order::STATUS_APPROVED;
            $code   = '100000';
        }

        if ($transactionState == 'failed') {
            $status = Order::STATUS_DECLINED;
        }

        $paymentOrderCollection = new Collection();
        $paymentOrderCollection->set('payment_order_id', $params['transaction']['brn'] ?? $params['uuid'] ?? '0');
        $paymentOrderCollection->set('code', $code ?? $params['transaction']['error_code'] ?? '');
        $paymentOrderCollection->set('result', $params['transaction']['error_desc'] ?? $transactionState ?? '');
        $paymentOrderCollection->set('type', Order::TYPES_SALE);
        $paymentOrderCollection->set('remark', $params['transaction']['uuid'] ?? '');
        $paymentOrderCollection->set('html', '');
        $paymentOrderCollection->set('status', $status);

        $code = self::$_channelExternalCode[$paymentOrderCollection->code] ?? get_system_code('099');

        if ($status == Order::STATUS_APPROVED) {
            $code = get_system_code('000');
        }
        
        if ($transactionState == 'time out') {
            $code = get_system_code('095');
        }

        $orderCollection = new Collection();
        $orderCollection->set('type', Order::TYPES_SALE);
        $orderCollection->set('status', self::$_respCode[$code][0]);
        $orderCollection->set('code', $code);
        $orderCollection->set('result', self::$_respCode[$code][1]);
        $orderCollection->set('remark', self::$_respCode[$code][2]);

        $collection = new Collection();
        $collection->set('order', $orderCollection->toArray());
        $collection->set('payment_order', $paymentOrderCollection->toArray());

        return $collection;
    }

    public static function _retrieveParser($params): Collection
    {
        return self::authParser($params);
    }

    public static function _refundParser($params): Collection
    {
        $status            = Order::STATUS_PENDING;
        $transactionState  = $params['transaction']['transaction_state'] ?? 'time out';

        if ($transactionState == 'success') {
            $status = Order::STATUS_APPROVED;
            $code   = '100000';
        }

        if ($transactionState == 'failed') {
            $status = Order::STATUS_DECLINED;
        }

        $paymentRefundCollection = new Collection();
        $paymentRefundCollection->set('payment_refund_id', $params['transaction']['brn'] ?? '0');
        $paymentRefundCollection->set('code', $code ?? $params['transaction']['error_code'] ?? '');
        $paymentRefundCollection->set('result', $params['transaction']['error_desc'] ?? $transactionState ?? '');
        $paymentRefundCollection->set('remark', $params['transaction']['uuid'] ?? '');
        $paymentRefundCollection->set('status', $status);

        $code = self::$_channelExternalCode[$paymentRefundCollection->code] ?? get_system_code('099');

        if ($status == Refund::STATUS_APPROVED) {
            $code = get_system_code('000');
        }
        
        if ($transactionState == 'time out') {
            $code = get_system_code('095');
        }

        $refundCollection = new Collection();
        $refundCollection->set('status', self::$_respCode[$code][0]);
        $refundCollection->set('code', $code);
        $refundCollection->set('result', self::$_respCode[$code][1]);
        $refundCollection->set('remark', self::$_respCode[$code][2]);

        $collection = new Collection();
        $collection->set('refund', $refundCollection->toArray());
        $collection->set('payment_refund', $paymentRefundCollection->toArray());

        return $collection;
    }

    public static function _asyncParser($params): Collection
    {
        return self::authParser($params);
    }

}
