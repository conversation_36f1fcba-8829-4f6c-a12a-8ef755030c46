<?php

namespace App\Classes\Pay\Parser;

use App\Classes\Pay\Pay;
use App\Classes\Supports\Arr;
use App\Classes\Supports\Collection;
use App\Http\Controllers\Traits\PaymentController;
use App\Models\Channel;
use App\Models\Order;
use App\Models\Refund;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class Fomo extends BaseParser
{
    use PaymentController;

    protected static $_supplierName = 'Fomo';

    public static function _authParser($params): Collection
    {
        $status = Order::STATUS_PENDING;

        // fomo 502 返回 从异步通知获取数据
        if (!isset($params['status']) && count($params) == 1 && isset($params['orderNo'])) {
            $backData = [];
            for ($i = 0; $i < 10; $i++) {
                sleep(1);

                // 获取缓存并跳出循环
                $cacheKey = MD5($params['orderNo'] . 'fomoAsync');

                if (Cache::has($cacheKey)) {
                    $backData = Cache::get($cacheKey);
                    break;
                }
            }

            if ($backData) {
                return $backData;
            }
        }

        if (isset($params['status']) && in_array($params['status'], ['FAIL', 'CLOSED', 'ERROR'])) {
            $status = Order::STATUS_DECLINED;
        }

        if (isset($params['status']) && $params['status'] == 'SUCCESS') {
            $status = Order::STATUS_APPROVED;
        }

        $code = $params['state'] ?? $params['status'] ?? '';
        $paymentOrderCollection = new Collection();
        $paymentOrderCollection->set('payment_order_id', $params['id'] ?? '0');
        $paymentOrderCollection->set('type', Order::TYPES_SALE);
        $paymentOrderCollection->set('code', $code);
        $paymentOrderCollection->set('result', $params['message'] ?? '');
        $paymentOrderCollection->set('remark', $params['primaryTransactionId'] ?? $params['hint'] ?? '');
        $paymentOrderCollection->set('html', '');
        $paymentOrderCollection->set('status', $status);

        $code = self::$_channelExternalCode[$paymentOrderCollection->code] ?? get_system_code('099');

        if ($status == Order::STATUS_PENDING && isset($params['url']) && !empty($params['url'])) {
            $code = get_system_code('200');
            $html = self::formPackage('', $params['url']);
            $paymentOrderCollection->set('html', $html);
        }

        if ($status == Order::STATUS_APPROVED) {
            $code = get_system_code('000');
        } elseif ($status == Order::STATUS_PENDING) {
            $code = get_system_code('210');
        }

        $orderCollection = new Collection();
        $orderCollection->set('type', Order::TYPES_SALE);
        $orderCollection->set('status', self::$_respCode[$code][0]);
        $orderCollection->set('code', $code);
        $orderCollection->set('result', self::$_respCode[$code][1]);
        $orderCollection->set('remark', self::$_respCode[$code][2]);

        // 判断是否3d
        if ($code == get_system_code('200') && $paymentOrderCollection->html) {
            $orderCollection->set('is_3d', 1);
        }

        $collection = new Collection();
        $collection->set('order', $orderCollection->toArray());
        $collection->set('payment_order', $paymentOrderCollection->toArray());
        if (isset($extra)) {
            $collection->set('extra', $extra);
        }
        return $collection;
    }

    public static function captureParser($params): Collection
    {
        return self::authParser($params);
    }

    public static function _refundParser($params): Collection
    {
        $status = Refund::STATUS_PENDING;

        if (isset($params['status']) && in_array($params['status'], ['FAIL', 'CLOSED', 'ERROR'])) {
            $status = Order::STATUS_DECLINED;
        }

        if (isset($params['status']) && $params['status'] == 'SUCCESS') {
            $status = Order::STATUS_APPROVED;
        }

        $paymentRefundCollection = new Collection();
        $paymentRefundCollection->set('payment_refund_id', $params['id'] ?? '0');
        $paymentRefundCollection->set('code', $params['status'] ?? '');
        $paymentRefundCollection->set('result', $params['message'] ?? '');
        $paymentRefundCollection->set('remark', $params['hint'] ?? '');
        $paymentRefundCollection->set('status', $status);

        $code = self::$_channelExternalCode[$paymentRefundCollection->code] ?? get_system_code('099');

        if ($status == Refund::STATUS_PENDING) {
            $code = get_system_code('200');
        } elseif ($status == Refund::STATUS_APPROVED) {
            $code = get_system_code('000');
        }

        $refundCollection = new Collection();
        $refundCollection->set('status', $status);
        $refundCollection->set('code', $code);
        $refundCollection->set('result', self::$_respCode[$code][1]);
        $refundCollection->set('remark', self::$_respCode[$code][2]);

        $collection = new Collection();
        $collection->set('refund', $refundCollection->toArray());
        $collection->set('payment_refund', $paymentRefundCollection->toArray());

        return $collection;
    }

    public static function _retrieveParser($params): Collection
    {
        if (isset($params['extra'])) {

            $status = $params['extra']['state'] == 'APPROVED' ? Order::STATUS_APPROVED : Order::STATUS_DECLINED;

            $paymentOrderCollection = new Collection();
            $paymentOrderCollection->set('type', Order::TYPES_SALE);
            $paymentOrderCollection->set('code', $params['extra']['state'] ?? '');
            $paymentOrderCollection->set('remark', $params['id'] ?? '');
            $paymentOrderCollection->set('html', '');
            $paymentOrderCollection->set('status', $status);

            $collection = new Collection();
            $code       = self::$_channelExternalCode[$paymentOrderCollection->code] ?? get_system_code('099');

            if ($status == Order::STATUS_APPROVED) {
                $code = get_system_code('000');
            } else {
                $collection->set('extra', true);
            }

            $orderCollection = new Collection();
            $orderCollection->set('type', Order::TYPES_SALE);
            $orderCollection->set('status', self::$_respCode[$code][0]);
            $orderCollection->set('code', $code);
            $orderCollection->set('result', self::$_respCode[$code][1]);
            $orderCollection->set('remark', self::$_respCode[$code][2]);

            $collection->set('order', $orderCollection->toArray());
            $collection->set('payment_order', $paymentOrderCollection->toArray());

            return $collection;

        }

        return self::authParser($params);
    }

    public static function syncParser($params): Collection
    {
        return self::_notifyParser($params);
    }

    public static function _asyncParser($params): Collection
    {
        return self::_notifyParser($params);
    }

    public static function _notifyParser($params): Collection
    {
        $mark     = MD5($params['orderNo'] . 'fomo');
        $markData = Cache::has($mark) ? Cache::get($mark) : [];


        // 获取订单信息
        $order = Order::with(['paymentOrder'])->findOrFail($params['orderNo']);

        if ($markData['channel_id'] != $order['channel_id']) {
            return new Collection();
        }
   
        // 获取账单标识信息
        $channel = Channel::with(['channelSupplier'])->findOrFail($order->channel_id);

        // 初始化
        $channelSupplierName = strtolower($channel->channelSupplier->file_name);
        $config              = [];

        // 循环组装数据
        foreach ($channel->config as $value) {
            $config[$value['key']] = $value['value'];
        }

        // 增加日志file
        if (!empty($config)) {
            $config['log'] = ['file' => storage_path('logs/' . $channelSupplierName . '.log')];
        }

        $orderArr = $order->toArray();

        if (empty($orderArr['payment_order']['payment_order_id'])) {
            $orderArr['payment_order']['payment_order_id'] = $params['orderId'] ?? '0';
        }

        if (!empty($params['transactionId'])) {
            $orderArr['payment_order']['remark'] = $params['transactionId'];
        }

        $channelName = Pay::$channelSupplierName($config);
        $receive     = $channelName->retrieve($orderArr);

        // 移除不更新的选项
        if (empty($receive['payment_order']['payment_order_id'])) {
            $receive['payment_order'] = Arr::except($receive['payment_order'], ['payment_order_id']);
        }

        // 保存异步返回的结果
        if (!empty($receive['payment_order']['payment_order_id'])) {
            // 缓存key
            $key = MD5($params['orderNo'] . 'fomoAsync');
            Cache::add($key, $receive, 2 * 60 * 60);
        }

        return $receive;
    }

    public static function _refundUpdate($params): Collection
    {
       return self::_refundParser($params);
    }
}
