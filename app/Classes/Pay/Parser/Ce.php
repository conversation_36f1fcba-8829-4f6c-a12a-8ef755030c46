<?php

namespace App\Classes\Pay\Parser;

use App\Classes\Supports\Collection;
use App\Models\Order;
use App\Models\Refund;

class Ce extends BaseParser
{
    protected static $_supplierName = 'Ce';

    public static function _authParser($params): Collection
    {
        $status = Order::STATUS_PENDING;

        if (($params['orderStatus'] ?? $params['queryResult']) == '1') {
            $status = Order::STATUS_APPROVED;
        }

        if (($params['orderStatus'] ?? $params['queryResult']) == '0') {
            $status = Order::STATUS_DECLINED;
        }

        $paymentOrderCollection = new Collection();
        $paymentOrderCollection->set('payment_order_id', $params['tradeNo'] ?? '0');
        $paymentOrderCollection->set('code', $params['orderErrorCode'] ?? $params['resultInfo'] ?? '');
        $paymentOrderCollection->set('result', $params['orderInfo'] ?? $params['resultInfo'] ?? '');
        $paymentOrderCollection->set('type', Order::TYPES_SALE);
        $paymentOrderCollection->set('remark', $params['remark'] ?? '');
        $paymentOrderCollection->set('html', '');
        $paymentOrderCollection->set('status', $status);

        $code = self::$_channelExternalCode[$paymentOrderCollection->result] ?? get_system_code('099');

        if ($status == Order::STATUS_APPROVED) {
            $code = get_system_code('000');
        }

        if ($status == Order::STATUS_PENDING && $params['orderStatus'] < 0) {
            $code = get_system_code('210');
        }

        $orderCollection = new Collection();
        $orderCollection->set('type', Order::TYPES_SALE);
        $orderCollection->set('status', self::$_respCode[$code][0]);
        $orderCollection->set('code', $code);
        $orderCollection->set('result', self::$_respCode[$code][1]);
        $orderCollection->set('remark', self::$_respCode[$code][2]);

        $collection = new Collection();
        $collection->set('order', $orderCollection->toArray());
        $collection->set('payment_order', $paymentOrderCollection->toArray());

        return $collection;
    }

    public static function _retrieveParser($params): Collection
    {
        $retrieve = $params['tradeList'][0] ?? $params;
        return self::authParser($retrieve);
    }

    public static function _refundParser($params): Collection
    {
        $refundInfo = $params['refundOrders'][0] ?? $params;
        $status     = Order::STATUS_PENDING;

        if (isset($refundInfo['refundStatus']) && $refundInfo['refundStatus'] == '1') {
            $status = Order::STATUS_APPROVED;
        }

        if (isset($refundInfo['refundStatus']) && $refundInfo['refundStatus'] == '0') {
            $status = Order::STATUS_DECLINED;
        }

        $paymentRefundCollection = new Collection();
        $paymentRefundCollection->set('payment_refund_id', '0');
        $paymentRefundCollection->set('code', $refundInfo['refundStatus'] ?? '');
        $paymentRefundCollection->set('result', $refundInfo['refundInfo'] ?? '');
        $paymentRefundCollection->set('remark', $refundInfo['tradeNo'] ?? '');
        $paymentRefundCollection->set('status', $status);

        $code = self::$_channelExternalCode[$paymentRefundCollection->code] ?? get_system_code('099');

        if ($status == Refund::STATUS_APPROVED) {
            $code = get_system_code('000');
        }

        if ($status == Refund::STATUS_PENDING) {
            $code = get_system_code('200');
        }

        $refundCollection = new Collection();
        $refundCollection->set('status', self::$_respCode[$code][0]);
        $refundCollection->set('code', $code);
        $refundCollection->set('result', self::$_respCode[$code][1]);
        $refundCollection->set('remark', self::$_respCode[$code][2]);

        $collection = new Collection();
        $collection->set('refund', $refundCollection->toArray());
        $collection->set('payment_refund', $paymentRefundCollection->toArray());

        return $collection;
    }

    public static function _asyncParser($params): Collection
    {
        return self::authParser($params);
    }

}
