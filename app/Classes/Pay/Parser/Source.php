<?php

namespace App\Classes\Pay\Parser;

use App\Classes\Supports\Collection;
use App\Models\Order;
use App\Models\Refund;

class Source extends BaseParser
{
    protected static $_supplierName = 'Source';

    public static function _authParser($params): Collection
    {
        $status    = Order::STATUS_PENDING;
        $payStatus = $code = $params['code'] ?? 'time out';
        $result    = $params['msg'] ?? '';
        if ($payStatus == '0') {
            $payStatus = $params['data']['state'] ?? $payStatus;
            if ($payStatus == '2') {
                $code = $payStatus;
            } else {
                $result = $params['data']['errMsg'] ?? '';
                $code   = $params['data']['errCode'] ?? $code;
            }
        }

        $result = strlen($result) > 128 ?  mb_strcut($result, 0, 128) : $result;
        switch ($payStatus) {
            case '2':
                $status = Order::STATUS_APPROVED;
                break;

            case '0':
            case '1':
            case 'time out':
                $status = Order::STATUS_PENDING;
                break;

            case '3':
            case '4':
            case '5':
            case '9':
            case '9999':
                $status = Order::STATUS_DECLINED;
                break;
        }

        $paymentOrderCollection = new Collection();
        $paymentOrderCollection->set('payment_order_id', $params['data']['payOrderId'] ?? '0');
        $paymentOrderCollection->set('type', Order::TYPES_SALE);
        $paymentOrderCollection->set('code', $code);
        $paymentOrderCollection->set('result', $result);
        $paymentOrderCollection->set('remark', '');
        $paymentOrderCollection->set('html', '');
        $paymentOrderCollection->set('status', $status);

        $code = self::$_channelExternalCode[$paymentOrderCollection->result] ?? get_system_code('099');

        if ($status == Order::STATUS_APPROVED) {
            $code = get_system_code('000');
        } elseif ($status == Order::STATUS_PENDING) {
            $code = get_system_code('200');
        }

        //超时默认用p095
        if ($paymentOrderCollection->code === 'time out') {
            $code = get_system_code('095');
        }

        $orderCollection = new Collection();
        //3D验证链接返回跳转
        if ((!empty($params['data']['payDataType']) && $params['data']['payDataType'] != 'none') && !empty($params['data']['payData'])) {
            $code = get_system_code('200');
            if ($params['data']['payDataType'] == 'form') {
                $paymentOrderCollection->set('html', $params['data']['payData']);
            } else {
                $html = self::formPackage('', $params['data']['payData']);
                $paymentOrderCollection->set('html', $html);
            }

            $orderCollection->set('is_3d', 1);
        }

        $orderCollection->set('status', self::$_respCode[$code][0]);
        $orderCollection->set('type', Order::TYPES_SALE);
        $orderCollection->set('code', $code);
        $orderCollection->set('result', self::$_respCode[$code][1]);
        $orderCollection->set('remark', self::$_respCode[$code][2]);

        $collection = new Collection();
        $collection->set('order', $orderCollection->toArray());
        $collection->set('payment_order', $paymentOrderCollection->toArray());

        return $collection;
    }

    public static function captureParser($params): Collection
    {
        return self::authParser($params);
    }

    public static function _refundParser($params): Collection
    {
        $status    = Refund::STATUS_PENDING;
        $payStatus = $code = $params['code'] ?? 'time out';
        $result    = $params['msg'] ?? '';
        if ($payStatus == '0') {
            $payStatus = $params['data']['state'] ?? $payStatus;
            if ($payStatus == '2') {
                $code = $payStatus;
            } else {
                $result = $params['data']['errMsg'] ?? '';
                $code   = $params['data']['errCode'] ?? $code;
            }
        }

        $result = strlen($result) > 128 ?  mb_strcut($result, 0, 128) : $result;
        switch ($payStatus) {
            case '2':
                $status = Refund::STATUS_APPROVED;
                break;

            case '0':
            case '1':
            case 'time out':
                $status = Refund::STATUS_PENDING;
                break;

            case '3':
            case '4':
            case '9999':
                $status = Refund::STATUS_DECLINED;
                break;
        }

        $paymentRefundCollection = new Collection();
        $paymentRefundCollection->set('payment_refund_id', $params['data']['refundOrderId'] ?? '0');
        $paymentRefundCollection->set('code', $code);
        $paymentRefundCollection->set('result', $result);
        $paymentRefundCollection->set('remark', '');
        $paymentRefundCollection->set('status', $status);

        $code = self::$_channelExternalCode[$paymentRefundCollection->result] ?? get_system_code('099');

        if ($status == Refund::STATUS_PENDING) {
            $code = get_system_code('200');
        } elseif ($status == Refund::STATUS_APPROVED) {
            $code = get_system_code('000');
        }

        //超时默认用p095
        if ($paymentRefundCollection->code === 'time out') {
            $code = get_system_code('095');
        }

        $refundCollection = new Collection();
        $refundCollection->set('status', self::$_respCode[$code][0]);
        $refundCollection->set('code', $code);
        $refundCollection->set('result', self::$_respCode[$code][1]);
        $refundCollection->set('remark', self::$_respCode[$code][2]);

        $collection = new Collection();
        $collection->set('refund', $refundCollection->toArray());
        $collection->set('payment_refund', $paymentRefundCollection->toArray());

        return $collection;
    }

    public static function _retrieveParser($params): Collection
    {
        $status = Order::STATUS_PENDING;
        $payStatus = $code = $params['code'] ?? 'time out';
        $result    = $params['msg'] ?? '';
        if ($payStatus == '0') {
            $payStatus = $params['data']['state'] ?? $payStatus;
            if ($payStatus == '2') {
                $code = $payStatus;
            } else {
                $result = $params['data']['errMsg'] ?? '';
                $code   = $params['data']['errCode'] ?? $code;
            }
        }

        $result = strlen($result) > 128 ?  mb_strcut($result, 0, 128) : $result;
        switch ($payStatus) {
            case '2':
                $status = Order::STATUS_APPROVED;
                break;

            case '0':
            case '1':
            case 'time out':
                $status = Order::STATUS_PENDING;
                break;

            case '3':
            case '4':
            case '5':
            case '6':
            case '9999':
                $status = Order::STATUS_DECLINED;
                break;
        }

        $paymentOrderCollection = new Collection();
        $paymentOrderCollection->set('payment_order_id', $params['data']['payOrderId'] ?? '0');
        $paymentOrderCollection->set('type', Order::TYPES_SALE);
        $paymentOrderCollection->set('code', $code);
        $paymentOrderCollection->set('result', $result);
        $paymentOrderCollection->set('remark', '');
        $paymentOrderCollection->set('status', $status);

        $code = self::$_channelExternalCode[$paymentOrderCollection->result] ?? get_system_code('099');
        if ($status == Order::STATUS_APPROVED) {
            $code = get_system_code('000');
        } elseif ($status == Order::STATUS_PENDING) {
            $code = get_system_code('200');
        }

        //超时默认用p095
        if ($paymentOrderCollection->code === 'time out') {
            $code = get_system_code('095');
        }

        $orderCollection = new Collection();
        $orderCollection->set('status', self::$_respCode[$code][0]);
        $orderCollection->set('type', Order::TYPES_SALE);
        $orderCollection->set('code', $code);
        $orderCollection->set('result', self::$_respCode[$code][1]);
        $orderCollection->set('remark', self::$_respCode[$code][2]);

        $collection = new Collection();
        $collection->set('order', $orderCollection->toArray());
        $collection->set('payment_order', $paymentOrderCollection->toArray());
        return $collection;
    }

    public static function syncParser($params): Collection
    {
        self::$_respCode            = self::getRespCode();
        self::$_channelExternalCode = self::getChannelExternalCode(static::$_supplierName);

        return self::_asyncParser($params);
    }

    /**
     * 异步支付通知
     * @param $params
     * @return Collection
     */
    public static function _asyncParser($params): Collection
    {
        $status = Order::STATUS_PENDING;
        $code   = $params['state'] ?? 'time out';
        $result = $params['errMsg'] ?? '';
        $result = strlen($result) > 128 ?  mb_strcut($result, 0, 128) : $result;

        switch ($code) {
            case '2':
                $status = Order::STATUS_APPROVED;
                break;

            case '0':
            case '1':
            case 'time out':
                $status = Order::STATUS_PENDING;
                break;

            case '3':
            case '4':
            case '5':
            case '6':
                $status = Order::STATUS_DECLINED;
                break;
        }

        $paymentOrderCollection = new Collection();
        $paymentOrderCollection->set('payment_order_id', $params['payOrderId'] ?? '0');
        $paymentOrderCollection->set('type', Order::TYPES_SALE);
        $paymentOrderCollection->set('code', $code);
        $paymentOrderCollection->set('result', $result);
        $paymentOrderCollection->set('remark', '');
        $paymentOrderCollection->set('html', '');
        $paymentOrderCollection->set('status', $status);

        $code = self::$_channelExternalCode[$paymentOrderCollection->code] ?? get_system_code('099');
        if ($status == Order::STATUS_APPROVED) {
            $code = get_system_code('000');
        } elseif ($status == Order::STATUS_PENDING) {
            $code = get_system_code('200');
        }

        //超时默认用p095
        if ($paymentOrderCollection->code == 'time out') {
            $code = get_system_code('095');
        }

        $orderCollection = new Collection();
        $orderCollection->set('status', self::$_respCode[$code][0]);
        $orderCollection->set('type', Order::TYPES_SALE);
        $orderCollection->set('code', $code);
        $orderCollection->set('result', self::$_respCode[$code][1]);
        $orderCollection->set('remark', self::$_respCode[$code][2]);

        $collection = new Collection();
        $collection->set('order', $orderCollection->toArray());
        $collection->set('payment_order', $paymentOrderCollection->toArray());

        return $collection;
    }

    public static function _notifyParser($params): Collection
    {
        $status = Refund::STATUS_PENDING;
        $code   = $params['state'] ?? 'time out';
        $result = $params['errMsg'] ?? '';
        $result = strlen($result) > 128 ?  mb_strcut($result, 0, 128) : $result;

        switch ($code) {
            case '2':
                $status = Order::STATUS_APPROVED;
                break;

            case '0':
            case '1':
                $status = Order::STATUS_PENDING;
                break;

            case '3':
            case '4':
                $status = Order::STATUS_DECLINED;
                break;
        }

        $paymentRefundCollection = new Collection();
        $paymentRefundCollection->set('payment_refund_id', $params['refundOrderId'] ?? '0');
        $paymentRefundCollection->set('code', $code);
        $paymentRefundCollection->set('status', $status);

        $code = self::$_channelExternalCode[$paymentRefundCollection->code] ?? get_system_code('099');

        if ($status == Refund::STATUS_APPROVED) {
            $code = get_system_code('000');
        } elseif ($status == Refund::STATUS_PENDING) {
            $code = get_system_code('200');
        }

        $refundCollection = new Collection();
        $refundCollection->set('status', self::$_respCode[$code][0]);
        $refundCollection->set('code', $code);
        $refundCollection->set('result', self::$_respCode[$code][1]);
        $refundCollection->set('remark', self::$_respCode[$code][2]);

        $collection = new Collection();
        $collection->set('refund', $refundCollection->toArray());
        $collection->set('payment_refund', $paymentRefundCollection->toArray());

        return $collection;
    }
}
