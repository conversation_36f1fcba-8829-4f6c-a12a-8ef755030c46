<?php

namespace App\Classes\Pay\Parser;

use App\Classes\Supports\Collection;
use App\Models\Order;
use App\Models\Refund;

class Kess extends BaseParser
{
    protected static $_supplierName = 'Kess';

    public static function _authParser($params): Collection
    {
        $status = Order::STATUS_PENDING;
        $code   = $params['data']['order_info']['status'] ?? $params['err_code'] ?? 'time out';
        $result = $params['error_des'] ?? '';
        $result = strlen($result) > 128 ?  mb_strcut($result, 0, 128) : $result;

        switch ($code) {
            case 'SUCCESS':
                $status = Order::STATUS_APPROVED;
                break;

            case 'WAITING':
            case 'time out':
                $status = Order::STATUS_PENDING;
                break;

            case 'CLOSED':
            case '400':
            case '401':
            case '403':
            case '404':
            case '409':
            case '419':
            case '422':
            case '500':
            case '503':
            case '504':
                $status = Order::STATUS_DECLINED;
                break;
        }

        $paymentOrderCollection = new Collection();
        $paymentOrderCollection->set('payment_order_id', $params['order_info']['transaction_id'] ?? '0');
        $paymentOrderCollection->set('type', Order::TYPES_SALE);
        $paymentOrderCollection->set('code', $code);
        $paymentOrderCollection->set('result', $result);
        $paymentOrderCollection->set('remark', '');
        $paymentOrderCollection->set('html', '');
        $paymentOrderCollection->set('status', $status);

        $code = self::$_channelExternalCode[$paymentOrderCollection->result] ?? self::$_channelExternalCode[$paymentOrderCollection->code] ?? get_system_code('099');

        if ($status == Order::STATUS_APPROVED) {
            $code = get_system_code('000');
        } elseif ($status == Order::STATUS_PENDING) {
            $code = get_system_code('200');
        }

        //超时默认用p095
        if ($paymentOrderCollection->code == 'time out') {
            $code = get_system_code('095');
        }

        $orderCollection = new Collection();
        //3D验证链接返回跳转
        if (!empty($params['data']['required_3ds']) && !empty($params['data']['html_confirm_payment'])) {
            $code = get_system_code('200');
            $paymentOrderCollection->set('html', $params['data']['html_confirm_payment']);
            $orderCollection->set('is_3d', 1);
        }

        $orderCollection->set('status', self::$_respCode[$code][0]);
        $orderCollection->set('type', Order::TYPES_SALE);
        $orderCollection->set('code', $code);
        $orderCollection->set('result', self::$_respCode[$code][1]);
        $orderCollection->set('remark', self::$_respCode[$code][2]);

        $collection = new Collection();
        $collection->set('order', $orderCollection->toArray());
        $collection->set('payment_order', $paymentOrderCollection->toArray());

        return $collection;
    }

    public static function captureParser($params): Collection
    {
        return self::authParser($params);
    }

    public static function _refundParser($params): Collection
    {
        $status = Refund::STATUS_PENDING;
        $code   = $params['data']['status'] ?? $params['err_code'] ?? 'time out';
        $result = $params['error_des'] ?? '';
        $result = strlen($result) > 128 ?  mb_strcut($result, 0, 128) : $result;

        switch ($code) {
            case 'REFUNDED':
                $status = Refund::STATUS_APPROVED;
                break;

            case 'WAITING':
            case 'time out':
                $status = Refund::STATUS_PENDING;
                break;

            case 'CLOSED':
            case '400':
            case '401':
            case '403':
            case '404':
            case '409':
            case '419':
            case '422':
            case '500':
            case '503':
            case '504':
                $status = Refund::STATUS_DECLINED;
                break;
        }

        $paymentRefundCollection = new Collection();
        $paymentRefundCollection->set('payment_refund_id', $params['data']['transaction_id'] ?? '0');
        $paymentRefundCollection->set('code', $code);
        $paymentRefundCollection->set('result', $result);
        $paymentRefundCollection->set('remark', '');
        $paymentRefundCollection->set('status', $status);

        $code = self::$_channelExternalCode[$paymentRefundCollection->result] ?? self::$_channelExternalCode[$paymentRefundCollection->code] ?? get_system_code('099');
        if ($status == Refund::STATUS_PENDING) {
            $code = get_system_code('200');
        } elseif ($status == Refund::STATUS_APPROVED) {
            $code = get_system_code('000');
        }

        //超时默认用p095
        if ($paymentRefundCollection->code == 'time out') {
            $code = get_system_code('095');
        }

        $refundCollection = new Collection();
        $refundCollection->set('status', self::$_respCode[$code][0]);
        $refundCollection->set('code', $code);
        $refundCollection->set('result', self::$_respCode[$code][1]);
        $refundCollection->set('remark', self::$_respCode[$code][2]);

        $collection = new Collection();
        $collection->set('refund', $refundCollection->toArray());
        $collection->set('payment_refund', $paymentRefundCollection->toArray());

        return $collection;
    }

    public static function _retrieveParser($params): Collection
    {
        $status = Order::STATUS_PENDING;
        $code   = $params['data']['status'] ?? $params['err_code'] ?? 'time out';
        $result = $params['error_des'] ?? $params['data']['message']['desc'] ?? '';
        $result = strlen($result) > 128 ?  mb_strcut($result, 0, 128) : $result;

        switch ($code) {
            case 'SUCCESS':
                $status = Order::STATUS_APPROVED;
                break;

            case 'WAITING':
            case 'time out':
                $status = Order::STATUS_PENDING;
                break;

            case 'CLOSED':
            case '400':
            case '401':
            case '403':
            case '404':
            case '409':
            case '419':
            case '422':
            case '500':
            case '503':
            case '504':
                $status = Order::STATUS_DECLINED;
                break;
        }

        $paymentOrderCollection = new Collection();
        $paymentOrderCollection->set('payment_order_id', $params['data']['transaction_id'] ?? '0');
        $paymentOrderCollection->set('type', Order::TYPES_SALE);
        $paymentOrderCollection->set('code', $code);
        $paymentOrderCollection->set('result', $result);
        $paymentOrderCollection->set('remark', '');
        $paymentOrderCollection->set('status', $status);

        $code = self::$_channelExternalCode[$paymentOrderCollection->result] ?? self::$_channelExternalCode[$paymentOrderCollection->code] ?? get_system_code('099');
        if ($status == Order::STATUS_APPROVED) {
            $code = get_system_code('000');
        } elseif ($status == Order::STATUS_PENDING) {
            $code = get_system_code('200');
        }

        //超时默认用p095
        if ($paymentOrderCollection->code == 'time out') {
            $code = get_system_code('095');
        }

        $orderCollection = new Collection();
        $orderCollection->set('status', self::$_respCode[$code][0]);
        $orderCollection->set('type', Order::TYPES_SALE);
        $orderCollection->set('code', $code);
        $orderCollection->set('result', self::$_respCode[$code][1]);
        $orderCollection->set('remark', self::$_respCode[$code][2]);

        $collection = new Collection();
        $collection->set('order', $orderCollection->toArray());
        $collection->set('payment_order', $paymentOrderCollection->toArray());

        return $collection;
    }

    public static function syncParser($params): Collection
    {
        self::$_respCode            = self::getRespCode();
        self::$_channelExternalCode = self::getChannelExternalCode(static::$_supplierName);

        return self::_syncParser($params);
    }

    /**
     * 同步支付通知
     * @param $params
     * @return Collection
     */
    public static function _syncParser($params): Collection
    {
        $status = Order::STATUS_PENDING;
        $code   = $params['success'] ?? 'time out';
        $result = $params['pay_response']['acquirerMessage'] ?? $params['message'] ?? '';
        $result = strlen($result) > 128 ?  mb_strcut($result, 0, 128) : $result;

        switch ($code) {
            case 1:
                $status = Order::STATUS_APPROVED;
                break;

            case 'time out':
                $status = Order::STATUS_PENDING;
                break;

            case 0:
                $status = Order::STATUS_DECLINED;
                break;
        }

        $paymentOrderCollection = new Collection();
        $paymentOrderCollection->set('payment_order_id', $params['token'] ?? '0');
        $paymentOrderCollection->set('type', Order::TYPES_SALE);
        $paymentOrderCollection->set('code', $code);
        $paymentOrderCollection->set('result', $result);
        $paymentOrderCollection->set('remark', '');
        $paymentOrderCollection->set('html', '');
        $paymentOrderCollection->set('status', $status);

        $code = self::$_channelExternalCode[$paymentOrderCollection->result] ?? self::$_channelExternalCode[$paymentOrderCollection->code] ?? get_system_code('099');
        if ($status == Order::STATUS_APPROVED) {
            $code = get_system_code('000');
        } elseif ($status == Order::STATUS_PENDING) {
            $code = get_system_code('200');
        }

        //超时默认用p095
        if ($paymentOrderCollection->code == 'time out') {
            $code = get_system_code('095');
        }

        $orderCollection = new Collection();
        $orderCollection->set('status', self::$_respCode[$code][0]);
        $orderCollection->set('type', Order::TYPES_SALE);
        $orderCollection->set('code', $code);
        $orderCollection->set('result', self::$_respCode[$code][1]);
        $orderCollection->set('remark', self::$_respCode[$code][2]);

        $collection = new Collection();
        $collection->set('order', $orderCollection->toArray());
        $collection->set('payment_order', $paymentOrderCollection->toArray());

        return $collection;
    }

    /**
     * 异步支付通知
     * @param $params
     * @return Collection
     */
    public static function _asyncParser($params): Collection
    {
        $status = Order::STATUS_PENDING;
        $code   = $params['status'] ?? 'time out';
        $result = $params['error_logs'][0]['message'] ?? '';
        $result = strlen($result) > 128 ?  mb_strcut($result, 0, 128) : $result;

        switch ($code) {
            case 'SUCCESS':
                $status = Order::STATUS_APPROVED;
                break;

            case 'CLOSED':
                $status = Order::STATUS_DECLINED;
                break;

            case 'time out':
                $status = Order::STATUS_PENDING;
                break;
        }

        $paymentOrderCollection = new Collection();
        $paymentOrderCollection->set('payment_order_id', $params['transaction_id'] ?? '0');
        $paymentOrderCollection->set('type', Order::TYPES_SALE);
        $paymentOrderCollection->set('code', $code);
        $paymentOrderCollection->set('result', $result);
        $paymentOrderCollection->set('remark', '');
        $paymentOrderCollection->set('html', '');
        $paymentOrderCollection->set('status', $status);

        $code = self::$_channelExternalCode[$paymentOrderCollection->result] ?? self::$_channelExternalCode[$paymentOrderCollection->code] ?? get_system_code('099');
        if ($status == Order::STATUS_APPROVED) {
            $code = get_system_code('000');
        } elseif ($status == Order::STATUS_PENDING) {
            $code = get_system_code('200');
        }

        //超时默认用p095
        if ($paymentOrderCollection->code == 'time out') {
            $code = get_system_code('095');
        }

        $orderCollection = new Collection();
        $orderCollection->set('status', self::$_respCode[$code][0]);
        $orderCollection->set('type', Order::TYPES_SALE);
        $orderCollection->set('code', $code);
        $orderCollection->set('result', self::$_respCode[$code][1]);
        $orderCollection->set('remark', self::$_respCode[$code][2]);

        $collection = new Collection();
        $collection->set('order', $orderCollection->toArray());
        $collection->set('payment_order', $paymentOrderCollection->toArray());

        return $collection;
    }
}
