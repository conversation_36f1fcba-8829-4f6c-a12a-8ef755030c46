# 网关设计文档

## 1. 网关整体设计结构

### 1.1 Gateways网关

#### 1.1.1 渠道文件夹

每个支付渠道都应该有自己独立的文件夹，文件夹名称为渠道名称，例如 `NewChannel`（这里`NewChannel`为假设的渠道名）。

在该渠道文件夹内包含以下文件：

1. **AuthGateway.php**
   - 负责处理请求授权的逻辑。如果需要进行支付操作，系统会调用此文件。
   - 如果是Sale模式（即直接扣款模式），会将授权捕获两个动作合并。

2. **CaptureGateway.php**
   - 负责捕获操作，将授权交易的金额实际扣除。
   - 如果是Sale模式，无需捕获操作，因此不需要触发此文件。

3. **Gateway.php**
   - 作为网关的基类，包含支付网关的主要接口。其他网关（如`AuthGateway.php`、`CaptureGateway.php`等）继承自此类。

4. **RefundGateway.php**
   - 负责处理退款操作，支持全额退款或部分退款。

5. **RetrieveGateway.php**
   - 负责处理交易查询，查询交易是否成功，获取交易的详细信息等。

6. **Support.php**
   - 包含网关操作的辅助方法，如：
      - 请求发起代码
      - 签名生成与验签
      - 数据加密与解密等。

#### 1.1.2 渠道配置文件

每个渠道的配置文件应直接放在Gateways文件夹内：

1. **NewChannel.php**
   - 用于初始化渠道的配置信息，如渠道ID、API密钥、支付接口地址等。

### 1.2 Parser 解析/映射

1. **NewChannel.php**
   - 用于解析不同支付操作的返回数据，如授权、捕获、退款、查询、同步通知和异步通知等。
   - 每个渠道的解析逻辑应在相应的文件中进行配置。

## 2. 对接新渠道的步骤

### 2.1 步骤概述

以下是将一个新的支付渠道接入系统的步骤。

<font color=red>注意如果是接入新渠道请先通过写 demo，或者使用 apipost，postman等工具确认调用渠道接口正常无误！！！</font>


#### 2.2 步骤一：为新渠道创建文件夹

1. 在`Gateways`目录下为新渠道创建一个文件夹，命名为渠道名称。例如，创建一个名为`NewChannel`的文件夹。

2. 在该文件夹内创建上述的网关文件：`AuthGateway.php`、`CaptureGateway.php`、`RefundGateway.php`、`RetrieveGateway.php`、`Gateway.php`和`Support.php`，可以直接复制其他渠道文件进行修改。

#### 2.3 步骤二：实现网关功能

1. **实现支付网关基类**
   - 在`Gateway.php`文件中，提供支付网关的基础接口，并实现公共功能。

2. **实现授权、捕获、退款等操作**
   - 在`AuthGateway.php`、`CaptureGateway.php`、`RefundGateway.php`等文件中实现新渠道的具体支付操作。根据新渠道的API文档，定制每个操作的请求方式、响应格式和处理逻辑。
  
3. **实现加解密等操作**
   - 在`Support.php`文件中实现新渠道的加解密，发起请求等方法。

4. **实现数据解析和映射**
   - 在`Parser/NewChannel.php`文件中，解析新渠道的请求和响应数据，进行数据映射。

#### 2.4 步骤三：配置渠道参数

1. 在`Gateways/NewChannel.php`中配置新渠道的初始化参数，包括：
   - 支付接口URL
   - 其他渠道特定参数(方法)

#### 2.5 步骤四：测试与验证

1. **本地测试**
   - 在本地环境中进行全面测试，确保支付请求能正确发送，响应能正确处理。

2. **接口联调**
   - 与支付渠道方进行接口联调，验证请求和响应格式是否正确。

3. **生产环境部署**
   - 在确认无误后，将新渠道的网关部署到生产环境中。

## 3. 示例文件结构

```shell
Gateways 
├── NewChannel 
│       ├── AuthGateway.php 
│       ├── CaptureGateway.php 
│       ├── RefundGateway.php 
│       ├── RetrieveGateway.php 
│       ├── Gateway.php 
│       └── Support.php 
└── NewChannel.php
Parser
└── NewChannel.php
```

通过这些步骤，可以顺利将新支付渠道接入到现有的支付网关系统中。
