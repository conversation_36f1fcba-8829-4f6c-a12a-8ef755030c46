<?php

namespace App\Classes\Risk\Gateways;

use App\Classes\Supports\Traits\HasHttpRequest;

class Seon
{
    use HasHttpRequest;

    public function fraud($order): bool
    {
        $payload = [
            'config'               => [
                'ip'                    => [
                    'include' => 'flags,history,id',
                    'version' => 'v1',
                ],
//                'email'                 => [
//                    'include' => 'flags,history,id',
//                    'version' => 'v2',
//                ],
//                'phone'                 => [
//                    'include' => 'flags,history,id',
//                    'version' => 'v1',
//                ],
                'ip_api'                => true,
//                'email_api'             => true,
//                'phone_api'             => true,
                'device_fingerprinting' => true,
            ],
            'ip'                   => $order['ip'],
            'action_type'          => 'withdrawal',
            'transaction_id'       => $order['transaction_id'],
//            'email'                => $order['email'],
//            'phone_number'         => $order['phone_number'],
            'merchant_id'          => $order['merchant_id'],
            'shipping_country'     => $order['shipping_country'],
            'shipping_city'        => $order['shipping_city'],
            'shipping_region'      => $order['shipping_region'],
            'shipping_zip'         => $order['shipping_zip'],
            'shipping_street'      => $order['shipping_street'],
            'shipping_phone'       => $order['shipping_phone'],
            'shipping_fullname'    => $order['shipping_fullname'],
            'billing_country'      => $order['billing_country'],
            'billing_city'         => $order['billing_city'],
            'billing_region'       => $order['billing_region'],
            'billing_zip'          => $order['billing_zip'],
            'billing_street'       => $order['billing_street'],
            'billing_phone'        => $order['billing_phone'],
            'payment_mode'         => 'card',
            'cvv_result'           => true,
            'transaction_type'     => 'purchase',
            'card_fullname'        => $order['card_fullname'],
            'card_bin'             => $order['card_bin'],
            'card_hash'            => $order['card_hash'],
            'card_expire'          => $order['card_expire'],
            'card_last'            => $order['card_last'],
            'transaction_amount'   => $order['transaction_amount'],
            'transaction_currency' => $order['transaction_currency'],
            'session_id'           => $order['transaction_id'],
            'session'              => 'W;6.7.0;mMyGzBiEah0VEx8JBt2Kbg==;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'
        ];

        $header = [];

        $res = $this->getRequest('/SeonRestService/fraud-api/v2', $payload, $header);

        switch ($res['state'] ?? 'DECLINE') {
            case 'APPROVE':
            case 'REVIEW':
                return false;
            case 'DECLINE':
            default:
                return true;
        }
    }

    public function getRequest(string $url, array $data, array $header = [])
    {
        $baseHeader = [
            'Content-Type' => 'application/json',
            'X-API-KEY'    => config('seon.key'),
        ];

        $header = array_merge($header, $baseHeader);

        try {
            $res = $this->post(config('seon.hosts') . $url, [], ['json' => $data, 'headers' => $header]);
        } catch (\Exception $e) {
            return [];
        }

        if (!isset($res['success']) || $res['success'] !== true) {
            return [];
        }

        return $res['data'];
    }

}