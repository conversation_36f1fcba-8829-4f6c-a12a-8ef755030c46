<?php

namespace App\Classes\Risk\Gateways;

use App\Classes\Supports\Traits\HasHttpRequest;
use App\Models\DirectoryDictionary;
use Illuminate\Contracts\Cache\LockTimeoutException;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class LN
{
    use HasHttpRequest;

    const LEXIS_NEIXIS_ENTITY_TYPE_INDIVIDUAL = 'Individual';
    const LEXIS_NEIXIS_ENTITY_TYPE_BUSINESS   = 'Business';

    const LN_TONKEN_LOCK_KEY = 'LN_TOKEN_LOCK';

    const LN_TONKEN_KEY = 'LN_TOKEN';


    public function fraud($entity, $reference)
    {
        $config = config('ln');

        // 构建请求体
        $req = [
            'ClientContext'       => [
                'ClientReference' => $reference
            ],
            'SearchConfiguration' => [
                'AssignResultTo'            => [
                    'Division'          => $config['division'],
                    'EmailNotification' => $config['emailNotification'],
                    'RolesOrUsers'      => [$config['rolesOrUsers']],
                    'Type'              => 'Role'
                ],
                'PredefinedSearchName'      => $config['predefinedSearchName'],
                'WriteResultsToDatabase'    => true,
                'ExcludeScreeningListMatch' => "false"
            ],
            'SearchInput'         => [
                'Records' => [
                    ['Entity' => $entity]
                ]
            ]
        ];

        return self::getRequest('/LN.WebServices/api/Lists/Search', $req);
    }

    protected static function generateInputAddress($addresses): array
    {
        if (empty($addresses)) return [];

        $address = ['Type' => 'Current'];
        foreach ($addresses as $field => $value) {
            $address[$field] = $value;
        }
        return [$address];
    }

    public function getRequest(string $url, array $data, array $header = [])
    {
        $config = config('ln');

        $baseHeader = [
            'Content-Type'  => 'application/json',
            'X-API-Key'     => $config['apiKey'],
            'Authorization' => 'Bearer ' . $this->getToken(),
        ];

        $header = array_merge($header, $baseHeader);

        try {
            $res = $this->post($config['baseUrl'] . $url, [], ['json' => $data, 'headers' => $header]);
        } catch (\Exception $e) {
            return [];
        }

        return $res;
    }

    public function getToken()
    {
        // 查询缓存是否存在 如果存在直接返回
        $token = Cache::get(self::LN_TONKEN_KEY);
        if ($token) {
            return $token;
        }

        // 使用 缓存锁
        $lock = Cache::lock(self::LN_TONKEN_LOCK_KEY, 60);
        try {
            $lock->block(50);
        } catch (LockTimeoutException $e) {
            Log::error('获取锁超时:' . $e->getMessage());
            return '';
        } finally {
            // 查询缓存是否存在 如果存在直接返回
            $token = Cache::get(self::LN_TONKEN_KEY);
            if ($token) {
                return $token;
            }

            $builder = DirectoryDictionary::query();
            $userInfo = $builder
                ->where('type', 'LN')
                ->first(['remarks']);
            $userInfo = json_decode($userInfo['remarks'], true);

            $config = config('ln');

            // 构造 Basic 认证头部
            $username  = $userInfo['username'];
            $password  = $userInfo['password'];
            $auth      = $username . ':' . $password;
            $basicAuth = 'Basic ' . base64_encode($auth);

            // 请求 Token
            $url     = $config['baseUrl'] . "/LN.WebServices/api/Token/Issue";
            $headers = [
                "Authorization" => $basicAuth,
                "Content-Type"  => "application/json; charset=utf-8"
            ];

            try {
                $res = $this->post($url, [], ['headers' => $headers]);
            } catch (\Exception $e) {
                return '';
            }

            $token = $res['access_token'];
            Cache::put(self::LN_TONKEN_KEY, $token, $res['expires_in'] - 5);

            optional($lock)->release();

            return $token;
        }
    }
}
