<?php

namespace App\Classes\Risk;

use App\Classes\Risk\Gateways\Seon;
use App\Jobs\SendSlsLog;
use App\Models\CardCipherWhitelist;
use App\Models\CardWhiteList;
use App\Models\DirectoryCurrency;
use App\Models\MerchantBlacklist;
use App\Models\MerchantBusiness;
use App\Models\Order;
use DES3;
use Illuminate\Support\Facades\Cache;

class Risk
{

    /**
     * @param object $merchantBusiness
     * @param array $data
     * @param object $order
     * @return object
     */
    public static function orderRisk(object $merchantBusiness, object $order, array $data = []): object
    {
        //验证BID日月金额限额
        if (self::moneyAstrictCheck($merchantBusiness, $data)) {
            $order->risk_type  = Order::RISK_TYPE_MODEL;
            $order->risk_level = Order::RISK_LEVEL_HIGH;

            $order->status = Order::STATUS_DECLINED;
            $order->code   = get_system_code('073');
            $order->result = 'Daily/Monthly Limit';
            $order->remark = '日/月限额';

            return $order;
        }

        //验证BID单笔限额
        if (self::singleLimitCheck($merchantBusiness, $data)) {
            $order->risk_type  = Order::RISK_TYPE_MODEL;
            $order->risk_level = Order::RISK_LEVEL_HIGH;

            $order->status = Order::STATUS_DECLINED;
            $order->code   = get_system_code('074');
            $order->result = 'Risk rule out';
            $order->remark = '规则排除';

            return $order;
        }

        //验证BID成功笔数限制
        if (self::paymentsLimitCheck($merchantBusiness, $data)) {
            $order->risk_type  = Order::RISK_TYPE_MODEL;
            $order->risk_level = Order::RISK_LEVEL_HIGH;

            $order->status = Order::STATUS_DECLINED;
            $order->code   = get_system_code('075');
            $order->result = 'Risk rule out';
            $order->remark = '规则排除';

            return $order;
        }

        //国内ip检测
        if (self::ipCountryCheck($data)){
            $order->risk_type  = Order::RISK_TYPE_MODEL;
            $order->risk_level = Order::RISK_LEVEL_HIGH;

            $order->status = Order::STATUS_DECLINED;
            $order->code   = get_system_code('801');
            $order->result = 'Not allowed country IP addresses';
            $order->remark = '不允许国家IP';

            return $order;
        }

        //cvv限制限制
        if (self::cardCvvCheck($data)){
            $order->risk_type  = Order::RISK_TYPE_MODEL;
            $order->risk_level = Order::RISK_LEVEL_HIGH;

            $order->status = Order::STATUS_DECLINED;
            $order->code   = get_system_code('803');
            $order->result = 'Invalid card verification value';
            $order->remark = '校验码错误';

            return $order;
        }

        // 卡有效期验证
        if (self::cardDateVerify($data)) {
            $order->risk_type  = Order::RISK_TYPE_MODEL;
            $order->risk_level = Order::RISK_LEVEL_HIGH;

            $order->status = Order::STATUS_DECLINED;
            $order->code   = get_system_code('810');
            $order->result = 'Invalid Expiration Date';
            $order->remark = '有效期错误';

            return $order;
        }

		//获取是否刷单商户标识
        $swipe = config('swipe.bid') == $order->business_id ? true : false;

        //刷单商户跳过排除卡属国为中国的交易和同卡号24小时内连续失败3笔验证
        if ($swipe) {
            return $order;
        }

        // 商户170986603998366且卡号是356886XXXXXX8879 排除卡属国为中国的交易和同卡号24小时内连续失败3笔验证
        if (isset($merchantBusiness->merchant_id) && $merchantBusiness->merchant_id == '170986603998366' && isset($data['card']['card_mask']) && $data['card']['card_mask'] == '356886XXXXXX8879') {
            return $order;
        }

		//排除卡属国为中国的交易
        if (self::cardCountryCheck($data)) {
            $order->risk_type  = Order::RISK_TYPE_MODEL;
            $order->risk_level = Order::RISK_LEVEL_HIGH;

            $order->status = Order::STATUS_DECLINED;
            $order->code   = get_system_code('802');
            $order->result = 'Restricted card';
            $order->remark = '受限制卡';

            return $order;
        }

        //同卡号短时间频繁请求支付的限制
        if (self::cardFakedOrdersLimit($data)) {
            $order->risk_type  = Order::RISK_TYPE_MODEL;
            $order->risk_level = Order::RISK_LEVEL_HIGH;

            $order->status = Order::STATUS_DECLINED;
            $order->code   = get_system_code('804');
            $order->result = 'Card is being processed';
            $order->remark = '并发交易';

            return $order;
        }

        //同卡号24小时内失败3笔验证
        if (self::cardContinuousFailure($data)) {
            $order->risk_type  = Order::RISK_TYPE_MODEL;
            $order->risk_level = Order::RISK_LEVEL_HIGH;

            $order->status = Order::STATUS_DECLINED;
            $order->code   = get_system_code('080');
            $order->result = 'Risk rule out';
            $order->remark = '规则排除';

            return $order;
        }

        //验证BID卡号白名单
        if (!empty($merchantBusiness['whitelist_card_type']) && in_array($data['card']['cc_type'], explode(',', $merchantBusiness['whitelist_card_type']))) {
            //BID白名单验证通过
            $order->_white = false;

            if (self::cardWhiteListCheck($data)) {
                //BID白名单验证不通过，只能使用b组账单标识，存储一个值代表白名单验证不通过，使用完毕释放。
                $order->_white = true;
            }
        }

        // 是否启用 seon
        if (config('seon.activate')){
            if (self::fraudVerify($data)){
                $order->risk_type  = Order::RISK_TYPE_MODEL;
                $order->risk_level = Order::RISK_LEVEL_HIGH;

                $order->status = Order::STATUS_DECLINED;
                $order->code   = get_system_code('089');
                $order->result = 'Suspected fraud';
                $order->remark = '有欺诈嫌疑';

                return $order;
            }
        }

        return $order;
    }

    /**
     * 验证卡号白名单
     * @param $data
     * @return bool
     */
    static function cardWhiteListCheck($data): bool
    {
        $dataCard     = DES3::decrypt($data['card']['card_number']);
        $dataCardMask = get_markcard($dataCard);
        $orderId      = $data['order']['order_id'] ?? $data['order_id'] ?? '';

        $cardWhiteList = CardWhiteList::where('card_mask', $dataCardMask)->get();
        if (count($cardWhiteList) == 0) {
            logger()->channel('intercept')->info('卡掩码不存在', [
                'order_id'  => $orderId,
                'card_mask' => $dataCardMask,
            ]);
            dispatch(new SendSlsLog(
                ['message' => '卡掩码不存在'],
                ['order_id'  => $orderId, 'card_mask' => $dataCardMask],
                'info',
                'intercept'
            ));

            return true;
        }

        foreach ($cardWhiteList as $value) {
            $valueCard = DES3::decrypt($value['card_number']);

            //判断是否跟卡掩码相等
            if ($valueCard == $dataCardMask) {
                return false;
            }

            //判断是否跟卡号相等
            if ($valueCard == $dataCard) {
                return false;
            }
        }

        logger()->channel('intercept')->info('白名单校验未通过', [
            'order_id'  => $orderId,
            'card_mask' => $dataCardMask,
        ]);
        dispatch(new SendSlsLog(
            ['message' => '白名单校验未通过'],
            ['order_id'  => $orderId, 'card_mask' => $dataCardMask],
            'info',
            'intercept'
        ));

        return true;
    }

    static function singleLimitCheck($merchantBusiness, $data): bool
    {
        $singleMoneyAstrict = $merchantBusiness['single_money_astrict'] ?? [];

        if (count($singleMoneyAstrict) == 0) {
            return false;
        }

        $singleAstrict = array_column($singleMoneyAstrict, 'money_astrict', 'cc_type');
        $order         = $data['order'];
        $ccType        = $data['card']['cc_type'];
        // 订单金额转美元判断（USD）
        if ($order['currency'] == 'USD') {
            $amountUsd = $order['amount'];
        } else {
            $currencyRate = DirectoryCurrency::firstWhere('code', $order['currency']);
            $amountUsd    = amount_format($order['amount'] / $currencyRate->rate, 2);
        }

        if (isset($singleAstrict['*']) && $singleAstrict['*'] > 0 && $singleAstrict['*'] < $amountUsd) {
            return true;
        }

        if (isset($singleAstrict[$ccType]) && $singleAstrict[$ccType] > 0 && $singleAstrict[$ccType] < $amountUsd) {
            return true;
        }

        return false;
    }

    /**
     * 黑名单拦截
     *
     * @param  $requestData
     * @return bool
     */
    public static function merchantBlacklist($requestData, $source): array
    {
        $address    = $requestData['address'];
        $cardNumber = $requestData['card']['card_number'];

        // 黑名单验证数据
        $whereIn = [
            $address['ip'],
            $address['bill_first_name'] . ' ' . $address['bill_last_name'],
            $address['bill_email'],
            $address['bill_address'],
        ];

        // 169838465687968、171031004584662、163953459255967、164791825957292和166520855232531商户黑名单不拦截持卡人名称要素
        if (in_array($requestData['merchant_id'], ['169838465687968', '171031004584662', '163953459255967', '164791825957292', '166520855232531'])) {
            $whereIn = [
                $address['ip'],
                $address['bill_email'],
                $address['bill_address'],
            ];
        }

        // 卡密文白名单验证(卡号不存在白名单里需要进行黑名单拦截)
        $encrypted = DES3::encrypt($cardNumber);
        if (!CardCipherWhitelist::query()->select('id', 'card_number')->where('card_number', $encrypted)->exists()) {
            $whereIn[] = $requestData['card']['card_number'];
            $whereIn[] = get_markcard($requestData['card']['card_number']);
            $whereIn[] = substr($cardNumber, 0, 6);
        }

        $black = MerchantBlacklist::select('limit_value', 'limit_type')
            ->whereIn('source', $source)
            ->whereIn('limit_value', $whereIn)->first();

        if (empty($black)) {
            return [];
        }

        $info       = '黑名单拦截';
        $infoDetail = 'Invalid Info';

        switch ($black['limit_type']) {
            case MerchantBlacklist::TYPE_IP:
                $info .= '(' . MerchantBlacklist::$typesMap[MerchantBlacklist::TYPE_IP] . ')';
                $infoDetail = 'IP: ' . $black['limit_value'];
                break;
            case MerchantBlacklist::TYPE_CARD_NUMBER:
                $info .= '(' . MerchantBlacklist::$typesMap[MerchantBlacklist::TYPE_CARD_NUMBER] . ')';
                $infoDetail = 'Card Number: ' . $black['limit_value'];
                break;
            case MerchantBlacklist::TYPE_CARDHOLDER_NAME:
                $info .= '(' . MerchantBlacklist::$typesMap[MerchantBlacklist::TYPE_CARDHOLDER_NAME] . ')';
                $infoDetail = 'Cardholder Name: ' . $black['limit_value'];
                break;
            case MerchantBlacklist::TYPE_MAILBOX:
                $info .= '(' . MerchantBlacklist::$typesMap[MerchantBlacklist::TYPE_MAILBOX] . ')';
                $infoDetail = 'Mailbox: ' . $black['limit_value'];
                break;
            case MerchantBlacklist::TYPE_ADDRESS:
                $info .= '(' . MerchantBlacklist::$typesMap[MerchantBlacklist::TYPE_ADDRESS] . ')';
                $infoDetail = 'Address: ' . $black['limit_value'];
                break;
            case MerchantBlacklist::TYPE_CARD_BIN:
                $info .= '(' . MerchantBlacklist::$typesMap[MerchantBlacklist::TYPE_CARD_BIN] . ')';
                $infoDetail = 'Card BIN: ' . $black['limit_value'];
                break;
        }

        // 记录日志
        logger()->channel('intercept')->info($info, [
            'order_number' => $requestData['order_number'],
            'merchant_id'  => $requestData['merchant_id'],
            'info_detail'  => $infoDetail,
        ]);

        dispatch(new SendSlsLog(
            ['message' => $info],
            ['order_number' => $requestData['order_number'], 'merchant_id'  => $requestData['merchant_id'], 'info_detail' => $infoDetail],
            'info',
            'intercept'
        ));

        return ['info' => $info, 'infoDetail' => $infoDetail];
    }

    /**
     * 验证BID成功笔数限制
     * @param $merchantBusiness
     * @param $data
     * @return bool
     */
    static function paymentsLimitCheck($merchantBusiness, $data): bool
    {
        if (empty($merchantBusiness['time_limits'])) {
            return false;
        }

        $timeLimitMap = array_column($merchantBusiness['time_limits'], 'times_limit', 'cc_type');
		$limitTimeMap = array_column($merchantBusiness['time_limits'], 'limit_time', 'cc_type');

        if (isset($timeLimitMap[$data['card']['cc_type']]) && $timeLimitMap[$data['card']['cc_type']]) {
            $keyName = 'Bid_Transaction_Card_Limit_Any_' . $data['order']['business_id'] . md5($data['card']['card_number']);

            if (Cache::has($keyName)) {
                $cacheData = json_decode(Cache::get($keyName), true);

                if (count($cacheData) >= $timeLimitMap[$data['card']['cc_type']]) {
                    if (!isset($limitTimeMap[$data['card']['cc_type']]) || $limitTimeMap[$data['card']['cc_type']] <= 0) {
                        $limitTimeMap[$data['card']['cc_type']] = 1;
                    }

                    $time = time() - $limitTimeMap[$data['card']['cc_type']] * 3600;

                    if ($time <= strtotime(min($cacheData))) {
                        return true;
                    }
                }
            }
        }
//        $keyEmailName = 'Bid_Transaction_Email_Limit_' . $data['order']['business_id'] . md5($data['address']['bill_email']);
//
//        if (Cache::has($keyEmailName)) {
//            $cacheData = Cache::get($keyEmailName);
//
//            if ($cacheData >= $merchantBusiness['times_limit']) {
//                return true;
//            }
//        }

        return false;
    }

     /**
     * 排除卡属国为中国的交易
     *
     * @param $data
     * @return boolean
     */
    static function cardCountryCheck($data): bool
    {
        if ($data['card']['card_country'] == 'China'){
            return true;
        }

        return false;
    }

    /**
     * @param $data
     * @return bool
     * 判断ip是否为国内
     */
    static function ipCountryCheck($data)
    {
        if (empty($data['address']['ip_country_isoa2']) || in_array($data['address']['ip_country_isoa2'], ["CN", "RU", "UA", "YE", "IR", "CU"])) {
            return true;
        }

        return false;
    }

    /**
     * @param $data
     * @return bool
     * cvv限制
     */
    static function cardCvvCheck($data): bool
    {
        if (!in_array($data['card']['cc_type'], ['A', 'J'] ) && strlen($data['card_cvv']) != 3) {
            return true;
        }

        return false;
    }

    /**
     * @param $data
     * @return bool
     * BID同卡号24小时内是否失败限制
     */
    static function cardContinuousFailure($data): bool
    {
        list($number, $hour) = self::getCardContinuousFailure($data['order']['business_id'], $data['card']['cc_type']);
        $keyName             = 'Card_Continuous_Failure_Any_' . $data['order']['business_id'] . md5($data['card']['card_number']);

        if ($number == 0) {
            return false;
        }

		if (Cache::has($keyName)) {
            $cacheData = json_decode(Cache::get($keyName), true);

            if (count($cacheData) >= $number) {

                $time = time() - $hour * 3600;

                if ($time <= strtotime(min($cacheData))) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * AE卡 国内卡bin列表
     *
     * @return string[]
     */
    public static function cardCountryList(): array
    {
        return ['370502','370512','370513','378008','378331','370504','378330','370505','378256','377659','370506','370507','378259','379983','370515','370503','378009','378257','379981','370510','379131','370535','370509','370532','370571','370524','370563','370551','370550','379555','379905','370508','370569','370576','370338','377959','377138','377139','370330','370566','370568','370339','370345','377499'];
    }

    /**
     * 验证BID日月金额限制
     * @param $merchantBusiness
     * @param $data
     * @return bool
     */
    static function moneyAstrictCheck($merchantBusiness, $data): bool
    {
        $dayMoneyAstrict   = $merchantBusiness['day_money_astrict'] ?? [];
        $monthMoneyAstrict = $merchantBusiness['month_money_astrict'] ?? [];

        if (count($dayMoneyAstrict) == 0 && count($monthMoneyAstrict) == 0) {
            return false;
        }

        $dayAstrict              = array_column($dayMoneyAstrict, 'money_astrict', 'cc_type');
        $monthAstrict            = array_column($monthMoneyAstrict, 'money_astrict', 'cc_type');
        $order                   = $data['order'];
        $ccType                  = $data['card']['cc_type'];
        $anyCardDayAstrict       = $dayAstrict['*'] ?? 0;
        $counterCardDayAstrict   = $dayAstrict[$ccType] ?? 0;
        $anyCardMonthAstrict     = $monthAstrict['*'] ?? 0;
        $counterCardMonthAstrict = $monthAstrict[$ccType] ?? 0;
        $transactionBusiness     = Cache::get('Transaction_' . $merchantBusiness['business_id'] . '_Amount_' . date('Ymd')) ?? [];

        if ($order['currency'] == 'USD') {
            $amountUsd = $order['amount'];
        } else {
            $currencyRate = DirectoryCurrency::firstWhere('code', $order['currency']);
            $amountUsd    = amount_format($order['amount'] / $currencyRate->rate, 2);
        }

        if (self::moneyAstrictJudge($transactionBusiness, $amountUsd, $anyCardDayAstrict)) {
            logger()->channel('intercept')->info('商户日限额*卡种达标', [
                'order_id'    => $data['order']['order_id'],
                'business_id' => $merchantBusiness['business_id'],
            ]);
            dispatch(new SendSlsLog(
                ['message' => '商户日限额*卡种达标'],
                ['order_id'    => $data['order']['order_id'], 'business_id' => $merchantBusiness['business_id']],
                'info',
                'intercept'
            ));

            return true;
        }

        if (self::moneyAstrictJudge($transactionBusiness, $amountUsd, $counterCardDayAstrict, 'day_amount', $ccType)) {
            logger()->channel('intercept')->info('商户日限额' . $ccType . '卡种达标', [
                'order_id'    => $data['order']['order_id'],
                'business_id' => $merchantBusiness['business_id'],
            ]);
            dispatch(new SendSlsLog(
                ['message' => '商户日限额' . $ccType . '卡种达标'],
                ['order_id'    => $data['order']['order_id'], 'business_id' => $merchantBusiness['business_id']],
                'info',
                'intercept'
            ));

            return true;
        }

        if (self::moneyAstrictJudge($transactionBusiness, $amountUsd, $anyCardMonthAstrict, 'month_amount')) {
            logger()->channel('intercept')->info('商户月限额*卡种达标', [
                'order_id'    => $data['order']['order_id'],
                'business_id' => $merchantBusiness['business_id'],
            ]);
            dispatch(new SendSlsLog(
                ['message' => '商户月限额*卡种达标'],
                ['order_id'    => $data['order']['order_id'], 'business_id' => $merchantBusiness['business_id']],
                'info',
                'intercept'
            ));

            return true;
        }

        if (self::moneyAstrictJudge($transactionBusiness, $amountUsd, $counterCardMonthAstrict, 'month_amount', $ccType)) {
            logger()->channel('intercept')->info('商户月限额' . $ccType . '卡种达标', [
                'order_id'    => $data['order']['order_id'],
                'business_id' => $merchantBusiness['business_id'],
            ]);
            dispatch(new SendSlsLog(
                ['message' => '商户月限额' . $ccType . '卡种达标'],
                ['order_id'    => $data['order']['order_id'], 'business_id' => $merchantBusiness['business_id']],
                'info',
                'intercept'
            ));

            return true;
        }

        return false;
    }

    /**
     * 判断Bid是否限额
     * @param array $transactionBusiness
     * @param [type] $amountUsd 订单USD金额
     * @param [type] $judgeMoney 限制金额
     * @param string $judgeType  限制类型
     * @param string $ccType    订单卡种
     * @return boolean
     */
    public static function moneyAstrictJudge(array $transactionBusiness = [], $amountUsd, $judgeMoney, string $judgeType = 'day_amount', string $ccType = '*'): bool
    {
        if ($judgeMoney != 0) {
            if ($amountUsd > $judgeMoney) {
                return true;
            }

            if (
                isset($transactionBusiness[$judgeType][$ccType])
                && ($transactionBusiness[$judgeType][$ccType] + $amountUsd) > $judgeMoney
            ) {
                return true;
            }
        }

        return false;
    }

    /**
     * 同卡号短时间频繁请求支付的限制规则
     * @param $data
     * @return bool
     */
    static function cardFakedOrdersLimit($data): bool
    {
        $lock = Cache::lock('Card_Faked_Orders_Limit_Lock_' . md5($data['card']['card_number']), 10);

        if (!$lock->get() && $data['order']['merchant_id'] != '171031004584662') {
            return true;
        }

        return false;
    }

    /**
     * 卡有效期验证
     *
     * @param $data
     * @return boolean
     */
    static function cardDateVerify($data): bool
    {
        $expirationYear  = DES3::decrypt($data['card']['expiration_year']);
        $expirationMonth = DES3::decrypt($data['card']['expiration_month']);
        $cardDate        = '20' . $expirationYear . $expirationMonth;
        if (date('Ym') > $cardDate || $expirationMonth < 1 || $expirationMonth > 12) {
            return true;
        }

        return false;
    }

    /**
     * 获取同卡号24小时失败限制
     *
     * @param $businessId
     * @param $ccType
     * @return array
     */
    static function getCardContinuousFailure($businessId, $ccType):array
    {
        $number     = 3;
        $hour       = 24;
        $failLimits = MerchantBusiness::where('business_id', $businessId)->value('fail_limits');
        if (!empty($failLimits)) {
            $failLimit = [];
            foreach ($failLimits as $value) {
                $failLimit[$value['cc_type']] = $value;
            }

            $number = $failLimit[$ccType]['fail_limit'] ?? $failLimit['*']['fail_limit'] ?? 3;
            $hour   = $failLimit[$ccType]['limit_time'] ?? $failLimit['*']['limit_time'] ?? 24;
        }

        return [$number, $hour];
    }

    static function fraudVerify($data): bool
    {
        $req = [
            'ip'                   => $data['address']['ip'],
            'transaction_id'       => $data['order']['order_id'],
            'email'                => $data['address']['bill_email'],
            'phone_number'         => $data['address']['bill_phone'],
            'merchant_id'          => $data['order']['merchant_id'],
            'shipping_country'     => $data['address']['ship_country_isoa2'],
            'shipping_city'        => $data['address']['ship_city'],
            'shipping_region'      => $data['address']['ship_state'],
            'shipping_zip'         => $data['address']['ship_postcode'],
            'shipping_street'      => $data['address']['ship_address'],
            'shipping_phone'       => $data['address']['ship_phone'],
            'shipping_fullname'    => $data['address']['ship_name'],
            'billing_country'      => $data['address']['bill_country_isoa2'],
            'billing_city'         => $data['address']['bill_city'],
            'billing_region'       => $data['address']['bill_state'],
            'billing_zip'          => $data['address']['bill_postcode'],
            'billing_street'       => $data['address']['bill_address'],
            'billing_phone'        => $data['address']['bill_phone'],
            'card_fullname'        => $data['address']['bill_name'],
            'card_bin'             => substr($data['card']['card_mask'], 0, 6),
            'card_hash'            => $data['card']['card_number'],
            'card_expire'          => '20'.DES3::decrypt($data['card']['expiration_year']).'-'.DES3::decrypt($data['card']['expiration_month']),
            'card_last'            => substr($data['card']['card_mask'], -4),
//            'avs_result'           => $data['card']['bill_name'],
            'transaction_amount'   => $data['order']['amount'],
            'transaction_currency' => $data['order']['currency'],
        ];

        $seon = new Seon();
        return $seon->fraud($req);
    }
}
