<?php


namespace App\Classes\Alert\Gateways;

use App\Classes\Alert\Contracts\GateWay;
use App\Classes\Supports\Log;
use App\Classes\Supports\Logger;
use App\Classes\Supports\Traits\HasHttpRequest;
use App\Jobs\SendSlsLog;
use App\Classes\Alert\Contracts\Support;
use App\Models\ChargebackCase;
use App\Services\AlertService;

class Wintranx implements GateWay
{
    use HasHttpRequest;

    private $accessToken;

    public $param;

    public function __construct($param)
    {
        $this->param = $param;
        $log         = new Logger();
        $config      = ['file' => storage_path('logs/wintranxNotice.log')];
        
        $log->setConfig($config);
        Log::setInstance($log);
    }
    
    public function getCaseInfo($request)
    {
        
    }

    public function noticeModifyStatus($caseId, $data) 
    {
        $signData = [
            'method'   => 'POST',
            'url'      => $this->param['url'],
            'endpoint' => '/winshield/api/update',
            'data'     => Support::xmlEncode($data, 'winshield'),
            'date'     => date('Y-m-d H:i:s')
        ];

        $data = [
            'header' => [
                'Content-Type'          => 'application/xml',
                'Authorization'         => 'Basic ' . $this->_auth(),
                'x-winshield-signature' => Support::_sign($signData, $this->param['secretKey']),
                'x-winshield-timestamp' => $signData['date'],
            ],
            'data' => $signData['data'],
        ];

        $result = $this->requestApi($signData['endpoint'], $data, strtolower($signData['method']));
        $code   = $result['status'] ?? '';
        if ($code != 'success') {
            // 发送预警告警
            $tempData = [
                'alert_from'      => ChargebackCase::ALERT_FROM_WINTRANX,
                'chargeback_from' => ChargebackCase::FROM_ETHOCA,
                'case_id'         => $caseId,
                'warning_type'    => ChargebackCase::WARNING_TYPE_RESPONSE_FAILED
            ];

            AlertService::sendAlertWarning($tempData);
        }
                
        return $result;
    }
    
    public function requestApi(string $endpoint, array $data, string $method)
    {
        $result = [];
        $url    = $this->param['url'] . $endpoint;
        
        Log::info('Wintranx Request API', [$url, $data]);
        dispatch(new SendSlsLog(
            ['message' => 'Wintranx Request API'],
            [$url, $data],
            'info',
            'wintranx'
        ));
        
        try {
            $result = $this->$method($url, $data['data'], ['headers' => $data['header']]);
        } catch (\Exception $e) {
            // 记录日志
            Log::warning($e->getMessage());
            dispatch(new SendSlsLog(
                ['message' => $e->getMessage()],
                [],
                'warning',
                'wintranx'
            ));
        }
        
        Log::info('Wintranx Result API', $result);
        dispatch(new SendSlsLog(
            ['message' => 'Wintranx Result API'],
            $result,
            'info',
            'wintranx'
        ));

        return $result;
    }
    
    private function _auth() 
    {
        return base64_encode($this->param['userName'] . ':' . $this->param['password']);
    }
}
