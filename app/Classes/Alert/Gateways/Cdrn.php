<?php


namespace App\Classes\Alert\Gateways;

use App\Classes\Alert\Contracts\GateWay;
use App\Classes\Supports\Log;
use App\Classes\Supports\Logger;
use App\Classes\Supports\Traits\HasHttpRequest;
use App\Jobs\SendSlsLog;
use App\Classes\Alert\Contracts\Support;
use App\Models\ChargebackCase;
use App\Services\AlertService;

class Cdrn implements GateWay
{
    use HasHttpRequest;

    private $accessToken;

    public $param;

    public function __construct($param)
    {
        $this->param = $param;
        $log         = new Logger();
        $config      = ['file' => storage_path('logs/cdrnNotice.log')];
        
        $log->setConfig($config);
        Log::setInstance($log);
    }
    
    public function getCaseInfo($request)
    {
        $signData = [
            'method'     => 'GET',
            'cdrnDomain' => explode('//', $this->param['url'])[1],
            'endpoint'   => '/partners/cases/' . $request['caseid'],
            'body'       => '',
            'version'    => $this->param['version'],
            'stringDate' => $this->_getStringDate(),
        ];
        
        $data = [
            'header' => [
                'x-verifi-api-version' => $this->param['version'],
                'x-verifi-partner'     => $this->param['partnerId'],
                'x-verifi-signature'   => Support::_sign($signData, $this->param['secretKey']),
                'x-verifi-timestamp'   => $signData['stringDate'],
            ]
        ];
          
        return $this->requestApi($signData['endpoint'], $data, strtolower($signData['method']));
    }
    
    public function noticeModifyStatus($caseId, $data) 
    {
        $xml = Support::xmlEncode($data, 'case', 'item', 'xmlns="http://api.partner.cdrn.com/domain"', 'id', 'utf-8', false);
        
        $signData = [
            'method'     => 'PUT',
            'cdrnDomain' => explode('//', $this->param['url'])[1],
            'endpoint'   => '/partners/cases/' . $caseId,
            'body'       => $xml,
            'version'    => $this->param['version'],
            'stringDate' => $this->_getStringDate(),
        ];
        
        $data = [
            'header' => [
                'Content-Type'         => 'application/xml',
                'x-verifi-api-version' => $this->param['version'],
                'x-verifi-partner'     => $this->param['partnerId'],
                'x-verifi-signature'   => Support::_sign($signData, $this->param['secretKey']),
                'x-verifi-timestamp'   => $signData['stringDate'],
            ],
            'data'  => $xml,
        ];

        $request = $this->requestApi($signData['endpoint'], $data, strtolower($signData['method']));
        $code    = $request['code'] ?? '';
        if ($code != '202') {
            // 发送预警告警
            $tempData = [
                'alert_from'      => ChargebackCase::ALERT_FROM_VERIFI,
                'chargeback_from' => ChargebackCase::FROM_CDRN,
                'case_id'         => $caseId,
                'warning_type'    => ChargebackCase::WARNING_TYPE_RESPONSE_FAILED
            ];

            AlertService::sendAlertWarning($tempData);
        }

        return $request;
    }
    
    public function requestApi(string $endpoint, array $data, string $method)
    {
        $result = [];
        $url    = $this->param['url'] . $endpoint;
        
        Log::info('Cdrn Request API', [$url, $data]);
        dispatch(new SendSlsLog(
            ['message' => 'Cdrn Request API'],
            [$url, $data],
            'info',
            'cdrn'
        ));
        
        try {
            if ($method == 'get') {
                $result = $this->get($url, $data['data'] ?? [], $data['header']);  
            } else {
                $result = $this->put($url, $data['data'], ['headers' => $data['header']]);
            }
            
        } catch (\Exception $e) {
            // 记录日志
            Log::warning($e->getMessage());
            dispatch(new SendSlsLog(
                ['message' => $e->getMessage()],
                [],
                'warning',
                'cdrn'
            ));
        }
        
        Log::info('Cdrn Result API', $result);
        dispatch(new SendSlsLog(
            ['message' => 'Cdrn Result API'],
            $result,
            'info',
            'cdrn'
        ));
        
        return $result;
    }

    private function _getStringDate() 
    {
        $date = now()->setTimezone('UTC')->toIso8601String();
        
        return explode('+', $date)[0] . 'Z';
    }
}
