<?php

namespace App\Classes\Alert\Contracts;


trait Support
{
    /**
     * XML
     *
     * @param $data
     * @param string $root
     * @param string $item
     * @param string $attr
     * @param string $id
     * @param string $encoding
     * @return string
     */
    public static function xmlEncode($data, $root = 'titan', $item = 'item', $attr = '', $id = 'id', $encoding = 'utf-8', $type = true)
    {
        if (is_array($attr)) {
            $_attr = array();
            foreach ($attr as $key => $value) {
                $_attr[] = "{$key}=\"{$value}\"";
            }
            $attr = implode(' ', $_attr);
        }
        $attr = trim($attr);
        $attr = empty($attr) ? '' : " {$attr}";
        if ($type) {
            $xml  = "<?xml version='1.0' encoding='{$encoding}'?>";
        }else {        
            $xml = '';
        }

        $xml  .= "<{$root}{$attr}>";
        $xml  .= self::_dataToXml($data, $item, $id);
        $xml  .= "</{$root}>";
        return $xml;
    }

    /**
     * XML 子方法
     *
     * @param $data
     * @param string $item
     * @param string $id
     * @return string
     */
    public static function _dataToXml($data, $item = 'item', $id = 'id')
    {
        $xml = $attr = '';
        foreach ($data as $key => $val) {
            if (is_numeric($key)) {
                $id && $attr = " {$id}=\"{$key}\"";
                $key = $item;
            }
            $xml .= "<{$key}{$attr}>";
            $xml .= (is_array($val) || is_object($val)) ? self::_dataToXml($val, $item, $id) : $val;
            $xml .= "</{$key}>";
        }
        return $xml;
    }

    /**
     * 将xml转为数组
     *
     * @param $xml
     * @return mixed
     */
    public static function _xmlToArr($xml)
    {
        $parser = xml_parser_create();
        $arr    = array();

        if (xml_parse($parser, $xml, true)) {
            $arr = json_decode(json_encode(simplexml_load_string($xml)), true);
            xml_parser_free($parser);
        }

        return self::_cleanArr($arr);
    }

    /**
     * 将数组中的空数组转为字符串
     *
     * @param $arr
     * @return array
     */
    public static function _cleanArr($arr)
    {
        $data = array();

        if (!empty($arr)) {
            foreach ($arr as $key => $val) {
                if (is_array($val)) {
                    if (empty($val)) {
                        $data[$key] = '';
                    } else {
                        $data[$key] = self::_cleanArr($val);
                    }
                } else {
                    $data[$key] = $val;
                }
            }
        }

        return $data;
    }
    
    public static function _sign(&$data, $secretKey)
    {
        return base64_encode(utf8_encode(hash_hmac(
            'sha256',
            self::_genSignContent($data),
            $secretKey,
            false
        )));
    }
    
    public static function _genSignContent($data)
    {
        $string = '';
        
        foreach($data as $vo) {
            $string .= $vo . "\n";
        }
        
        return $string;
    }
}
