<?php

namespace App\Classes\LocalPay\Events;

class SlsLog extends Event
{
    /**
     * Content.
     *
     * @var array
     */
    public $content;
    
    /**
     * RequestId.
     *
     * @var string
     */
    public $requestId;

    /**
     * Message.
     *
     * @var string
     */
    public $message;
    
    /**
     * Level.
     *
     * @var string
     */
    public $level;

    /**
     * Bootstrap.
     */
    public function __construct(string $driver, string $message, array $content, string $requestId, string $level = 'info', string $gateway = '')
    {
        $this->content   = $content;
        $this->message   = $message;
        $this->requestId = $requestId;
        $this->level     = $level;

        parent::__construct($driver, $gateway);
    }
}
