<?php

namespace App\Classes\LocalPay\Gateways\Request;

use App\Classes\LocalPay\Contracts\GatewayInterface;
use App\Classes\LocalPay\Exceptions\InvalidArgumentException;
use App\Classes\Supports\Collection;

abstract class Gateway implements GatewayInterface
{
    /**
     * Mode.
     *
     * @var string
     */
    protected $mode;

    /**
     * Bootstrap.
     *
     * @throws InvalidArgumentException
     */
    public function __construct()
    {
        $this->mode = Support::getInstance()->mode;
    }

    /**
     * LocalPay an order.
     *
     * @param string $endpoint
     *
     * @return Collection
     */
    abstract public function pay($endpoint, array $payload);
}
