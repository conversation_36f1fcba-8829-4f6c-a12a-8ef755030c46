<?php

namespace App\Classes\LocalPay\Gateways\Request;

use App\Classes\Supports\Collection;
use App\Classes\LocalPay\Events;
use App\Classes\LocalPay\Parser\Request;
use App\Models\LocalRefund;

class RefundGateway extends Gateway
{
	/**
	 * LocalPay an order.
	 *
	 * @param string $endpoint
	 *
	 * @param array $payload
	 * @return Collection
	 */
	public function pay($endpoint, array $payload)
	{
        $post = [
            'status'          => LocalRefund::STATUS_APPROVED,
            'orderId'         => $payload['order_id'],
            'code'            => get_system_code('000'),
            'transactionType' => 'refund',
            'result'          => 'Transaction is approved',
            'remark'          => '交易成功'
        ];

		Events::dispatch(new Events\ApiRequesting('Request', '',  $endpoint, $payload));
        Events::dispatch(new Events\SlsLog(
            'Request', 
            'Requesting To Api',
            [$endpoint, $payload], 
            $payload['refund_id']
        ));
		
		return Request::refundParser($post);
	}
}
