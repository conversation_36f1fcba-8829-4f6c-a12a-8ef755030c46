<?php

namespace App\Classes\LocalPay\Gateways\Request;


use App\Classes\Supports\Collection;
use App\Classes\LocalPay\Events;
use App\Classes\LocalPay\Parser\Request;
use App\Models\LocalOrder;
use Illuminate\Support\Facades\Cache;

class AuthGateway extends Gateway
{
    /**
     * LocalPay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload)
    {
        // 缓存key
        $mark    = MD5($payload['order_id'] . 'request');
        $markArr = [
            'order_id' => $payload['order_id'],
            'class'    => 'Request',
            'code'     => get_system_code('000'),
            'status'   => LocalOrder::STATUS_APPROVED,
            'result'   => 'Transaction is approved',
            'remark'   => '交易成功',
            'orderId'  => time(),
        ];

        Cache::add($mark, $markArr, 24 * 60 * 60);

        $post = [
            'url'     => route('api.v1.localVerification', ['mark' => $mark], true),
            'code'    => get_system_code('200'),
            'status'  => LocalOrder::STATUS_PENDING,
            'result'  => 'Transaction is pending',
            'remark'  => '交易待定',
            'orderId' => time(),
        ];

        Events::dispatch(new Events\ApiRequesting('Request', '',  $endpoint, $payload));
        Events::dispatch(new Events\SlsLog(
            'Request', 
            'Requesting To Api',
            [$endpoint, $payload], 
            $payload['order_id']
        ));
        
        return Request::authParser($post);
    }
}
