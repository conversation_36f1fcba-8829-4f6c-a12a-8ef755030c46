<?php

namespace App\Classes\LocalPay\Parser;

use App\Classes\LocalPay\Contracts\GatewayParserInterface;
use App\Classes\LocalPay\Contracts\Support;
use App\Classes\Supports\Arr;
use App\Classes\Supports\Collection;
use App\Models\ChannelSupplier;
use App\Models\DirectoryExternalCode;
use App\Models\Order;

class BaseParser implements GatewayParserInterface
{
    protected static $_respCode = [];
    protected static $_channelExternalCode = [];
    public static $_orderType = Order::TYPES_SALE;

    public static function authParser($params): Collection
    {
        self::$_respCode            = self::getRespCode();
        self::$_channelExternalCode = self::getChannelExternalCode(static::$_supplierName);

        return static::_authParser($params);
    }

    public static function captureParser($params): Collection
    {
        return self::authParser($params);
    }

	public static function refundParser($params) : Collection
	{
		self::$_respCode            = self::getRespCode();
		self::$_channelExternalCode = self::getChannelExternalCode(static::$_supplierName);

		return static::_refundParser($params);
	}

	public static function retrieveParser($params) : Collection
	{
		self::$_respCode            = self::getRespCode();
		self::$_channelExternalCode = self::getChannelExternalCode(static::$_supplierName);

		return static::_retrieveParser($params);
	}

	public static function syncParser($params) : Collection
	{
		return self::authParser(Support::processingApiResult($params));
	}

	public static function asyncParser($params) : Collection
	{
		self::$_respCode            = self::getRespCode();
		self::$_channelExternalCode = self::getChannelExternalCode(static::$_supplierName);

		return static::_asyncParser($params);
	}

	public static function notifyParser($params) : Collection
	{
		self::$_respCode            = self::getRespCode();
		self::$_channelExternalCode = self::getChannelExternalCode(static::$_supplierName);

		return static::_notifyParser($params);
	}

    public static function refundUpdate($params) : Collection
    {
        self::$_respCode            = self::getRespCode();
        self::$_channelExternalCode = self::getChannelExternalCode(static::$_supplierName);

        return static::_refundUpdate($params);
    }

	/**
	 * @return array
	 */
	public static function getRespCode(): array
	{
		// 获取映射码列表
		$directoryCodeList = DirectoryExternalCode::all()->toArray();
		$data              = [];

		if (!empty($directoryCodeList)) {
			foreach ($directoryCodeList as $val) {
				$isThrow                     = ($val['is_throw'] > 0) ? true : false;
				$data[$val['external_code']] = array($val['transaction_status'], $val['external_results'], $val['external_remarks'], $isThrow);
			}
		}

		return $data;
	}

	/**
	 * @param $supplierName
	 * @return array
	 */
	public static function getChannelExternalCode($supplierName): array
	{
		// 获取映射码列表
		$externalCode        = ChannelSupplier::with('channelExternalCode')->where('file_name', $supplierName)->first();
		$channelExternalCode = [];
		$data                = [];

		if (!empty($externalCode)) {
			$channelExternalCode = $externalCode->channelExternalCode ?? [];
		}

		if (!empty($channelExternalCode)) {
			$data = Arr::pluck($channelExternalCode->toArray(), 'external_code', 'transaction_code');

		}

		return $data;
	}

	/**
	 * 表单拼接
	 *
	 * @param $formMethod
	 * @param $formUrl
	 * @param $formData
	 * @return string
	 */
	public static function formPackage($formMethod, $formUrl, $formData = array())
	{
		if (!empty($formMethod)) {
			$paymentForm  = '<form method="' . $formMethod . '" action="' . $formUrl . '" id="checkform" name="checkform" target="_top">' . "\n";

			if (!empty($formData)) {
				foreach ($formData as $key => $val) {
					$paymentForm .= '<input type="hidden" value="' . $val . '" name="' . $key . '">' . "\n";
				}
			}

			$paymentForm .= '</form>' . "\n";
			$paymentForm .= '<script type="text/javascript">' . "\n";
			$paymentForm .= 'window.onload=function(){' . "\n";
			$paymentForm .= 'document.checkform.submit();' . "\n";
			$paymentForm .= '};' . "\n";
			$paymentForm .= '</script>' . "\n";
		} else {
			$paymentForm = '<script type="text/javascript">' . "\n";
			$paymentForm .= 'window.onload=function(){' . "\n";
			$paymentForm .= 'top.location="' . $formUrl . '";' . "\n";
			$paymentForm .= '};' . "\n";
			$paymentForm .= '</script>' . "\n";
		}

		$head = '<!DOCTYPE html><html><head><meta charset="UTF-8"><title></title>
				<style type="text/css">
					* {padding: 0; margin: 0;}
					body {color: #00112C; font-family: "Fakt",sans-serif,Helvetica,Arial; font-size: 17px; font-weight: 400; line-height: 26px; -webkit-font-smoothing: antialiased;}
					.info {padding-top: 280px;margin: 0px auto;text-align: center;font-size: 35px;font-weight: 400;line-height: 1.5;color: #444;}
					.info-load {margin-top: 50px;}
					.info-safety {margin-top: 300px;}
					.info-safety img {vertical-align: middle;margin: 0 10px;width: 200px;}
				</style></head><body>';

		$foot = '<div class="info">
					<div class="info-load">
						<img src="data:image/gif;base64,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" />
					</div>
				</div></body></html>';

		return $head . $paymentForm . $foot;
	}
}
