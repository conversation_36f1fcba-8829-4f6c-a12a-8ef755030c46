<?php

namespace App\Classes\LocalPay\Exceptions;

use App\Jobs\SendNotice;

class Exception extends \Exception
{
    const UNKNOWN_ERROR = 9999;

    const INVALID_GATEWAY = 1;

    const INVALID_CONFIG = 2;

    const INVALID_ARGUMENT = 3;

    const ERROR_GATEWAY = 4;

    const INVALID_SIGN = 5;

    const ERROR_BUSINESS = 6;

    /**
     * Raw error info.
     *
     * @var array
     */
    public $raw;

    /**
     * Bootstrap.
     *
     * @param string       $message
     * @param array|string $raw
     * @param int|string   $code
     */
    public function __construct($message = '', $raw = [], $code = self::UNKNOWN_ERROR)
    {
        $message = '' === $message ? 'Unknown Error' : $message;
        $this->raw = is_array($raw) ? $raw : [$raw];

        // 组装告警数据
        $content  = sprintf("[交易告警]\n故障详情: %s\n来源: %s",
            $message,
            config('app.url')
        );
        $data = [
            'level'             => 1,
            'contents'          => $content,
            'notice_user_roles' => 'Administrator,Operate Supervisor',
            'type'              => 3,
            'status'            => 2,
        ];

        dispatch(new SendNotice($data, 5));

        parent::__construct($message, intval($code));
    }
}
