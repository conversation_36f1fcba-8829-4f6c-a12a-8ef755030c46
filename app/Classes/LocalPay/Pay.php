<?php

namespace App\Classes\LocalPay;

use App\Classes\LocalPay\Gateways\Xborderpay;
use Exception;
use App\Classes\LocalPay\Contracts\GatewayApplicationInterface;
use App\Classes\LocalPay\Exceptions\InvalidGatewayException;
use App\Classes\LocalPay\Listeners\KernelLogSubscriber;
use App\Classes\Supports\Config;
use App\Classes\Supports\Log;
use App\Classes\Supports\Logger;
use App\Classes\Supports\Str;

/**
 * @method static Xborderpay xborderpay(array $config) Xborderpay
 */
class Pay
{
    /**
     * Config.
     *
     * @var Config
     */
    protected $config;

    /**
     * Bootstrap.
     *
     * @throws Exception
     */
    public function __construct(array $config)
    {
        $this->config = new Config($config);

        $this->registerLogService();
        $this->registerEventService();
    }

    /**
     * Magic static call.
     *
     * @param string $method
     * @param array $params
     *
     * @return GatewayApplicationInterface
     * @throws InvalidGatewayException
     * @throws Exception
     *
     */
    public static function __callStatic($method, $params): GatewayApplicationInterface
    {
        $app = new self(...$params);

        return $app->create($method);
    }

    /**
     * Create a instance.
     *
     * @param string $method
     *
     * @return GatewayApplicationInterface
     * @throws InvalidGatewayException
     */
    protected function create($method): GatewayApplicationInterface
    {
        $gateway = __NAMESPACE__ . '\\Gateways\\' . Str::studly($method);

        if (class_exists($gateway)) {
            return self::make($gateway);
        }

        throw new InvalidGatewayException("Gateway [{$method}] Not Exists");
    }

    /**
     * Make a gateway.
     *
     * @param string $gateway
     *
     * @return GatewayApplicationInterface
     * @throws InvalidGatewayException
     */
    protected function make($gateway): GatewayApplicationInterface
    {
        $app = new $gateway($this->config);

        if ($app instanceof GatewayApplicationInterface) {
            return $app;
        }

        throw new InvalidGatewayException("Gateway [{$gateway}] Must Be An Instance Of GatewayApplicationInterface");
    }

    /**
     * Register log service.
     *
     * @throws Exception
     */
    protected function registerLogService()
    {
        $config = $this->config->get('log');
        $config['identify'] = 'embracy.pay';

        $logger = new Logger();
        $logger->setConfig($config);

        Log::setInstance($logger);
    }

    /**
     * Register event service.
     */
    protected function registerEventService()
    {
        Events::setDispatcher(Events::createDispatcher());

        Events::addSubscriber(new KernelLogSubscriber());
    }
}
