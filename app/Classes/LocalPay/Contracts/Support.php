<?php

namespace App\Classes\LocalPay\Contracts;

use App\Classes\LocalPay\Events;
use App\Classes\LocalPay\Exceptions\GatewayException;
use App\Classes\LocalPay\Exceptions\InvalidArgumentException;
use App\Classes\LocalPay\Exceptions\InvalidSignException;
use App\Classes\Supports\Collection;
use App\Classes\Supports\Config;
use App\Classes\Supports\Traits\HasHttpRequest;

/**
 * @property array http http options
 * @property string mode current mode
 * @property array log log options
 * @property string sign_key
 */
trait Support
{
    use HasHttpRequest;

    /**
     * gateway.
     *
     * @var string
     */
    protected $baseUri;

    /**
     * 网关名
     *
     * @var string
     */
    protected static $gatewayName;

    /**
     * Config.
     *
     * @var Config
     */
    protected $config;

    protected $timeout = 120;
    protected $connectTimeout = 120;

    /**
     * Instance.
     *
     * @var Support
     */
    private static $instance;

    /**
     * instanceList
     *
     * @var Support
     */
    private static $instanceList;

    /**
     * __get.
     *
     * @param $key
     *
     * @return mixed|Config|null
     */
    public function __get($key)
    {
        return $this->getConfig($key);
    }

    /**
     * create.
     *
     * @param Config $config
     * @return Support
     */
    public static function create(Config $config)
    {
        if (isset(self::$instanceList[$config['channel']])) {
            self::$instance = self::$instanceList[$config['channel']];
            return self::$instance;
        }

        if ('cli' === php_sapi_name() || !isset(self::$instanceList[$config['channel']])) {
            self::$instanceList[$config['channel']] = new self($config);
            self::$instance                         = self::$instanceList[$config['channel']];
        }

        return self::$instance;
    }

    /**
     * Set Http options.
     */
    protected function setHttpOptions(): self
    {
        if ($this->config->has('http') && is_array($this->config->get('http'))) {
            $this->config->forget('http.base_uri');
            $this->httpOptions = $this->config->get('http');
        }

        return $this;
    }

    /**
     * Get Base Uri.
     *
     * @return string
     */
    public function getBaseUri()
    {
        return $this->baseUri;
    }

    /**
     * getInstance.
     *
     * @return Support
     * @throws InvalidArgumentException
     *
     */
    public static function getInstance()
    {
        if (is_null(self::$instance)) {
            throw new InvalidArgumentException('You Should [Create] First Before Using');
        }

        return self::$instance;
    }

    /**
     * Get service config.
     *
     * @param string|null $key
     * @param mixed|null $default
     *
     * @return mixed|null
     */
    public function getConfig($key = null, $default = null)
    {
        if (is_null($key)) {
            return $this->config->all();
        }

        if ($this->config->has($key)) {
            return $this->config[$key];
        }

        return $default;
    }

    /**
     * Get API result.
     *
     * @param array $data
     * @return Collection
     * @throws GatewayException
     * @throws InvalidSignException
     */
    public static function requestApi(array $data): Collection
    {
        Events::dispatch(new Events\ApiRequesting(self::$gatewayName, '', self::$instance->getBaseUri(), $data));

        $result = json_decode(self::$instance->post('', $data), true);

        Events::dispatch(new Events\ApiRequested(self::$gatewayName, '', self::$instance->getBaseUri(), $result));

        return self::processingApiResult($result);
    }

    /**
     * processingApiResult.
     *
     * @param $result
     *
     * @return Collection
     * @throws GatewayException
     * @throws InvalidSignException
     */
    protected static function processingApiResult($result): Collection
    {
        if (!empty($result)) {
            $result['pay_config'] = self::$instance->config;
        }
        
        return new Collection($result);
    }

    /**
     * 格式化金额
     *
     * @param $amount
     * @param int $decimals
     * @return string
     */
    public static function amount_format($amount, $decimals = 2)
    {
        return number_format($amount, $decimals, '.', '');
    }

    /**
     * 获取当前毫秒级时间戳
     *
     * @return string
     */
    public static function millisecond()
    {
        $time = explode(' ', microtime());
        return $time[1] . str_pad(intval($time[0] * 1000), 3, '0', STR_PAD_LEFT);
    }

    /**
     * XML
     *
     * @param $data
     * @param string $root
     * @param string $item
     * @param string $attr
     * @param string $id
     * @param string $encoding
     * @return string
     */
    public static function xmlEncode($data, $root = 'titan', $item = 'item', $attr = '', $id = 'id', $encoding = 'utf-8')
    {
        if (is_array($attr)) {
            $_attr = array();
            foreach ($attr as $key => $value) {
                $_attr[] = "{$key}=\"{$value}\"";
            }
            $attr = implode(' ', $_attr);
        }
        $attr = trim($attr);
        $attr = empty($attr) ? '' : " {$attr}";
        $xml  = "<?xml version='1.0' encoding='{$encoding}'?>";
        $xml  .= "<{$root}{$attr}>";
        $xml  .= self::_dataToXml($data, $item, $id);
        $xml  .= "</{$root}>";
        return $xml;
    }

    /**
     * XML 子方法
     *
     * @param $data
     * @param string $item
     * @param string $id
     * @return string
     */
    protected static function _dataToXml($data, $item = 'item', $id = 'id')
    {
        $xml = $attr = '';
        foreach ($data as $key => $val) {
            if (is_numeric($key)) {
                $id && $attr = " {$id}=\"{$key}\"";
                $key = $item;
            }
            $xml .= "<{$key}{$attr}>";
            $xml .= (is_array($val) || is_object($val)) ? self::_dataToXml($val, $item, $id) : $val;
            $xml .= "</{$key}>";
        }
        return $xml;
    }

    /**
     * 将xml转为数组
     *
     * @param $xml
     * @return mixed
     */
    public static function _xmlToArr($xml)
    {
        $parser = xml_parser_create();
        $arr    = array();

        if (xml_parse($parser, $xml, true)) {
            $arr = json_decode(json_encode(simplexml_load_string($xml)), true);
            xml_parser_free($parser);
        }

        return self::_cleanArr($arr);
    }

    /**
     * 将数组中的空数组转为字符串
     *
     * @param $arr
     * @return array
     */
    private static function _cleanArr($arr)
    {
        $data = array();

        if (!empty($arr)) {
            foreach ($arr as $key => $val) {
                if (is_array($val)) {
                    if (empty($val)) {
                        $data[$key] = '';
                    } else {
                        $data[$key] = self::_cleanArr($val);
                    }
                } else {
                    $data[$key] = $val;
                }
            }
        }

        return $data;
    }
}
