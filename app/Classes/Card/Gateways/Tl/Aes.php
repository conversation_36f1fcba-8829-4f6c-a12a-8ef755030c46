<?php

namespace App\Classes\Card\Gateways\Tl;

class Aes
{

    // 常量定义
    const ECB_PKCS5 = 'AES-128-ECB'; // 默认模式
    const KEY_128 = 128;
    const KEY_192 = 192;
    const KEY_256 = 256;

    private function __construct()
    {
        // 私有构造函数，防止实例化
    }

    /**
     * data为String, 输出为Base64
     */
    public static function encrypt($data, $key, $isHexKey = false, $mode = self::ECB_PKCS5, $charset = 'UTF-8')
    {
        $keyB = null;
        if ($isHexKey) {
            $keyB = self::hexToByte($key);
        } else {
            $keyB = mb_convert_encoding($key, 'UTF-8', $charset);
        }

        $encryptedData = self::encryptBytes(mb_convert_encoding($data, 'UTF-8', $charset), $keyB, $mode);
        return base64_encode($encryptedData);
    }

    /**
     * data为Base64格式
     */
    public static function decrypt($data, $key, $isHexKey = false, $mode = self::ECB_PKCS5, $charset = 'UTF-8')
    {
        $keyB = null;
        if ($isHexKey) {
            $keyB = self::hexToByte($key);
        } else {
            $keyB = mb_convert_encoding($key, 'UTF-8', $charset);
        }

        $dataB = base64_decode($data);
        $decryptedData = self::decryptBytes($dataB, $keyB, $mode);
        return mb_convert_encoding($decryptedData, $charset, 'UTF-8');
    }

    private static function encryptBytes($data, $key, $mode)
    {
        $cipher = self::getCipher($key, $mode);
        return openssl_encrypt($data, $cipher, $key, OPENSSL_RAW_DATA);
    }

    private static function decryptBytes($data, $key, $mode)
    {
        $cipher = self::getCipher($key, $mode);
        return openssl_decrypt($data, $cipher, $key, OPENSSL_RAW_DATA);
    }

    private static function getCipher($key, $mode)
    {
        if (strpos($mode, 'ECB') !== false) {
            return 'AES-' . (strlen($key) * 8) . '-ECB';
        } else {
            throw new InvalidArgumentException('Unsupported mode: ' . $mode);
        }
    }

    private static function hexToByte($hex)
    {
        $len = strlen($hex);
        $data = '';
        for ($i = 0; $i < $len; $i += 2) {
            $data .= chr(hexdec(substr($hex, $i, 2)));
        }
        return $data;
    }
}
