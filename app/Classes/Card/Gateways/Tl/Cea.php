<?php

namespace App\Classes\Card\Gateways\Tl;

/**
 * 基于64位加密技术
 * 作者：yactor
 * 版本：1.0
 */
class Cea
{
    // const ENCRYPTKEY = '12345678';
    private $extendKey;
    private $ceaRotate;
    private $ceaMpiLong;

    public function __construct()
    {
        $this->ceaRotate = 6;
        $this->ceaMpiLong = 2;
        $this->extendKey = array_fill(0, 2 * $this->ceaRotate + 4, array_fill(0, 2, 0));
    }

    public function encrypt($vClearText, $keyStr)
    {
        $key       = mb_convert_encoding($keyStr, 'ISO-8859-1');  // 确保 key 字符串以字节格式处理
        $keyLength = strlen($key);
        $intStrLen = strlen($vClearText);

        if ($intStrLen < 16) {
            $vClearText = str_pad($vClearText, 16, " ");
        }

        $plain       = mb_convert_encoding($vClearText, 'ISO-8859-1');     // 确保 vClearText 以字节格式处理
        $plainLength = strlen($plain);
        $cipher      = $this->getBytes(str_repeat(chr(0), $plainLength));

        $this->tongEncrypt($this->getBytes($plain), $cipher, $plainLength, $this->getBytes($key), $keyLength);

        return base64_encode($this->toStr($cipher));
    }

    public function decrypt($vClearText, $keyStr)
    {
        $decode = base64_decode($vClearText);
        $res    = $this->getBytes(str_repeat(chr(0), strlen($decode)));  // 初始化结果数组
        // 解密
        $this->tongDecrypt($this->getBytes($decode), $res, strlen($decode), $this->getBytes($keyStr), strlen($keyStr));

        return $this->toStr($res);
    }

    protected function add($s1, $s2, &$res, $length)
    {
        $c = 0;
        for ($i = $length - 1; $i >= 0; $i--) {
            $res[$i] = $s1[$i] + $s2[$i] + $c;
            $c = ($res[$i] & 0xff00000000) >> 32;
            $res[$i] = $res[$i] & 0xffffffff;
        }
    }

    public function byteToInt($source, &$result, $length)
    {
        for ($i = 0; $i < $length; $i++) {
            $result[$i] = $source[$i] >= 0 ? $source[$i] : $source[$i] + 256;
        }
    }

    public function ceaDecrypt($cipher, &$plain)
    {
        $a = array_fill(0, 2, 0);
        $b = array_fill(0, 2, 0);
        $temp1 = array_fill(0, 2, 0);
        $temp2 = array_fill(0, 2, 0);
        $temp = array_fill(0, 16, 0);
        $this->byteToInt($cipher, $temp, 16);
        $a[0] = ($temp[0] << 24) | ($temp[1] << 16) | ($temp[2] << 8) | $temp[3];
        $a[1] = ($temp[4] << 24) | ($temp[5] << 16) | ($temp[6] << 8) | $temp[7];
        $b[0] = ($temp[8] << 24) | ($temp[9] << 16) | ($temp[10] << 8) | $temp[11];
        $b[1] = ($temp[12] << 24) | ($temp[13] << 16) | ($temp[14] << 8) | $temp[15];
        $this->add($a, $this->extendKey[0], $a, $this->ceaMpiLong);
        $this->add($b, $this->extendKey[1], $b, $this->ceaMpiLong);
        for ($i = 1; $i <= $this->ceaRotate; $i++) {
            $this->xor($a, $b, $temp1, $this->ceaMpiLong);
            $shift = (int)($b[1] & 63);
            $this->lShift($temp1, $temp2, $shift, $this->ceaMpiLong);
            $this->add($temp2, $this->extendKey[2 * $i], $a, $this->ceaMpiLong);
            $this->xor($b, $a, $temp1, $this->ceaMpiLong);
            $shift = (int)($a[1] & 63);
            $this->lShift($temp1, $temp2, $shift, $this->ceaMpiLong);
            $this->add($temp2, $this->extendKey[2 * $i + 1], $b, $this->ceaMpiLong);
        }

        $this->xor($a, $this->extendKey[2 * $this->ceaRotate + 2], $temp1, $this->ceaMpiLong);
        $this->copy($temp1, $a, $this->ceaMpiLong);
        $this->xor($b, $this->extendKey[2 * $this->ceaRotate + 3], $temp1, $this->ceaMpiLong);
        $this->copy($temp1, $b, $this->ceaMpiLong);
        $this->lShift($a, $temp1, 1, $this->ceaMpiLong);
        $this->lShift($b, $temp2, 1, $this->ceaMpiLong);
        $bit1 = $temp1[1] & 1;
        $bit2 = $temp2[1] & 1;
        $temp1[1] = ($temp1[1] & 0xfffffffe) | $bit2;
        $temp2[1] = ($temp2[1] & 0xfffffffe) | $bit1;
        $bit1 = $temp1[0] & 0x80000000;
        $bit2 = ($temp1[0] & 0x40000000) << 1;
        $bit1 ^= $bit2;
        $temp1[0] = ($temp1[0] & 0x7fffffff) | $bit1;
        $this->copy($temp1, $a, $this->ceaMpiLong);
        $this->copy($temp2, $b, $this->ceaMpiLong);
        for ($i = $this->ceaRotate; $i >= 1; $i--) {
            $this->sub($b, $this->extendKey[2 * $i + 1], $temp1, $this->ceaMpiLong);
            $shift = (int)($a[1] & 63);
            $this->rShift($temp1, $temp2, $shift, $this->ceaMpiLong);
            $this->xor($temp2, $a, $b, $this->ceaMpiLong);
            $this->sub($a, $this->extendKey[2 * $i], $temp1, $this->ceaMpiLong);
            $shift = (int)($b[1] & 63);
            $this->rShift($temp1, $temp2, $shift, $this->ceaMpiLong);
            $this->xor($temp2, $b, $a, $this->ceaMpiLong);
        }

        $this->sub($b, $this->extendKey[1], $temp1, $this->ceaMpiLong);
        $temp[8] = ($temp1[0] >> 24) & 0xFF;
        $temp[9] = ($temp1[0] >> 16) & 0xFF;
        $temp[10] = ($temp1[0] >> 8) & 0xFF;
        $temp[11] = $temp1[0] & 0xFF;
        $temp[12] = ($temp1[1] >> 24) & 0xFF;
        $temp[13] = ($temp1[1] >> 16) & 0xFF;
        $temp[14] = ($temp1[1] >> 8) & 0xFF;
        $temp[15] = $temp1[1] & 0xFF;
        $this->sub($a, $this->extendKey[0], $temp1, $this->ceaMpiLong);
        $temp[0] = ($temp1[0] >> 24) & 0xFF;
        $temp[1] = ($temp1[0] >> 16) & 0xFF;
        $temp[2] = ($temp1[0] >> 8) & 0xFF;
        $temp[3] = $temp1[0] & 0xFF;
        $temp[4] = ($temp1[1] >> 24) & 0xFF;
        $temp[5] = ($temp1[1] >> 16) & 0xFF;
        $temp[6] = ($temp1[1] >> 8) & 0xFF;
        $temp[7] = $temp1[1] & 0xFF;
        $this->intToByte($temp, $plain, 16);
    }

    public function ceaEncrypt($plain, &$cipher)
    {
        $a = array_fill(0, 2, 0);
        $b = array_fill(0, 2, 0);
        $temp1 = array_fill(0, 2, 0);
        $temp2 = array_fill(0, 2, 0);
        $temp = array_fill(0, 16, 0);
        $this->byteToInt($plain, $temp, 16);
        $a[0] = ($temp[0] << 24) | ($temp[1] << 16) | ($temp[2] << 8) | $temp[3];
        $a[1] = ($temp[4] << 24) | ($temp[5] << 16) | ($temp[6] << 8) | $temp[7];
        $b[0] = ($temp[8] << 24) | ($temp[9] << 16) | ($temp[10] << 8) | $temp[11];
        $b[1] = ($temp[12] << 24) | ($temp[13] << 16) | ($temp[14] << 8) | $temp[15];
        $this->add($a, $this->extendKey[0], $a, $this->ceaMpiLong);
        $this->add($b, $this->extendKey[1], $b, $this->ceaMpiLong);
        for ($i = 1; $i <= $this->ceaRotate; $i++) {
            $this->xor($a, $b, $temp1, $this->ceaMpiLong);
            $shift = (int)($b[1] & 63);
            $this->lShift($temp1, $temp2, $shift, $this->ceaMpiLong);
            $this->add($temp2, $this->extendKey[2 * $i], $a, $this->ceaMpiLong);
            $this->xor($b, $a, $temp1, $this->ceaMpiLong);
            $shift = (int)($a[1] & 63);
            $this->lShift($temp1, $temp2, $shift, $this->ceaMpiLong);
            $this->add($temp2, $this->extendKey[2 * $i + 1], $b, $this->ceaMpiLong);
        }

        $this->lShift($a, $temp1, 63, $this->ceaMpiLong);
        $this->lShift($b, $temp2, 63, $this->ceaMpiLong);
        $bit1 = $temp1[0] & 0x80000000;
        $bit2 = $temp2[0] & 0x80000000;
        $temp1[0] = $temp1[0] & 0x7fffffff;
        $temp1[0] |= $bit2;
        $temp2[0] = $temp2[0] & 0x7fffffff;
        $temp2[0] |= $bit1;
        $bit1 = $temp1[0] & 0x40000000;
        $bit2 = ($temp1[0] & 0x20000000) << 1;
        $bit1 ^= $bit2;
        $temp1[0] = $temp1[0] & 0xbfffffff;
        $temp1[0] |= $bit1;
        $this->xor($temp1, $this->extendKey[2 * $this->ceaRotate + 2], $a, $this->ceaMpiLong);
        $this->xor($temp2, $this->extendKey[2 * $this->ceaRotate + 3], $b, $this->ceaMpiLong);
        for ($i = $this->ceaRotate; $i >= 1; $i--) {
            $this->sub($b, $this->extendKey[2 * $i + 1], $temp1, $this->ceaMpiLong);
            $shift = (int)($a[1] & 63);
            $this->rShift($temp1, $temp2, $shift, $this->ceaMpiLong);
            $this->xor($temp2, $a, $b, $this->ceaMpiLong);
            $this->sub($a, $this->extendKey[2 * $i], $temp1, $this->ceaMpiLong);
            $shift = (int)($b[1] & 63);
            $this->rShift($temp1, $temp2, $shift, $this->ceaMpiLong);
            $this->xor($temp2, $b, $a, $this->ceaMpiLong);
        }

        $this->sub($b, $this->extendKey[1], $temp1, $this->ceaMpiLong);
        $temp[8] = ($temp1[0] >> 24) & 0xFF;
        $temp[9] = ($temp1[0] >> 16) & 0xFF;
        $temp[10] = ($temp1[0] >> 8) & 0xFF;
        $temp[11] = $temp1[0] & 0xFF;
        $temp[12] = ($temp1[1] >> 24) & 0xFF;
        $temp[13] = ($temp1[1] >> 16) & 0xFF;
        $temp[14] = ($temp1[1] >> 8) & 0xFF;
        $temp[15] = $temp1[1] & 0xFF;
        $this->sub($a, $this->extendKey[0], $temp1, $this->ceaMpiLong);
        $temp[0] = ($temp1[0] >> 24) & 0xFF;
        $temp[1] = ($temp1[0] >> 16) & 0xFF;
        $temp[2] = ($temp1[0] >> 8) & 0xFF;
        $temp[3] = $temp1[0] & 0xFF;
        $temp[4] = ($temp1[1] >> 24) & 0xFF;
        $temp[5] = ($temp1[1] >> 16) & 0xFF;
        $temp[6] = ($temp1[1] >> 8) & 0xFF;
        $temp[7] = $temp1[1] & 0xFF;
        $this->intToByte($temp, $cipher, 16);
    }

    public function copy($source, &$result, $length)
    {
        for ($i = 0; $i < $length; $i++) {
            $result[$i] = $source[$i];
        }
    }

    private function blockDec($inData, &$outData, $inTemp, $outTemp, $n)
    {
        $blockSize = 16; // 每块的大小为16字节
        for ($i = 0; $i < $n; $i++) {
            $k = $i * $blockSize;
            for ($j = 0; $j < $blockSize; $j++) {
                $inTemp[$j] = $inData[$k];
                $k++;
            }

            $this->ceaDecrypt($inTemp, $outTemp);

            $k = $i * $blockSize;
            for ($j = 0; $j < $blockSize; $j++) {
                $outData[$k] = $outTemp[$j];
                $k++;
            }
        }
    }


    private function fragDec($inData, &$outData, $inTemp, $outTemp, $n, $remainder)
    {
        $blockSize = 16; // 每块的大小为16字节
        $k = ($n - 1) * $blockSize + $remainder;

        // 从输入数据中提取块到临时数组
        for ($j = 0; $j < $blockSize; $j++) {
            $inTemp[$j] = $inData[$k];
            $k++;
        }

        // 解密临时数组
        $this->ceaDecrypt($inTemp, $outTemp);

        // 将解密结果的前部分拷贝到输出数据中
        $k = $n * $blockSize;
        for ($j = 0; $j < $remainder; $j++) {
            $outData[$k] = $outTemp[$j];
            $k++;
        }

        // 从输入数据中提取剩余部分到临时数组
        $k = ($n - 1) * $blockSize;
        for ($j = 0; $j < $remainder; $j++) {
            $inTemp[$j] = $inData[$k];
            $k++;
        }

        // 填充剩余部分
        for ($j = $remainder; $j < $blockSize; $j++) {
            $inTemp[$j] = $outTemp[$j];
        }

        // 再次解密
        $this->ceaDecrypt($inTemp, $outTemp);

        // 将解密结果写回输出数据
        $k = ($n - 1) * $blockSize;
        for ($j = 0; $j < $blockSize; $j++) {
            $outData[$k] = $outTemp[$j];
            $k++;
        }
    }


    public function tongDecrypt($inData, &$outData, $inLen, $key, $keyLen)
    {
        if ($inLen < 16) {
            return -1;
        }

        $blockSize = 16;
        $inTemp = array_fill(0, $blockSize, 0);
        $outTemp = array_fill(0, $blockSize, 0);

        // 初始化解密密钥
        $this->init($key, $keyLen);

        $remainder = $inLen % $blockSize;
        $n = intdiv($inLen, $blockSize);

        if ($remainder == 0) {
            $this->blockDec($inData, $outData, $inTemp, $outTemp, $n);
        } else {
            $this->blockDec($inData, $outData, $inTemp, $outTemp, $n - 1);
            $this->fragDec($inData, $outData, $inTemp, $outTemp, $n, $remainder);
        }

        return 0;
    }


    public function tongEncrypt($inData, &$outData, $inLen, $key, $keyLen)
    {
        if ($inLen < 16) {
            return -1;
        }

        $blockSize = 16;
        $inTemp = array_fill(0, $blockSize, 0);
        $outTemp = array_fill(0, $blockSize, 0);

        // 初始化加密密钥
        $this->init($key, $keyLen);

        $remainder = $inLen % $blockSize;
        $n = intdiv($inLen, $blockSize);

        // 处理完整块
        for ($i = 0; $i < $n; $i++) {
            $k = $i * $blockSize;
            for ($j = 0; $j < $blockSize; $j++) {
                $inTemp[$j] = $inData[$k];
                $k++;
            }

            $this->ceaEncrypt($inTemp, $outTemp);
            $k = $i * $blockSize;
            for ($j = 0; $j < $blockSize; $j++) {
                $outData[$k] = $outTemp[$j];
                $k++;
            }
        }

        // 处理剩余块
        if ($remainder != 0) {
            $k = $n * $blockSize;
            for ($j = 0; $j < $remainder; $j++) {
                $inTemp[$j] = $inData[$k];
                $k++;
            }

            $k = ($n - 1) * $blockSize + $remainder;
            for ($j = $remainder; $j < $blockSize; $j++) {
                $inTemp[$j] = $outData[$k];
                $k++;
            }

            $this->ceaEncrypt($inTemp, $outTemp);
            $k = ($n - 1) * $blockSize + $remainder;
            for ($j = 0; $j < $blockSize; $j++) {
                $outData[$k] = $outTemp[$j];
                $k++;
            }
        }
        // $outData = $this->getBytes($outData);
        return 0;
    }

    public function init($key, $keylength)
    {
        $blockSize = $this->ceaMpiLong;
        $keyRounds = 2 * $this->ceaRotate + 4;

        $srcKey = array_fill(0, $keyRounds, array_fill(0, $blockSize, 0));
        $a = array_fill(0, $blockSize, 0);
        $b = array_fill(0, $blockSize, 0);
        $temp1 = array_fill(0, $blockSize, 0);
        $temp2 = array_fill(0, $blockSize, 0);

        $keycount = (int) ceil($keylength / ($blockSize * 4));
        $temp = array_fill(0, $keycount * $blockSize * 4, 0);

        $this->byteToInt($key, $temp, $keylength);

        $n = 0;
        for ($i = 0; $i < $keycount; $i++) {
            for ($j = 0; $j < $blockSize; $j++) {
                $srcKey[$i][$j] = 0;
                for ($k = 0; $k < 4; $k++) {
                    $srcKey[$i][$j] = ($srcKey[$i][$j] << 8) | ($temp[$n] & 0xff);
                    $n++;
                }
            }
        }

        $this->extendKey[0][0] = 0xb7e15163;
        $this->extendKey[0][1] = 0x9e3779b9;
        for ($i = 1; $i < $keyRounds; $i++) {
            $this->lShift($this->extendKey[$i - 1], $this->extendKey[$i], $i, $blockSize);
        }

        for ($i = 0; $i < $keyRounds; $i++) {
            $this->add($a, $b, $temp1, $blockSize);
            $this->add($this->extendKey[$i], $temp1, $temp2, $blockSize);
            $this->lShift($temp2, $this->extendKey[$i], 5, $blockSize);
            $this->copy($this->extendKey[$i], $a, $blockSize);
            $this->add($a, $b, $temp1, $blockSize);
            $this->add($temp1, $srcKey[$i % $keycount], $temp2, $blockSize);
            $t = $temp1[1] % 64;
            $this->lShift($temp2, $srcKey[$i % $keycount], $t, $blockSize);
            $this->copy($srcKey[$i % $keycount], $b, $blockSize);
        }
    }


    public function intToByte($source, &$result, $length)
    {
        for ($i = 0; $i < $length; $i++) {
            $result[$i] = $source[$i] < 128 ? $source[$i] : $source[$i] - 256;
        }
    }

    public function lShift($source, &$result, $count, $length)
    {
        $mid = array_fill(0, $length, 0);
        $count %= $length * 32;
        $shiftLong = $count >> 5;
        $shiftBit = $count & 0x1f;
        $rShiftBit = 32 - $shiftBit;

        for ($i = 0; $i < $length; $i++) {
            $mid[$i] = ($source[$shiftLong] << $shiftBit) & 0xffffffff;
            if (++$shiftLong >= $length) $shiftLong = 0;
            $mid[$i] |= ($source[$shiftLong] >> $rShiftBit) & 0xffffffff;
        }

        for ($i = 0; $i < $length; $i++) {
            $result[$i] = $mid[$i];
        }
    }

    public function rShift($source, &$result, $count, $length)
    {
        $count %= $length * 32;
        $count = $length * 32 - $count;
        $count %= $length * 32;
        $this->lShift($source, $result, $count, $length);
    }

    public function sub($s1, $s2, &$result, $length)
    {
        $b = 0;
        for ($i = $length - 1; $i >= 0; $i--) {
            $result[$i] = $s1[$i] - $s2[$i] - $b;
            if ($result[$i] >= 0) {
                $b = 0;
            } else {
                $result[$i] += 0x100000000;
                $b = 1;
            }
        }
    }

    public function xor($s1, $s2, &$result, $length)
    {
        for ($i = 0; $i < $length; $i++) {
            $result[$i] = $s1[$i] ^ $s2[$i];
        }
    }

    /**
     * 将字节数组转化为string类型的数据
     * @param $bytes 字节数组
     * @return string
     */
    public function toStr($bytes): string
    {
        $str = '';
        foreach ($bytes as $ch) {
            $str .= chr($ch);
        }
        return $str;
    }

    /**
     * 转换一个string字符串为byte数组
     * @param $str 需要转换的字符串
     * @return array
     */
    public function getBytes($str): array
    {
        $len = strlen($str);
        $bytes = array();
        for ($i = 0; $i < $len; $i++) {
            if (ord($str[$i]) >= 128) {
                $byte = ord($str[$i]) - 256;
            } else {
                $byte = ord($str[$i]);
            }
            $bytes[] =  $byte;
        }
        return $bytes;
    }
}
