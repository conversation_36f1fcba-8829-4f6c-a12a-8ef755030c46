<?php


namespace App\Classes\Card\Gateways\Tl;

use App\Classes\Card\Exceptions\CardException;
use App\Classes\Supports\Traits\HasHttpRequest;
use App\Jobs\SendSlsLog;
use App\Classes\Supports\Log;
use App\Classes\Card\Contracts\CardBase;
use App\Classes\Card\Gateways\GatewaysAbstract;
use DateTime;
use DateTimeZone;
use Illuminate\Support\Facades\Cache;

abstract class Support extends GatewaysAbstract
{
    use HasHttpRequest;

    const URL_MAP = [
        // self::MODE_LIVE => 'https://global.allinpay.com',
        self::MODE_DEV => 'https://global-test.allinpay.com',
    ];


    const SIGN_ALGORITHM = 'GCP1-RSA-SHA512';

    const SHA256 = 'sha256';

    const RESPONSE_CODE_SUCCESS = '0000';  //请求渠道成功

    const RESPONSE_CODE_SUCCESS_DOWN_FILE = 200; // 请求下载文件成功


    // 卡申请单状态
    const TONG_LIAN_CARD_OPERATION_STATUS_PENDING            = '01'; // '待审核'
    const TONG_LIAN_CARD_OPERATION_STATUS_AUDIT_FAIL         = '02'; // '审核不通过'
    const TONG_LIAN_CARD_OPERATION_STATUS_PROCESSING         = '03'; // '处理中'
    const TONG_LIAN_CARD_OPERATION_STATUS_SUCCESS            = '04'; // '成功'
    const TONG_LIAN_CARD_OPERATION_STATUS_FAIL               = '05'; // '失败'
    const TONG_LIAN_CARD_OPERATION_STATUS_CARD_CLOSE_PROCESS = '06'; // '退卡处理中'
    const TONG_LIAN_CARD_OPERATION_STATUS_CLOSE              = '07'; // 交易已关闭
    const TONG_LIAN_CARD_OPERATION_STATUS_REVIEWED           = '08'; // '待复核'

    // 授权交易状态
    const TONG_LIAN_AUTH_TRAN_STATUS_SUCCESS    = '00'; // 成功
    const TONG_LIAN_AUTH_TRAN_STATUS_FAIL       = '01'; // 失败
    const TONG_LIAN_AUTH_TRAN_STATUS_WASHED_OUT = '02'; // 被冲正

    protected $config;

    /**
     * 设置配置
     *
     * @param string $config [{"key":"app_id","value":"111"}]
     * @return void
     */
    public function setConfig(string $config = ''): CardBase
    {
        $config       = json_decode($config, true);
        $this->config = array_column($config, 'value', 'key');
        return $this;
    }

    /**
     * 获取请求地址
     *
     * @param string $suffix
     * @return string
     */
    protected function getUrl(string $reqId, string $suffix = '', array $additionalData = []): string
    {
        if (isset($this->config['transitUrl'])) {
            return $this->config['transitUrl'];
        }

        return $this->getHeaderDoUrl($reqId, $suffix, $additionalData);
    }

    /**
     * 获取header中转地址
     *
     * @param string $suffix
     * @return string
     */
    protected function getHeaderDoUrl(string $reqId, string $suffix = '', array $additionalData = []): string
    {
        $url = empty($this->config["mode"]) ? self::URL_MAP[self::MODE_LIVE] : self::URL_MAP[self::MODE_DEV];
        return $url . $suffix . '?' . $this->getUrlCom($reqId, $additionalData);
    }

    /**
     * 返回请求头
     *
     * @return array
     */
    /**
     * Undocumented function
     *
     * @param string $reqId 请求id
     * @param string $doUrl 转发请求地址
     * @param string $postData 转发请求地址
     * @param array $additionalData 附加数据
     * @return array
     */
    protected function getHeader(string $reqId, string $doUrl = '', string $postData = '{}', array $additionalData = [], string $uploadFieData = ''): array
    {
        $integrityUrl = $this->getHeaderDoUrl($reqId, $doUrl);
        $url          = $doUrl;
        $urlCom       = $this->getUrlCom($reqId);
        $timeToUTC    = $this->getUTC();
        if ($uploadFieData != '') {
            $urlComData   = json_decode($postData, true);
            $urlCom       = $this->getUrlCom($reqId, $urlComData);
            $integrityUrl = $this->getHeaderDoUrl($reqId, $doUrl, $urlComData);
            $postData     = $uploadFieData;
        }

        $requestPayload = hash(self::SHA256, $postData);
        $strSign        = self::SIGN_ALGORITHM . "\n" .
            $timeToUTC . "\n" .
            $url . "\n" .
            $urlCom . "\n" .
            $requestPayload;

        $header = [
            'Content-Type' => 'application/json;charset=UTF-8',
            'X-AGCP-Send'  => $this->getUTCTimestamp(),
            'X-AGCP-Date'  => $timeToUTC,
            'X-AGCP-Auth'  => sprintf("%s:%s", self::SIGN_ALGORITHM, self::genSign($strSign, $this->config["privateKey"])),
            'X-AGCP-Crdt'  => sprintf("%s:%s:%s", $this->config["keyId"], substr($timeToUTC, 0, 8), 'gcpservice'),
        ];

        if (!empty($doUrl) && isset($this->config['transitUrl'])) {
            $header['Do-Url']     = $integrityUrl;
            $header['System-Tag'] = $this->getSystemTag();
        }

        if (!empty($additionalData)) {
            $header = array_merge($header, $additionalData);
        }

        return $header;
    }

    /**
     * 生成签名
     *
     * @param $toSign
     * @param $privateKey
     * @return string
     */
    public static function genSign($toSign, $privateKey): string
    {
        //这里他是拼接成和pem文件一样的格式
        $privateKey = "-----BEGIN RSA PRIVATE KEY-----\n" .
            wordwrap($privateKey, 64, "\n", true) .
            "\n-----END RSA PRIVATE KEY-----";

        $key = openssl_get_privatekey($privateKey);
        openssl_sign($toSign, $signature, $key, OPENSSL_ALGO_SHA512);
        // 释放私钥资源
        openssl_free_key($key);

        return bin2hex($signature);
    }

    protected function getUTC()
    {
        // 创建一个新的 DateTime 对象，并设置时区为 UTC
        $date = new DateTime("now", new DateTimeZone("UTC"));
        // 格式化日期为 YYYYMMDDHHMMSS
        return $date->format("YmdHis");
    }

    protected function getUTCTimestamp()
    {
        // 获取毫秒部分
        $milliseconds = round(microtime(true) * 1000) % 1000;

        // 格式化为 YYYYMMDDHHmmssSSS 格式
        return gmdate("YmdHis") . sprintf("%03d", $milliseconds);
    }

    protected function getUrlCom(string $reqId, array $urlComData = []): string
    {
        $urlCom = [
            'authcus' => $this->getAuthcus(),
            'merid'   => $this->config["merId"],
            'reqid'   => $reqId
        ];

        $urlCom = array_merge($urlCom, $urlComData);
        ksort($urlCom);
        return implode('&', array_map(function ($key, $value) {
            return "$key=$value";
        }, array_keys($urlCom), $urlCom));
    }

    protected function getAuthcus(): string
    {
        return substr($this->config["merId"], 0, 15);
    }

    protected function getSystemTag(): string
    {
        $tag = empty($this->config["mode"]) ? env('APP_NAME', 'Laravel') : self::MODE_DEV;
        return md5($tag);
    }

    protected function sendLog(string $reqId, string $message, array $data, string $level): void
    {
        Log::$level($reqId . PHP_EOL . $message, $data);
        dispatch(new SendSlsLog(
            ['reqId' => $reqId, 'message' => $message],
            $data,
            $level,
            'virtual_card',
        ));
    }

    protected function getReqId(): string
    {
        return uniqid() . bin2hex(random_bytes(4));
    }

    protected function getMeraplId(string $reqId): string
    {
        return 'tong' . $reqId;
    }

    protected function getReqIdTime(string $reqId): string
    {
        $uniqidPart = substr($reqId, 0, 13);
        // 转换为时间戳（秒级别）
        $timestamp = hexdec(substr($uniqidPart, 0, 8));
        return date('Y-m-d H:i:s', $timestamp);
    }


    public function verifySign(string $reqId, string $url, array $result)
    {
        if (isset($result['response']['rspcode']) && $result['response']['rspcode'] != Support::RESPONSE_CODE_SUCCESS) {
            $this->sendLog($reqId, 'url:' . $url . 'request fail, result:' . json_encode($result), [], 'warning');
            return;
        }

        $header         = $result['header'];
        $publicKey      = $this->config['publicKey'];
        $respDate       = $header['X-AGCP-Date'][0];
        $requestPayload = hash(self::SHA256, json_encode($result['response'], JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE));
        // 取后半段签名值
        [$algorithm, $respAuth] = explode(':', $header['X-AGCP-Auth'][0]);
        // 拼接签名字符串
        $strSign = $algorithm . "\n" .
            $respDate . "\n" .
            $url . "\n" . "\n" .
            $requestPayload;
        if (!$this->responseVerifySign($strSign, $respAuth, $publicKey)) {
            $this->sendLog($reqId, 'response verify sign fail', ['result' => $result, 'strSign' => $strSign], 'warning');
        }
    }

    function responseVerifySign($plainText, $signature, $publicKey): bool
    {
        $signature = hex2bin($signature);
        $publicKey = "-----BEGIN PUBLIC KEY-----\n" .
            wordwrap($publicKey, 64, "\n", true) .
            "\n-----END PUBLIC KEY-----";
        $key       = openssl_pkey_get_public($publicKey);
        if (!$key) {
            return false;
        }

        $verified = openssl_verify($plainText, $signature, $key, OPENSSL_ALGO_SHA512);
        openssl_free_key($key);
        return $verified === 1;
    }

    function handelPostData(array $postData = []): string
    {
        if (!count($postData)) {
            return '{}';
        }
        ksort($postData);
        return json_encode($postData, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
    }

    function base64UrlEncode($data)
    {
        return strtr(rtrim(base64_encode($data), '='), '+/', '-_');
    }

    /**
     * Send a HEAD request.
     *
     * @return array|string
     */
    public function hasHeaderPost(string $reqId, string $endpoint, $data, array $options = [], bool $isFile = false)
    {
        if (!is_array($data)) {
            $options['body'] = $data;
        } else {
            $options['form_params'] = $data;
        }


        try {
            $response = $isFile ? $this->allRequest('post', $endpoint, $options) : $this->hasHeaderRequest('post', $endpoint, $options);
            if (!is_array($response)) {
                throw new CardException(CardException::ERROR_GATEWAY);
            }
        } catch (\Exception $e) {
            $this->sendLog($reqId, 'hasHeaderPost API ERROR', ['url' => $endpoint, 'request' => $data ?? '', 'response' => $response, 'err' => $e->getMessage()], 'error');
            return ['header' => [], 'response' => []];
        }

        return $response;
    }

    /**
     * Send a HEAD request.
     *
     * @return array|string
     */
    public function hasHeaderRequest(string $method, string $endpoint, array $options = [])
    {
        $response = $this->getHttpClient()->{$method}($endpoint, $options);
        return ['header' => $response->getHeaders(), 'response' => $this->unwrapResponse($response)];
    }

    public function allRequest(string $method, string $endpoint, array $options = [])
    {
        $response = $this->getHttpClient()->{$method}($endpoint, $options);
        return ['header' => [], 'response' => $response];
    }

    public function storeFile(string $reqId, array $result): string
    {
        $response = $result['response'];
        try {
            if (!$response->getStatusCode() == self::RESPONSE_CODE_SUCCESS_DOWN_FILE) {
                $this->sendLog($reqId, 'request down file fail', ['result' => $result], 'error');
                return '';
            }

            $responseHeader = $response->getHeaders();
            $stream         = $response->getBody();
            $targetDir      = public_path('/data/tonglian');
            preg_match('/filename=[\'"]?([^\'"\s]+)[\'"]?/', $responseHeader['Content-Disposition'][0], $matches);
            $filename = '';
            if (isset($matches[1])) {
                $filename = $matches[1];
            }

            $targetFile = $targetDir . '/' . $filename;
            if (!is_dir($targetDir)) {
                mkdir($targetDir, 0755, true);
            }

            file_put_contents($targetFile, $stream->getContents());
            return $targetFile;
        } catch (\Exception $e) {
            $this->sendLog($reqId, 'Exception caught during file storage', ['exception' => $e->getMessage(), 'result' => $result], 'error');
        }
        return '';
    }

    public function getPayerId()
    {
        // 如果配置付款账号则使用付款账号，否则使用默认的USD，VA账户
        $payerNo = $this->config['payerNo'] ?? 'USD_VA';
        $payerId = Cache::get('TONGLIAN_PAYERID_' . $this->config['payerNo']);
        if (empty($payerId)) {
            // 获取主卡信息
            if ($payerNo == 'USD_VA') {
                $queryParams['inquire_currency'] = 'USD';
            } else {
                $queryParams['account_no'] = $payerNo;
            }
            $payerIdRes = $this->accountBalance($queryParams);
            if (!isset($payerIdRes['accts'][0]['id'])) {
                $this->sendLog('', 'Apply API accountBalance payerId Info error', [], 'warning');
                throw new CardException(CardException::ERROR_BUSINESS);
            }
            // 1天防止过期
            Cache::put('TONGLIAN_PAYERID_' . $this->config['payerNo'], $payerIdRes['accts'][0]['id'], 24 * 60 * 60);
            $payerId = $payerIdRes['accts'][0]['id'];
        }

        return $payerId;
    }
}
