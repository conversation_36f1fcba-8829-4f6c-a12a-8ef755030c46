<?php


namespace App\Classes\Card\Gateways;

use App\Classes\Card\Contracts\CardSupplierGateWay;


abstract class GatewaysAbstract implements CardSupplierGateWay
{
    /**
     * 申请开卡
     *
     * @param array $params
     * @param integer $n
     * @return array
     * @throws \App\Classes\Card\Exceptions\CardException
     */
    public function batchApply(array $params, int $n): array
    {
        return [];
    }

    /**
     * 查询卡信息
     *
     * @param array $cardIds
     * @return array
     * @throws \App\Classes\Card\Exceptions\CardException
     */
    public function batchGetCardInfo(array $cardIds): array
    {
        return [];
    }

    /**
     * 查询卡余额
     *
     * @param array $virtual
     * @return array
     * @throws \App\Classes\Card\Exceptions\CardException
     */
    public function inquireBalance(array $virtual): array
    {
        return [];
    }

    /**
     * 查询共享卡卡余额
     *
     * @param array $virtual
     * @return array
     * @throws \App\Classes\Card\Exceptions\CardException
     */
    public function inquireShareBalance(array $virtual = []): array
    {
        return [];
    }

    /**
     * 充值
     *
     * @param array $params
     * @return array
     * @throws \App\Classes\Card\Exceptions\CardException
     */
    public function recharge(array $params): array
    {
        return [];
    }

    /**
     * 交易查询
     *
     * @param array $param
     * @return array
     * @throws \App\Classes\Card\Exceptions\CardException
     */
    public function inquiryTrade(array $param): array
    {
        return [];
    }

    /**
     * 结算查询
     *
     * @param array $param
     * @return array
     * @throws \App\Classes\Card\Exceptions\CardException
     */
    public function inquirySettlement(array $param): array
    {
        return [];
    }

    /**
     * 卡操作查询
     *
     * @param array $param
     * @return array
     * @throws \App\Classes\Card\Exceptions\CardException
     */
    public function inquiryCardOperation(array $param): array
    {
        return [];
    }

    /**
     * 账户余额查询
     *
     * @return array
     * @throws \App\Classes\Card\Exceptions\CardException
     */
    public function accountBalance(array $params = []): array
    {
        return [];
    }

    /**
     * 退款
     *
     * @param array $params
     * @return array
     * @throws \App\Classes\Card\Exceptions\CardException
     */
    public function refund(array $params): array
    {
        return [];
    }

    /**
     * 冻结
     *
     * @param array $params
     * @return array
     * @throws \App\Classes\Card\Exceptions\CardException
     */
    public function block(array $params): array
    {
        return [];
    }

    /**
     * 解冻
     *
     * @param array $params
     * @return array
     * @throws \App\Classes\Card\Exceptions\CardException
     */
    public function unblock(array $params): array
    {
        return [];
    }

    /**
     * 销卡
     *
     * @param array $params
     * @return array
     * @throws \App\Classes\Card\Exceptions\CardException
     */
    public function cancellation(array $params): array
    {
        return [];
    }

    /**
     * 共享卡限制
     *
     * @param array $params
     * @return array
     * @throws \App\Classes\Card\Exceptions\CardException
     */
    public function shareCardLimit(array $params): array
    {
        return [];
    }

    /**
     * 获取欺诈列表（黑名单）
     *
     * @return array
     * @throws \App\Classes\Card\Exceptions\CardException
     */
    public function getFraudList(): array
    {
        return [];
    }

    /**
     * 设置欺诈列表（黑名单）
     *
     * @param array $params
     * @return array
     * @throws \App\Classes\Card\Exceptions\CardException
     */
    public function setFraud(array $params): array
    {
        return [];
    }

    /**
     * 文件上传
     *
     * @param string $filePath
     * @param array $params
     * @return array
     * @throws \App\Classes\Card\Exceptions\CardException
     */
    public function uploadFile(string $filePath, array $params = []): array
    {
        return [];
    }

    /**
     * 交易明细下载
     *
     * @param string $date
     * @return string
     * @throws \App\Classes\Card\Exceptions\CardException
     */
    public function downTransDetail(string $date): string
    {
        return '';
    }

    /**
     * 对账单下载
     *
     * @param string $date
     * @return string
     * @throws \App\Classes\Card\Exceptions\CardException
     */
    public function downDailyReport(string $date): string
    {
        return '';
    }

    /**
     * 卡授权明细下载
     *
     * @param string $authTime
     * @return string
     * @throws \App\Classes\Card\Exceptions\CardException
     */
    public function downAuthTxn(string $authTime): string
    {
        return '';
    }

    /**
     * 创建持卡人
     *
     * @param array $params
     * @return array
     * @throws \App\Classes\Card\Exceptions\CardException
     */
    public function createHolder(array $params): array
    {
        return [];
    }

    /**
     * 查询持卡人
     *
     * @param array $params
     * @return array
     * @throws \App\Classes\Card\Exceptions\CardException
     */
    public function queryHolder(array $params): array
    {
        return [];
    }

    /**
     * 更新持卡人
     *
     * @param array $params
     * @return array
     * @throws \App\Classes\Card\Exceptions\CardException
     */
    public function updateHolder(array $params): array
    {
        return [];
    }

    /**
     * 绑定持卡人
     *
     * @param array $params
     * @return array
     * @throws \App\Classes\Card\Exceptions\CardException
     */
    public function bindCardHolder(array $params): array
    {
        return [];
    }
}
