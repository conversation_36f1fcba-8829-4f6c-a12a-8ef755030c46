<?php

namespace App\Classes\Card\Exceptions;

use App\Jobs\SendNotice;
use App\Jobs\SendSlsLog;

class CardException extends \Exception
{
    const UNKNOWN_ERROR = 9999;

    const INVALID_GATEWAY = 1001; //无效网关错误

    const INVALID_CONFIG = 1002; //无效配置错误

    const INVALID_ARGUMENT = 1003; //无效参数错误

    const ERROR_GATEWAY = 1004; //网关错误

    const INVALID_SIGN = 1005; //无效签名

    const ERROR_BUSINESS = 1006; //业务错误

    const ERROR_MAP = [
        self::UNKNOWN_ERROR    => '未知错误',
        self::INVALID_GATEWAY  => '无效网关',
        self::INVALID_CONFIG   => '无效配置',
        self::INVALID_ARGUMENT => '无效参数',
        self::ERROR_GATEWAY    => '网关错误',
        self::INVALID_SIGN     => '无效签名',
        self::ERROR_BUSINESS   => '业务错误'
    ];

    /**
     * Bootstrap.
     *
     * @param string       $message
     * @param array|string $raw
     * @param int|string   $code
     */
    public function __construct($message = '', $code = self::UNKNOWN_ERROR)
    {
        $message = $message === '' ? self::ERROR_MAP[$code] : $message;
        // 组装告警数据
        $content  = sprintf(
            "[虚拟卡告警]\n故障详情: %s\n来源: %s",
            $message,
            config('app.url')
        );
        $data = [
            'level'             => 1,
            'contents'          => $content,
            'notice_user_roles' => 'Administrator,Operate Supervisor',
            'type'              => 3,
            'status'            => 2,
        ];

        dispatch(new SendNotice($data, 5));
        dispatch(new SendSlsLog(
            ['message' => $content],
            [],
            'warning',
        ));

        parent::__construct($message, intval($code));
    }
}
