<?php

namespace App\Classes\Supports\Traits;

use App\Classes\Card\Exceptions\CardException;
use Guzzle<PERSON>ttp\Client;
use GuzzleHttp\Promise\Utils;
use Psr\Http\Message\ResponseInterface;

/**
 * Trait HasHttpRequest.
 *
 * @property string $baseUri
 * @property float $timeout
 * @property float $connectTimeout
 */
trait HasHttpRequest
{
    /**
     * Http client.
     *
     * @var Client|null
     */
    protected $httpClient = null;

    /**
     * Http client options.
     *
     * @var array
     */
    protected $httpOptions = [];



    /**
     * Send a GET request.
     *
     * @return array|string
     */
    public function get(string $endpoint, array $query = [], array $headers = [])
    {
        $option = [];

        if (!empty($query)) {
            $option['query'] = $query;
        }

        if (!empty($headers)) {
            $option['headers'] = $headers;
        }

        return $this->request('get', $endpoint, $option);
    }

    /**
     * Send more Get request
     */
    public function batchGet($endpoints, array $query = [], array $headers = [])
    {
        $promises = [];
        $option   = [];
        if (!empty($query)) {
            $option['query'] = $query;
        }

        if (!empty($headers)) {
            $option['headers'] = $headers;
        }
        foreach ($endpoints as $endpoint) {
            $promises[] = $this->getHttpClient()->getAsync($endpoint, $option);
        }
        $responses = Utils::unwrap($promises);
        $results   = [];
        foreach ($responses as $response) {
            $results[] = json_decode($response->getBody(), true);
        }
        return $results;
    }

    /**
     * Send a POST request.
     *
     * @param string|array $data
     *
     * @return array|string
     */
    public function post(string $endpoint, $data, array $options = [])
    {
        if (!is_array($data)) {
            $options['body'] = $data;
        } else {
            $options['form_params'] = $data;
        }

        return $this->request('post', $endpoint, $options);
    }

    /**
     * Send a PUT request.
     *
     * @param string|array $data
     *
     * @return array|string
     */
    public function put(string $endpoint, $data, array $options = [])
    {
        if (!is_array($data)) {
            $options['body'] = $data;
        } else {
            $options['form_params'] = $data;
        }

        $response = $this->getHttpClient()->put($endpoint, $options);
        $contents = $response->getBody()->getContents();
        $code     = $response->getStatusCode();

        return ['code' => $code, 'content' => $contents];
    }

    /**
     * Send more Post request
     */
    public function batchPost($endpoint, $data, $n, array $options = [])
    {
        $promises = [];
        if (!is_array($data)) {
            $options['body'] = $data;
        } else {
            $options['form_params'] = $data;
        }
        for ($i = 0; $i < $n; $i++) {
            $promises[] = $this->getHttpClient()->postAsync($endpoint, $options);
        }
        $responses = Utils::unwrap($promises);
        $results   = [];
        foreach ($responses as $response) {
            $results[] = json_decode($response->getBody(), true);
        }
        return $results;
    }

    /**
     * Send request.
     *
     * @return array|string
     */
    public function request(string $method, string $endpoint, array $options = [])
    {
        return $this->unwrapResponse($this->getHttpClient()->{$method}($endpoint, $options));
    }

    /**
     * Set http client.
     *
     * @return $this
     */
    public function setHttpClient(Client $client): self
    {
        $this->httpClient = $client;

        return $this;
    }

    /**
     * Return http client.
     */
    public function getHttpClient(): Client
    {
        if (is_null($this->httpClient)) {
            $this->httpClient = $this->getDefaultHttpClient();
        }

        return $this->httpClient;
    }

    /**
     * Get default http client.
     */
    public function getDefaultHttpClient(): Client
    {
        return new Client($this->getOptions());
    }

    /**
     * setBaseUri.
     *
     * @return $this
     */
    public function setBaseUri(string $url): self
    {
        if (property_exists($this, 'baseUri')) {
            $parsedUrl = parse_url($url);

            $this->baseUri = ($parsedUrl['scheme'] ?? 'http') . '://' .
                $parsedUrl['host'] . (isset($parsedUrl['port']) ? (':' . $parsedUrl['port']) : '');
        }

        return $this;
    }

    /**
     * getBaseUri.
     */
    public function getBaseUri(): string
    {
        return property_exists($this, 'baseUri') ? $this->baseUri : '';
    }

    public function getTimeout(): float
    {
        return property_exists($this, 'timeout') ? $this->timeout : 120.0;
    }

    public function setTimeout(float $timeout): self
    {
        if (property_exists($this, 'timeout')) {
            $this->timeout = $timeout;
        }

        return $this;
    }

    public function getConnectTimeout(): float
    {
        return property_exists($this, 'connectTimeout') ? $this->connectTimeout : 10.0;
    }

    public function setConnectTimeout(float $connectTimeout): self
    {
        if (property_exists($this, 'connectTimeout')) {
            $this->connectTimeout = $connectTimeout;
        }

        return $this;
    }

    /**
     * Get default options.
     */
    public function getOptions(): array
    {
        return array_merge([
            'base_uri'        => $this->getBaseUri(),
            'timeout'         => $this->getTimeout(),
            'connect_timeout' => $this->getConnectTimeout(),
            'verify'          => false
        ], $this->getHttpOptions());
    }

    /**
     * setOptions.
     *
     * @return $this
     */
    public function setOptions(array $options): self
    {
        return $this->setHttpOptions($options);
    }

    public function getHttpOptions(): array
    {
        return $this->httpOptions;
    }

    public function setHttpOptions(array $httpOptions): self
    {
        $this->httpOptions = $httpOptions;

        return $this;
    }

    /**
     * Convert response.
     *
     * @return array|string
     */
    public function unwrapResponse(ResponseInterface $response)
    {
        $contentType = $response->getHeaderLine('Content-Type');
        $contents    = $response->getBody()->getContents();

        if (false !== stripos($contentType, 'json') || stripos($contentType, 'javascript')) {
            return json_decode($contents, true);
        } elseif (false !== stripos($contentType, 'xml')) {
            return json_decode(json_encode(simplexml_load_string($contents, 'SimpleXMLElement', LIBXML_NOCDATA), JSON_UNESCAPED_UNICODE), true);
        }

        return $contents;
    }
}
