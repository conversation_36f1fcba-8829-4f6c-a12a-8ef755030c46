<?php

declare(strict_types=1);

namespace App\Classes\Supports\Traits;

use ReflectionClass;
use App\Classes\Supports\Str;

trait Arrayable
{
    /**
     * toArray.
     *
     * @throws \ReflectionException
     */
    public function toArray(): array
    {
        $result = [];

        foreach ((new ReflectionClass($this))->getProperties() as $item) {
            $k = $item->getName();
            $method = 'get'.Str::studly($k);

            $result[Str::snake($k)] = method_exists($this, $method) ? $this->{$method}() : $this->{$k};
        }

        return $result;
    }
}
